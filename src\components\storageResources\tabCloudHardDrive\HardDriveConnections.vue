<template>
  <div>
    <Modal v-model="model" width="600" :mask-closable="false">
      <template #header><p><span style="color:green">{{tableRow.name}}</span> 云硬盘管理连接</p></template>
      <div>
        <Table :columns="tableColumn" :data="tableData">
          <template v-slot:operation="{ row }">
            <Button type="primary" @click="separateDist(row)" :disabled="row.mountpoint == '/dev/vda'">分离云硬盘</Button>
          </template>
        </Table>
      </div>
      <div class="connecting_cloud_hosts" style="display:none">
        <h3 style="padding: 10px 0">连接到云主机</h3>
        <Select v-model="formItem.slect" :label-in-value='true' @on-change="hostChange">
          <Option v-for="item in selectData" :value="item.id" :key="item.id" >{{ item.name }}</Option>
        </Select>
      </div>
      <div slot="footer">
        <Button type="text" @click="model = false">取消</Button>
        <Button type="primary" @click="modelOK" :disabled="disabled">确认</Button>
      </div>
    </Modal>
  </div>
</template>
<script>
import {
  cloudDiskManageConnect, // 云硬盘表 管理连接
  cloudDiskSeparate, // 云硬盘 分离
  manageConnectCloudDisk, // 管理连接云硬盘
  cloudDiskConnect, // 云硬盘 连接
} from '@/api/storage';
export default {
  props: {
    connectionsTime: String,
    tableRow: Object,
  },
  watch: {
    connectionsTime(news){
      this.cloudDiskData(this.tableRow.id)
      this.model = true
      this.disabled = false
    },
    tableData:{
      handler(newValue, oldValue) {
        // if(newValue.length!==0) {
        //   document.getElementsByClassName("connecting_cloud_hosts")[0].style.display = "none"
        // }else{
        //   document.getElementsByClassName("connecting_cloud_hosts")[0].style.display = "block"
        // }
      }
    }
  },
  data(){
    return {
      model: false,
      disabled: false,
      formItem: {
        slect: '',
        id: '',
        mountpoint: '',
        vmID: '',
      },
      selectData: [],
      tableColumn: [
        { title: "名称", key: "vmname" },
        { title: "设备", key: "mountpoint", align: "center" },
        { title: "操作", key: "operation", width: 120, slot: "operation" }
      ],
      tableData: [],
    }
  },
  methods: {
    // 查询云硬盘表
    cloudDiskData(id) {
      cloudDiskManageConnect({id:id}).then(callback=>{
        this.tableData = callback.data.map(em=>{
          if(callback.data.vmname !== '') {
            return em
          }
        })
      })
      manageConnectCloudDisk().then(callback=>{
        if(callback.data.length !==0) {
          let arr = new Array();
          callback.data.forEach(item=>{
            arr.push({
              id:item.id+':'+item.mountpoint,
              name:item.name,
            })
          })
          this.selectData = arr
        }
      })
    },
    // 网络选择
    hostChange(item) {
      this.formItem.id = item.value.split(':')[0]
      this.formItem.mountpoint = item.value.split(':')[1]
    },
    // 分离云硬盘
    separateDist(row) {
      cloudDiskSeparate({
        volumeid: this.tableRow.id,
        vmid: row.vmid
      }).then(em=>{
        this.cloudDiskData(this.tableRow.id)
      })
    },
    // 确认事件
    modelOK() {
        if (formItem.slect!=='') {
          this.disabled = true;
          cloudDiskConnect({
            id: this.tableRow.id,
            vmid: this.formItem.vmID,
            mountpoint: this.formItem.mountpoint,
          }).then(callback=>{
            this.model = false;
            this.$emit("return-ok","管理连接操作完成");
          })
          .catch((error) => {
            this.$emit("return-error",'管理连接操作失败');
            this.disabled = false;
          });
        }else {
          this.$Message.warning({
            background: true,
            closable: true,
            duration: 5,
            content: "未选云主机",
          });
        }
    },
  }
}
</script>
