<template>
  <!-- 首页饼状图组件 -->
 <div class="pie-echarts-wrap">
   <div class="title-wraps mb-10">容量</div>
   <div class="title-pie-wrap title-pie-wrap1">
     <div class="capacity-wrap">
       <div class="title-name-size-wrap">
        <span class="name-size-wrap">裸容量</span>
       </div>
       <div class="info-list-wrap">
        <el-progress type="circle" :percentage="percentNum" color="#3367ff" :width="width" :stroke-width="strokeWidth" :show-text="true"></el-progress>
        <!-- <div class="show-text-wrap">
          <div class="fs-number-wrap">{{ percentNum }} %</div>
          <div class="user-text-wrap">(已使用)</div>
        </div> -->
       </div>
     </div>
     <div class="pie-wrap-right">
       <div class="info-item-wrap">
           <div class="color-box"></div>
           <span class="use-name-wrap">已使用</span>
           <span class="use-number-wrap">{{ df.state.userBytes }}GB</span>
         </div>
         <div class="info-item-wrap">
           <div class="color-box1"></div>
           <span class="use-name-wrap">未使用</span>
           <span class="use-number-wrap">{{ df.state.availBytes  }}GB</span>
         </div>
         <div class="info-item-wrap">
           <div class="color-box2"></div>
           <span class="use-name-wrap">共计</span>
           <span class="use-number-wrap">
             {{
               this.df.state.totalBytes
             }}GB</span>
         </div>
     </div>
   </div>
   <div class="title-wraps mb-10 mt-15">信息</div>
   <div class="title-pie-bottom-wrap" id="bar-charts-wrap">
     <div class="title-name-size-wrap">
      <span class="name-size-wrap">THE 分布式存储服务平台</span>
    </div>
    <img src="../../../assets/imager/coreTotal.png" alt="" class="mt-30 img-wrap">
    <div class="core-total-num-wrap">{{ clusterServer.core }}</div>
    <div class="sum-text-wrap">核心总数</div>
    <div class="memory-disk-wrap">
      <div class="center-wrap">
        <div>内存总计</div>
        <div>
          <span class="set-color1-wrap">{{ clusterServer.memory }}</span>
          <span>GB</span>
        </div>
      </div>
      <div class="line-wrap"></div>
      <div class="center-wrap">
        <div>磁盘总计</div>
        <div class="set-color2-wrap">{{ clusterServer.hardDisk }}</div>
      </div>
    </div>
   </div>
 </div>
</template>

<script>
  import * as echarts from 'echarts';
  import RadialIndicator from './RadialIndicator.vue' 
  export default {
    props: {
      datas: {
        type: Object,
        default: () => {}
      }
    },
    data() {
      return{
        strokeWidth: 18,
        width: 180,
        loading: false,
        percentNum: 0,
        df: {
          state: {
            availBytes: 0,
            userBytes: 0,
            totalBytes: 0
          }
        },
        clusterServer: {
          core: 0,
          memory: 0,
          hardDisk: 0
        }
      }
    },
    components: {
      RadialIndicator
    },
    mounted(){
      this.getNodeCpuTotalFun()
      this.getNodeMenoryTotalFun()
      this.getNodeCephOsdTotalFun()
    },
    methods:{
        /**
         * 获取总内存数量
         */
        getNodeMenoryTotalFun() {
          this.$axios.get('/apiquery/api/v1/query?query=node_memory_MemTotal_bytes').then(res => {
            let memoryNumber = 0
            res.data.data.result.map(item => {
              memoryNumber += item.value && item.value[1] * 1
            })
            let memoryNumberStr = memoryNumber / 1024 /1024 /1024
            this.clusterServer.memory = memoryNumberStr.toFixed(2)
          })
        },
        /**
         * 获取总硬盘数量
         */
        getNodeCephOsdTotalFun() {
          this.$axios.get('/apiquery/api/v1/query?query=node_memory_MemTotal_bytes').then(res => {
            let hardDiskNumber = 0
            res.data.data.result.map(item => {
              hardDiskNumber += item.value && item.value[1] * 1
            })
            this.clusterServer.hardDisk = hardDiskNumber
          })
        },
        /**
         * 获取集群核心CPu
         */
        getNodeCpuTotalFun() {
          this.$axios.get('/apiquery/api/v1/query?query=node_cpu_guest_seconds_total{mode="nice"}').then(res => {
            this.clusterServer.core = res.data.data.result.length
          })
        },
       roundNumber(num) {
        if (Number.isInteger(num)) {
          return num;
        } else {
          return num.toFixed(1);
        }
      },
      computedNumber() {
        this.df.state.availBytes = this.keepTwoDecimal(this.datas.df.stats.total_avail_bytes / 1024 /1024 / 1024)
        this.df.state.userBytes = this.keepTwoDecimal(this.datas.df.stats.total_used_raw_bytes / 1024 /1024 / 1024)
        this.df.state.totalBytes = this.roundNumber(this.df.state.availBytes *100 /100 + this.df.state.userBytes*100 / 100)
        this.percentNum = (this.df.state.userBytes  / this.df.state.totalBytes *100).toFixed(3) * 1
      },
      keepTwoDecimal(num) {
        var result = parseFloat(num);
        if (isNaN(result)) {
            return false;
        }
        result = Math.floor(num * 10) / 10;
        return result;
    },
     animationFinished() {},
    },
    watch: {
      datas:{
        deep:true,//true为进行深度监听,false为不进行深度监听
        handler(newVal, lodVal){
          this.computedNumber()
        }
      }
    }
  }
</script>

<style scoped lang='less'>
  .pie-echarts-wrap {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    .title-wraps {
      font-size: 20px;
      font-weight: 600;
    }
    .title-pie-wrap {
      width: 100%;
      height: 40%;
      background: white;
      border-radius: 8px;
    }
    .title-pie-bottom-wrap {
      flex: 1;
      background: #fff;
      border-radius: 8px;
      display: flex;
      flex-direction: column;
      align-items: center;
       .title-name-size-wrap {
        width: 100%;
        display: flex;
        height: 25px;
        align-items: center;
        .name-size-wrap {
          color: #101010;
          font-size: 16px;
          font-weight: 600;
          margin-left: 10px;
        }
      }
      .core-total-num-wrap {
        font-size: 30px;
        font-weight: 600;
        color: #212121;
        margin-top: 15px;
      }
      .sum-text-wrap {
        color: #b6b6b6;
        font-size: 14px;
        font-weight: 600;
      }
      .memory-disk-wrap {
        display: flex;
        justify-content: space-between;
        margin-top: 30px;
        color: #b2b2b2;
        .line-wrap {
          width: 1px;
          height: 20px;
          background: #eaeaea;
          margin: 20px 20px 0 20px;
        }
      }
      .center-wrap {
        display: flex;
        flex-direction: column;
        align-items: center;
      }
      .set-color1-wrap {
        color: #fb8183;
        font-size: 20px;
      }
      .set-color2-wrap {
        font-size: 20px;
        color: #22cec2;
      }
    }
  }
  .title-pie-wrap1 {
    display: flex;
  }
  .capacity-wrap {
    width: 46%;
    height: 100%;
    padding: 10px 0 0 10px;
    .title-name-size-wrap {
      width: 100%;
      display: flex;
      height: 25px;
      align-items: center;
      .name-size-wrap {
        color: #101010;
        font-size: 16px;
        font-weight: 600;
        margin-left: 10px;
      }
    }
    .info-list-wrap {
      width: 100%;
      // padding-left: 20px;
      height: calc(100% - 25px);
      display: flex;
      flex-direction: column;
      justify-content: center;
      position: relative;
      .show-text-wrap {
        position: absolute;
        top: 42%;
        left: 50%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        .user-text-wrap {
          margin-top: 5px;
          font-size: 12px;
          color: #959595;
        }
        .fs-number-wrap {
          font-size: 20px;
          font-weight: 600;
        }
      }
    }
  }
  .pie-wrap-right {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    padding-top: 20px;
    margin-left: 50px;
    .info-item-wrap {
      height: 50px;
      width: 100%;
      display: flex;
      align-items: center;
      .use-name-wrap {
        color: #131313;
        font-size: 12px;
        font-weight: 600;
        display: block;
        min-width: 45px;
        margin-right: 8px;
      }
      .use-number-wrap {
        color: #131313;
        font-size: 16px;
        font-weight: 600;
      }
      .color-box {
        width: 10px;
        height: 10px;
        background: #3367ff;
        margin-right: 5px;
      }
      .color-box1 {
        width: 10px;
        height: 10px;
        background: #e5e9f2;
        margin-right: 5px;
      }
      .color-box2 {
        width: 10px;
        height: 10px;
        margin-right: 5px;
      }
    }
  }
  #bar-charts-wrap {
    padding: 10px;
    display: flex;
    flex-direction: column;
    .the-css-title-wrap {
      color: #fd6865;
      font-size: 20PX;
      font-weight: 600;
    }
    .the-css-list-wrap {
      flex: 1;
      display: flex;
      flex-direction: column;
      .the-css-item-wrap {
        font-weight: 600;
        color: #101010;
        font-size: 20px;
        margin-top: 15px;
      }
    }
    @media screen and (min-width: 2000px) { // 在2k分辨率下的适配
      .img-wrap {
        margin-top: 100px;
      }
    }
  }
</style>