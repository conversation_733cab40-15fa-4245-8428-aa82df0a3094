<template>
  <div>
    <Modal v-model="model" width="600" :mask-closable="false">
      <template #header><p>{{ tableRow.username }} 账号权限设置</p></template>
      <div class="power_tree">
        <ul id="powerDemo" class="ztree"></ul>
      </div>
      <div slot="footer">
        <Button type="primary" @click="treeData">刷新</Button>
        <Button type="primary" @click="resetClick">重置权限</Button>
        <Button type="text" @click="model = false">取消</Button>
        <Button type="primary" @click="modelOK" :disabled="disabled">确认</Button>
      </div>
    </Modal>
  </div>
</template>
<script>
import {
  powerTreeQuery, // 权限树获取
  powerTreeEdit, // 权限树修改
  powerTreeReset, // 权限树重置
} from '@/api/other';
export default {
  props: {
    powerTime: String,
    tableRow: Object,
  },
  watch: {
    powerTime(news) {
      this.model = true
      this.disabled = false
      this.treeData();
    }
  },
  data() {
    return {
      model: false,
      disabled: false,
      zNodes: [],
      zTreeObj: null,
    }
  },
  methods: {
    // 获取权限树
    treeData(){
      powerTreeQuery({
        username: this.tableRow.username,
        role: this.tableRow.role_name
      }).then(callback=>{
        this.zNodes = callback.data.data
        this.initZTree()
      })
      this.initZTree()
    },
    initZTree() {
      let seting = {
        data: {
          simpleData: {
            enable: true,
            idKey: "id",
            pIdKey: "pid",
            rootPId: 0,
          },
          key: {
            name: "module" // 这里是用来指定节点名称的字段
          }
        },
        view: {
          showIcon: false, // 显示图标
        },
        check: {
          enable: true // 启用复选框
        },
        callback: {
          // onCheck: this.onNodeCheck,
        }
      }
      this.zTreeObj = $.fn.zTree.init($("#powerDemo"), seting, this.zNodes);
      this.zTreeObj.expandAll(true); // 默认展开所有树的分支
    },
    // 勾选状态改变
    onNodeCheck(event, treeId, treeNode) {
      console.log('权限树勾选',treeNode)
    },
    // 重置权限
    resetClick(){
      this.$Modal.confirm({
        title: "提示",
        content:
          `<p>是否对 ${this.tableRow.username} 登录账号进行重置权限操作？</p>`,
        onOk: () => {
          powerTreeReset({
            user_id: this.tableRow.id
          }).then(callback=>{
            this.$Message.success({
              background: true,
              closable: true,
              duration: 5,
              content: this.tableRow.username+" 账号权限重置完成",
            });
            this.model = false;
          })
        },
        onCancel: () => {},
      })
    },
    // 确认
    modelOK(){
      this.disabled = true;
      this.model = false;
      const allNodes = this.getAllNodesRecursively(this.zTreeObj.getNodes());
      powerTreeEdit({
        permissions: allNodes,
        user_id: this.tableRow.id,
        user_role: this.tableRow.role_name,
      }).then(callback=>{
        this.$Message.success({
          background: true,
          closable: true,
          duration: 5,
          content: this.tableRow.username+"账号权限设置完成",
        });
      }).catch((error) => {
        this.$Message.error({
          background: true,
          closable: true,
          duration: 5,
          content: this.tableRow.username+"账号权限设置失败",
        });
      });
    },
    // 递归获取所有节点
    getAllNodesRecursively(nodes, allNodes = []) {
      if (!nodes || nodes.length === 0) return allNodes;
      nodes.forEach((node) => {
        if (!node.checked) {
          const filteredNode = {
            id: node.id,
            pid: node.pid,
            role: node.role,
            module: node.module,
            module_code: node.module_code,
            checked: node.checked,
          };
          allNodes.push(filteredNode);
        }
        if (node.children && node.children.length > 0) {
          this.getAllNodesRecursively(node.children, allNodes);
        }
      });
      return allNodes;
    },
  },
}
</script>
<style lang="less">
.power_tree {
  height: 600px;
  overflow: auto;
  padding: 0 20px;
  >ul {
    margin: 0 auto;
    border-radius: 10px;
    padding: 0 40px;  
    li {
      padding: 5px 0;
      ul {
        padding: 0 0 0 30px!important;
      }
    }
  }
}
.ztree * {
  font-size: 15px!important;
}
</style>