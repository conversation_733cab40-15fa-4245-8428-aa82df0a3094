<template>
  <div class="connection-container">
    <div
      v-for="(item, index) in items"
      :key="index"
      :ref="el => itemRefs[index] = el"
      class="connectable-item"
      :style="{ left: item.x + 'px', top: item.y + 'px' }"
    >
      {{ item.label }}
    </div>
    <svg class="connection-svg">
      <polyline
        v-for="(line, index) in lines"
        :key="index"
        :points="line.points"
        fill="none"
        stroke="black"
      />
    </svg>
  </div>
</template>

<script>
export default {
  data() {
    return {
      items: [
        { label: 'Item 1', x: 50, y: 150 },
        { label: 'Item 2', x: 550, y: 50 },
      ],
      lines: [],
      itemRefs: [],
    };
  },
  mounted() {
    this.$nextTick(this.drawConnections);
  },
  methods: {
    drawConnections() {
      const item1 = this.itemRefs[0];
      const item2 = this.itemRefs[1];
      if (!item1 || !item2) return;

      const rect1 = item1.getBoundingClientRect();
      const rect2 = item2.getBoundingClientRect();

      const containerRect = this.$el.getBoundingClientRect();

      const x1 = rect1.left - containerRect.left + rect1.width ;
      const y1 = rect1.top - containerRect.top + rect1.height / 2;
      const x2 = rect2.left - containerRect.left;
      const y2 = rect2.top - containerRect.top + rect2.height / 2;

      // 计算直角路径的拐点
      const midX = x1 + (x2 - x1) / 2;
      const points = `${x1},${y1} ${midX},${y1} ${midX},${y2} ${x2},${y2}`;

      this.lines.push({ points });
    },
  },
};
</script>

<style>
.connection-container {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
}
.connectable-item {
  position: absolute;
  padding: 10px;
  background-color: #f0f0f0;
  border: 1px solid #ccc;
}
.connection-svg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}
</style>
