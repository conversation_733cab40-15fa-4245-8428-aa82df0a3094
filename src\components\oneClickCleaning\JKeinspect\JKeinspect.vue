<style lang="less">
@import "./JKeinspect.less";
</style>
<template>
  <div class="health_examination">
    <div class="inspection_area">
      <div>
        <h3>健康检查 <span id="fraction">{{totalScore}}分</span></h3>
        <ul>
          <li>耗时： <span>{{timeConsuming}}</span></li>
          <li>总体进度： <span>{{overallProgress}} %</span></li>
          <br>
          <li>检测结果： 共{{total}}项，其中{{JCsuccess}}项成功，{{JCwarning}}项警告，{{JCerror}}项故障</li>
        </ul>
      </div>
      <div class="inspection_btn" >
        <Button v-if="jianche" class="plus_btn inspect_btn" @click="startDetection()" ><span class="icon iconfont icon-jiance"></span> 开始检测</Button>
        <Button v-if="!jianche" class="plus_btn inspect_btn" @click="startDetection()" ><span class="icon iconfont icon-jiance"></span> 重新检测</Button>
      </div>
    </div>
    
    <div class="inspection_content">
      <Button v-if="this.tableData.length!==0" class="export_check_btn" type="primary" @click="exportData">
          <Icon type="ios-download-outline"></Icon>导出检查项
        </Button>
      <div class="detection_navigation">
        <p>检测汇总</p>
        <div>
          <Table :show-header="false" stripe :columns="hzColumn" :data="tableData">
            <!-- 检测结果 -->
            <template v-slot:result="{row}">
              <Icon v-if="row.result=='ok'" color="green" type="md-checkmark-circle"></Icon>
              <Icon v-if="row.result=='warning'" color="#ffb909" type="md-alert"></Icon>
              <Icon v-if="row.result=='error'" color="red" type="md-close-circle"></Icon>
            </template>
          </Table>
        </div>
      </div>
      <div class="check_list">
        <p>检测数据</p>
        
        <Table v-if="!leida" stripe :columns="jcColumn" ref="JCtable" :data="tableData">
          <!-- 检测项 -->
          <template v-slot:name="{row}">
            <a v-if="row.result !== 'error'" class="text_overflow" @click="nameClick(row)">{{row.name}}</a>
            <span v-else class="text_overflow">{{row.name}}</span>
          </template>
          <!-- 检测结果 -->
          <template v-slot:result="{row}">
            <Icon v-if="row.result=='ok'" color="green" type="md-checkmark-circle"></Icon>
            <Icon v-if="row.result=='warning'" color="#ffb909" type="md-alert"></Icon>
            <Icon v-if="row.result=='error'" color="red" type="md-close-circle"></Icon>
          </template>
          <!-- 扣分 -->
           <template v-slot:weigth="{row}">
            <span v-if="row.result=='ok'">0</span>
            <span v-if="row.result=='warning'" style="color:red">{{ row.weigth }}</span>
            <span v-if="row.result=='error'" style="color:red">{{ row.weigth }}</span>
          </template>
        </Table>
        <!-- 雷达扫描 -->
        <div v-if="leida" class="radar_chart">
          <div class="scanogram">
            <div class="scanogram">
              <div class="scanogram">
                <div class="scanogram">
                  <div id="radar"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 检测项详情 抽屉 -->
    <Drawer width="50%" :title="testTitle" :closable="false" v-model="testDetails">
      <div style="padding:10px">
        <Table :columns="JCXcolumn" :data="JCXData"></Table>
      </div>
    </Drawer>
  </div>
</template>
<script>
import {
  healthSummaryQuery, // 健康汇总
  healthSummaryCheck, // 健康单查
} from '@/api/other';
export default {
  data() {
    return {
      // 健康检测模块 
      jianche:true,
      timeConsuming:'0分0秒',
      overallProgress:0,
      total:0,
      JCsuccess:0,
      JCwarning:0,
      JCerror:0,
      totalScore:100,
      // 雷达模块
      leida:false,
      // 检测汇总
      hzColumn:[
        { title:"检测项",key:"name" },
        { title:"检测结果",key:"result",align: "right", slot: 'result' }
      ],
      tableData:[],
      // 检测数据
      jcColumn:[
        { title:"检测项",key:"name",align: "center", slot: 'name' },
        { title:"检测结果",key:"result",align: "center", slot: 'result' },
        { title:"扣分",key:"weigth",align: "center", slot: 'weigth'},
        // { title:"详情",key:"xiangqing",align: "center"},
      ],
      tableData:[],
      times:null,
      randomData:null,
      
      // 检测项目详情抽屉
      testDetails:false,
      testTitle:"",
      JCXcolumn:[],
      JCXData:[],

      
      weigthList:0,
      listTime:0,
    }
  },
  watch: {
    totalScore(news){
      let fraction = document.getElementById("fraction")
      if(news<=70 && news>50){
        fraction.style.color = "#ffb909"
      }else if(news<=50){
        fraction.style.color = "red"
      }else{
        fraction.style.color = "green"
      }
    }
  },
  mounted(){
    // this.testSummary()
  },
  methods: {
    startDetection() {
      this.testSummary()
      this.jianche=false
      this.leida = true
    },
    // 获取检测汇总数据
    testSummary(){
      healthSummaryQuery()
      .then((callback) => {
        this.timePackage() // 时间初始化
        this.overallProgress= 0 // 进度初始化
        this.tableData=new Array() // 表数据初始化
        this.total=0 // 检查结果 总数 初始化
        this.JCwarning=0 // 检查结果 警告 初始化
        this.JCerror=0 // 检查结果 故障 初始化
        this.totalScore = 100 // 分数初始化
        this.weigthList = 0
        let itemweigth = 0
        setTimeout(()=>{
          clearInterval(this.times)
        },12000)
        callback.data.forEach(item=>{
          this.weigthList+=item.weigth
        })
        callback.data.forEach(item=>{
          healthSummaryCheck(item.key)
          .then((calKey,index) => {
            let randomData = this.randomRange(1, 2)
            this.listTime +=randomData
            setTimeout(() => {
              this.tableData.push({
                key:calKey.data.key,
                name:calKey.data.name,
                result:calKey.data.result,
                weigth:calKey.data.result=="ok"?'-0':calKey.data.result=="warning"?'-'+(calKey.data.weigth/this.weigthList*50).toFixed(0) :'-'+(calKey.data.weigth/this.weigthList*100).toFixed(0),
              })
              this.total++
              if(calKey.data.result=="ok"){
                this.JCsuccess++
              }else if(calKey.data.result=="warning"){
                this.JCwarning++
                this.totalScore=(this.totalScore-(calKey.data.weigth/this.weigthList*50)).toFixed(0)
              }else if(calKey.data.result=="error"){
                this.JCerror++
                this.totalScore=(this.totalScore-calKey.data.weigth/this.weigthList*100).toFixed(0)
              }
              itemweigth+=calKey.data.weigth
              this.overallProgress=(itemweigth/this.weigthList*100).toFixed(1)
              if(itemweigth==this.weigthList) {
                this.jianche = true
                this.leida = false
                clearInterval(this.times)
              }
            },this.listTime*1000)
          }).catch((error) => {
            let randomData = this.randomRange(1, 2)
            this.listTime +=randomData
            this.tableData.push({
              key:item.key,
              name:item.name,
              result:'error',
              weigth:'-'+(item.weigth/this.weigthList*100).toFixed(0),
            })
            this.total++
            this.JCerror++
            this.totalScore=(this.totalScore-item.weigth/this.weigthList*100).toFixed(0)
            itemweigth+=item.weigth
            if(itemweigth==this.weigthList) {
              this.jianche = true
              this.leida = false
              clearInterval(this.times)
            }
          });
        })
			})
    },
    // 健康检查时间封装
    timePackage(){
      this.timeConsuming='0分0秒'
      let S = 0
      let M = 0
      this.times=setInterval(() => {
        S++
        if(S>=60) {
          S=0
          M++
        }
        this.timeConsuming=M+'分'+S+'秒'
      }, 1000);
    },
    // 随机数据封装
    randomRange(min, max) { 
      return Math.floor(Math.random() * (max - min)) + min;
    },

    // 检测项详情
    testTableData(row){
      this.JCXcolumn= new Array()
      this.JCXData= new Array()
      healthSummaryCheck(row.key)
      .then((calKey) => {
        this.JCXcolumn= calKey.data.columns
        this.JCXData= calKey.data.data
      })
    },
    // 导出表数据
    exportData() {
       function getFormatTime(datetime) {
        let time = new Date(datetime)//时间戳为10位要*1000，13位不用*1000
        let year = time.getFullYear()
        let month = time.getMonth() + 1
        let date = time.getDate()
        let hours = time.getHours()
        let minute = time.getMinutes()
        let second = time.getSeconds()
        if (month < 10) { month = '0' + month }
        if (date < 10) { date = '0' + date }
        if (hours < 10) { hours = '0' + hours }
        if (minute < 10) { minute = '0' + minute }
        if (second < 10) { second = '0' + second }
        return year + '-' + month + '-' + date + ' ' + hours + ':' + minute + ':' +second
      }
      let timestamp = Date.parse(new Date());
      this.$refs.JCtable.exportCsv({
        filename: "健康检查"+getFormatTime(timestamp),
      });
    },
    // 检测项详情
    nameClick(row) {
      this.testDetails = true;
      this.testTableData(row)
      this.testTitle = row.name+"详情"
    },
  },
  beforeDestroy() {
    clearInterval(this.times)
  },
}
</script>