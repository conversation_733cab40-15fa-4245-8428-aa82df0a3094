<template>
  <div>
    <Modal
      v-model="model"
      width="600"
      :mask-closable="false"
    >
      <template #header><p>拉取容器镜像</p></template>
      <Form :model="formItem" ref="formItem" :rules="ruleValidate" :label-width="120">
					<FormItem label="容器镜像名称" prop="name">
            <Input v-model="formItem.name" placeholder="请输入容器镜像名称"></Input>
        	</FormItem>
          <FormItem label="主机选择" prop="host">
            <Select v-model="formItem.host" >
              <Option v-for="item in hostData" :value="item.uuid" :key="item.uuid">{{ item.hostname }}</Option>
            </Select>
          </FormItem>
				</Form>
      <div slot="footer">
        <Button type="text" @click="model = false">取消</Button>
        <Button type="primary" @click="modalOK" :disabled="disabled">确认</Button>
      </div>
    </Modal>
  </div>
</template>
<script>
import { hostQuery,containerImagePull } from '@/api/container';  // 容器镜像 拉取
export default {
  props: {
    newTime:String,
  },
  watch: {
    newTime(news){
      this.model = true
      this.disabled = false
      this.$refs.formItem.resetFields()
      // 获取主机数据
      hostQuery().then(callback=>{
        this.hostData = callback.data.hosts
        this.formItem.host = callback.data.hosts[0].uuid
      })
    }
  },
  data(){
    return {
      model:false,
      disabled: false,
      formItem:{
        name: '',
        host: '',
        hostData: [],
      },
      hostData: [],
      ruleValidate:{
        name:[
          { required: true, message: '必填项', trigger: 'change' },
          { type: 'string', min: 2,max: 32, message: '请输入2-32位字符', trigger: 'change' }
        ],
        host:[{ required: true, message: '必选项', trigger: 'blur' }],
      }
    }
  },
  methods: {
    modalOK() {
      this.$refs.formItem.validate((valid) => {
        if(valid) {
          this.disabled = true
          containerImagePull({
            repo: this.formItem.name,
            host: this.formItem.host
          })
          .then((callback) => {
            this.model = false;
            this.$emit("return-ok",{
              msg: '拉取容器镜像完成',
              type: 'ok'
            });
          })
          .catch((error) => {
            this.disabled = false
            this.$emit("return-ok",{
              msg: '拉取容器镜像失败',
              type: 'error'
            });
          })
        }
      })
    },
  }
}
</script>