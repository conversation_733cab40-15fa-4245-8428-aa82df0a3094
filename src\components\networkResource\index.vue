<style lang="less">
  @import "./networkResource.less";
</style>
<template>
  <div class="network_tabs">
    <Tabs  class="tabs_template" name="1" v-model="tabName" @on-click="tabsClick">
      <TabPane
        v-if="item.show"
        v-for="item in tabsData"
        :key="item.name"
        :name="item.name"
        tab="1"
        :label="renderTabLabel(item.name)">
        <TabNetwork v-if="item.name === '网络'" :tabSelected="tabName" />
        <TabTopology v-if="item.name === '拓扑'" :tabSelected="tabName" />
        <TabSecurity v-if="item.name === '安全组'" :tabSelected="tabName" />
        <TabPhysical v-if="item.name === '物理网络'" :tabSelected="tabName" />
        <topoTab v-if="item.name === '拓扑例子'" :tabSelected="tabName" ></topoTab>
      </TabPane>
    </Tabs>
  </div>
</template>
<script>
import { powerCodeQuery } from '@/api/other'; // 权限可用性 查询

import TabNetwork from "./tabNetwork/TabNetwork.vue";
import TabTopology from "./tabTopology/TabTopology.vue";
import TabSecurity from "./tabSecurity/TabSecurity.vue";
import TabPhysical from "./tabPhysical/TabPhysical.vue";

import topoTab from "./topoTab/topoTab.vue";
export default {
	components: {
    TabNetwork,
    TabTopology,
    TabSecurity,
    TabPhysical,
    topoTab,
  },
	data() {
		return {
      tabName: '',
      tabsData: [
        { name: '网络', code: 'wangluo', show: false },
        { name: '拓扑', code: 'tuopu', show: false },
        { name: '安全组', code: 'anquanzu', show: false },
        { name: '物理网络', code: 'wuliwangluo', show: false },
      ],
		}
	},
  mounted(){
    this.tabsQuery()
  },
  methods: {
    renderTabLabel(item){
      return (h) => {
        return h('div', [
          h('span', {
            class: 'select_tab_border',
            style: { background: this.tabName === item ? '#fb6129' : '' }
          }),
          h('span', item)
        ]);
      };
    },
    // 查询标签页权限
    tabsQuery(){
      powerCodeQuery({
        module_code:[
          'wangluo',
          'tuopu',
          'anquanzu'
        ]
      }).then(callback=>{
        if(callback.data.data.wangluo) {
          this.tabName = '网络'
        }else if(callback.data.data.anquanzu) {
          this.tabName = '安全组'
        }
        this.tabsData.forEach(item => {
          if (callback.data.data.hasOwnProperty(item.code)) {
            item.show = callback.data.data[item.code];
          }
        });
        this.$store.state.power.networkResourceTab = this.tabName
      })
    },
    tabsClick(name){
      this.$store.state.power.networkResourceTab = name
    },
  },
}
</script>
