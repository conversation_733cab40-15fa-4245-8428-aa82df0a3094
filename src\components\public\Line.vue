<style lang="less" scoped>
  .line_area{
    width: 100%;
    height:100%;
  }
  .no_chartsdata_area {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    color: #999;
  }
  .no_chartsdata_area {
  width: 100%;
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  color: #999;
  img {
    height: 120px;
  }
}
</style>
<template>
<!-- 折线图 -->
<div class="line_area">
  <div class="no_chartsdata_area" v-if="this.datanone">
    <img src="../../assets/wushuju.png" alt="">
    <span>暂时无数据，请在目标主机安装监控工具</span>
  </div>
  <div ref="detailsChart" style="width: 100%;height:100%;" v-if="!this.datanone"></div>
</div>
</template>
<script>
  export default {
    // 接受父组件传值
    props: {
      datas: Object,
    },
    watch: {
      datas(value) {
        this.chartdata=value
        if(this.chartdata!==undefined){
          this.datanone = false
          this.renderChart(value)
        }else {
          this.datanone = true
        }
      },
    },
    data() {
      return {
        chartdata:undefined,
        datanone: false,
      }
    },
    mounted() {},
    methods: {
      renderChart(vls) {
        let chartDom = this.$refs.detailsChart
        this.$echarts.dispose(chartDom)
        let chart = this.$echarts.init(chartDom);
        let color = [
          "#F32370","#2BC043","#1684FC","#386aff","#ff950a","#a042ed","#f06361","#ff8400"
          ];
        const hexToRgba = (hex, opacity) => {
          let rgbaColor = "";
          let reg = /^#[\da-f]{6}$/i;
          if (reg.test(hex)) {
            rgbaColor = `rgba(${parseInt("0x" + hex.slice(1, 3))},${parseInt(
              "0x" + hex.slice(3, 5)
            )},${parseInt("0x" + hex.slice(5, 7))},${opacity})`;
          }
          return rgbaColor;
        };

        let arr = new Array
        let _this = this
        vls.data.forEach((item,index) => {
          arr.push({
              name: item.title,
              type: "line",
              smooth: true,
              symbolSize: 1,
              zlevel: 3,
              lineStyle: {
                // 波浪线下边的虚线
                normal: {
                  color: color[index],
                  shadowBlur: 3,
                  shadowColor: hexToRgba(color[index], 0.5),
                  shadowOffsetY: 8,
                },
              },
              symbol: "circle", //数据交叉点样式
              areaStyle: {
                normal: {
                  color: new this.$echarts.graphic.LinearGradient(
                    0,0,0,1,
                    [
                      {
                        offset: 0,
                        color: hexToRgba(color[index], 0.3),
                      },
                      {
                        offset: 1,
                        color: hexToRgba(color[index], 0.1),
                      },
                    ],
                    false
                  ),
                  shadowColor: hexToRgba(color[index], 0.1),
                  shadowBlur: 10,
                },
              },
              data: item.list,
            })
        });
        chart.setOption ({
          backgroundColor: "#fff",
          color: color,
          legend: {
            // top: 20,
            itemWidth: 0,
            itemHeight: 10,
            type:'scroll',
          },
          tooltip: {
            trigger: "axis",
            formatter: function (params) {
              let html = "";
              params.forEach((v) => {
                  html += `<div style="color: #666;font-size: 14px;line-height: 24px">
                        <span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${
                          color[v.componentIndex]
                        };"></span>
                        ${_this.upAndDown("name",v.seriesName)}
                        <span style="color:${_this.upAndDown("color",v.seriesName)}">${_this.upAndDown("text",v.seriesName)}</span>
                        <span style="color:${
                          color[v.componentIndex]
                        };font-weight:700;font-size: 18px;margin-left:5px">${
                  _this.formatNumber(vls.unit,v.value)<0?_this.formatNumber(vls.unit,v.value)*-1:_this.formatNumber(vls.unit,v.value)
                }</span> ${ _this.formatUnit(vls.unit,v.value) }`;
              });
              return html;
            },
            extraCssText:
              "background: #fff; border-radius: 0;box-shadow: 0 0 3px rgba(0, 0, 0, 0.2);color: #333;",
            axisPointer: {
              type: "shadow",
              shadowStyle: {
                color: "#ffffff",
                shadowColor: "rgba(225,225,225,1)",
                shadowBlur: 5,
              },
            },
          },
          grid: {
            top: 50,
            left:30,
            right:30,
            bottom:20,
            containLabel: true,
          },
          xAxis: [
            {
              type: "category",
              boundaryGap: false,
              axisLabel: {
                formatter: "{value}",
                textStyle: {
                  color: "#333",
                },
              },
              axisLine: {
                lineStyle: {
                  color: "#D9D9D9",
                },
              },
              data: _this.chartdata.time,
            },
          ],
          yAxis: [
            {
              type: "value",
              // name: "单位（MB）",
              name: "",
              axisLabel: {
                textStyle: {
                  color: "#b3b3b3",
                  fontSize: 14,
                },
                formatter: function(value) {
                    // 这里将原始的值（value）和单位（'元'）拼接起来
                  return _this.formatNumber(vls.unit,value) + _this.formatUnit(vls.unit,value)
                }
              },
              nameTextStyle: {
                color: "#666",
                fontSize: 12,
                lineHeight: 40,
              },
              // 分割线
              splitLine: {
                lineStyle: {
                  type: "dashed",
                  color: "#E9E9E9",
                },
              },
              axisLine: {
                show: false,
              },
              axisTick: {
                show: false,
              },
            },
          ],
          series: arr,
        })
      },
      // 字节+单位转换
      formatNumber(unit,size){
        if(unit=="%") {
          return Math.floor(size * 100) / 100
        }else {
          const units = ['MB', 'GB', 'TB', 'PB'];
          let unitIndex = 0;
          while (size >= 1024 && unitIndex < units.length - 1) {
              size /= 1024;
              unitIndex++;
          }
          return Math.floor(size * 100) / 100
        }
      },
      // 字节+单位转换
      formatUnit(unit,size){
        if(unit=="%") {
          return unit
        }else {
          const units = ['MB', 'GB', 'TB', 'PB'];
          let unitIndex = 0;
          while (size >= 1024 && unitIndex < units.length - 1) {
              size /= 1024;
              unitIndex++;
          }
          return units[unitIndex]
        }
      },
      upAndDown(type,name){
        let sx = name.split("上行");
        let xx = name.split("下行");
        if(type=="text") {
          if(sx.length == 2) {
            return "上行"
          } else if(xx.length == 2) {
            return "下行"
          }else {
            return ""
          }
        }else if(type=="color") {
          if(sx.length == 2) {
            return "#216fed"
          } else if(xx.length == 2) {
            return "#37d159"
          }else {
            return ""
          }
        }else {
          if(sx.length == 2) {
            return sx[0]
          } else if(xx.length == 2) {
            return xx[0]
          }else {
            return name
          }
        }
      },
    }
  }
</script>
<style>
.charts-title::before {
  content: "\2022"; /* 使用Unicode字符作为符号（实心圆点）*/
  color: #ff0000; /* 修改符号颜色 */
  margin-right: 0.5em; /* 可以调整符号与文字之间的距离 */
}
</style>