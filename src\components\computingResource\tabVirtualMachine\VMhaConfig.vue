<template>
  <div>
    <Modal
      v-model="model"
      width="600"
      :mask-closable="false"
    >
      <template #header><p><span style="color:green"></span>高可用设置-群操作</p></template>
      <div>
        高可用设置：
        <i-switch size="large" v-model="status">
          <span slot="open">启用</span>
          <span slot="close">禁用</span>
        </i-switch>
      </div>
      <div slot="footer">
        <Button type="text" @click="model = false">取消</Button>
        <Button type="primary" @click="modelOK" :disabled="disabled">确认</Button>
      </div>
    </Modal>
  </div>
</template>
<script>
import { vmHAaction } from '@/api/virtualMachine'; // 高可用操作
export default {
  props: {
    tableArr: Array,
    haGroupTime: String,
  },
  watch: {
    haGroupTime(news){
      this.ids = new Array()
      this.names = new Array()
      this.tableArr.forEach(em=>{
        this.ids.push(em.id)
        this.names.push(em.name)
      })
      this.status = true
      this.disabled = false
      this.model = true
    }
  },
  data(){
    return {
      disabled:true,
      model:false,
      ids: [],
      names: [],
      status: true,
    }
  },
  methods: {
    // 虚拟机表操作（设置）确认事件
    modelOK() {
      this.disabled = true
      // 判断 硬盘容量 是否有改变
      vmHAaction({
        ids: this.ids,
        names: this.names,
        ha_status: this.status
      })
      .then((returndata) => {
        if(returndata.data.msg == "ok"){
          this.$emit("return-ok",'虚拟机高可用设置，操作完成');
          this.model = false
        }else {
          this.$emit("return-ok",'虚拟机高可用设置，操作失败');
          this.disabled = false
        }
      }).catch((error) => {
        this.disabled=false
        this.$emit("custom-event",'虚拟机高可用设置，操作失败');
      })
    },
    // cpu选择
    formCpu(val) {
      return val + "核";
    },
    // 内存选择
    formMemory(val) {
      return val + " GB";
    },
    // 硬盘选择
    formDisk(val) {
      return val + " GB";
    },
  }
}
</script>