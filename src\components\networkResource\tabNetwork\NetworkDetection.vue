<template>
<div>
  <Modal v-model="model" width="600" :mask-closable="false">
    <template #header><p>网络探测</p></template>
    <Form
      :model="formItem"
      ref="formItem"
      :rules="rulesForm"
      :label-width="120"
    >
      <FormItem label="IP地址" prop="ip">
        <Input v-model="formItem.ip"></Input>
      </FormItem>
      <FormItem label="端口" prop="port" >
        <Input v-model="formItem.port" placeholder="输入端口"></Input>
      </FormItem>
      <FormItem label="协议">
        <Select v-model="formItem.protocol">
          <Option v-for="item in protocolData" :value="item.value" :key="item.value">{{ item.label }}</Option>
        </Select>
      </FormItem>
    </Form>
    <div slot="footer">
      <Button type="text" @click="model = false">关闭</Button>
      <Button type="primary" @click="modelOK" :disabled="disabled">探测</Button>
    </div>
  </Modal>
</div>
</template>
<script>
import {netDetection} from '@/api/network';

export default {
  props: {
    detectionTime: String,
  },
  watch: {
    detectionTime(news){
      this.$refs.formItem.resetFields();
      this.formItem.protocol = 'http'
      this.model = true
      this.disabled = false
    }
  },
  data(){
    const propIP = (rule, value, callback) => {
      let list = /^(25[0-4]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-4]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-4]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-4]|2[0-4][0-9]|[01]?[1-9][0-9]?|1[0-9]{2}|2[0-4][0-9])$/
      if(list.test(value)) {
        callback();
      }else {
        callback(new Error("请输入正确的IP地址"));
      }
    };
    const propPort = (rule, value, callback) => {
      let list = /^(?:[0-9]{1,4}|[1-5][0-9]{4}|6[0-4][0-9]{4}|65[0-4][0-9]{2}|655[0-2][0-9]|6553[0-5])$/;
      if(list.test(value)) {
        callback();
      }else {
        callback(new Error("请输入正确的端口（0-65535之间）"));
      }
    };
    return {
      model: false,
      disabled: false,
      formItem:{
        ip:'',
        port: '',
        protocol: 'http',
      },
      protocolData: [
        { value: "http", label: "http" },
        { value: "tcp", label: "tcp" },
      ],
      
      // 正则验证
      rulesForm: {
        ip: [
          { required: true, message: "必填项", trigger: "change" },
          { validator: propIP, trigger: "change" },
        ],
        port: [
          { required: true, message: "必填项", trigger: "change" },
          { validator: propPort, trigger: "change" },
        ],
      },
    }
  },
  methods: {
    // 网络探测确认事件
    modelOK() {
      this.$refs.formItem.validate((valid) => {
        if (valid) {
          this.disabled = true;
          netDetection({
            ip: this.formItem.ip,
            port: this.formItem.port,
            proto: this.formItem.protocol,
          })
          .then(callback => {
            this.$Message.success({
              background: true,
              closable: true,
              duration: 5,
              content: callback.data.msg,
            });
            this.model = false
          })
          .catch((error) => {
            this.$emit("return-error",'网络探测失败');
            this.disabled = false;
          });
        }
      });
    },
    
  }
}
</script>
