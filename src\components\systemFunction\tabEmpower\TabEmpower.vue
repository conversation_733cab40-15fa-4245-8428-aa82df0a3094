<style lang="less">
@import "../systemFunction.less";
</style>
<template>
  <div class="empower_region">
    <Button class="remove" @click="authorizationGET">刷新</Button>
    <div class="empower_data">
      <Card class="empower_card" v-for="item in empowerData" :key='item.name'>
        <h4>{{ item.name }}</h4>
        <h2>{{ item.value }}</h2>
      </Card>
    </div>
  </div>
</template>
<script>
import {authorizationQuery} from '@/api/system';
export default {
  props: {
    tabName: String,
  },
  watch: {
    tabName(value) {
      if(value=='授权管理') {
        this.authorizationGET()
      }
    },
  },
  mounted() {
    if(this.$store.state.power.systemFunctionTab == '授权管理') {
      this.authorizationGET()
    }
  },
  data() {
    return {
      empowerData: [
        { name: '项目', value: '***' },
        { name: '节点数', value: '***' },
        { name: 'CPU', value: '***' },
        { name: '过期时间', value: '***' },
        { name: '集群码', value: '***' },
      ],
    };
  },

  methods: {
    // 获取授权信息
    authorizationGET(){
      authorizationQuery({}).then(callback => {
        let list = JSON.parse(callback.data.data)
        let date = new Date(list.time);
        let year = date.getUTCFullYear();
        let month = String(date.getUTCMonth() + 1).padStart(2, '0'); // 月份从0开始，所以加1
        let day = String(date.getUTCDate()).padStart(2, '0');
        let formattedDate = `${year}-${month}-${day}`
        this.empowerData = [
          { name: '项目', value: list.name },
          { name: '节点数', value: list.scale },
          { name: 'CPU', value: list.cpu },
          { name: '过期时间', value: formattedDate },
          { name: '集群码', value: list.code },
        ]
      })
    },
  }
};
</script>