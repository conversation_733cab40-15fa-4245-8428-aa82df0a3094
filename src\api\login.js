// 登录
import axios from "axios";
import * as basic_proxy from "@/api/config";

// 登录接口
export async function logon(params) {
  return await axios.post(basic_proxy.login + "/login", params);
}
// 登录 查询密码规则
export async function queryCodeRule() {
  return await axios.get(basic_proxy.login + "/v2/getconfig");
}
// 修改密码规则
export async function modifyCodeRule(params) {
  return await axios.post(basic_proxy.login + "/v2/setconfig", params);
}
// 登录过期改密码
export async function loginExpiredChangePassword(params) {
  return await axios.put(basic_proxy.login + "/v2/setpassword/token", params);
}
// 修改密码
export async function userChangesPassword(params) {
  return await axios.put(basic_proxy.login + "/v2/setpassword", params);
}

// 获取授权信息
export async function authorizationDataQuery(params) {
  return await axios.post(basic_proxy.login + "/license", params);
}

// 退出登录接口（未生效）
export async function logout(params) {
  return await axios.get(basic_proxy.login + "/logout", params);
}
