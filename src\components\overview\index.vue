<style lang="less">
@import "./overview.less";
</style>
<template>
  <div class="overview_page">
    <div class="capacity">
      <div class="capacity_mod physical_machine_img" >
        <div class="quantity_module">
          <p class="measure_title"><span>物理机数量（台）</span></p>
          <p class="rate_title" @click="detailsJump('wuliji')">{{ capacityData.wljNumber }}</p>
        </div>
        <img src="../../assets/wlj.png" alt="" />
      </div>
      <div class="capacity_mod vm_img" >
        <div class="quantity_module">
          <p class="measure_title"><span>虚拟机数量（台）</span> <span style="border-right-color: #ffb22b" class="triangle" ></span></p>
          <p class="rate_title" @click="detailsJump('xuniji')">{{ capacityData.xnjNumber }}</p>
        </div>
        <img src="../../assets/xnj.png" alt="" />
      </div>
      <div class="capacity_mod storage_img" >
        <div class="quantity_module">
          <p class="measure_title" ><span>存储总量（{{byteUnitConversion("unit",capacityData.rwsNumber) }}）</span> <span style="border-right-color: #529ae8" class="triangle" ></span></p>
          <p class="rate_title"  @click="detailsJump('cuncu')">{{byteUnitConversion("byte",capacityData.rwsNumber) }}</p>
        </div>
        <img src="../../assets/rws.png" alt="" />
      </div>
    </div>
    <div class="utilization_rate">
      <!-- CPU -->
      <div class="circle">
        <div class="titles">
          <span><span :class="cpuZY" @click="CPUswitch('zy')">CPU使用率</span> <span :class="cpuFP" @click="CPUswitch('fp')">CPU分配比</span></span>
          <Tooltip>
            <span class="help"><Icon type="md-help" /></span>
            <div slot="content">
                <p>CPU使用率：显示集群中的物理CPU平均使用率</p>
                <p>CPU分配比：集群中所有虚拟机已分配的CPU核数占集群所有CPU总核数的比例</p>
                <p>物理CPU总数(核)：显示集群中的物理CPU总核数，即各主机物理CPU核数数量总和</p>
                <p>VCPU总数(核)：显示集群中的虚拟CPU总核数，即各主机虚拟CPU核数数量总和</p>
                <p>已分配VCPU(核)：显示集群中已经分配给虚拟机使用的虚拟CPU总核数</p>
            </div>
          </Tooltip>
        </div>
        <div class="rate_ring">
          <!-- stroke-color="#3767ff" -->
          <i-circle
            :percent="Math.floor(usageRate.cpu)"
            stroke-color="#4fa940"
            :stroke-width="8"
            :trail-width="7"
            :size="circleSize"
          >
            <div class="demo-Circle-custom">
              <h1>{{progressLoop(usageRate.cpu)}}</h1>
              <p>（使用量）</p>
            </div>
          </i-circle>
          <div class="rate_quantity">
            <div v-if="usageRate.cpu_surplus">
              <h3  @click="detailsJump('wuliji')">{{ usageRate.cpu_surplus }}</h3>
              <p>{{ titleALL.cpu_title_surplus }}</p>
            </div>
            <div>
              <h3  @click="detailsJump('wuliji')">{{ usageRate.cpu_total }}</h3>
              <p>{{ titleALL.cpu_title_total }}</p>
            </div>
          </div>
        </div>
      </div>
      <div class="circle">
        <div class="titles">
          <span><span :class="memoryZY" @click="memorySwitch('zy')">内存使用率</span> <span :class="memoryFP" @click="memorySwitch('fp')">内存分配比</span></span>
          <Tooltip>
            <span class="help"><Icon type="md-help" /></span>
            <div slot="content">
                <p>已使用内存：显示集群中已使用的物理内存</p>
                <p>内存总数：显示集群中物理内存总容量</p>
                <p>已分配内存：显示集群中已经分配给虚拟机使用的内存总数</p>
                <p>内存分配比：已分配内存/内存总数 × 100%</p>
                <p>内存使用率：显示集群中的物理内存平均使用率</p>
            </div>
          </Tooltip>
        </div>
        <div class="rate_ring">
          <i-circle
            :percent="Math.floor(usageRate.memory)"
            stroke-color="#4fa940"
            :stroke-width="8"
            :trail-width="7"
            :size="circleSize"
          >
            <div class="demo-Circle-custom">
              <h1>{{progressLoop(usageRate.memory)}}</h1>
              <p>（使用量）</p>
            </div>
          </i-circle>
          <div class="rate_quantity">
            <div>
              <h3 @click="titleALL.memory_title_surplus =='已使用内存'?detailsJump('wuliji'):detailsJump('xuniji')">{{byteUnitConversion("byte",usageRate.memory_surplus)}}</h3>
              <p>{{ titleALL.memory_title_surplus }}（ {{byteUnitConversion("unit",usageRate.memory_surplus)}} ）</p>
            </div>
            <div>
              <h3 @click="titleALL.memory_title_surplus =='已使用内存'?detailsJump('wuliji'):detailsJump('xuniji')">{{byteUnitConversion("byte",usageRate.memory_total)}}</h3>
              <p>{{ titleALL.memory_title_total }}（ {{byteUnitConversion("unit",usageRate.memory_total) }} ）</p>
            </div>
          </div>
        </div>
      </div>
      <div class="circle">
        <div class="titles">
          <span>存储使用率</span>
          <Tooltip placement="left">
              <span class="help"><Icon type="md-help" /></span>
              <div slot="content">
                  <p>已用存储：显示集群中已使用的存储总量</p>
                  <p>存储总量:显示集群中可使用的存储总量</p>
                  <p>存储使用率：已用存储/存储总量 × 100%</p>
              </div>
          </Tooltip>
        </div>
        <div class="rate_ring">
          <!-- stroke-color="#ff9f29" -->
          <i-circle
            :percent="Math.floor(usageRate.storage)"
            stroke-color="#4fa940"
            :stroke-width="8"
            :trail-width="7"
            :size="circleSize"
          >
            <div class="demo-Circle-custom">
              <h1>{{progressLoop(usageRate.storage)}}</h1>
              <p>（使用量）</p>
            </div>
          </i-circle>
          <div class="rate_quantity">
            <div>
              <h3 @click="detailsJump('cuncu')">{{byteUnitConversion("byte",usageRate.storage_surplus)}}</h3>
              <p>已用存储（ {{byteUnitConversion("unit",usageRate.storage_surplus)}} ）</p>
            </div>
            <div>
              <h3 @click="detailsJump('cuncu')">{{byteUnitConversion("byte",usageRate.storage_total)}}</h3>
              <p>存储总量（ {{byteUnitConversion("unit",usageRate.storage_total)}} ）</p>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="alert_reminder">
      <div class="system_state">
         <div class="titles">
          <span>系统状态</span>
          <Tooltip placement="top">
            <span class="help"><Icon type="md-help" /></span>
            <div slot="content">
                <p>系统监控页面中的系统健康度是对系统整体运行状况的一种量化评估，用于反映系统当前的状态是否稳定、可靠，以及是否存在潜在的性能或故障问题</p>
            </div>
          </Tooltip>
        </div>
        <div class="systatus">
          <div id="system_dashboard"></div>
        </div>
      </div>
      <div class="gaojing">
        <div class="gaojing_title">
          <span style="display:inline-block">告警提醒</span>
          <ul>
            <li>
              提示<span style="color: #656eea">{{info}}</span>
            </li>
            <li>
              次要<span style="color: #fff82d">{{warning}}</span>
            </li>
            <li>
              重要<span style="color: #ffa12d">{{major}}</span>
            </li>
            <li>
              严重<span style="color: #ff7842">{{collapse}}</span>
            </li>
          </ul>
        </div>
        <div class=gaojing_content>
          <Table
            :row-class-name="rowClassName"
            :columns="columnsAlarm"
            :data="dataAlarm"
            :show-header="false"
          ></Table>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { powerCodeQuery } from '@/api/other'; // 权限可用性 查询

import {alarmDataQuery} from '@/api/log';
import {
  homepageData,  // 大屏 基本数据
  largeScreenMachineNumber,  // 大屏 机器数量
} from '@/api/other';

// let echarts = require("echarts/lib/echarts");
// // 引入柱状图组件
// require("echarts/lib/chart/bar");
// require("echarts/lib/chart/pie");
// require("echarts/lib/chart/line");
// // 引入提示框和title组件
// require("echarts/lib/component/tooltip");
// require("echarts/lib/component/title");
export default {
  data() {
    return {
      // 进度环默认值
      circleSize: 150,
      screenWindht: document.body.clientHeight,
      capacityData:{
        wljNumber:0,
        xnjNumber:0,
        rwsNumber:0,
      },
      // 分配比使用率数据
      usageRate:{
        cpu:0,
        cpu_surplus:null,
        cpu_total:null,
        memory:0,
        memory_surplus:null,
        memory_total:null,
        storage:0,
        storage_surplus:null,
        storage_total:null,
      },
      // CPU数据 获取
      cpuData:{
        occupy:0, // 占用率
        occupy_surplus:null, // 占用 已使用
        occupy_total:null, // 占用 总量
        distribution:0, // 分配率
        distribution_surplus:null, // 分配 已使用
        distribution_total:null, // 分配 总量
      },
      // 内存数据 获取
      memoryData:{
        occupy:0,  // 占用率
        occupy_surplus:null, // 占用 已使用
        occupy_total:null, // 占用 总量
        distribution:0, // 分配率
        distribution_surplus:null, // 分配 已使用
        distribution_total:null, // 分配 总量
      },
      // 分配比使用率文字描述
      titleALL:{
        cpu_title_total:"VCPU总数(核)",
        cpu_title_surplus:"已分配VCPU(核)",
        memory_title_total:"内存总数",
        memory_title_surplus:"已使用内存",
      },
      // 占用/分配 转换
      cpuZY:'ordinary',
      cpuFP:'occupy',
      memoryZY:'ordinary',
      memoryFP:'occupy',
      columnsAlarm:[
        { title: "告警名称", key: "summary" ,minWidth: 200,tooltip:true },
        { title: "告警时间", key: "activeAt",align: "center" ,minWidth: 200 },
        { title: "告警详情", key: "description",align: "center",minWidth: 400,tooltip:true },
        { title: "告警类型", key: "severity",align: "center",width: 120,
           render: (h, params) => {
            switch (params.row.severity) {
              case "critical":
                return h("span",{style: { color: "#ff7842" },},"严重");
              break;
              case "warning":
                return h("span",{style: { color: "#ffa12d" },},"警告");
              break;
              case "info":
                return h("span",{style: { color: "#656eea" },},"提示");
              break;
            }
          },
        }
      ],
      dataAlarm:[],
      totalData:0,
      collapse:0,
      major:0,
      warning:0,
      info:0,

      systemScore:1, // 系统分数

      powerAcitons: {}, // 操作权限数据
    };
  },
  created() {
    this.homePageGET()
    // 浏览器首次获取高度
    window.screenWindht = document.body.clientHeight;
    this.windowsHeight(window.screenWindht);
    // 虚拟数据
    
  },
  mounted() {
    // 监听浏览器高度定义进度环大小
    // const that = this;
    // window.onresize = () => {
    //   window.screenWindht = document.body.clientHeight;
    //   that.windowsHeight(window.screenWindht);
    // };
    this.giveANalarmGET()
    this.tabsQuery()
  },
  watch: {},
  methods: {
    // 详情跳转
    detailsJump(item){
      switch (item) {
        case "wuliji":
          if(this.powerAcitons.wuliziyuan) {
            this.$router.push("/computingResource");
          }else {
            this.$Message.warning({
              background: true,
              closable: true,
              duration: 5,
              content: '未配置物理资源权限'
            });
          }
          break;
        case "xuniji":
          if(this.powerAcitons.xuniji) {
            this.$store.state.power.computingResourceTab = "虚拟机"
            this.$router.push("/computingResource");
          }else {
            this.$Message.warning({
              background: true,
              closable: true,
              duration: 5,
              content: '未配置虚拟机权限'
            });
          }
          break;
        case "cuncu":
          if(this.powerAcitons.cunchujiankong) {
            this.$store.state.power.itMonitoringTab = "存储监控"
            this.$router.push("/monitoring");
          }else {
            this.$Message.warning({
              background: true,
              closable: true,
              duration: 5,
              content: '未配置存储监控权限'
            });
          }
          break;
      }
    },
    // 获取首页数据
    homePageGET() {
      homepageData().then(em=>{
        // 监听浏览器高度定义进度环大小
        this.systemScore =(em.data.sys_status*100).toFixed(0)
        this.echarSystemState(this.systemScore);
        // 概览上
        this.capacityData.wljNumber = em.data.count
        // this.capacityData.xnjNumber = em.data.running_vms
        this.capacityData.rwsNumber = em.data.ceph_pool_max
        // CPU数据
        this.cpuData.occupy = em.data.usage_percent_cpu* 100 // CPU使用率
        this.cpuData.occupy_surplus = null
        this.cpuData.occupy_total = em.data.vcpus
        this.cpuData.distribution =em.data.vcpus_used/(em.data.vcpus*4)* 100
        this.cpuData.distribution_surplus = em.data.vcpus_used
        this.cpuData.distribution_total = em.data.vcpus*4
        // 内存数据
        this.memoryData.occupy = em.data.usage_percent_ram*100
        this.memoryData.occupy_surplus = em.data.usage_ram_used/1024
        this.memoryData.occupy_total = em.data.usage_ram_total/1024
        this.memoryData.distribution = em.data.memory_mb_used/em.data.memory_mb* 100
        this.memoryData.distribution_surplus = em.data.memory_mb_used/1024
        this.memoryData.distribution_total = em.data.memory_mb/1024
        // 存储使用数据
        this.usageRate.storage = em.data.ceph_pool_stored/em.data.ceph_pool_max* 100
        this.usageRate.storage_surplus = em.data.ceph_pool_stored
        this.usageRate.storage_total = em.data.ceph_pool_max
        this.CPUswitch('zy')
        this.memorySwitch('zy')
      })
      largeScreenMachineNumber().then(em=>{
        this.capacityData.xnjNumber = em.data.vms
      })
    },
    progressLoop(size){
      if(isNaN(size)) {
        return "无数据"
      }else {
        return Math.floor(size * 100) / 100 +"%"
      }
    },
    // 告警表格隔行变色
    rowClassName(row, index) {
      if (index % 2 == 1) {
        return "odNumber";
      }
    },
    // 系统状态不同角度的表现形式定义
    echarSystemState(pointData) {
      let system_dashboard = this.$echarts.init(document.getElementById("system_dashboard"));
      let jibie = "";
      let jibiecolor = "";
      let nodbox = document.getElementsByClassName("systatus")[0]
      let atext = (nodbox.offsetHeight/17).toFixed(0);
      let lintext = (nodbox.offsetHeight/10).toFixed(0);
      let btext = (nodbox.offsetHeight/25).toFixed(0);
      if(pointData<=30){
        jibie = "优"
        jibiecolor = "#2ebe76";
      }else if(pointData>30 && pointData<=70){
        jibie = "良"
        jibiecolor = "#fcc65c";
      } else if(pointData>70){
        jibie = "差"
        jibiecolor = "#f45d3f";
      }
      system_dashboard.setOption({
        backgroundColor: "#fff",
        series: [
          {
            type: "gauge",
            radius: "95%",
            z: 1,
            startAngle: 225,
            endAngle: -45,
            splitNumber: 8,
            splitLine: {
              show: false,
            },
            detail: {
              show: true,
              offsetCenter: [0, '50%'],
              // fontSize: 18,
              // formatter: (val) => [`{a|${pointData}}`, `{b|系统状态}`].join(""),
              formatter: `{a|${jibie}}\n{b|系统状态}`,
              rich: {
                a: {
                  fontSize: atext,
                  lineHeight: lintext,
                  fontFamily: "Microsoft YaHei",
                  fontWeight: "bold",
                  color: jibiecolor,
                },
                b: {
                  fontSize: btext,
                  // lineHeight: 14,
                  padding: [-15, 0, 0, 0],
                  fontFamily: "Microsoft YaHei",
                  fontweight: "400",
                  color: "#000",
                },
              },
            },
            pointer: {
              show: true,
              width: "3",
              length: "60%",
            },
            itemStyle: {
              color: "#ccc",
              borderColor: "#000",
              borderWidth: 3,
            },
            data: [{ value: pointData }],
            // 仪表盘的线，颜色值为一个数组
            axisLine: {
              show: true,
              lineStyle: {
                width: 30,
                opacity: 1,
                color: [
                  [
                    pointData,
                    {
                      x: 0,
                      y: 0,
                      x1: 0,
                      y1: 0,
                      colorStops: [
                        {
                          offset: 1,
                          color: "#f45d3f",
                        },
                        {
                          offset: 0.5,
                          color: "#fcc65c",
                        },
                        {
                          offset: 0,
                          color: "#2ebe76",
                        },
                      ],
                    },
                  ],
                  // [1, "rgba(82, 255, 0, 0.7)"],
                ],
              },
            },
            // 仪表盘刻度标签
            axisLabel: {
              show: true,
              distance: -45,
              formatter: (val) => {
                const num = Math.floor(val);
                return num % 20 === 0 ? num : "";
              },
              textStyle: {
                color: "#ffffff",
                fontSize: "10",
                fontFamily: "Microsoft YaHei",
                fontWeight: 400,
              },
            },
            axisTick: {
              show: true,
              lineStyle: {
                color: "#fff",
                width: 4,
              },
              length: 30,
            }, //刻度样式
          },
          {
            //指针外环
            type: "pie",
            hoverAnimation: false,
            legendHoverLink: false,
            radius: ["5%", "10%"],
            z: 10,
            label: {
              normal: {
                show: false,
              },
            },
            labelLine: {
              normal: {
                show: false,
              },
            },
            data: [
              {
                value: 100,
                itemStyle: {
                  normal: {
                    color: "#67b3ef",
                  },
                },
              },
            ],
          },
          {
            //指针内环
            type: "pie",
            hoverAnimation: false,
            legendHoverLink: false,
            radius: ["0%", "5%"],
            z: 10,
            label: {
              normal: {
                show: false,
              },
            },
            labelLine: {
              normal: {
                show: false,
              },
            },
            data: [
              {
                value: 100,
                itemStyle: {
                  normal: {
                    color: "#12214c",
                  },
                },
              },
            ],
          },
        ],
      });
    },
    // 封装不同浏览器高度下的进度环大小
    windowsHeight(windHT) {
			var fontSIZE = document.documentElement
      if (windHT > 950) {
        this.circleSize = 260;
				fontSIZE.style.fontSize = '20px';
      } else if (windHT <= 950 && windHT > 750) {
        this.circleSize = 200;
				fontSIZE.style.fontSize = '16px';
      } else if (windHT <= 750) {
        this.circleSize = 160;
				fontSIZE.style.fontSize = '12px';
      }
      setTimeout(() => {
        let parent = document.getElementsByClassName("systatus")[0]
        let nods = document.getElementById('system_dashboard');
        parent.removeChild(nods);
        let ids = document.createElement('div')
        ids.id = "system_dashboard"
        parent.appendChild(ids)
        this.echarSystemState(this.systemScore);
      }, 2000);
    },
    // 告警信息获取
    giveANalarmGET(){
      alarmDataQuery().then(callback=>{
        if(callback.data.length!==0) {
          this.totalData=callback.data.length
          let yanzhong =0
          let zhongyao =0
          let ciyao =0
          let tishi =0
          callback.data.forEach(item=>{
            if(item.severity=="critical"){
              yanzhong=yanzhong+1
            }
            if(item.severity=="major"){
              zhongyao=zhongyao+1
            }
            if(item.severity=="warning"){
              jinggao=jinggao+1
            }
            if(item.severity=="info"){
              tishi=tishi+1
            }
          })
          this.dataAlarm= callback.data
          this.collapse=yanzhong
          this.major=zhongyao
          this.warning=ciyao
          this.info=tishi
        }
      })
    },
    // CPU使用率/CPU分配比
    CPUswitch(data){
      if(data == 'zy' ) {
        this.cpuZY = 'ordinary'
        this.cpuFP = 'occupy'
        this.titleALL.cpu_title_total = "物理CPU总数(核)"
        this.usageRate.cpu = this.cpuData.occupy
        this.usageRate.cpu_surplus = this.cpuData.occupy_surplus
        this.usageRate.cpu_total = this.cpuData.occupy_total
      }else {
        this.cpuZY = 'occupy'
        this.cpuFP = 'ordinary'
        this.titleALL.cpu_title_total = "VCPU总数(核)"
        this.usageRate.cpu = this.cpuData.distribution
        this.usageRate.cpu_surplus = this.cpuData.distribution_surplus
        this.usageRate.cpu_total = this.cpuData.distribution_total
      }
    },
    // 内存使用率/内存使用率
    memorySwitch(data){
      if(data == 'zy' ) {
        this.memoryZY = 'ordinary'
        this.memoryFP = 'occupy'
        this.titleALL.memory_title_surplus = "已使用内存"
        this.usageRate.memory = this.memoryData.occupy
        this.usageRate.memory_surplus = this.memoryData.occupy_surplus
        this.usageRate.memory_total = this.memoryData.occupy_total
      }else {
        this.memoryZY = 'occupy'
        this.memoryFP = 'ordinary'
        this.titleALL.memory_title_surplus = "已分配内存"
        this.usageRate.memory = this.memoryData.distribution
        this.usageRate.memory_surplus = this.memoryData.distribution_surplus
        this.usageRate.memory_total = this.memoryData.distribution_total
      }
    },
    // 字节+单位转换
    byteUnitConversion(type,size) {
      const units = ['GB', 'TB', 'PB'];
      let unitIndex = 0;
      while (size >= 1024 && unitIndex < units.length - 1) {
          size /= 1024;
          unitIndex++;
      }
      if(type=="byte") {
        return Math.floor(size * 100) / 100 
      }else if(type=="unit") {
        return units[unitIndex] 
      }else {
        return Math.floor(size * 100) / 100 + ' ' + units[unitIndex];
      }
    },
    // 查询标签页权限
    tabsQuery(){
      powerCodeQuery({
        module_code:[
          'wuliziyuan',
          'xuniji',
          'cunchujiankong',
        ]
      }).then(callback=>{
        this.powerAcitons = callback.data.data
      })
    },
  },
};
</script>
