<template>
  <div>
    <Modal v-model="model" width="600" :mask-closable="false">
      <template #header><p>新建镜像</p></template>
      <Form
        :model="formItem"
        ref="formItem"
        :rules="ruleValidate"
        :label-width="120"
      >
        <FormItem label="镜像名称" prop="name">
          <Input v-model="formItem.name" placeholder="输入镜像名"></Input>
        </FormItem>
        <FormItem label="镜像类型">
          <Select v-model="formItem.imgType" @on-change="typeChange">
            <Option
              v-for="item in typeData"
              :value="item.value"
              :key="item.value"
              >{{ item.value }}</Option
            >
          </Select>
        </FormItem>
        <FormItem label="系统类型" v-if="formItem.architecture">
          <RadioGroup v-model="formItem.os_type">
            <Radio label="windows"></Radio>
            <Radio label="linux"></Radio>
          </RadioGroup>
        </FormItem>
        <FormItem label="上传镜像">
          <Upload
            ref="upload"
            :headers="headers"
            action="/upload/v1/images/upload"
            :on-progress="onPropress"
            :on-success="empowerOK"
            :on-error="empowerError"
          >
            <Button icon="ios-cloud-upload-outline" @click="clearFill"
              >上传镜像文件</Button
            >
          </Upload>
          <div style="color: #ed4014">{{ fileTitle }}</div>
        </FormItem>
      </Form>
      <div slot="footer">
        <Button type="text" @click="model = false">取消</Button>
        <Button type="primary" :loading="disabled" :disabled="disabled" @click="modelOK">
          <span v-if="!disabled">确认</span>
          <span v-else>创建中</span>
        </Button>
        <!-- <Button type="primary" @click="modelOK" :disabled="disabled"
          >确认</Button
        > -->
      </div>
    </Modal>
  </div>
</template>
<script>
import {
  imageTableNew, // 镜像表 新建
} from '@/api/image'; 
export default {
  props: {
    newTime: String,
  },
  watch: {
    newTime(news) {
      this.formItem.architecture = window.gurl.architecture == 'X86'
      this.formItem.os_type = this.formItem.architecture?'windows':'linux'
      this.$refs.upload.clearFiles()
      this.$refs.formItem.resetFields();
      this.fileCallback.filename = ''
      this.fileTitle = ''
      this.fileCallback.upload_status = false
      this.model = true;
      this.disabled = false;
    },
  },
  data() {
    const propName = (rule, value, callback) => {
      let list = /^[\u4e00-\u9fa5_a-zA-Z0-9@_.-]{2,32}$/;
      if (list.test(value)) {
        callback();
      } else {
        callback(new Error("2-32 个中文、英文、数字、特殊字符(@_.-"));
      }
    };
    return {
      model: false,
      disabled: false,
      typeData:[
				{ value:'iso' },
				{ value:'qcow2' },
				{ value:'raw' }
			],
      formItem: {
        name: '',
        imgType: 'iso',
        os_type:"windows",
        architecture: true,
        id: 0,
      },
      fileCallback:{
        filename:"",
        hash512:"",
        size:0,
        upload_status:false,
      },
      fileTitle: '',
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      ruleValidate: {
        name: [
          { required: true, message: "必填项", trigger: "change" },
          { validator: propName, trigger: "change" },
        ],
      },
    };
  },
  methods: {
    // 镜像类型
    typeChange(item){
      if(this.fileCallback.filename !==""){
        this.upimgs(this.fileCallback.filename)
      }
    },
    // 点击上传按钮
    clearFill(){
      this.fileTitle=""
    },
    // 上传开始
    onPropress(event, file, fileList){
      this.fileCallback.filename = file.name
    },
    // 上传OK 
    empowerOK(response,file,fileList){
      this.upimgs(response.filename)
      this.fileCallback.filename = response.filename
      this.fileCallback.hash512 = response.hash512
      this.fileCallback.size = response.size
      this.fileCallback.upload_status = true

      this.$Message.success({
        background: true,
        closable: true,
        duration: 5,
        content: "上传文件已完成",
      });
    },
    // 上传Error 
    empowerError(error,file,fileList){
      this.$Message.error({
        background: true,
        closable: true,
        duration: 5,
        content: "上传文件失败",
      });
    },
    // 确认事件
    modelOK() {
      this.$refs.formItem.validate((valid) => {
        if (valid) {
          this.disabled = true;
          if(this.upimgs(this.fileCallback.filename)) {
            if(this.fileCallback.upload_status){
              imageTableNew({
                name:this.formItem.name,
                disk_format:this.formItem.imgType,
                os_type:this.formItem.os_type,
                filename:this.fileCallback.filename,
                hash512:this.fileCallback.hash512,
                size:this.fileCallback.size
              })
              .then(callback=>{
                this.model = false
                this.$emit("return-ok", "新建镜像操作完成");
              })
            }else {
              this.disabled = false;
              this.$Message.warning({
                background: true,
                closable: true,
                duration: 5,
                content: '上传镜像未完成，无法进行确认操作'
              });
            }
          }else {
            this.disabled = false;
          }
        }
      });
    },
    upimgs(value){
      let type = this.formItem.imgType
      let lastIndex = value.lastIndexOf(".")
      let upname = value.slice(lastIndex + 1,lastIndex+type.length+1)
      if(type==upname) {
        this.fileTitle=""
        return true
      }else {
        this.fileTitle='上传的镜像与镜像类型不匹配'
        return false
      }
    },
  },
};
</script>