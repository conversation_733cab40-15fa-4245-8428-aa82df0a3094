<template>
  <div>
    <!-- 此页面为和HZ联调后的页面备份 -->
    <Modal v-model="model" fullscreen>
      <template #header><p @click="tablesQuery">动态资源调度待确认表格</p></template>
      <div>
        <Table :columns="tableColumns" :data="tableData">
          <!-- 调度方式 -->
          <template v-slot:auto="{row}">
            <span>{{row.auto=='true'?'自动':'手动'}}</span>
          </template>
          <!-- 因素 -->
          <template v-slot:factor="{row}">
            <Tag v-if="row.cpu_enabled=='true'" checkable color="success">CPU</Tag>
            <Tag v-if="row.mem_enabled=='true'" checkable color="success">内存</Tag>
          </template>
          <!-- 主机CPU -->
          <template v-slot:host_cpu="{row}">
            <span>{{row.host_cpu}} %</span>
          </template>
          <!-- 主机内存 -->
          <template v-slot:host_mem="{row}">
            <span>{{row.host_mem}} %</span>
          </template>
          <!-- 虚拟机CPU -->
          <template v-slot:vm_cpu="{row}">
            <span>{{row.vm_cpu}} %</span>
          </template>
          <!-- 虚拟机内存 -->
          <template v-slot:vm_mem="{row}">
            <span>{{row.vm_mem}} %</span>
          </template>
          <!-- 分级策略 -->
          <template v-slot:strategy="{row}">
            <span>主机负载大于<span style="color:#b97"> {{translationInfo('dy',row.strategy)}}</span>，且主机负载差值超过<span style="color:#b97"> {{translationInfo('cg',row.strategy)}}</span></span>
          </template>
          <!-- 推荐节点 -->
          <template v-slot:recommend_data="{row}">
            <Select v-model="row.recommend_node">
              <Option v-for="item in row.recommend_data" :value="item" :key="item">{{ item }}</Option>
            </Select>
          </template>
          <!-- 操作 -->
          <template v-slot:operation="{row}">
            <Button @click="tablesConfirm(row)">迁移</Button>
          </template>
        </Table>
      </div>
      <div slot="footer">
        <Button type="text" @click="model = false">取消</Button>
      </div>
    </Modal>
  </div>
</template>
<script>
import { dispatchTableQuery,dispatchTableConfirm } from '@/api/other';  // 动态资源调度

export default {
  props: {
    datas: Number,
  },
  watch: {
    datas(news){
      this.tablesQuery()
      this.model = true
    }
  },
  data() {
    return {
      model: false,
      tableColumns: [
        { title: "调度方式", key: 'auto', slot:'auto',width:100  },
        { title: "衡量因素", key: 'factor', slot:'factor',width:120 },
        { title: "主机CPU", key: "host_cpu",slot:'host_cpu',width:100 },
        { title: "主机内存", key: "host_mem",slot:'host_mem',width:100 },
        { title: "虚拟机", key: "vm_name", tooltip: true },
        { title: "虚拟机CPU", key: "vm_cpu",slot:'vm_cpu',width:100 },
        { title: "虚拟机内存", key: "vm_mem",slot:'vm_mem',width:100 },
        { title: "分级策略", key: "strategy", slot:'strategy',width:300 },
        { title: "当前节点", key: "node" },
        { title: "推荐节点", key: "recommend_data", slot: 'recommend_data' },
        { title: "操作", key: "operation", width:80, slot: 'operation' }
      ],
      tableData: [],
    };
  },
  methods: {
    tablesQuery(){
      dispatchTableQuery().then(callback=>{
        this.tableData = callback.data
      })
    },
    tablesConfirm(row){
      console.log('tablesConfirm',row)
      this.$Modal.confirm({
        title: '迁移确认',
        content: `是否将节点从 ${row.node} 节点迁移至 ${row.recommend_node} 节点?`,
        onOk: () => {
          dispatchTableConfirm({
            node: row.node,
            recommend_node: row.recommend_node,
          })
          .then(callback=>{
            if(callback.data.msg == 'ok') {
              this.$emit("scheduling-ok");
              this.tablesQuery()
              this.$Message.success({
                background: true,
                closable: true,
                duration: 5,
                content: '节点迁移完成',
              });
            }else {
              this.$Message.error({
                background: true,
                closable: true,
                duration: 5,
                content: '节点迁移失败',
              });
            }
          })
        },
        onCancel: () => {}
      });
    },
    translationInfo(list,item){
      let q = '70%'
      let h = '30%'
      if(item =='conservative') {
        q = '70%'
        h = '30%'
      }else if(item =='neutral'){
        q = '60%'
        h = '20%'
      }else if(item =='radical'){
        q = '50%'
        h = '10%'
      }else if(item =='most_radical'){
        q = '50%'
        h = '5%'
      }
      if(list == 'dy') {
        return q
      }else {
        return h
      }
    },
    
  },
};
</script>