<style lang="less">
@import "./tabThrough.less";
</style>
<template>
  <div class="through_management_area">
    <Spin fix v-if="spinShow" size="large" style="color:#ef853a">
      <Icon type="ios-loading" size=50 class="demo-spin-icon-load"></Icon>
      <div style="font-size:16px;padding:20px">Loading...</div>
    </Spin>
    <div class="through_management_btn">
      <Button class="plus_btn" @click="newThrough"><Icon type="ios-link" /> 连接直通</Button>
      <!-- <Button class="plus_btn" @click="refresThrough"><Icon type="md-refresh" />刷新</Button> -->
      <!-- <Button class="close_btn" @click="delThrough"><span class="icon iconfont icon-close-circle"></span> 删除</Button> -->
    </div>
    <div class="through_management_table">
      <Table :columns="throughColumn" :data="throughData" @on-selection-change="throughSlectChange"></Table>
    </div>
    <!-- 连接直通 -->
    <Modal v-model="throughModal" width="900" title="连接直通" @on-ok="addOK" @on-cancel="cancel" :mask-closable="false">
      <div class="connect_selected_area">
        <div class="through_vm">
          <h3>已选虚拟机</h3>
          <Table disabled-hover :columns="vmColumn" :data="selectVM"></Table>
        </div>
        <div class="through_device">
          <h3>已选直通设备</h3>
          <Table disabled-hover :columns="diskColumn" :data="selecteDisk"></Table>
        </div>
      </div>
      <div class="connect_unselected_area">
        <div class="through_vm">
          <div class="connect_unselected_search">
            <h3>虚拟机列表</h3>
            <Input v-model="httpTableData.search_str" style="width:300px" search enter-button placeholder="请输入名称或IP" @on-search="tableVMsearchInput" />
          </div>
            <Table highlight-row :columns="vmColumn" :data="vmData" @on-row-click="onRowVM"></Table>
            <div style="display: flex;justify-content: center;padding:20px 0;" v-if="this.vmData.length>0&&this.totalpages>1">
              <Page :total="totalAll" show-total :page-size="httpTableData.pagecount" @on-change="onPage" />
            </div>
        </div>
        <div class="through_device">
          <div class="connect_unselected_search">
            <h3>直通设备表</h3>
            <!-- <Input v-model="httpDiskData.search_str" style="width:300px" search enter-button placeholder="请输入名称或IP" @on-search="tableDiskInput" /> -->
          </div>
          <Table highlight-row :columns="diskColumn" :data="diskData" @on-row-click="onRowDisk"></Table>
          <!-- <div class="pages" v-if="this.diskData.length>0">
            <Page :total="totalDisk" show-total @on-change="onDiskPage"/>
          </div> -->
        </div>
      </div>
    </Modal>
  </div>
</template>
<script>
import {directDeviceQuery,throughQuery,connectStraightThrough,throughDelete} from '@/api/storage';
import {vmTableQuery} from '@/api/virtualMachine';
export default {
  props: {
    tabName: String,
  },
  data() {
    return {
      spinShow:false,
      throughColumn:[
        { title: "虚拟机名称", key: "vmname" },
        // { title: "云硬盘名称", key: "volume_name" },
        { title: "云硬盘名称", key: "name" },
        { title: "虚拟机IP", key: "ip" },
        { title: "云硬盘大小", key: "size" },
        { title: "设备名称", key: "device" },
        { title: "操作", key: "operation",width: 120,
          render: (h, params) => {
            return h("Button",{
              style: {
                color:"red"
              },
              nativeOn: {
                click: () => {
                  this.$Modal.confirm({
                    title: '删除直通管理数据',
                    content: '<p>是否删除直通管理 <span style="font-weight: 600;color:red;">'+params.row.vmname+'</span> 的表数据?</p>',
                    onOk: () => {
                      throughDelete({data:{id: params.row.id}}).then(callback=>{
                        if(callback.data.msg=="ok") {
                          setTimeout(() => {
                            this.throughGet()
                            this.$Message.success({
                              background: true,
                              closable: true,
                              duration: 5,
                              content: '删除直通设备完成',
                            });
                          }, 5000);
                          this.$Message.info({
                              background: true,
                              closable: true,
                              duration: 5,
                              content: '删除直通设备进行中',
                            });
                        }else {
                          this.$Message.error({
                            background: true,
                            closable: true,
                            duration: 5,
                            content: '删除直通设备失败'
                          });
                        }
                      })
                    },
                    onCancel: () => {
                      
                    }
                  });
                }  
              },
            },"删除")
          }
        }
      ],
      throughData:[],
      throughSlectData:[],
      throughSlectId:[],
      throughSlectName:[],
      // 连接直通
      throughModal:false,
      // 虚拟机表格数据需求
      vmColumn:[
        { title: "虚拟机名称", key: "name" },
        { title: "IP", key: "ip",align: "center" },
        { title: "状态", key: "status",align: "center",width:80,
          render: (h, params) => {
            if(params.row.status=='SHUTOFF'){
              return h(
                "span",
                {
                  style: { color: "#000" },
                },
                "关机"
              );
            }else {
              return h(
                  "span",
                  {
                    style: { color: "#000" },
                  },
                  "开机"
                );
            }
          }
        },
      ],
      vmData:[],
      selectVM:[],
      totalAll:0,
      totalpages:0,
      httpTableData: {
        id: 1,
        page: 1, // 当前页
        pagecount: 10, // 每页条数
        search_str: "", // 搜索
        order_type: "asc", // 排序规则
        order_by: "", // 排序字段
      },
      // 直通表格数据 
      diskColumn:[
        { title: "主机节点", key: "hostname" },
        { title: "可直通设备", key: "device" },
        { title: "IP", key: "hostip" },
      ],
      diskData:[],
      selecteDisk:[],
      // 直通设备分页
      totalDisk:0,
      httpDiskData: {
        page: 1, // 当前页
        pagecount: 15, // 每页条数
        search_str: "", // 搜索
        order_type: "asc", // 排序规则
        order_by: "", // 排序字段
      },
    }
  },
  watch: {
    tabName(value) {
      if(value=='直通管理'){
        this.throughGet()
      }
    },
  },
  mounted() {
    if(this.$store.state.power.storageResourceTab == '直通管理') {
      this.throughGet()
    }
  },
  methods: {
    // 刷新表格
    refresThrough(){
      this.throughGet()
    },
    // 获取虚拟机数据
    getVM(){
      vmTableQuery(this.httpTableData).then((callback) => {
        this.totalAll = callback.data.total;
        this.totalpages = callback.data.pages;
        this.vmData = callback.data.data;
      })
    },
    // 虚拟机列表 点击分页
    onPage(item) {
      this.httpTableData.page = item;
      this.getVM();
    },
    // 搜索虚拟机列表
    tableVMsearchInput(){
      this.getVM();
    },
    // 获取直通设备数据
    getDisk(){
      directDeviceQuery().then((callback) => {
        this.diskData = callback.data;
      })
    },
    // 直通设备列表 点击分页
    onDiskPage(item) {
      this.httpDiskData.page = item;
      this.getDisk();
    },
    // 搜索虚拟机列表
    tableDiskInput(){
      this.getDisk();
    },
    // 获取直通管理表数据
    throughGet(){
      this.throughData = new Array()
      this.spinShow = true
      throughQuery().then(callback=>{
        this.throughData = callback.data
        this.spinShow = false
      }).catch((error) => {
        this.spinShow=false;
      });
    },
    // 虚拟机表格选中行
    onRowVM(item) {
      this.selectVM = new Array()
      this.selectVM.push(item)
    },
    // 主机表格选中行
    onRowDisk(item) {
      this.selecteDisk = new Array()
      this.selecteDisk.push(item)
    },
    // 表格选中数据
    throughSlectChange(item){
      let arrID = new Array();
      let arrName = new Array();
      item.forEach((em) => {
        arrID.push(em.id);
        arrName.push(em.vmname);
      });
      this.throughSlectData = item
			this.throughSlectID=arrID
			this.throughSlectName=arrName
    },
    // 连接
    newThrough() {
      this.throughModal = true;
      this.selectVM = new Array()
      this.selecteDisk = new Array()
      this.httpTableData.page = 1
      this.httpTableData.search_str = ""
      this.httpDiskData.page = 1
      this.httpDiskData.search_str = ""
      this.getVM()
      this.getDisk()

    },
    // 连接直通确认
    addOK(){
      this.$Message.info({
        background: true,
        closable: true,
        duration: 5,
        content: '连接直通已进行'
      });
      connectStraightThrough({vmid:this.selectVM[0].id,device:this.selecteDisk[0].hostname+"("+this.selecteDisk[0].hostip+"):"+this.selecteDisk[0].device})
      .then((callback) => {
        if(callback.data.msg=="ok") {
          setTimeout(() => {
            this.$Message.success({
              background: true,
              closable: true,
              duration: 5,
              content: '连接直通设备成功'
            });
            this.throughGet()
          }, 2000);
        }else {
          this.$Message.error({
            background: true,
            closable: true,
            duration: 5,
            content: '连接直通设备失败'
          });
        }
        // this.diskData = callback.data.data;
      })
    },
    // 删除
    delThrough() {
      if(this.throughSlectData.length>0) {
        this.$Modal.confirm({
          title: '删除直通管理数据',
          content: '<p>是否删除直通管理 <span style="font-weight: 600;color:red;">'+this.throughSlectName+'</span> 的表数据?</p>',
          onOk: () => {
            throughDelete({data:{userids: this.throughSlectID}}).then(callback=>{
              if(callback.data.msg=="ok") {
                this.refreshUser()
                this.$Message.success({
                  background: true,
                  closable: true,
                  duration: 5,
                  content: '删除用户完成',
                });
              }else {
                this.$Message.error({
                  background: true,
                  closable: true,
                  duration: 5,
                  content: '删除用户失败'
                });
              }
            })
          },
          onCancel: () => {
             
          }
        });
      }else {
        this.$Message.warning({
          background: true,
          closable: true,
          duration: 5,
          content: '请先选择要删除的数据'
        });
      }
    },

    cancel(){},
  },
}
</script>
