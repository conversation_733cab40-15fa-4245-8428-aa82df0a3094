<template>
  <div>
    <Modal
      v-model="model"
      width="600"
      :mask-closable="false"
    >
      <template #header><p><span style="color:green">{{row.name}}</span>管理安全组</p></template>
      <Spin fix v-if="spinshow" size="large" style="color: #ef853a">
        <Icon type="ios-loading" size="50" class="demo-spin-icon-load"></Icon>
        <div style="font-size: 16px; padding: 20px">Loading...</div>
      </Spin>
      <div
        style="
          border-bottom: 1px solid #e9e9e9;
          padding-bottom: 6px;
          margin-bottom: 6px;
        "
      >
        <Checkbox
          :indeterminate="indeterminate"
          :value="checkAll"
          @click.prevent.native="handleCheckAll"
          >全选</Checkbox
        >
      </div>
      <CheckboxGroup v-model="aqzCheak" @on-change="checkAllGroupChange">
        <Checkbox
          style="width: 30%; padding-bottom: 10px"
          v-for="(item, index) in anzData"
          :label="item"
          :key="index"
        ></Checkbox>
      </CheckboxGroup>
      <template #footer>
      <Button type="text" @click="model = false">取消</Button>
      <Button
        type="info"
        class="plus_btn"
        @click="modelOK"
        :disabled="disabled"
        >确定</Button
      >
    </template>
    </Modal>
  </div>
</template>
<script>
import {
  securityGroupQuery, // 安全组 查询
  securityGroupSelected, // 安全组 查询
  securityGroupEdit, // 安全组 编辑
} from '@/api/virtualMachine';
export default {
  props: {
    row: Object,
    groupTime: String,
  },
  watch: {
    groupTime(news) {
      this.id = this.row.id
      this.editgroup()
      this.disabled = false
      this.model = true
    },
  },
  data() {
    return {
      disabled: true,
      model: false,
      spinshow: false,
      aqzCheak: [],
      anzData: [],
      id: "",
      // 全选
      indeterminate:false,
      checkAll:false,
    };
  },
  methods: {
    editgroup() {
      this.spinshow = true;
      setTimeout(() => {
        this.spinshow = false;
      }, 1000);
      // 全部
      securityGroupQuery()
      .then((callback) => {
        let arr = new Array();
        callback.data.forEach((em) => {
          arr.push(em.name);
        });
        this.anzData = arr;
      });
      // 已选
      securityGroupSelected({ id: this.id })
      .then((callback) => {
        let arr = new Array();
        callback.data.forEach((em) => {
          arr.push(em.name);
        });
        this.aqzCheak = arr;
        this.spinshow = false;
      });
    },
     // 安全组选中状态
    checkAllGroupChange(data){
      if (data.length ==this.anzData.length) {
          this.indeterminate = false;
          this.checkAll = true;
      } else if (data.length > 0) {
          this.indeterminate = true;
          this.checkAll = false;
      } else {
          this.indeterminate = false;
          this.checkAll = false;
      }
    },
    // 安全组全选
    handleCheckAll(){
      if (this.indeterminate) {
          this.checkAll = false;
      } else {
          this.checkAll = !this.checkAll;
      }
      this.indeterminate = false;

      if (this.checkAll) {
          this.aqzCheak = this.anzData
      } else {
          this.aqzCheak = [];
      }
    },
    // 管理安全组
    modelOK(){
      this.disabled = true
      securityGroupEdit({
        id:this.id,
        seclist:this.aqzCheak,
        vm_name: this.row.name
      })
      .then(callback=>{
        if(callback.data.msg == "ok"){
          this.model = false;
          this.$emit("return-ok",{
              msg: '编辑容器操作已完成',
              type: 'ok'
            });
        }else {
          this.disabled = false
          this.$emit("return-ok",{
              msg: '编辑容器操作失败',
              type: 'error'
            });
        }
      })
      .catch((error) => {
        this.disabled = false
        this.$emit("return-ok",{
              msg: '编辑容器操作失败',
              type: 'error'
            });
      });
    },
  },
};
</script>