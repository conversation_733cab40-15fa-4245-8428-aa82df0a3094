<style lang="less">
@import "./virtualMachineTab.less";
</style>
<template>
  <div>
    <Drawer width="50%" :title="drawerTitle" :closable="false" v-model="initialmodal">
      <ul class="vm_drawer_basic">
        <li >
          <Button ghost type="info" :disabled="onOFF" shape="circle" icon="md-power" @click="actionPackage('start')" ></Button>
          <span>开机</span>
        </li>
        <li>
          <Button ghost type="error" :disabled="!onOFF" shape="circle" icon="md-power" @click="actionPackage('stop')"></Button>
          <span>关机</span>
        </li>
        <li>
          <Button
            ghost
            type="warning"
            shape="circle"
            icon="md-refresh-circle"
            @click="actionPackage('reboot')"
          ></Button>
          <span>重启</span>
        </li>
        <li>
          <Button
            ghost
            type="error"
            shape="circle"
            icon="ios-desktop"
            @click="actionPackage('getVNCConsole')"
          ></Button>
          <span>控制台</span>
        </li>
        <!-- <li>
          <Button
            ghost
            type="primary"
            shape="circle"
            icon="md-git-network"
          ></Button>
          <span>网络</span>
        </li>
        <li>
          <Button
            ghost
            type="success"
            shape="circle"
            icon="md-cloud-upload"
          ></Button>
          <span>存储</span>
        </li>
        <li>
          <Button ghost type="success" shape="circle" icon="ios-clock"></Button>
          <span>时间</span>
        </li> -->
      </ul>
      <div class="vm_drawer_charts">
        <ul>
          <li>
            <p>虚拟机详情</p>
            <div class="xq">
              <div v-for="(lable, i) in detailsKey" :key="i" class="divxq">
                <span class="lable">{{lable}}</span>
                <Tooltip max-width="200" :content="detailsValue[i]" placement="bottom-start">
                  <span class="key text_overflow">{{detailsValue[i]}}</span>
                </Tooltip >
              </div>
            </div>
            <Table
              :columns='tableCloumns'
              :data="tableData"
            >
              <!-- MAC -->
              <template v-slot:mac="{row}">
                <span>{{ row['OS-EXT-IPS-MAC:mac_addr']}}</span>
              </template>
            </Table>
          </li>
          <li>
            <p>云硬盘连接信息</p>
            <div style="padding:15px;color:blue">
              <div style="padding: 5px 15px" v-for="(lable, i) in bindingInformation" :key="i">
                <a @click="jumpCloudDrive(i)">{{lable}}</a>
              </div>
            </div>
          </li>
          <li v-for="item in detailsDta">
            <p>{{item.title}}</p>
            <lineTemplate :datas="item.datas" style="width:99%;height:400px"></lineTemplate>
          </li>
        </ul>
      </div>
    </Drawer>
  </div>
</template>
<script>
import {
  vmGroupTableAction, // 虚拟机组表格 操作
  vmGroupTableSeparateClouddisc, // 虚拟机组表格 分离云硬盘
  vmDetailsNET, // 虚拟机详情 网络
  vmDetailsCPU, // 虚拟机详情 CPU
  vmDetailsMEM, // 虚拟机详情 内存
} from '@/api/virtualMachine';
import lineTemplate from "@/components/public/Line.vue"
export default {
  components: {
    lineTemplate,
  },
  props: {
    detailsTime: String,
    vmRow: Object,
  },
  watch: {
    detailsTime(news){
      this.ids = this.vmRow.id
      this.rowsData(this.ids)
      this.initialmodal = true
    }
  },
  data(){
    return {
      initialmodal:false,
      ids:"",
      drawerTitle:"详情",
      onOFF:true, // 抽屉禁用开关机判定
      // 虚拟机详情
      detailsKey:[
        "虚拟机名称：","ID：","VCPU：","内存：","硬盘：","镜像名称：","创建时间：","最后操作：","系统类型：","云硬盘类型："
      ],
      detailsValue: [],
      tableCloumns: [
        { title: "IP", key: "addr",align: 'center' },
        { title: "MAC地址", key: "mac",align: 'center',slot: 'mac' },
      ],
      tableData: [],
      bindingInformation:[], // 云硬盘绑定信息
      mountname:[], // 云硬盘绑定名称
      detailsDta:[
        {title:"网络使用情况",datas:null},
        {title:"CPU使用情况",datas:null},
        {title:"内存使用情况",datas:null},
      ]
    }
  },
  methods: {
    // 详情数据
    rowsData(ids){
      // 获取详情数据
      vmGroupTableSeparateClouddisc({id:ids})
      .then((callback) => {
        callback.data.status=="SHUTOFF"?this.onOFF=false:this.onOFF=true
        this.drawerTitle = callback.data.name+"详情"
        let arr = new Array()
        arr.push(callback.data.name)
        arr.push(callback.data.id)
        arr.push(callback.data.vcpus+" 核")
        arr.push(callback.data.ram+" GB")
        arr.push(callback.data.disk+" GB")
        arr.push(callback.data.imagename)
        arr.push(callback.data.created)
        arr.push(callback.data.updated)
        callback.data.metadata.os_type==undefined?arr.push("未设置"):arr.push(callback.data.metadata.os_type)
        arr.push(callback.data.volume_type == "__DEFAULT__"?"默认类型":callback.data.volume_type)
        this.detailsValue = arr
        this.bindingInformation = callback.data.attachment
        this.mountname = callback.data.mountname
        this.netData({vmid:ids,os_type:callback.data.metadata.os_type})
        this.cpuData({vmid:ids,os_type:callback.data.metadata.os_type})
        this.memoryData({vmid:ids,os_type:callback.data.metadata.os_type})
        let macData = new Array()
        Object.keys(callback.data.addresses).forEach(key => {
          callback.data.addresses[key].forEach(em=>{
            macData.push(em)
          })
        });
        // if(callback.data.addresses['flat']!==undefined){
        //   callback.data.addresses['flat'].forEach(item => {
        //     macData.push(item)
        //   })
        // }
        // if(callback.data.addresses['vlan-1']!==undefined){
        //   callback.data.addresses['vlan-1'].forEach(item => {
        //     macData.push(item)
        //   })
        // }
        this.tableData = macData
      })
    },
    // 跳转云硬盘
    jumpCloudDrive(index){
      this.$router.push('/storageResources')
      this.$store.state.power.storageResourceTab = "云硬盘"
      this.$store.state.power.cloudDriveName = this.mountname[index]
    },
    // 网络数据
    netData(data){
      vmDetailsNET(data)
      .then((callback) => {
        let arr = new Object()
        let value = new Array()
        value.push(
          { title:"上行", list:callback.data.u },
          { title:"下行", list:callback.data.d },
        )
        arr.time = callback.data.t
        arr.data = value
        arr.unit = "B"
        this.detailsDta[0].datas = arr
      })
    },
    // CPU数据
    cpuData(data){
      vmDetailsCPU(data)
      .then((callback) => {
        let arr = new Object()
        let value = new Array()
        value.push(
          { title:"CPU使用率", list:callback.data.v },
        )
        arr.time = callback.data.t
        arr.data = value
        arr.unit = "%"
        this.detailsDta[1].datas = arr
      })
    },
    // 内存数据
    memoryData(data){
      vmDetailsMEM(data)
      .then((callback) => {
        let arr = new Object()
        let value = new Array()
        value.push(
          { title:"内存使用率", list:callback.data.v },
        )
        arr.time = callback.data.t
        arr.data = value
        arr.unit = "%"
        this.detailsDta[2].datas = arr
      })
    },
    // 详情操作
    actionPackage(action){
      let lis = {
        id: this.detailsValue[1],
        action: action,
        data: '',
        name: this.detailsValue[0]
      }
      switch (action) {
        // 开机
        case "start":
          this.$Modal.confirm({
            title: this.detailsValue[0]+"开机操作",
            content:
              '<p>是否对<span style="font-weight: 600;font-siaze:30px;">' +
              this.detailsValue[0] +
              '</span>虚拟机进行<span style="font-weight: 600;color:green;">开机</span>操作?</p>',
            onOk: () => {
              this.actionAxios(lis, "开机");
            },
            onCancel: () => {
               
            },
          });
          break;
        // 关机
        case "stop":
          this.$Modal.confirm({
            title: this.detailsValue[0]+"关机操作",
            content:
              '<p>是否对<span style="font-weight: 600;font-siaze:30px;">' +
              this.detailsValue[0] +
              '</span>虚拟机进行<span style="font-weight: 600;color:red;">关机</span>操作?</p>',
            onOk: () => {
              this.actionAxios(lis, "关机");
            },
            onCancel: () => {
               
            },
          });
          break;
        // 重启
        case "reboot":
          this.$Modal.confirm({
            title: this.detailsValue[0]+"重启操作",
            content:
              '<p>是否对<span style="font-weight: 600;font-siaze:30px;">' +
              this.detailsValue[0] +
              '</span>虚拟机进行<span style="font-weight: 600;color:red;">重启</span>操作?</p>',
            onOk: () => {
              this.actionAxios(lis, "重启");
            },
            onCancel: () => {
               
            },
          });
          break;
        // 控制台
        case "getVNCConsole":
          vmGroupTableAction(lis)
          .then((callback) => {
            if(callback.data.msg == "ok") {
              window.open(callback.data.console)
            }else{
              this.$Message.error({
                background: true,
                closable: true,
                duration: 5,
                content: "打开控制台失败",
              });
            }
          })
          break;
      }
    },
    // 操作接口调用
    actionAxios(data,action){
      vmGroupTableAction(data)
      .then((callback) => {
        if(callback.data.msg == "ok") {
          setTimeout(() => {
            this.$emit("return-ok",action+'操作完成');
            this.rowsData(this.ids)
          }, 1000);
        }else{
          this.$emit("return-error",action+'失败');
        }
      });
    },
  }
}
</script>