<template>
  <div>
    <Modal
      v-model="model"
      width="600"
      :mask-closable="false"
    >
      <template #header><p>{{ tableCheckText }}告警规则</p></template>
      <div style="padding: 5px">
        <span>是否 {{ tableCheckText }} 下列告警规则？</span>
        <p style="font-weight: 800;word-wrap: break-word">{{tableNames.toString()}}</p>
      </div>
      <div slot="footer">
        <Button type="text" @click="model = false">取消</Button>
        <!-- <Button type="primary" :loading="disabled" @click="modelOK">
          <span v-if="!disabled">确认</span>
          <span v-else>删除中</span>
        </Button> -->
        <Button type="primary" @click="modelOK" :disabled="disabled">确认</Button>
      </div>
    </Modal>
  </div>
</template>
<script>
import { alarmRulesAction,alarmRulesDelet } from '@/api/log';  // 告警规则 启用禁用、删除

export default {
  props: {
    tableCheckData: Array,
    tableCheckTime: String,
    tableCheckText: String,
  },
  watch: {
    tableCheckTime(news){
      this.tableNames = this.tableCheckData.map(em=>{ return em.name})
      this.tableIDS = this.tableCheckData.map(em=>{ return em.id})
      this.model = true
      this.disabled = false
    }
  },
  data(){
    return {
      model:false,
      disabled: false,
      tableNames: [],
      tableIDS: [],
    }
  },
  methods: {
    modelOK() {
      this.disabled = true
      if(this.tableCheckText == '删除') {
        alarmRulesDelet({data:{
          ids: this.tableIDS,
          names: this.tableNames,
        }})
        .then((callback) => {
          this.model = false;
          if(callback.data.msg == 'ok') {
            this.$emit("return-ok",this.tableCheckText+'告警规则操作完成');
          }else {
            this.$emit("return-error",callback.data.msg);
          }
        })
        .catch((error) => {
          his.disabled = false
          this.$emit("return-error",this.tableCheckText+'告警规则操作失败');
        })
        
      }else {
        alarmRulesAction({
          ids: this.tableIDS,
          names: this.tableNames,
          status: this.tableCheckText == '启用'?'enabled':'disabled',
        })
        .then((callback) => {
          this.model = false;
          if(callback.data.msg == 'ok') {
            this.$emit("return-ok",this.tableCheckText+'告警规则操作完成');
          }else {
            this.$emit("return-error",this.tableCheckText+'告警规则操作失败');
          }
        })
        .catch((error) => {
          his.disabled = false
          this.$emit("return-error",this.tableCheckText+'告警规则操作失败');
        })
      }
        
    }
  }
}
</script>