.network_tabs {
  height: 100%;
  padding: 15px 15px 0 15px;
}

// 网络
.network_area {
  height: 100%;
  border-radius: 10px;
  background-color: #FFF;
  padding: 15px 5px;
  position: relative;
}

/* 网络拓扑区 */
.network_topology_area {
  height: calc(100% - 70px);

  .ivu-tabs-bar {
    border: none;
  }

  .ivu-tabs-nav {
    display: flex;
    flex-direction: row;

    .ivu-tabs-tab {
      display: flex;
      flex-direction: column;
      align-items: center;
      height: 100% !important;
      line-height: 100% !important;
    }

    .ivu-tabs-tab-active {
      color: #fb6129;
    }

    .ivu-tabs-tab:hover {
      color: #fb6129;
    }

    .ivu-icon {
      margin-bottom: 5px;
      font-size: 12px;
    }
  }

  .ivu-tabs-ink-bar {
    background: none;
  }

}
.canvas {
  position: absolute;
  top: 0;
  left: 0;
  z-index: -1;
}
/* 物理拓扑/网络拓扑 */
#net_node,
#mount_node,
.no_data_prompt {
  width: 100%;
  height: 90%;
  position: relative;
  border: 1px solid #ccc;
}

.host_topology_area {
  width: 95%;
  border: 1px solid #ccc;
  padding: 5px 15px;
  position: relative;
}

.net_chart_area {
  width: 100%;
  height: calc(100% - 120px);
  // height: 2000px;
  overflow: auto;
  // background: #fff;
  // padding: 10px
}
.net_topology_area {
  user-select: none;
  width: 95%;
  border: 1px solid #ccc;
  margin-bottom: 10px;

  .vm_tamp_area {
    padding-left: 20px;

    .vm_addr_area {
      display: flex;
      align-items: center;
      justify-content: space-between;

      >h3 {
        cursor: pointer;

        .vm_addr_lable {
          display: inline-block;
          width: 150px;
        }
      }
    }
  }
}

.g6-minimap {
  position: absolute;
  top: 10px;
  left: 10px;
}

.topology_module {
  position: relative;
  height: calc(100% - 50px);
  border-radius: 10px;
  background-color: #FFF;
  padding: 15px 5px;
  overflow: auto;

  .topology_module_btn {
    height: 40px;
  }

  .no_data_prompt>h5 {
    width: 100%;
    position: absolute;
    text-align: center;
    top: 50%;
    font-size: 30px;
  }
}

/* 物理网络 */
.physical_network_area {
  height: calc(100% - 70px);
  border-radius: 10px;
  background-color: #FFF;
  padding: 15px 5px;
  overflow: auto;

  .physical_network_content {
    display: flex;
    margin-bottom: 12px;
    height: 28%;

    .physical_network_title {
      width: 60px;
      padding: 28px 20px;
      font-size: 20px;
      border: 1px solid #000;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
    }
  }
}