.through_management_area {
  height: 100%;
  border-radius: 10px;
  background-color: #FFF;
  padding: 15px 5px;
  position: relative;

  .through_management_btn {
    height: 50px;
  }

  .through_management_table {
    height: calc(100% - 120px);
    overflow: auto
  }

}

/* 直通管理 */
.connect_selected_area,
.connect_unselected_area {
  display: flex;

  .through_vm {
    padding-right: 10px;
    width: 434px;

    >h3 {
      padding: 0 10px;
      font-weight: 800;
      border-left: 5px solid red;
    }
  }
}

.connect_selected_area {
  padding-bottom: 10px;

  .through_device {
    padding-left: 10px;
    width: 434px;
  }
}

.connect_unselected_search {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 5px;
  height: 40px;

  h3 {
    border-left: 2px solid red;
    padding-left: 10px;
  }
}

.ivu-notice-notice {
  padding: 5px;
}

.ivu-notice {
  width: 600px;
}