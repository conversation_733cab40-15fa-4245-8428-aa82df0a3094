<style lang="less">
@import "../systemFunction.less";
</style>
<template>
  <div class="change_password_area">
    <Form ref="formss" :model="formItem" :label-width="100" :rules="rulesPW">
        <!-- <FormItem label="登录密码" style="padding: 10px 0" prop="loginPW"> -->
        <FormItem label="登录账号" style="padding: 10px 0" >
          <Input
            v-model="formItem.usernames"
            disabled
            style="width: 300px"
          ></Input>
        </FormItem>
        <FormItem label="旧密码" style="padding: 10px 0" prop="loginPW">
          <Input
            v-model="formItem.loginPW"
            type="password"
            password
            placeholder="请输入登录密码"
            style="width: 300px"
          ></Input>
        </FormItem>
        <FormItem label="新密码" style="padding: 10px 0" prop="newPW">
          <Input
            v-model="formItem.newPW"
            type="password"
            password
            placeholder="请输入新密码"
            style="width: 300px"
          ></Input>
        </FormItem>
        <FormItem label="确认密码" style="padding: 10px 0" prop="confirmPW">
          <Input
            v-model="formItem.confirmPW"
            type="password"
            password
            placeholder="请重新输入新密码"
            style="width: 300px"
          ></Input>
        </FormItem>
        <FormItem style="padding: 10px 0">
          <Button type="warning" ghost style="width: 100px" @click="resetClick('formss')">重置输入</Button>
          <Button class="plus_btn" style="width: 100px; margin-left: 100px" @click="confirmClick">确认修改</Button>
        </FormItem>
      </Form>


      <!-- <h1>时间</h1>
      <p>{{addHoursToTime('08:51:46 AM')}}</p> -->
  </div>
</template>
<script>
import {queryCodeRule,userChangesPassword} from '@/api/login';
export default {
  props: {
    tabName: String,
  },
  watch: {
    tabName(value) {
      if(value=='修改密码') {
        this.load()
        this.queryPasswordRules()
        this.formItem.usernames = window.sessionStorage.getItem('username')
      }
    },
    pwdRules(news){
      this.queryPasswordRules()
    },
  },
  mounted() {
    if(this.$store.state.power.systemFunctionTab == '修改密码') {
      this.load()
      this.queryPasswordRules()
      this.formItem.usernames = window.sessionStorage.getItem('username')
    }
  },
  data() {
    const validateLoginPW = (rule, value, callback) => {
      let list = window.sessionStorage.getItem('userPW')
      if (list == value) {
        callback();
      } else {
        callback(new Error("登录密码有误,请重新输入"));
      }
    };
    const validateNewPW = (rule, value, callback) => {
      let pwd_length = this.formItem.pwd_length;
      let guize = new RegExp(`^(?=.*[a-zA-Z])(?=.*\\d)(?=.*[@._])[a-zA-Z\\d@._]{${pwd_length},32}$`);
      if(value == this.formItem.loginPW){
        callback(new Error("新密码与登录密码重复"))
      }else if (!this.formItem.pwd_status) {
        guize = new RegExp(`^(?=.*[a-zA-Z0-9@._])[a-zA-Z0-9@._]{8,32}$`);
        guize.test(value)?callback():callback(new Error('请输入8-32个英文数字特殊符号@_.'))
      }else if (this.formItem.pwd_class == "l") {
        guize = new RegExp(`^(?=.*[a-zA-Z0-9@._])[a-zA-Z0-9@._]{${pwd_length},32}$`);
        guize.test(value)?callback():callback(new Error('请输入'+pwd_length+'-32个英文数字特殊符号@_.'))
      } else if (this.formItem.pwd_class == "m") {
        guize = new RegExp(`^(?=.*[a-zA-Z])(?=.*\\d)[a-zA-Z\\d@._]{${pwd_length},32}$`)
        guize.test(value)?callback():callback(new Error('请输入'+pwd_length+'-32个英文数字特殊符号@_. ，必须有英文和数字。'))
      } else if (this.formItem.pwd_class == "h") {
        guize = new RegExp(`^(?=.*[a-zA-Z])(?=.*\\d)(?=.*[@._])[a-zA-Z\\d@._]{${pwd_length},32}$`);
        guize.test(value)?callback():callback(new Error('请输入'+pwd_length+'-32个英文数字及特殊符号@_.'))
      }else {
        guize = new RegExp(`^(?=.*[a-zA-Z])(?=.*\\d)(?=.*[@._])[a-zA-Z\\d@._]{${pwd_length},32}$`);
        guize.test(value)?callback():callback(new Error('请输入'+pwd_length+'-32个英文数字及特殊符号@_.'))
      }
    };
    const validateConfirmPW = (rule, value, callback) => {
      if (value==this.formItem.newPW) {
        callback();
      } else {
        callback(new Error("两次输入的密码不匹配"));
      }
    };
    return {
      formItem: {
        usernames:'',
        loginUser:'',
        loginPW: "",
        newPW: "",
        confirmPW: "",
        pwd_length:null,
        pwd_class:"l",
        pwd_status:true,
      },
      rulesPW: {
        loginPW:[
          { required: true, message: "密码不能为空"},
          { validator: validateLoginPW, trigger: 'blur' },
        ],
        newPW: [
          { required: true, message: "密码不能为空"},
          
          { validator: validateNewPW, trigger: 'blur' }
        ],
        confirmPW: [
          { required: true, message: "密码不能为空"},
          { validator: validateConfirmPW, trigger: 'blur' }
        ],
      },
      quanxian:[],
    };
  },
  computed: {
    pwdRules() {
      return this.$store.state.power.pwdRules
    }
  },
  methods: {
    addHoursToTime(time12h){
      const matches = time12h.match(/(\d{2}):(\d{2}):(\d{2}) (\w{2})/);
      if (!matches) return null;
      const [, hours, minutes, seconds, amPm] = matches;
      let hours24 = parseInt(hours, 10);
      if (amPm === 'AM') {
        hours24 += 8; // 加上12小时
      }else {
        let list = hours24+12+8
        hours24 = list>24?list-24:list;
      }
      // 将小时数转换为2位数字格式
      hours24 = hours24.toString().padStart(2, '0');
    
      return `${hours24}:${minutes}:${seconds}`;
    },
    // 获取权限
    load() {
       if(document.cookie.length > 0) {
        let arr = document.cookie.split('; ');
        let arrData = new Object()
        arr.forEach(item=>{
          let li = item.split('=')
          arrData[li[0]]=li[1]
        })
        this.formItem.loginUser = arrData.username
        this.quanxian = new Array()
        if(arrData.role == 'sysadm'){
          // 系统管理员 sysadm
          this.quanxian=[1,2,3,4]
        }else if(arrData.role == 'secadm') {
          // 安全管理员 secadm
          this.quanxian=[2]

        }else if(arrData.role == 'adtadm') {
          // 审计员 adtadm
          
        }else if(arrData.role == 'operator') {
          
          // 操作员 operator
          this.quanxian=[]
        }
      }
    },
    // 查询密码规则
    queryPasswordRules(){
      queryCodeRule().then(callback=>{
        this.formItem.pwd_length = callback.data.pwd_length
        this.formItem.pwd_class = callback.data.pwd_class
        this.formItem.pwd_status = callback.data.status=="on"?true:false
      })
    },
    // 重置表单
    resetClick(name){
      this.$refs[name].resetFields();
    },
    // 确认提交
    confirmClick(){
      this.$refs.formss.validate((valid) => {
        if (valid) {
          userChangesPassword({
            username: this.formItem.loginUser,
            password: this.formItem.loginPW,
            new_password: this.formItem.newPW
          }).then(callback=>{
            if(callback.data.msg == "ok") {
              this.$router.push('/login')
              this.$Message.success({
                background: true,
                closable: true,
                duration: 15,
                content: '用户'+this.formItem.loginUser+'密码修改成功，请重新登录'
              });
            }else {
              this.$Message.error({
                background: true,
                closable: true,
                duration: 5,
                // content: '未能修改用户'+this.formItem.loginUser+"的密码",
                content: '用户'+this.formItem.loginUser+"的密码修改失败",
              });
            }
          })
        }else {
          this.$Message.warning({
            background: true,
            closable: true,
            duration: 5,
            content: "输入内容中有不符合规定的值",
          });
        }
      })
    },
  }
};
</script>
<style scoped>

</style>