<style lang="less">
@import "./tabType.less";
</style>
<template>
  <div class="type_management_area">
    <Spin fix v-if="spinShow" size="large" style="color:#ef853a">
      <Icon type="ios-loading" size=50 class="demo-spin-icon-load"></Icon>
      <div style="font-size:16px;padding:20px">Loading...</div>
    </Spin>
    <div class="type_management_btn" v-if="false">
      <Button class="plus_btn" @click="newType"><span class="icon iconfont icon-plus-circle"></span> 新建</Button>
      <!-- <Button class="plus_btn" @click="refresType"><Icon type="md-refresh" />刷新</Button> -->
      <!-- <Button class="close_btn" @click="delType"><span class="icon iconfont icon-close-circle"></span> 删除</Button> -->
      <div style="float: right">
        <Input v-model="tablePageForm.search_str" search enter-button placeholder="请输入名称" style="width: 300px" @on-search="tableVMsearchInput" />
      </div>
    </div>
    <div class="type_management_table">
      <Table :columns="typeColumn" :data="typeData" @on-selection-change="typeSlectChange"></Table>
      <div class="pages" v-if="this.typeData.length>0">
        <!-- <Pagination  :total="tableTotal" :page-size="tablePageForm.pagecount"  @page-change="onPageChange" @page-size-change="onPageSizeChange"/> -->
      </div>
    </div>
    <!-- 新建类型 -->
    <Modal v-model="typeModal" width="600" title="新建类型" @on-ok="newOK" @on-cancel="cancel" :mask-closable="false">
      <div>
        <Form :model="typeForm" :label-width="120" :rules="ruleForm">
          <FormItem label="类型名称" prop="name">
            <Input v-model="typeForm.name" placeholder="请输入类型名称"></Input>
          </FormItem>
          <!-- <FormItem label="直通" prop="extra_specs">
            <div style="display: flex;">
              <Checkbox v-model="typeForm.through"></Checkbox>
              <div style="display: flex;" v-if="this.typeForm.through">
                <Select v-model="typeForm.extra_specs" >
                  <Option v-for="item in backendData" disabled='item.backend =="no"' :value="item.value" :key="item.value" >{{ item.value }}</Option>
                </Select>
              </div>
            </div>
          </FormItem> -->
          <!-- <FormItem label="后端存储" prop="extra_specs">
            <Select v-model="typeForm.extra_specs">
              <Option v-for="item in backendData" disabled='item.backend =="no"' :value="item.value" :key="item.value" >{{ item.value }}</Option>
            </Select>
          </FormItem> -->
          <FormItem label="描述">
            <Input type="textarea" v-model="typeForm.description" placeholder="请输入描述内容"></Input>
          </FormItem>
        </Form>
      </div>
    </Modal>
  </div>
</template>
<script>
import {typeQuery,typeNewBuilt,typeDelete,directDeviceQuery} from '@/api/storage';
import Pagination from '@/components/public/Pagination.vue';
export default {
  components: {
    Pagination,
  },
  props: {
    tabName: String,
  },
  data() {
     const propTypename = (rule, value, callback) => {
      let list = /^[\u4e00-\u9fa5_a-zA-Z0-9@_.-]{2,32}$/;
      if (list.test(value)) {
        callback();
      } else {
        callback(new Error("请输入2-32个中文或字符（特殊字符可用@_.-）"));
      }
    };
    return {
      tableTotal: 0,
      tablePageForm: {
        page: 1, // 当前页
        pagecount: this.$store.state.power.pagecount, // 每页条
        search_str: "", // 收索
        order_type: "desc", // 排序规则
        order_by: "", // 排序列
      },
      spinShow:false,
      typeColumn:[
        { title: "类型名称", key: "name", sortable: true,
          render: (h, params) => {
            let text = params.row.name == "__DEFAULT__"?"默认类型": params.row.name
            return h("span",text)
          }},
        { title: "后端存储卷", key: "volume_backend_name", sortable: true,
          render: (h, params) => {
            if(params.row.extra_specs.volume_backend_name!==undefined) {
              return h("span",{},params.row.extra_specs.volume_backend_name)
            }else {
              return h("span",{},"-")
            }
          }
        },
        { title: "描述", key: "description"},
        // { title: "操作", key: "operation",width: 120,
        //   render: (h, params) => {
        //     return h("Button",{
        //       style: {
        //         color:params.row.name == "__DEFAULT__"?"#ccc":"red"
        //       },
        //       props: {
        //         disabled: params.row.name == "__DEFAULT__"
        //       },
        //       nativeOn: {
        //         click: () => {
        //           this.$Modal.confirm({
        //             title: '删除卷类型',
        //             content: '<p>是否删除 <span style="font-weight: 600;color:red;">'+params.row.name+'</span> 卷类型?</p>',
        //             onOk: () => {
        //               typeDelete({data:{id: params.row.id}}).then(callback=>{
        //                 if(callback.data.msg=="ok") {
        //                   this.typeGet()
        //                   this.$Message.success({
        //                     background: true,
        //                     closable: true,
        //                     duration: 5,
        //                     content: '删除类型完成',
        //                   });
        //                 }else {
        //                   this.$Message.error({
        //                     background: true,
        //                     closable: true,
        //                     duration: 5,
        //                     content: '删除类型失败'
        //                   });
        //                 }
        //               })
        //             },
        //             onCancel: () => {}
        //           });
        //         }  
        //       },
        //     },"删除")
        //   }
        // }
      ],
      typeData:[],
      typeSlectData:[],
      typeSlectId:[],
      typeSlectName:[],
      // 新建类型
      typeModal:false,
      typeForm:{
        name:"",
        through:false,
        extra_specs:"",
        description:"",
      },
      ruleForm:{
        name:[
          { required: true, message: "必填项", trigger: "blur" },
          { validator: propTypename, trigger: "change" },
        ],
        extra_specs:[{ required: true, message: "必选项", trigger: "blur" }],
      },
      backendData:[],
    }
  },
  watch: {
    tabName(value) {
      if(value=='类型管理'){
        this.typeGet()
      }
    },
  },
  mounted() {
    if(this.$store.state.power.storageResourceTab == '类型管理') {
      this.typeGet()
    }
  },
  methods: {
    // 当前分页
    onPageChange(item) {
      this.tablePageForm.page = item;
      this.typeGet();
    },
    // 每页条数
    onPageSizeChange(item) {
      this.$store.state.power.pagecount = item
      this.tablePageForm.pagecount = this.$store.state.power.pagecount
      this.tablePageForm.page = 1
      this.tableTotal = 0
      this.tablePageForm.search_str = ""
      this.typeGet();
    },
    // 虚拟机列表 列排序
    sortColumn(column, key, order) {
      if (column.key !== "normal") {
        this.tablePageForm.order_by = column.key;
        this.tablePageForm.order_type = column.order;
        this.tablePageForm.page = 1
        this.tableTotal = 0
        this.tablePageForm.search_str = ""
        this.typeGet();
      }
    },
    // 搜索虚拟机列表
    tableVMsearchInput(){
      this.tablePageForm.page = 1
      this.tableTotal = 0
      this.typeGet();
    },
    // 获取类型管理表数据
    typeGet(){
      this.spinShow = true
      typeQuery().then(callback=>{
        let arr = new Array()
        callback.data.forEach(em=>{
          arr.push({
            id:em.id,
            name:em.name=="__DEFAULT__"?"默认类型": em.name,
            extra_specs:em.extra_specs,
            description:em.description,
          })
        })
        this.typeData = arr
        this.typeSlectData = new Array()
        this.spinShow = false
      }).catch((error) => {
        this.spinShow=false;
      });
    },
    // 刷新表数据
    refresType(){
      this.typeGet()
    },
    // 表格选中数据
    typeSlectChange(item){
      let arrID = new Array();
      let arrName = new Array();
      item.forEach((em) => {
        arrID.push(em.id);
        arrName.push(em.name);
      });
      this.typeSlectData = item
			this.typeSlectID=arrID
			this.typeSlectName=arrName
    },
    delType() {
      if(this.typeSlectData.length>0) {
        this.$Modal.confirm({
          title: '删除卷类型',
          content: '<p>是否删除 <span style="font-weight: 600;color:red;">'+this.typeSlectName+'</span> 卷类型?</p>',
          onOk: () => {
            typeDelete({data:{id: this.typeSlectID}}).then(callback=>{
              if(callback.data.msg=="ok") {
                this.typeGet()
                this.$Message.success({
                  background: true,
                  closable: true,
                  duration: 5,
                  content: '删除类型完成',
                });
              }else {
                this.$Message.error({
                  background: true,
                  closable: true,
                  duration: 5,
                  content: '删除类型失败'
                });
              }
            })
          },
          onCancel: () => {
             
          }
        });
      }else {
        this.$Message.warning({
          background: true,
          closable: true,
          duration: 5,
          content: '请先选择数据',
        });
      }
    },
    // 新建类型
    newType() {
      this.typeModal = true
      this.typeForm.name = ""
      this.typeForm.through = false
      this.typeForm.extra_specs = ""
      this.typeForm.description = ""
      // 直通设备数据
      directDeviceQuery().then((callback) => {
        let arr = new Array();
        callback.data.forEach(em => {
          // if(em.backend =="no") {
            arr.push({
              value:em.hostname+"("+em.hostip+"):"+em.device
            })
          // }
        });
        this.backendData = arr
        this.typeForm.extra_specs =arr[0].value 
      })
    },
    // 新建类型确认
    newOK() {
      let ruleName = /^[\u4e00-\u9fa5_a-zA-Z0-9@_.-]{2,32}$/;
      if(ruleName.test(this.typeForm.name)) {
        let data = {
          name:this.typeForm.name,
          description:this.typeForm.description,
        }
        this.typeForm.through?data.extra_specs = this.typeForm.extra_specs:data.extra_specs = ""
        typeNewBuilt(data).then((callback) => {
          if(callback.data.msg=="ok") {
            this.typeGet()
            this.$Message.success({
              background: true,
              closable: true,
              duration: 5,
              content: '新建类型完成',
            });
          }else {
            this.$Message.error({
              background: true,
              closable: true,
              duration: 5,
              content: '新建类型失败'
            });
          }
        })
      }else {
        this.$Message.error({
          background: true,
          closable: true,
          duration: 5,
          content: '类型名称或后端存储数据有误'
        });
      }
    },
    cancel(){},
  },
}
</script>
