<template>
  <div>
    <Modal
      v-model="model"
      width="600"
      :mask-closable="false"
    >
      <template #header><p>删除虚拟机</p></template>
      <div style="padding: 5px">
        <span>是否删除下列虚拟机？</span>
        <p style="color:red;word-wrap: break-word">{{tableNames.toString()}}</p>
      </div>
      <div slot="footer">
        <Button type="text" @click="model = false">取消</Button>
        <!-- <Button type="primary" :loading="disabled" @click="modelOK">
          <span v-if="!disabled">确认</span>
          <span v-else>删除中</span>
        </Button> -->
        <Button type="primary" @click="modelOK" :disabled="disabled">确认</Button>
      </div>
    </Modal>
  </div>
</template>
<script>
import { vmGroupTableDelete } from '@/api/virtualMachine';  // 虚拟机表格 删除
export default {
  props: {
    tableArr: Array,
    deletionTime: String,
  },
  watch: {
    deletionTime(news){
      this.tableNames = this.tableArr.map(em=>{ return em.name})
      this.tableIDS = this.tableArr.map(em=>{ return em.id})
      this.model = true
      this.disabled = false
    }
  },
  data(){
    return {
      model:false,
      disabled: false,
      tableNames: [],
      tableIDS: [],
    }
  },
  methods: {
    modelOK() {
      this.disabled = true
      vmGroupTableDelete({data:{
        ids: this.tableIDS,
        names: this.tableNames,
      }})
      .then((callback) => {
        this.model = false;
        if(callback.data.msg == 'ok') {
          this.$emit("return-ok",'删除虚拟机操作完成');
        }else {
          this.$emit("return-error",'删除虚拟机操作失败');
        }
      })
      .catch((error) => {
        this.$emit("return-error",'删除虚拟机操作失败');
      })
    }
  }
}
</script>