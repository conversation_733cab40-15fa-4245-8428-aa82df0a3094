{"_from": "path-to-regexp@0.1.7", "_id": "path-to-regexp@0.1.7", "_inBundle": false, "_integrity": "sha512-5DFkuoqlv1uYQKxy8omFBeJPQcdoE07Kv2sferDCrAq1ohOU+MSDswDIbnx3YAM60qIOnYa53wBhXW0EbMonrQ==", "_location": "/path-to-regexp", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "path-to-regexp@0.1.7", "name": "path-to-regexp", "escapedName": "path-to-regexp", "rawSpec": "0.1.7", "saveSpec": null, "fetchSpec": "0.1.7"}, "_requiredBy": ["/express"], "_resolved": "https://registry.npmmirror.com/path-to-regexp/-/path-to-regexp-0.1.7.tgz", "_shasum": "df604178005f522f15eb4490e7247a1bfaa67f8c", "_spec": "path-to-regexp@0.1.7", "_where": "/root/docker/node_modules/express", "bugs": {"url": "https://github.com/component/path-to-regexp/issues"}, "bundleDependencies": false, "component": {"scripts": {"path-to-regexp": "index.js"}}, "deprecated": false, "description": "Express style path to RegExp utility", "devDependencies": {"istanbul": "^0.2.6", "mocha": "^1.17.1"}, "files": ["index.js", "LICENSE"], "homepage": "https://github.com/component/path-to-regexp#readme", "keywords": ["express", "regexp"], "license": "MIT", "name": "path-to-regexp", "repository": {"type": "git", "url": "git+https://github.com/component/path-to-regexp.git"}, "scripts": {"test": "istanbul cover _mocha -- -R spec"}, "version": "0.1.7"}