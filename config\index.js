'use strict'
// Template version: 1.3.1
// see http://vuejs-templates.github.io/webpack for documentation.

const path = require('path')


module.exports = {
  dev: {
    // Paths
    assetsSubDirectory: "static",
    assetsPublicPath: "/",
    proxyTable: {
      "/login": {
        target: "http://************:8188", // 测试
        // target: "http://***********:8088", // jb
        // target: "http://192.168.202.126:8088", // hz
        changeOrigin: true,
        secure: false,
        pathRewrite: {
          "^/login": "",
        },
      },
      "/thelog": {
        target: "http://************:8193", // 测试
        // target: "http://*************:8092", // mjj
        // target: "http://192.168.213.214:8093", // sjb
        changeOrigin: true,
        secure: false,
        pathRewrite: {
          "^/thelog": "",
        },
      },
      "/theapi": {
        // target: "http://************:8106", // 测试
        target: "http://***********:8006", // sjb
        // target: "http://192.168.202.126:8006", // hz
        // target: "http://192.168.212.242:8006", // yjy
        // target: "http://10.168.2.3:8006", // 毛京京
        changeOrigin: true,
        logLevel: "debug",
        secure: false,
        pathRewrite: {
          "^/theapi": "",
        },
      },
      "/acapi": {
        target: "http://************:10098", // 测试
        // target: "http://***********:9998", // 生佳宝
        // target: "http://192.168.202.126:9998", // hz
        changeOrigin: true,
        secure: false,
        pathRewrite: {
          "^/acapi": "",
        },
      },
      "/thelicense": {
        target: "http://************:8155", // 测试
        // target: "http://*************:8055", // 毛京京
        // target: "http://*************:8055", // 测试
        // target: "http://***********:8059", // 生佳宝
        changeOrigin: true,
        secure: false,
        pathRewrite: {
          "^/thelicense": "",
        },
      },
      "/ws": {
        target: "http://your_backend_server_address",
        ws: true,
        changeOrigin: true,
      },
      "/thesc": {
        target: "http://************:9012", // 测试
        // target: "http://*************:9002", // 毛京京
        changeOrigin: true,
        logLevel: "debug",
        secure: false,
        pathRewrite: {
          "^/thesc": "",
        },
      },

      "/cloudy": {
        target: "http://************:8191", // 测试
        // target: 'http://*************:8091', // 春天
        // target: 'http://*************:8091', // 毛京京

        changeOrigin: true,
        secure: false,
        pathRewrite: {
          "^/cloudy": "",
        },
      },
      "/user": {
        target: "http://************:8189", // 测试
        // target: 'http://*************:8089', // 春天
        // target: "http://*************:8089", // 毛京京
        changeOrigin: true,
        secure: false,
        pathRewrite: {
          "^/user": "",
        },
      },
      // '/desk': {
      //   target: 'http://*************:8085', // 毛京京
      //   changeOrigin: true,
      //   secure: false,
      //   pathRewrite: {
      //     '^/desk': ''
      //   }
      // },
      "/upload": {
        target: "http://************:8190", // 测试
        changeOrigin: true,
        secure: false,
        pathRewrite: {
          "^/upload": "",
        },
        timeout: 1000 * 60 * 30,
      },
      "/dashboard": {
        target: "http://192.168.1.241/dashboard", // 开发机器
        changeOrigin: true,
        logLevel: "debug",
        secure: false,
        pathRewrite: {
          "^/dashboard": "",
        },
      },

      "/thewebsocket": {
        target: "ws://***************:9180", // 测试
        ws: true,
        changeOrigin: true,
        // secure: false,
        pathRewrite: {
          "^/thewebsocket": "",
        },
      },

      // 存储服务
      "/storage": {
        // target: 'http://*************:8089', // 春天
        target: "http://192.168.2.180:8000", // 生佳宝
        changeOrigin: true,
        secure: false,
        pathRewrite: {
          "^/storage": "",
        },
      },
      // 大数据ceph原生接口API
      "/thecephapi": {
        target: "http://192.168.2.180:9179", // 生佳宝
        changeOrigin: true,
        secure: false,
        pathRewrite: {
          "^/thecephapi": "",
        },
      },
      // 大数据java接口v2
      "/thecss": {
        target: "http://*************:10002", // 大数据机器
        // target: "http://***************:10002", // 开发机器
        // target: "http://192.168.2.180::10002", // 生佳宝
        // target: "http://192.168.5.207:10002", // 毛京京
        changeOrigin: true,
        secure: false,
        pathRewrite: {
          "^/thecss": "",
        },
      },
      "/apiquery": {
        // ceph端口9095的代理地址
        target: "http://***************:9097", // 获取首页数据
        changeOrigin: true,
        logLevel: "debug",
        secure: false,
        pathRewrite: {
          "^/apiquery": "/apiquery",
        },
      },
      "/dayuapi": {
        // target: "http://************:9001", // 废弃
        pathRewrite: {
          "^/dayuapi": "",
        },
      },
    },

    // Various Dev Server settings
    // host: "127.0.0.1", // can be overwritten by process.env.HOST
    host: "0.0.0.0",
    port: 8089, // can be overwritten by process.env.PORT, if port is in use, a free one will be determined
    autoOpenBrowser: true,
    errorOverlay: true,
    notifyOnErrors: true,
    poll: false, // https://webpack.js.org/configuration/dev-server/#devserver-watchoptions-

    /**
     * Source Maps
     */

    // https://webpack.js.org/configuration/devtool/#development
    devtool: "cheap-module-eval-source-map",

    // If you have problems debugging vue-files in devtools,
    // set this to false - it *may* help
    // https://vue-loader.vuejs.org/en/options.html#cachebusting
    cacheBusting: true,

    cssSourceMap: true,
  },

  build: {
    // Template for index.html
    index: path.resolve(__dirname, "../dist/index.html"),

    // Paths
    assetsRoot: path.resolve(__dirname, "../dist"),
    assetsSubDirectory: "static",
    assetsPublicPath: "./",

    /**
     * Source Maps
     */

    productionSourceMap: false,
    // https://webpack.js.org/configuration/devtool/#production
    devtool: "#source-map",

    // Gzip off by default as many popular static hosts such as
    // Surge or Netlify already gzip all static assets for you.
    // Before setting to `true`, make sure to:
    // npm install --save-dev compression-webpack-plugin
    productionGzip: false,
    productionGzipExtensions: ["js", "css"],

    // Run the build command with an extra argument to
    // View the bundle analyzer report after build finishes:
    // `npm run build --report`
    // Set to `true` or `false` to always turn it on or off
    bundleAnalyzerReport: process.env.npm_config_report,
  },
};
