.compute_cluster {
  display: flex;
  height: calc(100% - 70px);
  border-radius: 10px;
  background-color: #FFF;
  padding: 15px 5px;
  overflow: auto;

  p {
    font-weight: 600;
    border-left: 4px solid #fb6129;
    margin: 0 0 5px 5px;
    padding-left: 10px;
    color: #333;
    font-size: 14px;
  }

  .physical_unit {
    display: flex;
    flex-direction: row;
    justify-content: space-evenly;
    height: 40px;
    border-top: 1px solid #ccc;

    >.ivu-tooltip {
      line-height: 40px;
    }

    .ivu-icon {
      font-size: 20px;
    }
  }

  .ivu-menu-light {
    width: 200px !important;
    min-width: 200px !important;
    max-width: 200px !important;

    .ivu-menu-vertical {
      .ivu-menu-item-active:not(.ivu-menu-submenu) {
        color: #fff !important;
        background: #fb6129 !important;
      }

      .ivu-menu-item:hover,
      .ivu-menu-submenu-title:hover {
        color: #333;
        background: #ffe9e1;
      }

      .ivu-menu-item-active:not(.ivu-menu-submenu):after {
        background: none !important;
      }
    }
  }

  .mennuclass {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .menuspan {
      width: 130px;
      display: inline-block
    }

    .allow {
      float: right;
      cursor: pointer;
    }

    .prohibit {
      float: right;
      cursor: no-drop;
    }
  }

  // 主机列表
  .host_table_area {
    width: calc(100% - 200px);
    height: 100%;
    padding: 0 10px;

    .host_table_box {
      height: calc(100% - 100px);
      overflow: auto;
    }

    .ivu-progress-show-info .ivu-progress-outer {
      padding-right: 0px !important;
      margin-right: 0px !important;
    }

    .ivu-progress-text {
      position: absolute;
      line-height: 20px;
      left: 32%;
      top: 0;
      color: #333;
    }

    .ivu-progress-bg {
      height: 20px !important;
    }
  }
}

// 主机详情
.host_drawer_basic {
  display: none;
  height: 70px;
  border-bottom: 1px solid #ccc;

  li {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 60px;
    float: left;
    
  }
}

.host_drawer_charts {
  ul {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;

    li {
      width: 100%;
      padding: 20px 0;
      border-bottom: 1px solid #ccc;

      p {
        padding: 0 0 10px 20px;
        font-size: 14px;
        color: #333;
      }

      .xq {
        display: flex;
        flex-wrap: wrap;
        justify-content: flex-start;
        padding-left: 20px;

        .divxq {
          display: flex;

          .lable {
            width: 110px;
            color: #999;
            display: inline-block;
          }

          .key {
            width: 230px;
            color: #333;
            display: inline-block;
            white-space: nowrap;
            overflow: hidden;
          }
        }
      }
      .radio_area {
        padding: 0 0 10px 20px;
        font-size: 14px;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        .radio_piece {
          display: flex;
          align-content: space-between;
          flex-direction: column;
          align-items: center;
          margin-right: 26px;
          cursor: pointer;
          .radio_title {
            padding-bottom: 5px;
            font-weight: 800;
          }
          .radio_selected {
            width: 10px;
            height: 6px;
            border-radius: 3px;
          }
        }
      }
    }
  }
}