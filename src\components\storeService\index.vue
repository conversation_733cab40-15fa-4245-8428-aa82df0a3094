<template>
  <div class="storeService-index-wrap">
    <Tabs type="card" name="1" v-model="tabName" @on-click="Tabs">
      <TabPane :label="item.label" tab="1" :name="item.tabName" :index="index" v-for="(item, index) in tabList" :key="index">
        <component :is="item.tabName"></component>
      </TabPane>
    </Tabs>
  </div>
</template>

<script>
  import Iscsi from './Iscsi'
  import Nfs from './Nfs'
  import S3 from './S3'
  export default {
   components: {
     Iscsi,
     Nfs,
     S3
   },
   data() {
     return{
        tabName:"Iscsi",
        tabList: [
          {
            tabName: 'Iscsi',
            label: 'ISCSI'
          },
          {
            tabName: 'Nfs',
            label: 'NFS'
          },
          {
            tabName: 'S3',
            label: 'S3'
          }
        ]
     }
    },
    mounted(){
      
    },
    methods:{
      Tabs(name){
        this.tabName=name
      },
    },
    watch: {
      tabName(news){
      }
    },
  }
</script>

<style scoped lang='less'>
  .storeService-index-wrap {
    height:100%;
    padding:15px 15px 0 15px;
  } 
</style>