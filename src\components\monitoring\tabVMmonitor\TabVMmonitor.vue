<style lang="less">
@import "../monitoring.less";
@import "./vmMonitor.less";
</style>
<style>
  .tooltio-table table{
    width: 399px!important;
  }
</style>
<template>
  <div class="vm_monitor_area">
    <!-- <p class="vm_big_title">可用虚拟机数量：<span>{{allTotal}}</span></p> -->
    <div class="node_vm_area" v-for="(item,index) in vmInformation">
      <div class="vm_status_area">
        <div class="vm_status_title">
          <span>{{item.hostname}}（{{item.ip}}）</span>
          <span>虚拟机总数量：<span class="title_vm_count">{{item.vm_count}}</span></span>
        </div>
        <div class="node_vm_status">
          <img src="../../../assets/monitorIMG/vm.png">
          <div class="vm_status_number">
            <Tooltip :disabled="item.count_normal==0">
            <!-- <Tooltip :always='true'> -->
              <div class="vm_status_public vm_open">
                <span>开机数量 <span class="vm_public_number">{{item.count_normal}}</span></span>
              </div>
              <template #content>
                <div class="tooltio-table">
                  <Table :columns="Opencolumns" :data="item.count_normal_list" height=200 width=400></Table>
                </div>
              </template>
            </Tooltip>
            <Tooltip :disabled="item.count_fault==0?true:false">
              <div class="vm_status_public vm_fault">
                <span>故障数量 <span class="vm_public_number">{{item.count_fault}}</span></span>
              </div>
              <template #content>
                <div class="tooltio-table">
                  <Table :columns="VMcolumns" :data="item.count_fault_list" height=200 width=400></Table>
                </div>
              </template>
            </Tooltip>
            <Tooltip :disabled="item.count_shutoff==0?true:false">
              <div class="vm_status_public vm_close">
                <span>关机数量 <span class="vm_public_number">{{item.count_shutoff}}</span></span>
              </div>
              <template #content>
                <div class="tooltio-table">
                  <Table :columns="VMcolumns" :data="item.count_shutoff_list" height=200 width=400></Table>
                </div>
              </template>
            </Tooltip>
            <Tooltip :disabled="item.count_alarm==0?true:false">
              <div class="vm_status_public vm_warn">
                <span>告警数量 <span class="vm_public_number">{{item.count_alarm}}</span></span>
              </div>
              <template #content>
                <div class="tooltio-table">
                  <Table :columns="VMcolumns" :data="item.count_alarm_list" height=200 width=400></Table>
                </div>
              </template>
            </Tooltip>
          </div>
        </div>
      </div>
      <div class="vm_chart_area">
        <div class="radio_chart_select">
          <div class="radio_area">
            <div class="radio_piece" v-for="every in radioData" @click="trendONchange(index,every.lable,item.time,item.hostname)">
              <span class="radio_title" :style="{color:item.trend==every.lable?'#121529':'#717379'}">{{every.title}}</span>
              <span class="radio_selected" :style="{background:item.trend==every.lable?'#fe6902':''}"></span>
            </div>
          </div>
          <Select v-model="item.time" style="width:150px" @on-change="timeONchange(index,item.trend,item.time,item.hostname)">
            <Option v-for="item in timeData" :value="item.value" :key="item.value">{{ item.label }}</Option>
          </Select>
        </div>
        <ChartLine :datas="item.chatdata?item.chatdata:{}" :times="item.time" style="width:100%;height:84%"></ChartLine>
      </div>
    </div>
  </div>
</template>
<script>
import {vmBasicQuery,vmCPUquery,vmMemoryQuery,vmDiskQuery,vmNetworkQuery} from '@/api/monitor';

import ChartLine from "../chartTemplate/ChartLine.vue"
export default {
  components: {
    ChartLine,
  },
  props: {
    tabName: String,
  },
  watch: {
    tabName(value) {
      if(value=='虚拟机监控') {
        this.getVMbesicData()
      }
    },
  },
  mounted() {
    if(this.$store.state.power.itMonitoringTab == '虚拟机监控') {
      this.getVMbesicData()
    }
  },
  data() {
    return {
      allTotal:0,
      vmInformation:[
        {
          hostname:"占无数据",
          ip:"-",
          vm_count: 0,
          count_normal: 0, // 正常
          count_normal_list:[],
          count_shutoff: 0, // 关机
          count_shutoff_list:[],
          count_alarm: 0, // 告警
          count_alarm_list:[],
          count_fault: 0, // 故障
          count_fault_list:[],
          trend:"cpu", // 默认显示图表
          time:1, // 默认图表时间段
          chatdata:{},
        },
        {
          hostname:"占无数据1",
          ip:"111",
          vm_count: 10,
          count_normal: 0, // 正常
          count_normal_list:[],
          count_shutoff: 0, // 关机
          count_shutoff_list:[],
          count_alarm: 0, // 告警
          count_alarm_list:[],
          count_fault: 0, // 故障
          count_fault_list:[],
          trend:"cpu", // 默认显示图表
          time:1, // 默认图表时间段
          chatdata:{},
        }
      ],
      radioData:[
        {title:"CPU趋势",lable:"cpu"},
        {title:"内存趋势",lable:"memory"},
        {title:"磁盘趋势",lable:"disk"},
        {title:"网络趋势",lable:"network"},
      ],
      timeData:[{label:"最近1小时",value:1},{label:"最近24小时",value:24}],
      VMcolumns:[{title:"名称",key:"name",align: 'center'},{title:"IP",key:"ip",align: 'center'}],
      Opencolumns:[
        {title:"名称",key:"name", tooltip:true ,align: 'center'},
        {title:"IP",key:"ip",align: 'center'},
        {title:"代理",key:"rely",align: 'center',
          render: (h, params) => {
            return h('span',{
              style:{
                color:params.row.rely?"green":'red'
              }
            },params.row.rely?"已安装":"未安装")
          },
        }
      ],
    }
  },
  methods: {
    // 趋势
    async trendONchange(index,trend,time,name){
      this.vmInformation[index].trend = trend
     
      this.chartTimeEncapsulation(time,name) 
      let params = this.chartTimeEncapsulation(time,name)
      try {
        if(trend=="cpu"){
          this.cpuDataQuery(index,params)
        }else if(trend=="memory"){
          this.memoryDataQuery(index,params)
        }else if(trend=="disk"){
          this.distDataQuery(index,params)
        }else if(trend=="network"){
          this.networkDataQuery(index,params)
        }
      } catch (error) {
        console.error("获取数据时出错:", error);
      }
    },
    // 时间
    timeONchange(index,trend,time,name){
      this.trendONchange(index,trend,time,name)
    },
    // 虚拟机 基本 数据获取
    getVMbesicData(){
      vmBasicQuery().then(callback=>{
        let number = 0
        let arr = new Array()
        callback.data.forEach(em=>{
          number+=em.vm_count
          arr.push({
            hostname:em.hostname,
            ip:em.ip,
            vm_count:em.vm_count,
            count_normal:em.count_normal,
            count_normal_list:em.count_normal_list,
            count_shutoff:em.count_shutoff,
            count_shutoff_list:em.count_shutoff_list,
            count_alarm:em.count_alarm,
            count_alarm_list:em.count_alarm_list,
            count_fault:em.count_fault,
            count_fault_list:em.count_fault_list,
            trend:em.trend,
            time:em.time,
            chatdata:{},
          })
        })
        this.vmInformation = arr
        // this.vmInformation = callback.data
        this.allTotal = number
        this.infoCharts(this.vmInformation)
      })
    },
    // 初始化获取数据
    infoCharts(data){
      data.forEach((em,index)=>{
        this.trendONchange(index,em.trend,em.time,em.hostname)
      })
    },
    // cpu趋势
    cpuDataQuery(index,data){
      return new Promise((resolve, reject) => {
        vmCPUquery(data).then(callback => {
          this.vmInformation[index].chatdata=callback.data
          resolve();
        }).catch(error => {
          reject(error);
        });
      });
    },
    // 内存趋势
    memoryDataQuery(index,data){
      return new Promise((resolve, reject) => {
        vmMemoryQuery(data).then(callback => {
          this.vmInformation[index].chatdata=callback.data
          resolve();
        }).catch(error => {
          reject(error);
        });
      });
    },
    // 硬盘趋势
    distDataQuery(index,data){
      return new Promise((resolve, reject) => {
        vmDiskQuery(data).then(callback => {
          this.vmInformation[index].chatdata=callback.data
          resolve();
        }).catch(error => {
          reject(error);
        });
      });
    },
    // 网络趋势
    networkDataQuery(index,data){
      return new Promise((resolve, reject) => {
        vmNetworkQuery(data).then(callback => {
          this.vmInformation[index].chatdata=callback.data
          resolve();
        }).catch(error => {
          reject(error);
        });
      });
    },
    // 图表时间封装
    chartTimeEncapsulation(item,hostname){
      // 获取当前时间
      var currentDate = new Date();
      // 获取一小时前的时间
      var oneHourBefore = new Date(currentDate.getTime() - item*60 * 60 * 1000);
      // 获取年、月、日
      var year = currentDate.getFullYear();
      var month = ("0" + (currentDate.getMonth() + 1)).slice(-2); // 月份从0开始，所以要加1
      var day = ("0" + currentDate.getDate()).slice(-2);
      // 获取小时、分钟（当前时间）
      var hours = ("0" + currentDate.getHours()).slice(-2);
      var minutes = currentDate.getMinutes();
      // 调整分钟（当前时间）
      if (minutes % 10 < 5) {
        minutes = Math.floor(minutes / 10) * 10; // 当分钟尾数小于5时，取整为10的倍数
      } else {
        minutes = Math.ceil(minutes / 10) * 10 - 5; // 当分钟尾数大于6时，取整为10的倍数再减去5
      }
      // 格式化分钟（当前时间）
      minutes = ("0" + minutes).slice(-2);

      // 输出当前时间结果
      var formattedCurrentDateTime =
        year + "-" + month + "-" + day + " " + hours + ":" + minutes;

      // 获取一小时前的年、月、日、时、分
      var beforeYear = oneHourBefore.getFullYear();
      var beforeMonth = ("0" + (oneHourBefore.getMonth() + 1)).slice(-2);
      var beforeDay = ("0" + oneHourBefore.getDate()).slice(-2);
      var beforeHours = ("0" + oneHourBefore.getHours()).slice(-2);
      var beforeMinutes = oneHourBefore.getMinutes();

      // 调整分钟（一小时前的时间）
      if (beforeMinutes % 10 < 5) {
        beforeMinutes = Math.floor(beforeMinutes / 10) * 10; // 当分钟尾数小于5时，取整为10的倍数
      } else {
        beforeMinutes = Math.ceil(beforeMinutes / 10) * 10 - 5; // 当分钟尾数大于6时，取整为10的倍数再减去5
      }

      // 格式化分钟（一小时前的时间）
      beforeMinutes = ("0" + beforeMinutes).slice(-2);

      // 输出一小时前的时间结果
      var formattedBeforeDateTime =
        beforeYear +
        "-" +
        beforeMonth +
        "-" +
        beforeDay +
        " " +
        beforeHours +
        ":" +
        beforeMinutes;
      let params = {
        start:formattedBeforeDateTime,
        end:formattedCurrentDateTime,
        hostname:hostname
      }
      return params
    },
  },
}
</script>