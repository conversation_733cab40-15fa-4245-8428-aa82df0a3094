<style lang="less">
  @import "../assets/css/screenPage.less";
</style>
<template>
  <div class="large_screen">
    <!-- 大屏头部区域 -->
    <div class="screen_head">
      <ul>
        <li class="screen_logo">
          <img src="../assets/loginLog.png" alt="THXH">
        </li>
        <li class="project_name"><h4>{{THElogs}}</h4></li>
        <li class="system_time">
          <div style="font-size:28px;color:#ff9D00">{{currentTime}}</div>
          <div style="font-size:12px;color:#F9DFB6;">
            <p>{{currentDate}}</p>
            <p>{{currentWeek}}</p>
          </div>
        </li>
      </ul>
    </div>

    <!-- 大屏内容区域 -->
    <div class="screen_center">
      <!-- 内容左侧区域 -->
      <div class="center_left">
        <ul>
          <li>
            <div class="module_title">
              <span>CPU分配比</span>
            </div>
            <div class="module_ring">
              <!-- CPU环 -->
              <i-circle :percent="vcpuRate.toFixed(0)-0" :size="ringSize" :stroke-width="strokeWidth" :trail-width="trailWidth" :trail-color="trailbg" :stroke-color="CPUbj">
                <span v-if="vcpuRate==0" class="demo-i-circle-inner" style="font-size:24px">0%</span>
                <span v-if="vcpuRate>0&&vcpuRate<1" class="demo-i-circle-inner" style="font-size:24px">小于1%</span>
                <span v-if="vcpuRate>=1" class="demo-i-circle-inner" style="font-size:24px">{{vcpuRate.toFixed(0)}}%</span>
              </i-circle>
            </div>
            <div class="module_usage">
              <p>已分配<span class="orange_usage">{{vcpuUsageTotal}}（核）</span></p>
              <p>VCPU总数 <span class="orange_usage">{{vcpuTotal}}（核）</span></p>
            </div>
          </li>
          <li>
            <div class="module_title">
              <span>内存分配比</span>
            </div>
            <div class="module_ring">
              <!-- 内存环 -->
              <i-circle :percent="memoryRate.toFixed(0)-0" :size="ringSize" :stroke-width="strokeWidth" :trail-width="trailWidth" :trail-color="trailbg" :stroke-color="NCbj">
                <span  v-if="memoryRate==0" class="demo-i-circle-inner" style="font-size:24px">0%</span>
                <span v-if="memoryRate>0&&memoryRate<1" class="demo-i-circle-inner" style="font-size:24px">小于1%</span>
                <span v-if="memoryRate>=1" class="demo-i-circle-inner" style="font-size:24px">{{memoryRate.toFixed(0)}}%</span>
                <!-- <span class="demo-i-circle-inner" style="font-size:24px">{{memoryRate}}%</span> -->
              </i-circle>
            </div>
            <div class="module_usage">
              <p>已分配 <span class="blue_usage">{{byteUnitConversion("",memoryUsageTotal)}}</span></p>
              <p>总数 <span class="blue_usage">{{byteUnitConversion("",memoryTotal)}}</span></p>
            </div>
          </li>
          <li>
            <div class="module_title">
              <span>存储使用率</span>
            </div>
            <div class="module_ring">
              <!-- 存储环 -->
              <i-circle :percent="storageRate.toFixed(0)-0" :size="ringSize" :stroke-width="strokeWidth" :trail-width="trailWidth" :trail-color="trailbg" :stroke-color="CCbj">
                <span v-if="storageRate==0" class="demo-i-circle-inner" style="font-size:24px">0%</span>
                <span v-if="storageRate>0&&storageRate<1" class="demo-i-circle-inner" style="font-size:24px">小于1%</span>
                <span v-if="storageRate>=1" class="demo-i-circle-inner" style="font-size:24px">{{storageRate.toFixed(0)}}%</span>
                </i-circle>
            </div>
            <div class="module_usage">
              <p>已使用 <span class="orange_usage">{{byteUnitConversion("",storageUsageTotal)}}</span></p>
              <p>总容量 <span class="orange_usage">{{byteUnitConversion("",storageTotal)}}</span></p>
            </div>
          </li>
        </ul>
      </div>
       <!-- 内容居中区域 -->
      <div class="centerMiddle">
        <ul>
          <!-- <li class="middleMachine">

          </li> -->
          <li class="middle_chart">
            <div class="machine_usage">
              <div v-for="item in vmpmData" class="machine_module">
                <img src="../assets/daping/wlj.png" :alt="item.imgs" v-if="item.imgs=='物理机'">
                <img src="../assets/daping/xnj.png" :alt="item.imgs" v-if="item.imgs=='虚拟机'">
                <div class="machine_quantity">
                  <p style="padding-bottom: 20px" v-if="item.imgs=='物理机'">物理机总数 : <span style="font-size:26px;color:#ff9d00">{{item.pmTotl}}</span></p>
                  <p style="padding-bottom: 20px" v-if="item.imgs=='虚拟机'">可用虚拟机 : <span style="font-size:26px;color:#308dff;margin-right:20px">{{item.pmTotl}}</span>回收站 : <span style="font-size:26px;color:#308dff">{{item.vm_deleted_count}}</span></p>
                  <p v-if="item.imgs=='物理机'"><span style="margin-right:10%"><Icon style="color:#ff9d00" type="ios-radio-button-on" />在线数量 : {{item.pnON}}</span><span><Icon style="color:#ff9d00"  type="md-radio-button-off" />关机数量 : {{item.pmOFF}}</span></p>
                  <p v-if="item.imgs=='虚拟机'"><span style="margin-right:10%"><Icon style="color:#308dff" type="ios-radio-button-on" />运行数量 : {{item.pnON}}</span><span><Icon style="color:#308dff"  type="md-radio-button-off" />关机数量 : {{item.pmOFF}}</span></p>
                </div>
              </div>
            </div>
            <div class="dashboard_chart">
              <div id="screen_sys_dashboard" :style="{ width: '100%', height: '100%', margin: ' 0 auto' }"></div>
            </div>
          </li>
          <li class="physical_machine_onitor">
            <div class="module_title">
              <span>物理机监控</span>
            </div>
            <div style="padding:0 10px">
              <Table :columns="monitorColu" :data="monitordata" :disabled-hover="true"></Table>
            </div>
          </li>
        </ul>
      </div>
       <!-- 内容右侧区域 -->
      <div class="center_right">
        <ul>
          <li>
            <div class="module_title">
              <span @click="alarmData('datas')">告警信息</span>
            </div>
            <div class="chart_monitor">
              <div v-if="gaojingTotal!==0" id="alarmChart" :style="{ width: '100%', height: '100%', margin: ' 0 auto' }"></div>
              <div class="alarm_without_data" v-if="gaojingTotal==0">
                <img src="../assets/wushuju.png" alt="">
                <span>当前无告警信息</span>
              </div>
            </div>
          </li>
          <li>
            <div class="module_title">
              <span>存储池带宽监控</span>
            </div>
            <div class="chart_monitor">
              <div id="bandwidthChart" :style="{ width: '100%', height: '100%', margin: ' 0 auto' }"></div>
            </div>
          </li>
          <li>
            <div class="module_title">
              <span>存储池IOPS监控</span>
            </div>
            <div class="chart_monitor">
              <div id="IOPSChart" :style="{ width: '100%', height: '100%', margin: ' 0 auto' }"></div>
            </div>
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>
<script>
import {
  homepageData,  // 大屏 基本数据
  largeScreenBandwidth,  // 大屏 带宽
  largeScreenIOPS,  // 大屏 IOPS
  largeScreenMachineNumber,  // 大屏 机器数量
} from '@/api/other';
import {alarmDataQuery} from '@/api/log';
import {
  hostListQuery, // 主机列表 查询
} from '@/api/physics';
export default {
  data() {
    return {
      // 大屏头部区域
      THElogoTitle:"CRH",
      THElogs:"THE HCI超融合系统",
      currentDate:"未获取日期",
      currentTime:"未获取时间",
      currentWeek:"星期*",
      times:null,

      // 大屏内容 左侧区域
      vcpuUsageTotal:0, // cpu使用
      vcpuTotal:0, // cpu总
      vcpuRate:0, // cpu率
      memoryUsageTotal:0, // 内存使用
      memoryTotal:0, // 内存总
      memoryRate:0, // 内存率
      storageUsageTotal:0, // 存储使用
      storageTotal:0, // 存储总
      storageRate:0, // 存储率

      ringSize: 200,// 环大小
      strokeWidth: 9, // 环宽
      trailWidth: 10, // 环背景宽
      trailbg: "#000", // 环背景色
      CPUbj:"#ff9c01",
      NCbj:"#308dfe",
      CCbj:"#ff9c01",

      // 内容居中区域
      // 物理机虚拟机数量统计
      vmpmData:[
        { imgs: "物理机",pmTotl:"0",pnON:"0",pmOFF:"0"},
        { imgs: "虚拟机",pmTotl:"0",pnON:"0",pmOFF:"0",vm_deleted_count:"0"}
      ],

      // 物理机监控
      // 表格列
      monitorColu:[
        { title: 'IP', key: 'host_ip',minWidth:30 },
        { title: '主机名', key: 'hypervisor_hostname',align: 'center' },
        { title: '运行时间', key: 'time',align: 'center' },
        { title: 'CPU分配比', key: 'cpu',align: 'center',
          render: (h, params) => {
            let cpu_lv = (params.row.vcpus_used/(params.row.vcpus*4)*100).toFixed(1)
            return h(
              "Progress",
              {
                props: {
                  type:"Progress",
                  size:"small",
                  strokeColor:cpu_lv>70?["#f45d3f","#f45d3f"]:["#2ebe76","#2ebe76"],
                  percent: parseFloat(
                    cpu_lv >100 ? 99: cpu_lv
                  ),
                },
              },
              cpu_lv == null ? "0%" : cpu_lv+"%"
            );
          },
        },
        { title: '内存分配比', key: 'nomr',align: 'center',
          render: (h, params) => {
            let memory_lv = (params.row.memory_mb_used/params.row.memory_mb*100).toFixed(1)
            return h(
              "Progress",
              {
                props: {
                  strokeColor:memory_lv>70?["#f45d3f","#f45d3f"]:["#2ebe76","#2ebe76"],
                  percent: parseFloat(
                    memory_lv > 100 ? 99 : memory_lv
                  ),
                },
              },
              memory_lv == null ? 0 : memory_lv+"%"
            );
          },
        },
        { title: '下载带宽', key: 'download_bandwidth',align: 'center',
          render: (h, params) => {
            return h("span",params.row.download_bandwidth+" MB")
          }
        },
        { title: '上传带宽', key: 'upload_bandwidth',align: 'center',
          render: (h, params) => {
            return h("span",params.row.upload_bandwidth+" MB")
          }
        },
      ],
      monitordata:[],
      // 告警信息
      gaojingTotal:0,
      // 存储池带宽图表
      bandwidthSJ:[],
      bandwidthDu:[],
      bandwidthXie:[],
      // 存储池IOPS图表
      IOPSsj:[],
      IOPSdu:[],
      IOPSxie:[],

      timesList:null,
      monitorList:null,
      timerId:null,
    }
  },
  created() {
  },
  mounted() {
    this.THElogoTitle = window.gurl.THElogoTitle
    if(this.THElogoTitle=='CRH'){
      this.THElogs="THE HCI超融合系统"      
    }else {
      this.THElogs="THE Cloud云管理平台"
    }
    this.timesList = setInterval(() => {
      this.utilization() // 使用率
      this.serverStatistic() // 机器数量统计
      this.wlTable() // 物理机监控数据
      this.giveANalarmGET() // 告警信息获取
    },300000)
    this.timesFun() // 时间
    this.utilization() // 使用率
    this.serverStatistic() // 机器数量统计
    this.wlTable() // 物理机监控数据
    this.giveANalarmGET() // 告警信息获取
    this.bandwidthData() // 存储池带宽
    this.IOPSData() // 存储池IOPS
    this.monitorDatas()
    this.monitorList = setInterval(() => {
      this.monitorDatas()
    },60000)
  },
  methods: {
    // 时间
    timesFun(){
      this.times = setInterval(() => {
        let time = new Date()
        let year = time.getFullYear()
        let month = time.getMonth() + 1
        let date = time.getDate()
        let hours = time.getHours()
        let minute = time.getMinutes()
        let second = time.getSeconds()
        if (month < 10) { month = '0' + month }
        if (date < 10) { date = '0' + date }
        if (hours < 10) { hours = '0' + hours }
        if (minute < 10) { minute = '0' + minute }
        if (second < 10) { second = '0' + second }
        this.currentDate = year+"年"+month+"月"+date+"日"
        this.currentTime = hours+":"+minute+":"+second
        switch (time.getDay()) {
          case 0 :
            this.currentWeek = "星期日";
          break;
          case 1 :
            this.currentWeek = "星期一";
          break;
          case 2 :
            this.currentWeek = "星期二";
          break;
          case 3 :
            this.currentWeek = "星期三";
          break;
          case 4 :
            this.currentWeek = "星期四";
          break;
          case 5 :
            this.currentWeek = "星期五";
          break;
          case 6 :
            this.currentWeek = "星期六";
          break;
        }
      },1000)
    },
    // 使用率
    utilization() {
      homepageData()
      .then(em=>{
        this.systemCART((em.data.sys_status*100).toFixed(0))
        // CPU分配比
        this.vcpuUsageTotal = em.data.vcpus_used // 已用
        this.vcpuTotal = em.data.vcpus*4  // 总数
        this.vcpuRate = em.data.vcpus_used/(em.data.vcpus*4)* 100 // 百分比
        // 内存分配比
        this.memoryUsageTotal = em.data.memory_mb_used/1024 // 已用
        this.memoryTotal = em.data.memory_mb/1024 // 总数
        this.memoryRate = em.data.memory_mb_used/em.data.memory_mb* 100 // 百分比

        this.storageUsageTotal= em.data.ceph_pool_stored // 已用
        this.storageTotal = em.data.ceph_pool_max // 总数
        this.storageRate = em.data.ceph_pool_stored/em.data.ceph_pool_max* 100 // 百分比
      })
    },
    // 监控图表数据
    monitorDatas(){
      largeScreenBandwidth({})
      .then(iop=>{
        this.bandwidthSJ = iop.data.t.slice(-10)
        this.bandwidthDu = iop.data.d.slice(-10)
        this.bandwidthXie = iop.data.u.slice(-10)
        this.setEchartsbandwidth()
      })
      largeScreenIOPS({})
      .then(bd=>{
        this.IOPSsj = bd.data.t.slice(-10)
        this.IOPSdu = bd.data.d.slice(-10)
        this.IOPSxie = bd.data.u.slice(-10)
        this.setEchartsIOPS()
      })
    },
    // 机器数量统计
    serverStatistic(){
      largeScreenMachineNumber()
      .then(callback=>{
        this.vmpmData[0].pmTotl = callback.data.hypervisors
        this.vmpmData[0].pnON = callback.data.hp_online
        this.vmpmData[0].pmOFF = callback.data.hp_offline
        this.vmpmData[1].vm_deleted_count = callback.data.vm_deleted_count
        this.vmpmData[1].pmTotl = callback.data.vms-callback.data.vm_deleted_count
        this.vmpmData[1].pnON = callback.data.vm_online
        this.vmpmData[1].pmOFF = callback.data.vm_offline-callback.data.vm_deleted_count
      })
    },
    // 系统表现
    systemCART(datas){
      let screen_sys_dashboard = this.$echarts.init(document.getElementById("screen_sys_dashboard"));
      let angle = 0; //角度，用来做简单的动画效果的
      // let datas =80;
      let jibie = "";
      let jibiecolor = "";
      if(datas<=30){
        jibie = "优"
        jibiecolor = "#2ebe76";
      }else if(datas>30 && datas<=70){
        jibie = "良"
        jibiecolor = "#fcc65c";
      } else if(datas>70){
        jibie = "差"
        jibiecolor = "#f45d3f";
      }
      let value =datas;
      clearInterval(this.timerId);
      let option = new Object()
      option = {
        backgroundColor: "#000E1A00",
        title: {
          // text: "{a|" + value + "}{c|%}",
          text: "{a|" + jibie + "}\n{c|系统状态}",
          x: "center",
          y: "center",
          textStyle: {
            rich: {
              a: {
                fontSize: 48,
                lineHeight:60,
                color: jibiecolor,
              },

              c: {
                fontSize: 20,
                color: "#ffffff",
                // padding: [5,0]
              },
            },
          },
        },

        series: [
          // 紫色
          {
            name: "ring5",
            type: "custom",
            coordinateSystem: "none",
            renderItem: function (params, api) {
              return {
                type: "arc",
                shape: {
                  cx: api.getWidth() / 2,
                  cy: api.getHeight() / 2,
                  r: (Math.min(api.getWidth(), api.getHeight()) / 2) * 0.6,
                  startAngle: ((0 + angle) * Math.PI) / 180,
                  endAngle: ((90 + angle) * Math.PI) / 180,
                },
                style: {
                  stroke: "#8383FA",
                  fill: "transparent",
                  lineWidth: 1.5,
                },
                silent: true,
              };
            },
            data: [0],
          },
          {
            name: "ring5", //紫点
            type: "custom",
            coordinateSystem: "none",
            renderItem: function (params, api) {
              let x0 = api.getWidth() / 2;
              let y0 = api.getHeight() / 2;
              let r = (Math.min(api.getWidth(), api.getHeight()) / 2) * 0.6;
              let point = getCirlPoint(x0, y0, r, 90 + angle);
              return {
                type: "circle",
                shape: {
                  cx: point.x,
                  cy: point.y,
                  r: 4,
                },
                style: {
                  stroke: "#8450F9", //绿
                  fill: "#8450F9",
                },
                silent: true,
              };
            },
            data: [0],
          },
          // 蓝色

          {
            name: "ring5",
            type: "custom",
            coordinateSystem: "none",
            renderItem: function (params, api) {
              return {
                type: "arc",
                shape: {
                  cx: api.getWidth() / 2,
                  cy: api.getHeight() / 2,
                  r: (Math.min(api.getWidth(), api.getHeight()) / 2) * 0.6,
                  startAngle: ((180 + angle) * Math.PI) / 180,
                  endAngle: ((270 + angle) * Math.PI) / 180,
                },
                style: {
                  stroke: "#4386FA",
                  fill: "transparent",
                  lineWidth: 1.5,
                },
                silent: true,
              };
            },
            data: [0],
          },
          {
            name: "ring5", // 蓝色
            type: "custom",
            coordinateSystem: "none",
            renderItem: function (params, api) {
              let x0 = api.getWidth() / 2;
              let y0 = api.getHeight() / 2;
              let r = (Math.min(api.getWidth(), api.getHeight()) / 2) * 0.6;
              let point = getCirlPoint(x0, y0, r, 180 + angle);
              return {
                type: "circle",
                shape: {
                  cx: point.x,
                  cy: point.y,
                  r: 4,
                },
                style: {
                  stroke: "#4386FA", //绿
                  fill: "#4386FA",
                },
                silent: true,
              };
            },
            data: [0],
          },

          {
            name: "ring5",
            type: "custom",
            coordinateSystem: "none",
            renderItem: function (params, api) {
              return {
                type: "arc",
                shape: {
                  cx: api.getWidth() / 2,
                  cy: api.getHeight() / 2,
                  r: (Math.min(api.getWidth(), api.getHeight()) / 2) * 0.65,
                  startAngle: ((270 + -angle) * Math.PI) / 180,
                  endAngle: ((40 + -angle) * Math.PI) / 180,
                },
                style: {
                  stroke: "#0CD3DB",
                  fill: "transparent",
                  lineWidth: 1.5,
                },
                silent: true,
              };
            },
            data: [0],
          },
          // 橘色

          {
            name: "ring5",
            type: "custom",
            coordinateSystem: "none",
            renderItem: function (params, api) {
              return {
                type: "arc",
                shape: {
                  cx: api.getWidth() / 2,
                  cy: api.getHeight() / 2,
                  r: (Math.min(api.getWidth(), api.getHeight()) / 2) * 0.65,
                  startAngle: ((90 + -angle) * Math.PI) / 180,
                  endAngle: ((220 + -angle) * Math.PI) / 180,
                },
                style: {
                  stroke: "#FF8E89",
                  fill: "transparent",
                  lineWidth: 1.5,
                },
                silent: true,
              };
            },
            data: [0],
          },
          {
            name: "ring5",
            type: "custom",
            coordinateSystem: "none",
            renderItem: function (params, api) {
              let x0 = api.getWidth() / 2;
              let y0 = api.getHeight() / 2;
              let r = (Math.min(api.getWidth(), api.getHeight()) / 2) * 0.65;
              let point = getCirlPoint(x0, y0, r, 90 + -angle);
              return {
                type: "circle",
                shape: {
                  cx: point.x,
                  cy: point.y,
                  r: 4,
                },
                style: {
                  stroke: "#FF8E89", //粉
                  fill: "#FF8E89",
                },
                silent: true,
              };
            },
            data: [0],
          },
          {
            name: "ring5", //绿点
            type: "custom",
            coordinateSystem: "none",
            renderItem: function (params, api) {
              let x0 = api.getWidth() / 2;
              let y0 = api.getHeight() / 2;
              let r = (Math.min(api.getWidth(), api.getHeight()) / 2) * 0.65;
              let point = getCirlPoint(x0, y0, r, 270 + -angle);
              return {
                type: "circle",
                shape: {
                  cx: point.x,
                  cy: point.y,
                  r: 4,
                },
                style: {
                  stroke: "#0CD3DB", //绿
                  fill: "#0CD3DB",
                },
                silent: true,
              };
            },
            data: [0],
          },
          {
            name: "吃猪肉频率",
            type: "pie",
            radius: ["52%", "40%"],
            silent: true,
            clockwise: true,
            startAngle: 90,
            z: 0,
            zlevel: 0,
            label: {
              normal: {
                position: "center",
              },
            },
            data: [
              {
                value: value,
                name: "",
                itemStyle: {
                  normal: {
                    color: {
                      // 完成的圆环的颜色
                      colorStops: [
                        {
                          offset: 0,
                          color: "#A098FC", // 0% 处的颜色
                        },
                        {
                          offset: 0.3,
                          color: "#4386FA", // 0% 处的颜色
                        },
                        {
                          offset: 0.6,
                          color: "#4FADFD", // 0% 处的颜色
                        },
                        {
                          offset: 0.8,
                          color: "#0CD3DB", // 100% 处的颜色
                        },
                        {
                          offset: 1,
                          color: "#646CF9", // 100% 处的颜色
                        },
                      ],
                    },
                  },
                },
              },
              {
                value: 100 - value,
                name: "",
                label: {
                  normal: {
                    show: false,
                  },
                },
                itemStyle: {
                  normal: {
                    color: "#173164",
                  },
                },
              },
            ],
          },
          {
            name: "吃猪肉频率",
            type: "pie",
            radius: ["32%", "35%"],
            silent: true,
            clockwise: true,
            startAngle: 270,
            z: 0,
            zlevel: 0,
            label: {
              normal: {
                position: "center",
              },
            },
            data: [
              {
                value: value,
                name: "",
                itemStyle: {
                  normal: {
                    color: {
                      // 完成的圆环的颜色
                      colorStops: [
                        {
                          offset: 0,
                          color: "#00EDF3", // 0% 处的颜色
                        },
                        {
                          offset: 1,
                          color: "red", // 100% 处的颜色
                        },
                      ],
                    },
                  },
                },
              },
              {
                value: 100 - value,
                name: "",
                label: {
                  normal: {
                    show: false,
                  },
                },
                itemStyle: {
                  normal: {
                    color: "#173164",
                  },
                },
              },
            ],
          },
        ],
      };
      //获取圆上面某点的坐标(x0,y0表示坐标，r半径，angle角度)
      function getCirlPoint(x0, y0, r, angle) {
        let x1 = x0 + r * Math.cos((angle * Math.PI) / 180);
        let y1 = y0 + r * Math.sin((angle * Math.PI) / 180);
        return {
          x: x1,
          y: y1,
        };
      }

      function draw() {
        if(angle>360) {
          angle=0
        }
        angle = angle + 3;
        screen_sys_dashboard.setOption(option, true);
      }
      
      this.timerId = setInterval(function () {
        //用setInterval做动画感觉有问题
        draw();
      }, 100);
    },
    systemCART1(){
      let screen_sys_dashboard = this.$echarts.init(document.getElementById("screen_sys_dashboard"));
      const dataArr = 80;
      const dataX = 100;
      const height1 = { value: 80 };
      screen_sys_dashboard.setOption({
        backgroundColor: "#031f4500",
        /** 标题*/
        title: [
          {
            text:
              "{val|" + dataArr + "}\n{name|" + "系统状态" + "}",
            bottom: "32%",
            left: "center",
            textStyle: {
              rich: {
                val: {
                  fontSize: 24,
                  color: "#fff",
                  padding: [10, 0],
                },
                name: {
                  fontSize: 18,
                  color: "#E5E5E585",
                },
              },
            },
            triggerEvent: true,
          },
        ],

        /** 关闭必图例*/
        legend: {
          show: false,
        },
        series: [
          {
            name: "最外部进度条",
            type: "gauge",
            radius: "93%",
            splitNumber: 15,
            axisLine: {
              lineStyle: {
                color: [
                  [
                    1,
                    {
                      type: "linear",
                      x: 0,
                      y: 1,
                      x2: 0,
                      y2: 0,
                      colorStops: [
                        {
                          offset: 0,
                          color: "#03234785", // 0% 处的颜色
                        },
                        {
                          offset: 0.5,
                          color: "#0B8BAA85", // 100% 处的颜色
                        },
                        {
                          offset: 1,
                          color: "#03FCF385", // 100% 处的颜色
                        },
                      ],
                      global: false, // 缺省为 false
                    },
                  ],
                ],
                width: 12,
              },
            },
            axisLabel: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            splitLine: {
              show: true,
              length: 30,
              lineStyle: {
                color: "#031f4585",
                width: 3,
              },
            },
            itemStyle: {
              show: false,
            },
            detail: {
              show: false,
            },
            title: {
              // 标题
              show: false,
            },
            data: [
              {
                name: "title",
                value: dataArr,
              },
            ],
            pointer: {
              show: false,
            },
            animationDuration: 4000,
          },
          {
            name: "外二红线",
            type: "gauge",
            radius: "84.5%",
            axisLine: {
              lineStyle: {
                color: [
                  [dataArr / dataX, "#ccc85"],
                  [1, "#031f4585"],
                ],
                width: 4,
              },
            },
            axisLabel: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            splitLine: {
              show: false,
            },
            itemStyle: {
              show: false,
            },
            detail: {
              show: false,
            },
            title: {
              // 标题
              show: false,
            },
            data: [
              {
                name: "title",
                value: dataArr,
              },
            ],
            pointer: {
              show: false,
            },
            animationDuration: 4000,
          },
          {
            name: "刻度尺",
            type: "gauge",
            radius: "81%",
            splitNumber: 10, // 刻度数量
            min: 0, // 最小刻度
            max: dataX, // 最大刻度
            // 仪表盘轴线相关配置
            axisLine: {
              lineStyle: {
                color: [
                  [
                    1,
                    {
                      type: "radial",
                      x: 0.5,
                      y: 0.6,
                      r: 0.6,
                      colorStops: [
                        {
                          offset: 0.85,
                          color: "#031F4685", // 0% 处的颜色
                        },
                        {
                          offset: 0.93,
                          color: "#08698985", // 100% 处的颜色
                        },
                        {
                          offset: 1,
                          color: "#12D7EF85", // 100% 处的颜色
                        },
                      ],
                    },
                  ],
                ],
                width: 500,
              },
            },
            /** 分隔线样式*/
            splitLine: {
              show: true,
              length: 14,
              lineStyle: {
                width: 3,
                color: "#12E5FE85", // 用颜色渐变函数不起作用
              },
            },
            /** 刻度线*/
            axisTick: {
              show: true,
              splitNumber: 20,
              lineStyle: {
                color: "#12E5FE85", // 用颜色渐变函数不起作用
                width: 1,
              },
              length: 5,
            },
            /** 刻度标签*/
            axisLabel: {
              distance: 2,
              color: "#CEF3FE85",
            },
            detail: {
              show: false,
            },
            animationDuration: 4000,
          },
          {
            name: "渐变进度",
            type: "gauge",
            radius: "80%",
            splitNumber: 15,
            axisLine: {
              lineStyle: {
                color: [
                  [
                    dataArr / dataX,
                    {
                      type: "linear",
                      x: 0,
                      y: 1,
                      x2: 0,
                      y2: 0,
                      colorStops: [
                        {
                          offset: 0,
                          color: "rgba(60,207,223,0)", // 0% 处的颜色
                        },
                        {
                          offset: 0.9,
                          color: "rgba(60,207,223,0.5)", // 100% 处的颜色
                        },
                        {
                          offset: 1,
                          color: "rgba(60,207,223,0.9)", // 100% 处的颜色
                        },
                      ],
                      global: false, // 缺省为 false
                    },
                  ],
                ],
                width: 30,
              },
            },
            axisLabel: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            splitLine: {
              show: false,
            },
            itemStyle: {
              show: false,
            },
            detail: {
              show: false,
            },
            title: {
              // 标题
              show: false,
            },
            data: [
              {
                name: "title",
                value: dataArr,
              },
            ],
            pointer: {
              show: false,
            },
            animationDuration: 4000,
          },
          {
            name: "内层带指针",
            type: "gauge",
            radius: "61%",
            splitNumber: 10, // 刻度数量
            min: 0, // 最小刻度
            max: dataX, // 最大刻度
            // 仪表盘轴线相关配置
            axisLine: {
              lineStyle: {
                color: [
                  [
                    1,
                    {
                      type: "radial",
                      x: 0.5,
                      y: 0.59,
                      r: 0.6,
                      colorStops: [
                        {
                          offset: 0.72,
                          color: "#03204685",
                        },
                        {
                          offset: 0.94,
                          color: "#08698985",
                        },
                        {
                          offset: 0.98,
                          color: "#0FAFCB85",
                        },
                        {
                          offset: 1,
                          color: "#0EA4C185",
                        },
                      ],
                    },
                  ],
                ],
                width: 1000,
              },
            },
            /** 分隔线样式*/
            splitLine: {
              show: false,
            },
            /** 刻度线*/
            axisTick: {
              show: false,
            },
            /** 刻度标签*/
            axisLabel: {
              show: false,
            },
            /** 仪表盘指针*/
            pointer: {
              show: true,
              length: "95%",
              width: 5, // 指针粗细
            },
            /** 仪表盘指针样式*/
            itemStyle: {
              color: "#12E5FF",
            },
            data: [
              {
                value: dataArr,
              },
            ],
            detail: {
              show: false,
            },
          },
        ],
        graphic: {
          elements: [
            {
              type: "line",
              z: 4,
              style: {
                fill: "#075173",
                stroke: "#075173",
                lineWidth: 2,
                shadowBlur: 15,
                shadowOffsetX: 0,
                shadowOffsetY: -4,
                shadowColor: "#13E6FF",
              },
              shape: {
                x1: height1.value * 0.57,
                y1: 0,
                x2: 0,
                y2: 0,
              },
              left: "center",
              bottom: "21%",
              silent: true,
            },
            {
              type: "line",
              z: 4,
              style: {
                fill: "#075173",
                stroke: "#075173",
                lineWidth: 2,
                shadowBlur: 15,
                shadowOffsetX: 0,
                shadowOffsetY: -4,
                shadowColor: "#13E6FF",
              },
              shape: {
                x1: height1.value * 0.43,
                y1: 0,
                x2: 0,
                y2: 0,
              },
              left: "center",
              bottom: "28.5%",
              silent: true,
            },
          ],
        },
      });
    },
    // 告警信息获取
    giveANalarmGET(){
      let datas = new Array()
      alarmDataQuery()
      .then(callback=>{
        if(callback.data.length!==0) {
          this.gaojingTotal = callback.data.length
          let totalData=callback.data.length
          let bengkui =0
          let jinggao =0
          let tishi =0
          callback.data.forEach(item=>{
            if(item.severity=="critical"){
              bengkui=bengkui+1
            }else if(item.severity=="warning"){
              jinggao=jinggao+1
            }else if(item.severity=="info"){
              tishi=tishi+1
            }
          })
          datas.push((totalData/100).toFixed(2))
          datas.push((tishi/100).toFixed(2))
          datas.push((jinggao/100).toFixed(2))
          datas.push((bengkui/100).toFixed(2))
          setTimeout(() => {
            this.alarmData(datas)
          }, 1000);
        }
      })
    },
    // 告警封装
    getData(percent) {
      return [
        {
          value: percent,
          name: percent,
        },
        {
          value: 1 - percent,
          itemStyle: {
            normal: {
              color: "transparent",
            },
          },
        },
      ];
    },
    // 告警图表
    alarmData(das){
      let datas= das
      // let datas= [0.5,0.11, 0.26,0.07]

      let alarmChart = this.$echarts.init(document.getElementById("alarmChart"));
      let placeHolderStyle = {
        normal: {
          label: {
            show: false,
          },
          labelLine: {
            show: false,
          },
        },
      };
      let alarmTitle = ['通知：'+parseInt(datas[0]*100),'提示：'+parseInt(datas[1]*100),'告警：'+parseInt(datas[2]*100),'严重：'+parseInt(datas[3]*100)]
      alarmChart.setOption({
        // backgroundColor: "#152147",
        color:["#2c59b0", "#308dff", "#ff9d00", "#f57c38"],
        tooltip: {
          trigger: "item",
          formatter: function (params, ticket, callback) {
            return params.seriesName
          },
        },
        legend: {
          top: "1%",
          left: "5%",
          itemHeight: 18,
          data: alarmTitle,
          textStyle: {
            color: "#fff",
          },
          selectedMode: true,
          orient: "vertical",
        },
        series: [
          {
            name: alarmTitle[0],
            type: "pie",
            clockWise: true, //顺时加载
            hoverAnimation: false, //鼠标移入变大
            radius: [90, 100],
            itemStyle: placeHolderStyle,
            label: {
              normal: {
                show: false,
              },
            },
            data: this.getData(datas[0]),
          },
          {
            name: alarmTitle[1],
            type: "pie",
            clockWise: true, //顺时加载
            hoverAnimation: false, //鼠标移入变大
            radius: [70, 80],
            itemStyle: placeHolderStyle,
            data: this.getData(datas[1]),
          },
          {
            name: alarmTitle[2],
            type: "pie",
            clockWise: true, //顺时加载
            hoverAnimation: false, //鼠标移入变大
            radius: [50, 60],
            itemStyle: placeHolderStyle,
            data: this.getData(datas[2]),
          },
          {
            name: alarmTitle[3],
            type: "pie",
            clockWise: true, //顺时加载
            hoverAnimation: false, //鼠标移入变大
            radius: [30, 40],
            itemStyle: placeHolderStyle,
            data: this.getData(datas[3]),
          },
        ],
      })
    },
    wlTable(){
      hostListQuery()
      .then(callback=>{
        this.monitordata = callback.data
      })
    },
    // 存储池带宽图表
    bandwidthData(){
      this.bandwidthChart = this.$echarts.init(document.getElementById("bandwidthChart"));
      this.setEchartsbandwidth()
    },
    setEchartsbandwidth(){
      this.bandwidthChart.setOption({
        // backgroundColor: "#05224d",
        tooltip: {},
        grid: {
          top: "15%",
          left: "5%",
          right: "5%",
          bottom: "8%",
          containLabel: true,
        },
        legend: {
          itemGap: 50,
          top: "1%",
          data: ["读（MB）", "写（MB）"],
          textStyle: {
            color: "#f9f9f9",
            borderColor: "#fff",
          },
        },
        xAxis: [
          {
            type: "category",
            boundaryGap: true,
            axisLine: {
              //坐标轴轴线相关设置。数学上的x轴
              show: true,
              lineStyle: {
                color: "#f9f9f9",
              },
            },
            axisLabel: {
              //坐标轴刻度标签的相关设置
              textStyle: {
                color: "#d1e6eb",
                margin: 15,
              },
            },
            axisTick: {
              show: false,
            },
            data: this.bandwidthSJ,
          },
        ],
        yAxis: [
          {
            type: "value",
            min: 0,
            // max: 140,
            splitNumber: 7,
            splitLine: {
              show: true,
              lineStyle: {
                color: "#0a3256",
              },
            },
            axisLine: {
              show: false,
            },
            axisLabel: {
              margin: 20,
              textStyle: {
                color: "#d1e6eb",
              },
            },
            axisTick: {
              show: false,
            },
          },
        ],
        series: [
          {
            name: "读（MB）",
            type: "line",
            // smooth: true, //是否平滑曲线显示
            // 			symbol:'circle',  // 默认是空心圆（中间是白色的），改成实心圆
            showAllSymbol: true,
            symbol: "emptyCircle",
            symbolSize: 6,
            lineStyle: {
              normal: {
                color: "#ff9d00", // 线条颜色
              },
              borderColor: "#ff9d00",
            },
            label: {
              show: true,
              position: "top",
              textStyle: {
                color: "#fff",
              },
            },
            itemStyle: {
              normal: {
                color: "#ff9d00",
              },
            },
            tooltip: {
              show: false,
            },
            areaStyle: {
              //区域填充样式
              normal: {
                //线性渐变，前4个参数分别是x0,y0,x2,y2(范围0~1);相当于图形包围盒中的百分比。如果最后一个参数是‘true’，则该四个值是绝对像素位置。
                color: new this.$echarts.graphic.LinearGradient(
                  0,
                  0,
                  0,
                  1,
                  [
                    {
                      offset: 0,
                      color: "#ff9d00",
                    },
                    {
                      offset: 1,
                      color: "rgba(0,0,0, 0)",
                    },
                  ],
                  false
                ),
                shadowColor: "rgba(53,142,215, 0.9)", //阴影颜色
                shadowBlur: 20, //shadowBlur设图形阴影的模糊大小。配合shadowColor,shadowOffsetX/Y, 设置图形的阴影效果。
              },
            },
            data: this.bandwidthDu,
          },
          {
            name: "写（MB）",
            type: "line",
            // smooth: true, //是否平滑曲线显示
            // 			symbol:'circle',  // 默认是空心圆（中间是白色的），改成实心圆
            showAllSymbol: true,
            symbol: "emptyCircle",
            symbolSize: 6,
            lineStyle: {
              normal: {
                color: "#2c59b0", // 线条颜色
              },
              borderColor: "#f0f",
            },
            label: {
              show: true,
              position: "top",
              textStyle: {
                color: "#fff",
              },
            },
            itemStyle: {
              normal: {
                color: "#2c59b0",
              },
            },
            tooltip: {
              show: false,
            },
            areaStyle: {
              //区域填充样式
              normal: {
                //线性渐变，前4个参数分别是x0,y0,x2,y2(范围0~1);相当于图形包围盒中的百分比。如果最后一个参数是‘true’，则该四个值是绝对像素位置。
                color: new this.$echarts.graphic.LinearGradient(
                  0,
                  0,
                  0,
                  1,
                  [
                    {
                      offset: 0,
                      color: "#2c59b0",
                    },
                    {
                      offset: 1,
                      color: "rgba(0,0,0, 0)",
                    },
                  ],
                  false
                ),
                shadowColor: "rgba(53,142,215, 0.9)", //阴影颜色
                shadowBlur: 20, //shadowBlur设图形阴影的模糊大小。配合shadowColor,shadowOffsetX/Y, 设置图形的阴影效果。
              },
            },
            data: this.bandwidthXie,
          },
          
        ],
      })
    },
    // 存储池IOPS图表
    IOPSData(){
      this.IOPSChart = this.$echarts.init(document.getElementById("IOPSChart"));
      this.setEchartsIOPS()
    },
    setEchartsIOPS(){
      this.IOPSChart.setOption({
        
        // backgroundColor: "#05224d",
        tooltip: {},
        grid: {
          top: "15%",
          left: "5%",
          right: "5%",
          bottom: "8%",
          containLabel: true,
        },
        legend: {
          itemGap: 50,
          top: "1%",
          data: ["读", "写"],
          textStyle: {
            color: "#f9f9f9",
            borderColor: "#fff",
          },
        },
        xAxis: [
          {
            type: "category",
            boundaryGap: true,
            axisLine: {
              //坐标轴轴线相关设置。数学上的x轴
              show: true,
              lineStyle: {
                color: "#f9f9f9",
              },
            },
            axisLabel: {
              //坐标轴刻度标签的相关设置
              textStyle: {
                color: "#d1e6eb",
                margin: 15,
              },
            },
            axisTick: {
              show: false,
            },
            data: this.IOPSsj,
          },
        ],
        yAxis: [
          {
            type: "value",
            min: 0,
            // max: 140,
            splitNumber: 7,
            splitLine: {
              show: true,
              lineStyle: {
                color: "#0a3256",
              },
            },
            axisLine: {
              show: false,
            },
            axisLabel: {
              margin: 20,
              textStyle: {
                color: "#d1e6eb",
              },
            },
            axisTick: {
              show: false,
            },
          },
        ],
        series: [
          {
            name: "读",
            type: "line",
            // smooth: true, //是否平滑曲线显示
            // 			symbol:'circle',  // 默认是空心圆（中间是白色的），改成实心圆
            showAllSymbol: true,
            symbol: "emptyCircle",
            symbolSize: 6,
            lineStyle: {
              normal: {
                color: "#ff9d00", // 线条颜色
              },
              borderColor: "#ff9d00",
            },
            label: {
              show: true,
              position: "top",
              textStyle: {
                color: "#fff",
              },
            },
            itemStyle: {
              normal: {
                color: "#ff9d00",
              },
            },
            tooltip: {
              show: false,
            },
            areaStyle: {
              //区域填充样式
              normal: {
                //线性渐变，前4个参数分别是x0,y0,x2,y2(范围0~1);相当于图形包围盒中的百分比。如果最后一个参数是‘true’，则该四个值是绝对像素位置。
                color: new this.$echarts.graphic.LinearGradient(
                  0,
                  0,
                  0,
                  1,
                  [
                    {
                      offset: 0,
                      color: "#ff9d00",
                    },
                    {
                      offset: 1,
                      color: "rgba(0,0,0, 0)",
                    },
                  ],
                  false
                ),
                shadowColor: "rgba(53,142,215, 0.9)", //阴影颜色
                shadowBlur: 20, //shadowBlur设图形阴影的模糊大小。配合shadowColor,shadowOffsetX/Y, 设置图形的阴影效果。
              },
            },
            data: this.IOPSdu,
          },
          {
            name: "写",
            type: "line",
            // smooth: true, //是否平滑曲线显示
            // 			symbol:'circle',  // 默认是空心圆（中间是白色的），改成实心圆
            showAllSymbol: true,
            symbol: "emptyCircle",
            symbolSize: 6,
            lineStyle: {
              normal: {
                color: "#2c59b0", // 线条颜色
              },
              borderColor: "#f0f",
            },
            label: {
              show: true,
              position: "top",
              textStyle: {
                color: "#fff",
              },
            },
            itemStyle: {
              normal: {
                color: "#2c59b0",
              },
            },
            tooltip: {
              show: false,
            },
            areaStyle: {
              //区域填充样式
              normal: {
                //线性渐变，前4个参数分别是x0,y0,x2,y2(范围0~1);相当于图形包围盒中的百分比。如果最后一个参数是‘true’，则该四个值是绝对像素位置。
                color: new this.$echarts.graphic.LinearGradient(
                  0,
                  0,
                  0,
                  1,
                  [
                    {
                      offset: 0,
                      color: "#2c59b0",
                    },
                    {
                      offset: 1,
                      color: "rgba(0,0,0, 0)",
                    },
                  ],
                  false
                ),
                shadowColor: "rgba(53,142,215, 0.9)", //阴影颜色
                shadowBlur: 20, //shadowBlur设图形阴影的模糊大小。配合shadowColor,shadowOffsetX/Y, 设置图形的阴影效果。
              },
            },
            data: this.IOPSxie,
          },
          
        ],
      })
    },
    // 字节+单位转换
    byteUnitConversion(type,size) {
      const units = ['GB', 'TB', 'PB'];
      let unitIndex = 0;
      while (size >= 1024 && unitIndex < units.length - 1) {
          size /= 1024;
          unitIndex++;
      }
      if(type=="byte") {
        return Math.floor(size * 100) / 100 
      }else if(type=="unit") {
        return units[unitIndex] 
      }else {
        return Math.floor(size * 100) / 100 + '（' + units[unitIndex]+'）';
      }
    },
  },
  beforeDestroy() {
    clearInterval(this.times)
  },
}
</script>