<template>
<!-- 概览页面 -->
 <div class="generalize-wrap">
   <!-- <div class="title-wrap">状态</div> -->
   <div class="card-wrap mt-10">
     <div class="min-card run-time-wrap">
       <div class="card-titles">系统运行时间</div>
       <div class="bumbers-info-wraps mt-15">
         <span>{{ timeIntervalNumber }}</span>
       </div>
     </div>
     <div class="min-card colony-status-wrap">
       <div class="card-titles">集群状态</div>
       <div class="bumbers-info-wraps mt-15" v-if="datas.health">
         <span :class="datas.health.status && HEALTH_STATES[datas.health.status].class">{{datas.health.status && HEALTH_STATES[datas.health.status].name}}</span>
         <!-- <i class="el-icon-warning-outline ml-10"></i> -->
         <img v-if="datas.health.status" :src="require(`@/assets/imager/${HEALTH_STATES[datas.health.status].img}`)" alt="" class="status-img-wrap">
       </div>
     </div>
     <div class="min-card host-wrap">
       <div class="card-titles">主机节点</div>
       <div class="bumbers-info-wraps mt-15">
         <div>
           {{ datas.hosts }}
           <span class="ft-number-wrap">台</span>
         </div>
       </div>
     </div>
     <div class="min-card client-wrap">
       <div class="card-titles">客户端</div>
       <div class="bumbers-info-wraps mt-5 client-box-wrap">
         <span>{{ totalQuantity }}</span>
         <div class="data-list-wrap">
           <div>ISCSI {{ clientObject.iscsi }}</div>
           <!-- <div>NFC222</div> -->
           <div>客户端{{ clientObject.clientIqn }}</div>
         </div>
       </div>
     </div>
   </div>
   <div class="echarts-wrap">
     <div class="pie-wrap">
       <PieEcharts :datas="datas"></PieEcharts>
     </div>
     <div class="line-wrap">
       <LineEcharts :datas="datas" v-if="showLineEcharts"></LineEcharts>
     </div>
   </div>
 </div>
</template>
<script>
import PieEcharts from './PieEcharts'
import LineEcharts from './LineEcharts'
export default {
  props: {
    tabName: String,
  },
  computed: {
    totalQuantity() {
      const total = this.clientObject.clientIqn + this.clientObject.nfs + this.clientObject.iscsi
      return total
    }
  },
  components: {
    PieEcharts,
    LineEcharts,
  },
  data() {
    return {
      showLineEcharts: true,
      HEALTH_STATES: {
        HEALTH_WARN: { 
          name: '预警',
          class: 'health_warn-wrap',
          img: 'warning.png'
        },
        HEALTH_OK: {
          name:'健康',
          class: 'health_ok-wrap',
          img: 'health.png'
        },
        HEALTH_ERR: {
          name: '错误',
          class: 'health_err-wrap',
          img: 'error.png'
        }
      },
      timeIntervalNumber: '',
      datas: {},
      loading: false,
      total: '',
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        comId: 120
      },
      clientObject: {
        count: 0,
        clientIqn: 0,
        nfs: 0,
        iscsi: 0
      },
      setIntervalFun: null,
    };
  },
  watch: {
    tabName(value) {
      if(value=='gailan'){
        this.showLineEcharts = true
        this.getMinimalFun()
        this.getIscsiTargeFun()
        this.getSystemOnlineTimeFun()
      } else {
        this.showLineEcharts = false
      }
    },
  },
  methods: {
    getIscsiTargeFun() {//获取iscsi数量
      this.$axios.get("/thecephapi/api/iscsi/target").then((res)=> {
        res.map(item => {
            item.clients.forEach( clients => {
              this.clientObject.iscsi += clients.length
            })
        })
      })
    },
    getMinimalFun() { // 获取状态
      this.$axios.get("/thecephapi/api/health/minimal")
      .then((res) => {
        this.datas = res.data
      })
    },
    getSystemOnlineTimeFun() { // 获取首页时间数据
      var timeList = []
      var queryTime = 0
      this.$axios.get(`/apiquery/api/v1/query?query=process_start_time_seconds{job="node"}`).then(res => {
        res.data.data.result.forEach((item , index )=> {
          let str = item.value[1]
          queryTime = item.value[0] +''
          let num = str.substring(0,str.indexOf('.')) * 1
          timeList.push(num)
        });
        let mathMin = Math.min.apply(Math, timeList)
        let setNumber =  (queryTime.substring(0,queryTime.indexOf('.')) - mathMin) * 1000
        this.TimeInterval(setNumber)
      })
    },
    TimeInterval(timestamp) {
        //时间戳转化为天时分秒
        // 总秒数
        var second = Math.floor(timestamp/ 1000);
        // 天数
        var day = Math.floor(second / 3600 / 24);
        // 小时
        var hr = Math.floor(second / 3600 % 24);
        // 分钟
        var min = Math.floor(second / 60 % 60);
        // 秒
        var sec = Math.floor(second % 60);
        // return (day?day + "天":'') + (hr?hr+ "小时":'') + ( min?min + "分钟":'') + sec + "秒";
        this.timeIntervalNumber = (day?day + "天":'') + (hr?hr+ "小时":'') + ( min?min + "分钟":'')
        return 
      },
  },
};
</script>
<style scoped lang='less'>
.generalize-wrap {
    width: 100%;
    height: 100%;
    background: #F7F4F4;
    padding: 8px 18px 83px 18px;
    display: flex;
    flex-direction: column;
    .title-wrap {
      color: #101010;
      font-size: 26px;
      font-weight: 600;
    }
    .card-wrap {
      width: 100%;
      height: 133px;
      display: flex;
      justify-content: space-between;
      .min-card {
        width: 24%;
        height: 100%;
        border-radius: 4px;
        display: flex;
        flex-direction: column;
        padding: 14px 5px 14px 17px;
        .card-titles {
          font-size: 16px;
          font-weight: 600;
        }
        .bumbers-info-wraps {
          font-size: 28px;
          font-weight: 600;
          display: flex;
          align-items: center;
          padding-top: 10px;
          .data-list-wrap {
            font-size: 18px;
            margin-right: 10px;
          }
        }
        &:nth-child(1){
          color: #fff;
        }
        &:nth-child(2){
          color: #fff;
        }
        &:nth-child(3){
          color: #fff;
        }
        &:nth-child(4){
          color:#fff;
        }
      }
    }
    .echarts-wrap {
      flex: 1;
      padding-top: 20px;
      display: flex;
      justify-content: space-between;
      .pie-wrap {
        width: 24%;
        height: 100%;
        margin-right: 18px;
        display: flex;
      }
      .line-wrap {
        flex: 1;
        display: flex;
      }
    }
    .health_warn-wrap {
      color: #ffad5c;
    }
    .health_ok-wrap{
      color: #4ef562;
    }
    .health_err-wrap {
      color: #ff878f;
    }
    .run-time-wrap {
      background: url('../../../assets/imager/runTime.png');
      background-size: 100% 100%;
    }
    .colony-status-wrap {
      background: url('../../../assets/imager/colonyStatus.png');
      background-size: 100% 100%;
    }
    .host-wrap {
      background: url('../../../assets/imager/host.png');
      background-size: 100% 100%;
    }
    .client-wrap {
      background: url('../../../assets/imager/client.png');
      background-size: 100% 100%;
    }
    .ft-number-wrap {
      font-size: 16px;
    }
    .status-img-wrap {
      margin-left: 30%;
    }
    .client-box-wrap {
      display: flex;
      justify-content: space-between
    }
  }
</style>
