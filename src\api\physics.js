// 计算资源/物理机
import axios from "axios";
import * as basic_proxy from "@/api/config";

// 物理机全部 查询
export async function physicsTableAllQuery() {
  return await axios.get(basic_proxy.theapi + "/v1/clusters");
}
// 主机列表 查询
export async function hostListQuery() {
  return await axios.get(basic_proxy.theapi + "/v1/all/clusters");
}
// 主机未使用 查询
export async function hostUnusedQuery() {
  return await axios.get(basic_proxy.theapi + "/v1/available/hosts");
}
// 集群主机 查询
export async function clusterHostQuery(params) {
  return await axios.get(basic_proxy.theapi + "/v1/id/clusters/" + params);
}
// 集群组 新建
export async function clusterGroupNew(params) {
  return await axios.put(basic_proxy.theapi + "/v1/clusters/create", params);
}
// 集群组 编辑
export async function clusterGroupEdit(params) {
  return await axios.put(
    basic_proxy.theapi + "/v1/update/clusters/name",
    params
  );
}
// 集群组 删除
export async function clusterGroupDelete(params) {
  return await axios.delete(basic_proxy.theapi + "/v1/clusters/delete", params);
}
// 集群组 移入
export async function clusterGroupMovein(params) {
  return await axios.put(basic_proxy.theapi + "/v1/add/clusters", params);
}
// 集群组 移出
export async function clusterGroupRemove(params) {
  return await axios.delete(
    basic_proxy.theapi + "/v1/delete/clusters/host",
    params
  );
}
// 基本主机 详情
export async function basicHostDetails(params) {
  return await axios.post(basic_proxy.theapi + "/v1/hypervisor/detail", params);
}
// 主机监控 网络
export async function hostMonitoringNET(params) {
  return await axios.post(basic_proxy.theapi + "/v1/chart/host/net", params);
}
// 主机监控 CPU
export async function hostMonitoringCPU(params) {
  return await axios.post(basic_proxy.theapi + "/v1/chart/host/cpu", params);
}
// 主机监控 内存
export async function hostMonitoringMEM(params) {
  // return await axios.post(basic_proxy.theapi + "/v1/chart/host/mem", params);
  return await axios.post(basic_proxy.theapi + "/v2/chart/host/mem", params);
}


// 手动宕机迁移
export async function manualDowntimeMigration(params) {
  // return await axios.post(basic_proxy.recommendapi + "/v1/evacuate/host", params);
  // return await axios.post(basic_proxy.dayuapi + "/v1/evacuate/host", params); 废弃
  return await axios.post(basic_proxy.theapi + "/v1/evacuate/host", params);
}
// 迁移配置
export async function migrationConfiguration(params) {
  return await axios.post(
    basic_proxy.theapi + "/v1/cluster/dayu/getconfig",
    params
  );
}
// 迁移确认
export async function migrationConfirmation(params) {
  return await axios.post(
    basic_proxy.theapi + "/v1/cluster/config/dayu",
    params
  );
}


// 物理机表细节
export async function physicsTableDetail() {
  return await axios.get(basic_proxy.theapi + "/v1/clusters/detail");
}

// 风扇状态
export async function fanStatus() {
  return await axios.get(
    basic_proxy.theapi + "/v1/monitor/ipmi_chassis_cooling_fault_state"
  );
}
// 风扇速度当前转速
export async function fanCurrentSpeed() {
  return await axios.get(
    basic_proxy.theapi + "/v1/monitor/ipmi_fan_speed_rpm/current"
  );
}
// 当前温度
export async function temperatureCurrent() {
  return await axios.get(
    basic_proxy.theapi + "/v1/monitor/ipmi_temperature_celsius/current"
  );
}
// 当前电压
export async function voltageCurrent() {
  return await axios.get(
    basic_proxy.theapi + "/v1/monitor/ipmi_voltage_volts/current"
  );
}

// 电源状态（未使用）
export async function powerStatus() {
  return await axios.get(
    basic_proxy.theapi + "/v1/monitor/ipmi_chassis_power_state"
  );
}

// 风扇速度（未使用）
export async function fanSpeed(params) {
  return await axios.post(
    basic_proxy.theapi + "/v1/monitor/ipmi_fan_speed_rpm",
    params
  );
}
// 风扇传感器状态（未使用）
export async function fanSensor() {
  return await axios.get(
    basic_proxy.theapi + "/v1/monitor/ipmi_fan_speed_state"
  );
}
// 温度（未使用）
export async function temperature(params) {
  return await axios.post(
    basic_proxy.theapi + "/v1/monitor/ipmi_temperature_celsius",
    params
  );
}
// 温度传感器状态（未使用）
export async function temperatureSensor() {
  return await axios.get(
    basic_proxy.theapi + "/v1/monitor/ipmi_temperature_state"
  );
}
// 电压（未使用）
export async function voltage(params) {
  return await axios.post(
    basic_proxy.theapi + "/v1/monitor/ipmi_voltage_volts",
    params
  );
}
// 电压传感器状态（未使用）
export async function voltageSensor() {
  return await axios.get(basic_proxy.theapi + "/v1/monitor/ipmi_voltage_state");
}
