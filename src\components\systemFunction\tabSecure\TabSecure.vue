<style lang="less">
  .exit_time_btn {
    .ivu-input-number-controls-outside-btn i {
      font-weight: 800!important;
    }
  }
</style>
<template>
  <Collapse v-model="foldingPanel">
    <Panel name="1" v-show="powerAcitons.zidongdangjiqianyi">
      自动宕机迁移
      <div slot="content">
        <RadioGroup v-model="formItem.automatic" type="button" @on-change="migrateChange">
          <Radio label="off"> 关闭 </Radio>
          <Radio label="on"> 开启 </Radio>
        </RadioGroup>
      </div>
    </Panel>
    <Panel name="2"  v-show="powerAcitons.zidongtuichushijian">
      自动退出时间
      <div slot="content">
        请设置自动退出时间：<InputNumber controls-outside v-model="timeExit" class="exit_time_btn"></InputNumber> /分钟
        <div>
          <Button type="warning" ghost style="width: 100px" @click="changeSubmission">更改提交</Button>
        </div>
      </div>
    </Panel>
    <!-- <Panel name="2">
      防病毒配置
      <div slot="content">
        <RadioGroup v-model="formItem.list" type="button" @on-change="antivirusChange">
          <Radio label="not_have"> 无 </Radio>
          <Radio label="qita">第三方厂商</Radio>
        </RadioGroup>
      </div>
    </Panel> -->
  </Collapse>
</template>
<script>
import { powerCodeQuery } from '@/api/other'; // 权限可用性 查询

import {automaticExitTimeQuery,automaticExitTimeModify,antivirusConfiguration,automaticDowntimeMigrationQuery,automaticDowntimeMigrationModify} from '@/api/system';
export default {
  props: {
    tabName: String,
  },
  watch: {
    tabName(value) {
      if(value=='安全配置') {
        this.automaticDowntimeMigration()
        this.automaticExitTime()
        this.actionQuery(value)
      }
    },
  },
  mounted() {
    if(this.$store.state.power.systemFunctionTab == '安全配置') {
      this.automaticDowntimeMigration()
      this.automaticExitTime()
      this.actionQuery(this.tabName)
    }
  },
  data() {
    return {
      foldingPanel:['1','2'],
      formItem:{
        automatic:"off",
        list:"not_have",
      },
      timeExit: this.$store.state.power.exitTimeSave,

      powerAcitons: {}, // 操作权限数据
    };
  },
  updated() {
    // this.timeExit=this.$store.state.power.exitTimeSave
  },
  methods: {
    // 自动宕机迁移 查询
    automaticDowntimeMigration(){
      automaticDowntimeMigrationQuery().then(callback =>{
        if(callback.data.enabled_auto){
          this.formItem.automatic=callback.data.enabled_auto
        }
      })
    },
    // 自动宕机迁移 修改
    automaticDowntimeMigrationChange(item){
      automaticDowntimeMigrationModify({enabled_auto:item}).then(callback =>{
        this.automaticDowntimeMigration()
        if(callback.data.msg == 'ok'){
          this.$Message.success({
            background: true,
            closable: true,
            duration: 5,
            content: "自动宕机迁移设置 "+ (item=='on'?'开启':'关闭') +" 成功",
          })
        }else {
          this.$Message.error({
            background: true,
            closable: true,
            duration: 5,
            content: "自动宕机迁移设置 "+ (item=='on'?'开启':'关闭') +" 失败",
          })
        }
      })
    },
    migrateChange(item){
      if(item=="off") {
        this.automaticDowntimeMigrationChange("off")
      }else {
        this.automaticDowntimeMigrationChange("on")
      }
    },
    // 获取自动退出时间
    automaticExitTime(){
      automaticExitTimeQuery().then(callback =>{
        this.timeExit = callback.data.auto_exit_time
        this.$store.state.power.exitTimeSave = callback.data.auto_exit_time
      })
    },
    // 更改提交
    changeSubmission(){
      if(this.timeExit>=10){
        automaticExitTimeModify({auto_exit_time:this.timeExit}).then(callback =>{
          this.automaticExitTime()
          this.$Message.success({
            background: true,
            closable: true,
            duration: 10,
            content: '自动退出时间设置成功'
          });
        })
      }else {
        this.$Message.error({
          background: true,
          closable: true,
          duration: 10,
          content: '设置时间不能小于10分钟'
        });
      }
    },
    // 防病毒配置
    antivirusSelection(item){
      console.log('防病毒配置',item)
      // antivirusConfiguration({enabled_auto:item}).then(callback =>{})
    },
    antivirusChange(item){
      if(item=="not_have") {
        this.antivirusSelection("not_have")
      }else {
        this.antivirusSelection("qita")
      }
    },
    // 操作权限获取
    actionQuery(item){
      powerCodeQuery({
        module_code:[
          'zidongtuichushijian',
          'zidongdangjiqianyi',
        ]
      }).then(callback=>{
        this.powerAcitons = callback.data.data
      })
    },
  }
};
</script>
<style scoped>

</style>