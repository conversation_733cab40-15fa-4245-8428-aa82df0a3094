<template>
  <div>
    <Modal
      v-model="model"
      width="800"
      :mask-closable="false"
      style="position: relative"
    >
      <template #header><p><span style="color:green">{{groupSelect.name}}</span>分组移入虚拟机</p></template>
      <Spin fix v-if="spinShow" size="large" style="color:#ef853a">
        <Icon type="ios-loading" size=50 class="demo-spin-icon-load"></Icon>
        <div style="font-size:16px;padding:20px">Loading...</div>
      </Spin>
      <div>
        <Input v-model="tablePageForm.search_str" search enter-button placeholder="请输入符合查询的虚拟机名称" @on-search="moveInsearchInput" />
      </div>
      <Table
        :columns="vmColumn"
        :data="vmData"
        @on-sort-change="columnSort"
        @on-selection-change="tableChange"
      >
        <template v-slot:ip="{row}">
          <Tooltip :content="convertIP(row.addresses)" style="width:100%">
            <span class="text_overflow">{{ convertIP(row.addresses) }}</span>
          </Tooltip>
        </template>
        <template v-slot:status="{row}">
          <span>{{row.status=='SHUTOFF'?'已关机':'已开机'}}</span>
        </template>
      </Table>
      <!-- 虚拟机表格 分页  -->
      <div class="pages" v-if="this.vmData.length>0">
        <Page
          :total="moveIntotal"
          show-total
          show-sizer
          :page-size="tablePageForm.pagecount"
          placement="top"
          :page-size-opts="[10, 20, 30]"
          @on-change="pageChange"
          @on-page-size-change="pageSizeChange"
        />
      </div>
      <div slot="footer">
        <Button type="text" @click="model = false">取消</Button>
        <Button type="primary" :loading="disabled" @click="modelOK">
          <span v-if="!disabled">确认</span>
          <span v-else>移入中</span>
        </Button>
        <!-- <Button type="primary" @click="modelOK" :disabled="disabled">确认</Button> -->
      </div>
    </Modal>
  </div>
</template>
<script>
import {
  vmGroupTableQuery, // 虚拟机组表格 查询
  vmGroupTableMoveinGroup // 虚拟机组表格 移入分组
} from '@/api/virtualMachine';
export default {
  props: {
    moveinTime: String,
    groupSelect: Object,
  },
  watch: {
    moveinTime(news){
      this.tablePageForm.id = this.groupSelect.rootID
      this.groupID = this.groupSelect.id
      this.groupName = this.groupSelect.name
      this.model = true
      this.disabled = false
      this.vmaxios()
    }
  },
  data(){
    return {
      // 虚拟机移入分组
      spinShow:false,
      model:false,
      disabled:true,
      groupID:1,
      groupName:"",
      vmColumn: [
        { type: "selection", width: 30, align: "center" },
        { title: "虚拟机", key: "name",sortable: 'custom', tooltip:true, },
        { title: "IP", key: "ip",align: "center", slot:"ip" },
        { title: "主机名", key: "hostname", align: "center" },
        { title: "状态", key: "status",align: "center", slot:"status" }
      ],
      vmData: [],
      tableSelec: [],
      moveIntotal:0,
      tablePageForm: {
        id: 1,
        // 虚拟机分页当前页
        page: 1,
        // 虚拟机分页每页条数
        pagecount: 10,
        search_str: "",
        order_type: "desc",
        order_by: "",
      },
    }
  },
  methods: {
    // 获取全部虚拟机
    vmaxios(){
      this.spinShow = true
      this.tableSelec=new Array()
      vmGroupTableQuery(this.tablePageForm)
        .then((callback) => {
          this.spinShow = false
          this.moveIntotal = callback.data.total;
          this.vmData = callback.data.data;
        })
        .catch((error) => {
          this.spinShow = false
        });
    },
     // 表格选中
    tableChange(item) {
      this.tableSelec = item;
    },
    // 点击分页器
    pageChange(item) {
      this.tablePageForm.page = item;
      this.vmaxios();
    },
    // 切换 条数
    pageSizeChange(item) {
      this.tablePageForm.pagecount = item;
      this.vmaxios();
    },
    // 表格列排序 
    columnSort(column, key, order) {
      if (column.key !== "normal") {
        this.tablePageForm.order_by = column.key;
        this.tablePageForm.order_type = column.order;
        this.vmaxios();
      }
    },
    // 移入分组搜索
    moveInsearchInput(){
      this.vmaxios();
    },
    // 确认事件
    modelOK() {
      if(this.tableSelec == 0) {
        this.$Message.warning({
					background: true,
          closable: true,
					duration: 5,
					content: "未选择表格数据",
				});
      }else {
        let ids = new Array();
        let names = new Array();
        this.tableSelec.forEach((em) => {
          ids.push(em.id);
          names.push(em.name);
        });
        this.disabled = true
        vmGroupTableMoveinGroup({
          group_id: this.groupID,
          group_name: this.groupName,
          vmids: ids,
          vm_names: names
        })
        .then((callback) => {
          if(callback.data.msg == "ok") {
            this.model=false
            this.$emit("custom-ok",'虚拟机移入分组操作完成');
          }else {
            this.disabled = false
            this.$emit("custom-error",'虚拟机移入分组操作失败');
          }
        })
        .catch((error) => {
          this.disabled = false
          this.$emit("custom-error",'虚拟机移入分组操作失败');
        })
      }
    },
    // IP转换
    convertIP(item){
      let ips = item.map(em=>{return em.addr})
      return ips.toString()
    },
  }
}
</script>