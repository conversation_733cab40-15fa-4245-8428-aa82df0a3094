<template>
  <div>
    <Modal v-model="model" width="600" :mask-closable="false">
      <template #header><p>新建告警规则</p></template>
      <Form
        :model="formItem"
        ref="formItem"
        :rules="rulesForm"
        :label-width="120"
      >
        <FormItem label="名称" prop="name">
          <Input v-model="formItem.name" placeholder="请输入告警规则名称"></Input>
        </FormItem>
        <FormItem label="告警类别" prop="job">
          <RadioGroup v-model="formItem.job">
            <Radio label="集群告警"></Radio>
            <Radio label="存储告警"></Radio>
          </RadioGroup>
        </FormItem>
        <FormItem label="告警等级" prop="severity">
          <Select v-model="formItem.severity">
            <Option v-for="item in severityData" :value="item.value" :key="item.value">{{ item.label }}</Option>
          </Select>
        </FormItem>
        <FormItem label="持续时间" prop="for">
          <Input v-model="formItem.for" type="number">
            <template #append>
              <span>分钟</span>
            </template>
          </Input>
        </FormItem>
        <FormItem>
          <Checkbox v-model="formItem.enable">启用告警阈值</Checkbox>
        </FormItem>
        <FormItem label="告警阈值" prop="threshold" v-if="formItem.enable">
          <Input v-model="formItem.threshold" type="number">
            <template #append>
              <span>%</span>
            </template>
          </Input>
        </FormItem>
        <FormItem label="查询语句">
          <Input v-model="formItem.expr" type="textarea" :rows="3" placeholder="请输入查询语句"></Input>
        </FormItem>
        <FormItem label="摘要">
          <Input v-model="formItem.summary" type="textarea" :rows="1" placeholder="请输入摘要"></Input>
        </FormItem>
        <FormItem label="描述">
          <Input v-model="formItem.description"  type="textarea" :rows="3" placeholder="请输入描述内容"></Input>
        </FormItem>
      </Form>
      <div slot="footer">
        <Button type="text" @click="model = false">取消</Button>
        <Button type="primary" @click="modelOK" :disabled="disabled">确认</Button>
      </div>
    </Modal>
  </div>
</template>
<script>
import {
  alarmRulesAdd, // 告警规则 添加
} from '@/api/log';

export default {
  props: {
    newTime: String,
  },
  watch: {
    newTime(news){
      this.formItem.job = '集群告警'
      this.formItem.severity = 'warning'
      this.formItem.enable = false
      this.formItem.expr = ''
      this.formItem.description = ''
      this.formItem.summary = ''
      this.$refs.formItem.resetFields();
      this.model = true
      this.disabled = false
    }
  },
  data(){
    const propName =(rule,value,callback)=>{
      let list = /^[\u4e00-\u9fa5_a-zA-Z0-9@_.]{2,32}$/;
      if(list.test(value)){
        callback()
      }else{
        callback(new Error("2-32 个中文、英文、数字、特殊字符(@_.)"));
      }
    }
    const propFor =(rule,value,callback)=>{
      let list = /^[0-9]\d*$/
      if(list.test(value)){
        callback()
      }else{
        callback(new Error("请输入0以上的整数值"));
      }
    }
    const propThreshold =(rule,value,callback)=>{
      let list = /^(?:100|[1-9]?\d)$/
      if(list.test(value)){
        callback()
      }else{
        callback(new Error("请输1-100之内的数字"));
      }
    }
    return {
      model: false,
      disabled: false,
      formItem: {
        name: '',
        job: '集群告警',
        for: '1',
        enable: false,
        threshold: '0',
        severity: 'warning',
        expr: '',
        description: '',
        summary: '',
      },
      severityData: [
        { label: '提示', value: 'info' },
        { label: '紧急通知', value: 'page' },
        { label: '警告', value: 'warning' },
        { label: '严重', value: 'critical' },
      ],
      
      // 正则验证
      rulesForm: {
        name:[
          { required: true, message: '必填项', trigger: 'change' },
          { validator: propName, trigger:'change' }
        ],
        job:[{ required: true, message: '必选项', trigger: 'change' }],
        severity:[{ required: true, message: '必选项', trigger: 'change' }],
        for:[
          { required: true, message: '必填项', trigger: 'change' },
          { validator: propFor, trigger:'change' }
        ],
        threshold:[
          { required: true, message: '必填项', trigger: 'change' },
          { validator: propThreshold, trigger:'change' }
        ]
        
      },
    }
  },
  methods: {
    // 编辑网络确认事件
    modelOK() {
      this.$refs.formItem.validate((valid) => {
        if (valid) {
          this.disabled = true;
          alarmRulesAdd({
            name:this.formItem.name,
            job:this.formItem.job=='集群告警'?'':'ceph',
            for:this.formItem.for+'m',
            expr_code: this.formItem.enable?'1':'0',
            value: Number(this.formItem.threshold),
            severity:this.formItem.severity,
            expr:this.formItem.expr,
            description:this.formItem.description,
            summary:this.formItem.summary,
          })
          .then(callback => {
            if(callback.data.msg == "ok") {
              this.$emit("return-ok",'新建告警规则操作完成');
              this.model = false;
            }else {
              this.$emit("return-error",callback.data.msg);
              this.disabled = false;
            }
          })
          .catch((error) => {
            this.$emit("return-error",'新建告警规则操作失败');
            this.disabled = false;
          });
        }
      });
    },
  }
}
</script>
