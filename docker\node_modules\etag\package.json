{"_from": "etag@~1.8.1", "_id": "etag@1.8.1", "_inBundle": false, "_integrity": "sha512-aIL5Fx7mawVa300al2BnEE4iNvo1qETxLrPI/o05L7z6go7fCw1J6EQmbK4FmJ2AS7kgVF/KEZWufBfdClMcPg==", "_location": "/etag", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "etag@~1.8.1", "name": "etag", "escapedName": "etag", "rawSpec": "~1.8.1", "saveSpec": null, "fetchSpec": "~1.8.1"}, "_requiredBy": ["/express", "/send"], "_resolved": "https://registry.npmmirror.com/etag/-/etag-1.8.1.tgz", "_shasum": "41ae2eeb65efa62268aebfea83ac7d79299b0887", "_spec": "etag@~1.8.1", "_where": "/root/docker/node_modules/express", "bugs": {"url": "https://github.com/jshttp/etag/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "david.b<PERSON>rk<PERSON>@gmail.com"}], "deprecated": false, "description": "Create simple HTTP ETags", "devDependencies": {"beautify-benchmark": "0.2.4", "benchmark": "2.1.4", "eslint": "3.19.0", "eslint-config-standard": "10.2.1", "eslint-plugin-import": "2.7.0", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-node": "5.1.1", "eslint-plugin-promise": "3.5.0", "eslint-plugin-standard": "3.0.1", "istanbul": "0.4.5", "mocha": "1.21.5", "safe-buffer": "5.1.1", "seedrandom": "2.4.3"}, "engines": {"node": ">= 0.6"}, "files": ["LICENSE", "HISTORY.md", "README.md", "index.js"], "homepage": "https://github.com/jshttp/etag#readme", "keywords": ["etag", "http", "res"], "license": "MIT", "name": "etag", "repository": {"type": "git", "url": "git+https://github.com/jshttp/etag.git"}, "scripts": {"bench": "node benchmark/index.js", "lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "version": "1.8.1"}