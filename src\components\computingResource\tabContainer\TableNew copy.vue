<template>
  <div>
    <Modal
      v-model="model"
      width="600"
      :mask-closable="false"
    >
      <template #header><p>新建容器</p></template>
      <Form :model="formItem" ref="formItem" :rules="ruleValidate" :label-width="120">
					<FormItem label="容器名称" prop="name">
            <Input v-model="formItem.name"></Input>
        	</FormItem>
          <FormItem label="镜像" prop="image_name">
            <Select v-model="formItem.image_name" >
              <Option v-for="item in imgData" :value="item.name" :key="item.id">{{ item.name }}</Option>
            </Select>
          </FormItem>
          <FormItem label="网络" prop="network_id">
            <Select v-model="formItem.network_id" >
              <Option v-for="item in netData" :value="item.id" :key="item.id">{{ item.cidr }}</Option>
            </Select>
          </FormItem>
          <FormItem label="CPU">
            <div class="slider_area">
              <div style="width:350px">
                <Slider v-model="formItem.vcpus" :min='1' :max='64' :tip-format="formCPU" ></Slider>
              </div>
              <div style="width:80px">
                <InputNumber :min='1' :max="64" v-model="formItem.vcpus" :formatter="value => `${value}核`" :parser="value => value.replace('核', '')"></InputNumber>
              </div>
            </div>
          </FormItem>
          <FormItem label="内存">
            <div class="slider_area">
              <div style="width:350px">
                <Slider v-model="formItem.ram" :min='1' :max='1024' :tip-format="formMemory" ></Slider>
              </div>
              <div style="width:80px">
                <InputNumber :min='1' :max="2048" v-model="formItem.ram" :formatter="value => `${value}GB`" :parser="value => value.replace('GB', '')"></InputNumber>
              </div>
            </div>
          </FormItem>
				</Form>
      <div slot="footer">
        <Button type="text" @click="model = false">取消</Button>
        <Button type="primary" @click="modalOK" :disabled="disabled">确认</Button>
      </div>
    </Modal>
  </div>
</template>
<script>
import {  vmGroupNewBuilt } from '@/api/virtualMachine';  // 虚拟机组 新建
export default {
  props: {
    newTime:String,
  },
  watch: {
    newTime(news){
      this.model = true
      this.$refs.formItem.resetFields()
      // 获取镜像数据
      // this.$axios.get('/theapi/v1/containers/images').then(callback=>{
      //   this.imgData = callback.data
      // })
      // 获取网络数据
      this.$axios.get('/theapi/v1/containers/networks').then(callback=>{
        this.netData=callback.data
      })
    }
  },
  data(){
    const propName = (rule, value, callback) => {
      let list = /^[\u4e00-\u9fa5_a-zA-Z0-9@_.-]{2,32}$/;
      if (list.test(value)) {
        callback();
      } else {
        callback(new Error("2-32 个中文、英文、数字、特殊字符(@_.-"));
      }
    };
    return {
      model:false,
      disabled: false,
      formItem:{
        name:'',
        image_name:'',
        network_id:'',
        vcpus:1,
        ram:2,
      },
      imgData:[],
      netData:[],
      ruleValidate:{
        name:[
          { required: true, message: '必填项', trigger: 'change' },
          { validator: propName, trigger: "change" },
        ],
        image_name:[{ required: true, message: '必填项', trigger: 'change' }],
        network_id:[{ required: true, message: '必填项', trigger: 'change' }],
      }
    }
  },
  methods: {
    // 新建容器 cpu
    formCPU(val){
      return val + ' 核';
    },
    // 新建容器 容量
    formMemory (val) {
      return val + ' GB';
    },
    modalOK() {
      this.$refs.formItem.validate((valid) => {
        if(valid) {
          this.disabled = true
          let data = {}
          this.$axios.post("/theapi/v1/containers/create", data)
          .then((callback) => {
            this.model = false;
            this.$emit("return-ok",{
              msg: '新建虚拟机组完成',
              type: 'ok'
            });
          })
          .catch((error) => {
            this.disabled = false
            this.$emit("return-ok",{
              msg: '新建虚拟机组失败',
              type: 'error'
            });
          })
        }
      })
    },
  }
}
</script>