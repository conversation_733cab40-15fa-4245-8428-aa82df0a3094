{"_from": "escape-html@~1.0.3", "_id": "escape-html@1.0.3", "_inBundle": false, "_integrity": "sha512-NiSupZ4OeuGwr68lGIeym/ksIZMJodUGOSCZ/FSnTxcrekbvqrgdUxlJOMpijaKZVjAJrWrGs/6Jy8OMuyj9ow==", "_location": "/escape-html", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "escape-html@~1.0.3", "name": "escape-html", "escapedName": "escape-html", "rawSpec": "~1.0.3", "saveSpec": null, "fetchSpec": "~1.0.3"}, "_requiredBy": ["/express", "/finalhandler", "/send", "/serve-static"], "_resolved": "https://registry.npmmirror.com/escape-html/-/escape-html-1.0.3.tgz", "_shasum": "0258eae4d3d0c0974de1c169188ef0051d1d1988", "_spec": "escape-html@~1.0.3", "_where": "/root/docker/node_modules/express", "bugs": {"url": "https://github.com/component/escape-html/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Escape string for use in HTML", "devDependencies": {"beautify-benchmark": "0.2.4", "benchmark": "1.0.0"}, "files": ["LICENSE", "Readme.md", "index.js"], "homepage": "https://github.com/component/escape-html#readme", "keywords": ["escape", "html", "utility"], "license": "MIT", "name": "escape-html", "repository": {"type": "git", "url": "git+https://github.com/component/escape-html.git"}, "scripts": {"bench": "node benchmark/index.js"}, "version": "1.0.3"}