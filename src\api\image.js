// 计算资源/镜像
import axios from "axios";
import * as basic_proxy from "@/api/config";

// 切换系统类型
export async function switcTypes(params) {
  return await axios.put(
    basic_proxy.theapi + "/v2/images/update/ostype",
    params
  );
}

// 镜像表格 删除
export async function imageTableDelete(params) {
  return await axios.delete(basic_proxy.theapi + "/v1/images/delete", params);
}

// 镜像表格 查询
export async function imageTableQuery() {
  // return await axios.get(basic_proxy.theapi + "/v1/images");
  return await axios.get(basic_proxy.theapi + "/v2/images");
}

// 镜像表格 新建
export async function imageTableNew(params) {
  return await axios.post(basic_proxy.upload + "/v2/images/create", params);
}

// 镜像表格 编辑
export async function imageTableEdit(params) {
  return await axios.put(basic_proxy.theapi + "/v1/images/edit", params);
}

// 云硬盘 新建
export async function cloudHardDriveNew(params) {
  return await axios.post(
    basic_proxy.theapi + "/v1/volumes/createfromimage",
    params
  );
}
