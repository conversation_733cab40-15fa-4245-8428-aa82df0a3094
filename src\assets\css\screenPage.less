.large_screen {
  width: 1920px;
  height: 1080px;
  background-image: url(../daping/BG.png);
  background-size: cover;

  .screen_head {
    height: 80px;
    width: 100%;
    background-image: url(../daping/DBbg.png);
    background-size: cover;
    margin-bottom: 12px;

    ul {
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: space-evenly;

      li {
        width: 33%;
        height: 100%;
      }

      .screen_logo {
        display: flex;
        align-items: center;

        >img {
          width: 177px;
          height: 35px;
          margin-left: 10px;
        }
      }

      .project_name {
        display: flex;
        align-items: flex-end;
        justify-content: center;

        h4 {
          color: #fff;
          font-size: 30px;
          padding-bottom: 5px;
        }
      }

      .system_time {
        display: flex;
        justify-content: flex-end;

        div {
          height: 100%;
          display: flex;
          flex-direction: column;
          justify-content: center;
          padding: 0 10px;
        }
      }
    }
  }

  .screen_center {
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: space-evenly;

    .center_left {
      width: 455px;

      ul {
        height: 100%;
        color: #fff;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        li {
          width: 455px;
          height: 306px;
          margin-bottom: 22px;
          background-image: url(../daping/lRbg.png);
        }
      }
    }

    /* 内容居中区域 */
    .centerMiddle {
      width: 936px;

      ul {
        width: 936px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .middle_chart {
          width: 100%;

          .machine_usage {
            width: 100%;
            height: 138px;
            background-image: url(../daping/TBbg.png);
            background-size: cover;
            color: #ddd;
            display: flex;
            justify-content: space-evenly;

            .machine_module {
              width: 45%;
              height: 138px;
              display: flex;
              align-items: center;

              img {
                height: 86PX;
                margin-right: 5%;
              }

              .machine_quantity {
                width: 60%;
                height: 70%;
              }
            }
          }

          .dashboard_chart {
            width: 100%;
            height: 518px;
            background-image: url(../daping/keji1.gif);
            background-size: 100% 100%;
          }
        }

        .physical_machine_onitor {
          height: 306px;
          width: 936px;
          padding: 3px;
          overflow: hidden;
          background-image: url(../daping/JKbg.png);
          color: #fff;

          .ivu-table {
            background: none;

            th {
              background: #141d3a00;
              color: #308dff;
              font-size: 14px;

              border-bottom: 0px
            }

            td {
              background: #1f2d50;
              color: #fff;
              border: 2px solid #141d3a
                /* 背景 */
            }
          }

          .ivu-table:before {
            background: none;
          }

          .ivu-table-tbody tr:nth-child(odd) td:first-child {
            border-left: 2px solid #193580;
          }

          .ivu-table-tbody tr:nth-child(even) td:first-child {
            border-left: 2px solid #ff9400;
          }

          .ivu-progress-text {
            color: #fff;
          }
        }
      }
    }

    .center_right {
      width: 455px;

      ul {
        height: 100%;
        color: #fff;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        li {
          width: 455px;
          height: 306px;
          margin-bottom: 22px;
          background-image: url(../daping/lRbg.png);

          .chart_monitor {
            width: 100%;
            height: 260px;

            .alarm_without_data {
              width: 100%;
              height: 100%;
              display: flex;
              flex-direction: column;
              justify-content: space-around;
              align-items: center;
            }
          }
        }
      }
    }
  }

  /* 标题 */
  .module_title {
    font-size: 18px;
    line-height: 38px;
    padding-left: 20px;
  }

  /* 环 */
  .module_ring {
    height: 216px;
    display: flex;
    align-items: center;
    justify-content: space-around;
  }

  /* 环 底部 */
  .module_usage {
    height: 45px;
    font-size: 14px;
    display: flex;
    justify-content: space-around;
  }

  .orange_usage {
    color: #ff9d00;
    font-size: 24px;
    font-weight: 600;
  }

  .blue_usage {
    color: #308dff;
    font-size: 24px;
    font-weight: 600;
  }
}