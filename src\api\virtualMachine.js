// 计算资源/虚拟机
import axios from "axios";
import * as basic_proxy from "@/api/config";

// 虚拟机组 查询
export async function vmGroupQuery() {
  return await axios.get(basic_proxy.theapi + "/v1/groups");
}
// 虚拟机组 新建
export async function vmGroupNewBuilt(params) {
  return await axios.post(basic_proxy.theapi + "/v1/groups/create", params);
}
// 虚拟机组 修改
export async function vmGroupModify(params) {
  return await axios.post(basic_proxy.theapi + "/v1/groups/rename", params);
}
// 虚拟机组 删除
export async function vmGroupDelete(params) {
  return await axios.delete(basic_proxy.theapi + "/v1/delete/groups", params);
}

// 虚拟机组表格 查询
export async function vmGroupTableQuery(params) {
  // return await axios.post(basic_proxy.theapi + "/v1/groups/instances", params);
  return await axios.post(basic_proxy.theapi + "/v5/groups/instances", params);
}
// 虚拟机组表格 从镜像新建
export async function vmGroupTableNewFromMirror(params) {
  return await axios.post(basic_proxy.theapi + "/v3/instance/create", params);
}
// 虚拟机组表格 从ISO新建
export async function vmGroupTableNewFromISO(params) {
  return await axios.post(
    basic_proxy.theapi + "/v2/instance/isocreate",
    params
  );
}
// 虚拟机组表格 修改
export async function vmGroupTableModify(params) {
  return await axios.put(
    basic_proxy.theapi + "/v1/instance/update/name",
    params
  );
}
// 虚拟机组表格 删除
export async function vmGroupTableDelete(params) {
  return await axios.delete(basic_proxy.theapi + "/v1/instance/delete", params);
}
// 虚拟机组表格 移出虚拟机
export async function vmGroupTableRemoveVM(params) {
  // return await axios.post(basic_proxy.theapi + "/v1/groups/remove", params);
  return await axios.delete(basic_proxy.theapi + "/v2/groups/remove", params);
}
// 虚拟机组表格 移入分组
export async function vmGroupTableMoveinGroup(params) {
  return await axios.put(
    // basic_proxy.theapi + "/v1/groups/add",
    basic_proxy.theapi + "/v2/groups/add",
    params
  );
}
// 虚拟机组表格 操作(开机 关机 重启 控制台等接口调用)
export async function vmGroupTableAction(params) {
  return await axios.post(basic_proxy.theapi + "/v1/instance/action", params);
}
// 虚拟机组表格 批量开关机
export async function vmGroupTableBatchOnOffMachine(params) {
  return await axios.post(basic_proxy.theapi + "/v2/instance/action", params);
}
// 虚拟机组表格 修改配置 群操作
export async function vmGroupTableConfig(params) {
  return await axios.post(basic_proxy.theapi + "/v3/instance/action", params);
}
// 虚拟机组表格 切换系统类型
export async function vmGroupTableSystemType(params) {
  return await axios.put(basic_proxy.theapi + "/v1/modify/metadata", params);
}
// 虚拟机组表格 弹出光盘 X86
export async function vmEjectDiscX86(params) {
  return await axios.post(
    // basic_proxy.theapi + "/v1/instance/unmountiso",
    basic_proxy.theapi + "/v2/instance/unmountiso",
    params
  );
}
// 虚拟机组表格 弹出光盘 ARM
export async function vmEjectDiscARM(params) {
  return await axios.post(
    basic_proxy.theapi + "/v1/instance/unmountarm",
    params
  );
}
// 虚拟机组表格 迁移确认
export async function vmGroupTableMigrationConfirm(params) {
  return await axios.post(
    basic_proxy.theapi + "/v1/instances/dayu/setconfig",
    params
  );
}
// 虚拟机组表格 分离云硬盘
export async function vmGroupTableSeparateClouddisc(params) {
  return await axios.post(basic_proxy.theapi + "/v1/instance/detail", params);
}

// 虚拟机组表格 修改ip
export async function vmGroupTableReplaceIP(params) {
  return await axios.post(
    basic_proxy.theapi + "/v4/instance/changeip",
    params
  );
}
// 虚拟机组表格 添加网卡
export async function vmGroupTableAddAdapter(params) {
  return await axios.post(
    basic_proxy.theapi + "/v2/instance/addnetport",
    params
  );
}
// 虚拟机组表格 删除网卡
export async function vmGroupTableDeleteAdapter(params) {
  return await axios.delete(
    basic_proxy.theapi + "/v1/instance/deletenetport",
    params
  );
}
// 虚拟机组表格 绑定MAC地址
export async function vmMACaddress(params) {
  return await axios.post(
    basic_proxy.theapi + "/v1/instance/changemac",
    params
  );
}
// 虚拟机表格 高可用操作
export async function vmHAaction(params) {
  return await axios.post(
    basic_proxy.theapi + "/v1/instance/whitelist",
    params
  );
}

// 安全组 查询
export async function securityGroupQuery() {
  return await axios.get(
    basic_proxy.theapi + "/v1/securitygroups/list"
  );
}
// 安全组 已选
export async function securityGroupSelected(params) {
  return await axios.post(
    basic_proxy.theapi + "/v1/securitygroups/getbyserver",params
  );
}
// 安全组 编辑
export async function securityGroupEdit(params) {
  return await axios.post(
    basic_proxy.theapi + "/v1/securitygroups/addtoserver",
    params
  );
}
// 云硬盘 查询
export async function cloudHardDriveQuery() {
  return await axios.get(basic_proxy.theapi + "/v1/availablevolumes");
}
// 云硬盘 挂载
export async function loudHardDriveMount(params) {
  return await axios.post(basic_proxy.theapi + "/v1/instances/attach", params);
}
// 虚拟机废弃
export async function vmAbandoned(params) {
  return await axios.post(basic_proxy.theapi + "/v1/instance/shelve", params);
}
// 虚拟机取消废弃
export async function vmCancelAbandonment(params) {
  return await axios.post(basic_proxy.theapi + "/v1/instance/unshelve", params);
}
// 动态资源扩展查询
export async function dynamicResourceExtendQuery(params) {
  return await axios.get(basic_proxy.theapi + "/v1/drs/" + params);
}
// 动态资源扩展修改
export async function dynamicResourceExtendEdit(params) {
  return await axios.post(basic_proxy.theapi + "/v1/drs/update", params);
}
// 虚拟机克隆
export async function vmClone(params) {
  return await axios.post(basic_proxy.theapi + "/v3/desk/clone", params);
}

// 虚拟机 可迁移物理机
export async function vmMigratablePhysicalMachine() {
  return await axios.get(basic_proxy.theapi + "/v1/instances/hosts");
}


// 虚拟机 查询
export async function vmTableQuery(params) {
  return await axios.post(basic_proxy.theapi + "/v1/groups/instances", params);
}
// 虚拟机 模板新建
export async function vmTemplateNewBuilt(params) {
  return await axios.post(basic_proxy.theapi + "/v2/flavors/create", params);
}

// 虚拟机组表格 虚拟机任务查询
export async function vmGroupTableVMtask() {
  return await axios.get(
    basic_proxy.theapi + "/v1/task/instances?status=ready"
  );
}

// 新建虚拟机任务
export async function newVMtask(params) {
  return await axios.post(basic_proxy.theapi + "/v1/task/instances", params);
}

// 迁移虚拟机任务
export async function migrationVMtask(params) {
  return await axios.post(
    basic_proxy.theapi + "/v2/instance/list/migrate",
    params
  );
}

// 虚拟机详情 网络
export async function vmDetailsNET(params) {
  return await axios.post(basic_proxy.theapi + "/v1/chart/vm/net", params);
}

// 虚拟机详情 CPU
export async function vmDetailsCPU(params) {
  return await axios.post(basic_proxy.theapi + "/v1/chart/vm/cpu", params);
}

// 虚拟机详情 内存
export async function vmDetailsMEM(params) {
  return await axios.post(basic_proxy.theapi + "/v1/chart/vm/mem", params);
}

// 回收站 查询
export async function recycleBinTableQuery(params) {
  return await axios.post(basic_proxy.theapi + "/v1/recycle/list", params);
}
// 回收站 彻底删除虚拟机
export async function recyCompletelyDeleteVM(params) {
  return await axios.post(
    basic_proxy.theapi + "/v3/instance/forcedelete",
    params
  );
}

// 僵尸虚拟 查询
export async function zombieVMQuery(params) {
  return await axios.post(basic_proxy.theapi + "/v1/instances/cleanup", params);
}

// 虚拟机扩展查询
export async function vmextendQuery() {
  return await axios.get(
    basic_proxy.theapi + "/v1/instance/autoexpansion/info"
  );
}
// 虚拟机扩展确认
export async function vmextendConfirm(params) {
  return await axios.post(
    basic_proxy.theapi + "/v1/instance/autoexpansion/update",
    params
  );
}