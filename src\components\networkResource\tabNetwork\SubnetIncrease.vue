<template>
  <Modal v-model="model" width="600" :mask-closable="false">
    <template #header><p>添加子网</p></template>
    <Form
      ref="formItem"
      :model="formItem"
      :rules="rulesForm"
      :label-width="120"
    >
      <FormItem label="子网名称" prop="name">
        <Input v-model="formItem.name" placeholder="输入网络名"></Input>
      </FormItem>
      <FormItem label="子网" prop="cidr">
        <Input v-model="formItem.cidr" placeholder="例：***********/24"></Input>
      </FormItem>
      <FormItem label="网关地址" prop="gateway">
        <Input v-model="formItem.gateway" placeholder="输入网关"></Input>
      </FormItem>
      <FormItem label="启用DHCP">
        <Checkbox v-model="formItem.dhcp"></Checkbox>
      </FormItem>
      <div v-if="formItem.dhcp">
        <FormItem label="IP池起始" prop="start">
          <Input
            v-model="formItem.start"
            placeholder="例：***********"
          ></Input>
        </FormItem>
        <FormItem label="IP池结束" prop="end">
          <Input
            v-model="formItem.end"
            placeholder="例：***********54"
          ></Input>
        </FormItem>
      </div>
      <FormItem label="DNS">
        <Input v-model="formItem.dns" placeholder="输入DNS"></Input>
      </FormItem>
      <FormItem label="主机路由">
        <div style="display: flex; justify-content: space-evenly">
          <Input
            v-model="formItem.destination"
            placeholder="网段例：***********/24"
          ></Input>
          <Input
            v-model="formItem.nexthop"
            placeholder="下一跳例：********"
          ></Input>
          <Button type="text" @click="addRouter" icon="md-add-circle"></Button>
        </div>
      </FormItem>
      <div style="display: flex; flex-wrap: wrap">
        <ul
          v-for="(item, index) in formItem.routeData"
          style="margin: 0 2px; border-right: 1px solid #ccc"
        >
          <li style="display: inline-block; width: 120px">
            {{ item.destination }}
          </li>
          <li style="display: inline-block; width: 120px">
            {{ item.nexthop }}
          </li>
          <Icon
            type="ios-trash"
            @click="deletRoutes(index)"
            style="color: red; cursor: pointer; font-size: 20px"
          />
        </ul>
      </div>
    </Form>
    <template #footer>
      <Button type="text" @click="model = false">取消</Button>
      <Button
        type="info"
        class="plus_btn"
        @click="modalOK"
        :disabled="disabled"
        >确定</Button
      >
    </template>
  </Modal>
</template>
<script>
import {networkTableSubnetAdd} from '@/api/network';

export default {
  props: {
    tableRow: Object,
    nameAll: Array,
    addsubnetTime: String,
  },
  watch: {
    addsubnetTime(news){
      // this.formItem.title = this.tableRow.name+ " 网络添加子网"
      this.formItem.id = this.tableRow.id
      this.formItem.dhcp = false // 初始化 启用DHCP
      this.formItem.dns = "" // 初始化 DNS
      this.formItem.destination = "" // 初始化 网段
      this.formItem.nexthop = "" // 初始化 下一跳
      this.formItem.routeData = [] // 初始化 主机路由
      this.$refs.formItem.resetFields()
      this.model = true
      this.disabled = false
    }
  },
  data(){
    // 网络名称
    const propName = (rule, value, callback) => {
      let list = /^[\u4e00-\u9fa5_a-zA-Z0-9@_.-]{2,32}$/;
      if (list.test(value)) {
        this.nameAll.indexOf(value) == -1
          ? callback()
          : callback(new Error("名字已使用"));
      } else {
        callback(new Error("请输入2-32个中文或字符（特殊字符可用@_.-）"));
      }
    };
    // 子网
    const propCidr = (rule, value, callback) => {
      let ZW = /^(\d{1,3}\.){3}\d{1,3}\/\d{1,2}$/;
      let yanma = value.split("/")[1];
      if (!ZW.test(value)) {
        callback(new Error("请输入正确的子网格式，例：***********/24"));
      } else {
        yanma - 0 <= 32 && yanma - 0 >= 1
          ? callback()
          : callback(new Error("子网掩码应在1到32之间"));
      }
    };
    // 网关
    const propNet = (rule, value, callback) => {
      let list = /^(25[0-4]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-4]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-4]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-4]|2[0-4][0-9]|[01]?[1-9][0-9]?|1[0-9]{2}|2[0-4][0-9])$/
      if (list.test(value)) {
        this.ipInSubnet(value, this.formItem.cidr) ? callback() : callback(new Error("与子网不匹配"));
      } else {
        callback(new Error("该ip不可用"));
      }
    };
    // IP池起始
    const propStart = (rule, value, callback) => {
      let wg = this.formItem.gateway.split(".");
      let qs = value.split(".");
      for(var i=0;i<wg.length;i++) {
        if(i==3) {
          if(wg[i] !== qs[i]) {
            callback()
          }else {
            callback(new Error("网关地址不能在IP池范围内"))
            break
          }
        }else {
          if(wg[i] !== qs[i]) {
            callback(new Error("网关地址与IP池不在同一网段"));
            break
          }
        }
      }
    };
    // IP池结束
    const propEnd = (rule, value, callback) => {
      let qs = this.formItem.start.split(".");
      let js = value.split(".");
      for(var i=0;i<qs.length;i++) {
        if(i==3) {
          if(qs[i]*1 < js[i]*1) {
            callback()
          }else {
            callback(new Error("且结束IP应大于起始IP"))
            break
          }
        }else {
          if(qs[i] !== js[i]) {
            callback(new Error("网关地址与IP池不在同一网段"));
            break
          }
        }
      }
    };
    return {
      model: false,
      disabled: false,
      formItem:{
        title: '',
        id: '',
        name: '',
        cidr: '', // 子网
        gateway: '', // 网关地址
        dhcp: true, // 启用DHCP
        start: '', // IP池起始
        end: '', // IP池结束
        dns: '', // DNS
        destination: '', // 网段
        nexthop: '', // 下一跳
        routeData: [], // 主机路由集合
      },
      rulesForm: {
        name: [
          { required: true, message: "必填项" },
          { validator: propName, trigger: "blur" },
        ],
        cidr: [
          { required: true, message: "必填项" },
          { validator: propCidr, trigger: "blur" },
        ],
        gateway: [
          { required: true, message: "必填项" },
          { validator: propNet, trigger: "blur" },
        ],
        start: [
          { required: true, message: "必填项" },
          { validator: propStart, trigger: "blur" },
        ],
        end: [
          { required: true, message: "必填项" },
          { validator: propEnd, trigger: "blur" },
        ],
      },
    }
  },
  methods: {
    // 主机路由添加
    addRouter(){
      if(this.formItem.destination==""||this.formItem.nexthop==""){
        this.$Message.warning({
          background: true,
          closable: true,
          duration: 5,
          content: "主机路由输入有误",
        });
      }else {
        this.formItem.routeData.push({
          destination:this.formItem.destination,
          nexthop:this.formItem.nexthop,
        })
        this.formItem.destination = ""
        this.formItem.nexthop = ""
      }
    },
    // 主机路由删除
    deletRoutes(index){
      this.formItem.routeData.splice(index,1)
    },
    modalOK(){
      this.$refs.formItem.validate((valid) => {
        if(valid){
          this.disabled = true
          networkTableSubnetAdd({
            id: this.formItem.id,
            name: this.formItem.name,
            cidr: this.formItem.cidr,
            gateway_ip: this.formItem.gateway,
            enable_dhcp: this.formItem.dhcp,
            startip: this.formItem.dhcp?this.formItem.start:"",
            endip: this.formItem.dhcp?this.formItem.end:"",
            host_routes: this.formItem.routeData,
            ...(this.formItem.dns!==""?{dns_nameservers:this.formItem.dns}:""),
          }).then((callback) => {
            if (callback.data.msg == "ok") {
              this.model = false
              this.$emit("return-ok",'添加子网操作完成');
            } else if (callback.data.msg == "409") {
              this.disabled = false
              this.$emit("return-error",'网关地址不能出现在DHCP地址范围内');
            } else {
              this.disabled = false
              this.$emit("return-error",callback.data.msg);
            }
          }).catch((error) => {
            this.model = false;
            this.$emit("return-error",'添加子网操作失败');
          })
        }
      })
    },
    ipInSubnet(ip, subnet) {
      let [subnetIp, maskBits] = subnet.split('/');
      maskBits = parseInt(maskBits, 10);
      let ipBinary = ip.split('.').map(octet => parseInt(octet, 10).toString(2).padStart(8, '0')).join('');
      let subnetBinary = subnetIp.split('.').map(octet => parseInt(octet, 10).toString(2).padStart(8, '0')).join('');
      for(let i=0; i<maskBits; i++){
          if(ipBinary[i] != subnetBinary[i]){
              return false;
          }
      }
      return true;
    },
  }
}
</script>