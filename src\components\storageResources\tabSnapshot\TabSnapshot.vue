<style lang="less">
@import "../storageResources.less";
</style>
<template>
  <div class="snapshot_area">
    <Spin fix v-if="spinShow" size="large" style="color:#ef853a">
      <Icon type="ios-loading" size=50 class="demo-spin-icon-load"></Icon>
      <div style="font-size:16px;padding:20px">Loading...</div>
    </Spin>
    <div class="table-button-area">
      <div>
        <Button class="close_btn"  @click="deletClick(tableSelec)" v-show="powerAcitons.kuaizhaoshanchu"><span class="icon iconfont icon-close-circle"></span> 删除快照</Button>
      </div>
      <div v-show="powerAcitons.kuaizhaosousuo">
        <Input v-model="tablePageForm.search_str" search enter-button placeholder="请输入名称" style="width: 300px" @on-search="tableSearchInput" />
      </div>
    </div>
    <div class="table_currency_area" v-show="powerAcitons.kuaizhaoliebiao">
      <Table :columns="tableColumn" :data="tableData" @on-sort-change="sortColumn" @on-selection-change="tableChange">
        <!-- 大小 -->
        <template v-slot:size="{row}">
          <span>{{ row.size }} GB</span>
        </template>
        <!-- 状态 -->
        <template v-slot:status="{row}">
          <span>{{ statusConvert(row) }}</span>
        </template>
        <!-- 时间 -->
        <template v-slot:created_at="{row}">
          <span>{{ creatTime(row.created_at) }}</span>
        </template>
        <!-- 操作 -->
        <template v-slot:operation="{ row }">
          <Dropdown @on-click="dropdownClick($event, row)">
            <Button>配置 ▼</Button>
            <template #list>
              <DropdownMenu>
                <!-- <DropdownItem name="yzj">创建云主机</DropdownItem> -->
                <DropdownItem name="bjkz" v-show="powerAcitons.kuaizhaobianji">编辑快照</DropdownItem>
                <DropdownItem name="cjxj" v-show="powerAcitons.kuaizhaochuangjianxuniji">创建虚拟机</DropdownItem>
                <DropdownItem name="cjyp" v-show="powerAcitons.kuaizhaochuangjianyunyingpan">创建云硬盘</DropdownItem>
                <!-- <DropdownItem name="cjbf">创建备份</DropdownItem> -->
                <!-- <DropdownItem name="gxzt">更新状态</DropdownItem> -->
                <!-- <DropdownItem name="gxysj">更新元数据</DropdownItem> -->
                <DropdownItem name="sc" v-show="powerAcitons.kuaizhaoshanchu" style="color: red" divided
                  >删除</DropdownItem
                >
              </DropdownMenu>
            </template>
          </Dropdown>
        </template>
      </Table>
      <div class="pages" v-if="this.tableData.length>0">
        <!-- <Page :total="tableTotal" show-total show-sizer :page-size="tablePageForm.pagecount" placement=top :page-size-opts='[10, 20, 30, ]' @on-change="onPageChange" @on-page-size-change="onPageSizeChange" /> -->
        <Pagination  :total="tableTotal" :page-size="tablePageForm.pagecount"  @page-change="onPageChange" @page-size-change="onPageSizeChange"/>
      </div>
    </div>
    <!-- 编辑快照 -->
    <SnapEdit
      :tableRow="tableRow"
      :editTime="editTime"
      @return-ok="returnOK"
      @return-error="returnError"
    ></SnapEdit>
    <!-- 创建虚拟机 -->
    <SnapCreateVM
      :tableRow="tableRow"
      :vmTime="vmTime"
      @return-ok="returnOK"
      @return-error="returnError"
    ></SnapCreateVM>
    <!-- 创建虚云硬盘 -->
    <SnapCreateDrive
      :tableRow="tableRow"
      :vmDrive="vmDrive"
      @return-ok="returnOK"
      @return-error="returnError"
    ></SnapCreateDrive>
    
    <!-- 删除快照 -->
    <SnapDelet
      :tableDelet="tableDelet"
      :deleteTime="deleteTime"
      @return-ok="returnOK"
      @return-error="returnError"
    ></SnapDelet>
  </div>
</template>
<script>
import { powerCodeQuery } from '@/api/other'; // 权限可用性 查询

import {
  snapshotQuery, // 快照 查询
} from '@/api/storage';
import Pagination from '@/components/public/Pagination.vue';
import SnapEdit from "./SnapEdit.vue"; // 编辑快照
import SnapCreateVM from "./SnapCreateVM.vue"; // 快照建虚拟机
import SnapCreateDrive from "./SnapCreateDrive.vue"; // 快照建虚拟机
import SnapDelet from "./SnapDelet.vue"; // 快照删除
export default {
  components: {
    Pagination,
    SnapEdit,
    SnapCreateVM,
    SnapCreateDrive,
    SnapDelet,
  },
  props:{
    tabName:String
  },
  watch: {
    tabName(value) {
      if(value=='快照'){
        this.actionQuery()
      }
    },
  },
  mounted() {
    if(this.$store.state.power.storageResourceTab == '快照') {
      this.actionQuery()
    }
  },
  data() {
    return {
      spinShow:false,
      tableColumn: [],
      tableData:[],
      tableSelec:[],
      // 快照表格数据需求
      tablePageForm: {
        page: 1, // 当前页
        pagecount: this.$store.state.power.pagecount, // 每页条
        search_str: "", // 收索
        order_type: "asc", // 排序规则
        order_by: "", // 排序列
      },
      // 快照分页总条数
      tableTotal: 0,

      tableRow: {},
      editTime: '', // 编辑快照
      vmTime: '', // 快照创建虚拟机
      vmDrive: '', // 快照创建云硬盘
      tableDelet: [],
      deleteTime: '',// 删除快照

      powerAcitons: {}, // 操作权限数据
    };
  },
  updated() {
    this.tablePageForm.pagecount=this.$store.state.power.pagecount
  },
  methods: {
    cloumnManage(){
      this.tableColumn = [
        { type: "selection", width: 30, align: "center"},
        { type: "expand", width: 50,
          render: (h, params) => {
            return h("div",{style:{width:'100%'}},[
              h("span",{style:{width:'45%',display: 'inline-block'}},'ID：'+params.row.id),
              // h("span",'名称：'+params.row.name)
            ]);
          },
        },
        // { title: "主机", key: "host" },
        { title: "名称", key: "name",sortable: 'custom',tooltip:true },
        // { title: "描述", key: "miaoshu" },
        { title: "大小", key: "size", align: "center", slot: "size" },
        { title: "状态", key: "status", align: "center", slot: "status" },
        // { title: "组快照", key: "group" },
        { title: "创建时间", key: "created_at", slot: "created_at" },
        { title: "云硬盘名称", key: "volume_name", align: "center",tooltip:true },
        ...(this.operationShow()?[{ title: "操作", key: "operation",width:120,slot: "operation" }]:[]),
      ]
    },
    // 获取数据
    snapshotGET(){
      this.spinShow = true
      this.tableSelec=new Array()
      snapshotQuery(this.tablePageForm).then(callback =>{
        this.spinShow = false
        if(callback.data.msg == "ok"){
          this.tableTotal =callback.data.total
          this.tableData = callback.data.data
        }else{
          this.$Message.error({
            background: true,
            closable: true,
            duration: 5,
            content: '查询快照表失败'
          });
        }
      }).catch(error=>{
        this.spinShow = false
      })
    },
    // 操作下拉点击
    dropdownClick(event,row){
      switch (event) {
        case "yzj":
          // 创建云主机
          break;
        case "bjkz":
          this.tableRow = row
          this.editTime = "" + new Date()
          break;
        case "cjxj":
          this.tableRow = row
          this.vmTime = "" + new Date()
          break;
        case "cjyp":
          this.tableRow = row
          this.vmDrive = "" + new Date()
          break;
        case "cjbf":
          // 创建备份
          break;
        case "gxzt":
          // 更新状态
          break;
        case "gxysj":
          // 更新元数据
          break;
        case "sc":
          this.deletClick([row])
          break;
      }
    },
    // 快照表格选中数据
    tableChange(arr){
      this.tableSelec = arr
    },
    // 删除快照事件
    deletClick(data) {
      if(data == 0) {
        this.$Message.warning({
          background: true,
          closable: true,
          duration: 5,
          content: '未选择表格数据'
        });
      }else{
        this.tableDelet = data
        this.deleteTime = '' + new Date()
      }
    },
    // 当前分页
    onPageChange(item) {
      this.tablePageForm.page = item;
      this.snapshotGET();
    },
    // 每页条数
    onPageSizeChange(item) {
      this.$store.state.power.pagecount = item
      this.tablePageForm.pagecount = this.$store.state.power.pagecount
      this.tablePageForm.page=1
      this.tableTotal = 0
        this.tablePageForm.search_str = ""
      this.snapshotGET();
    },
    // 快照列表 列排序
    sortColumn(column, key, order) {
      if (column.key !== "normal") {
        this.tablePageForm.order_by = column.key;
        this.tablePageForm.order_type = column.order;
        this.tablePageForm.page=1
        this.tableTotal = 0
        this.tablePageForm.search_str = ""
        this.snapshotGET();
      }
    },
    // 搜索快照列表
    tableSearchInput(){
      this.tablePageForm.page=1
      this.tableTotal = 0
      this.snapshotGET();
    },
    // 时间转换
    creatTime(time){
      let tim = new Date(time);
      let time_stamp = tim.getTime()
      let local_stamp = time_stamp+(8 * 60 * 60 * 1000)
      let date = new Date(local_stamp)
      let year = date.getFullYear();
      let month = date.getMonth() + 1;
      let day = date.getDate();
      let hours = date.getHours();
      let minutes = date.getMinutes();
      let seconds = date.getSeconds();
      // 格式化月、日、时、分、秒为两位数
      month = month < 10 ? `0${month}` : month;
      day = day < 10 ? `0${day}` : day;
      hours = hours < 10 ? `0${hours}` : hours;
      minutes = minutes < 10 ? `0${minutes}` : minutes;
      seconds = seconds < 10 ? `0${seconds}` : seconds;

      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },
    // 状态转换
    statusConvert(row){
      let text = row.status
      switch (row.status) {
        case "available":
          text = '可用'
          break;
        case "error_deleting":
          text = '错误'
          break;
        case "error":
          text = '错误'
          break;
      }
      return text
    },
    // 子组件返回 更新表数据无loding
    returnError(data){
      this.$Message.error({
        background: true,
        closable: true,
        duration: 5,
        content: data,
      });
    },
    returnOK(data) {
      setTimeout(() => {
        this.snapshotGET()
      }, 1000);
      this.$Message.success({
        background: true,
        closable: true,
        duration: 5,
        content: data,
      });
    },
    // 操作列
    operationShow(){
      let list = false
      if(
        this.powerAcitons.kuaizhaobianji ||
        this.powerAcitons.kuaizhaochuangjianxuniji ||
        this.powerAcitons.kuaizhaochuangjianyunyingpan ||
        this.powerAcitons.kuaizhaoshanchu
      ){
        list = true
      }
      return list
    },
    // 操作权限获取
    actionQuery(){
      powerCodeQuery({
        module_code:[
          'kuaizhaoliebiao',
          'kuaizhaosousuo',
          'kuaizhaofenpei',
          'kuaizhaoshanchu',
          'kuaizhaobianji',
          'kuaizhaohuigun',
          'kuaizhaochuangjianxuniji',
          'kuaizhaochuangjianyunyingpan',
        ]
      }).then(callback=>{
        this.powerAcitons = callback.data.data
        this.cloumnManage()
        this.powerAcitons.kuaizhaoliebiao?this.snapshotGET():null
      })
    },
  }
};
</script>
