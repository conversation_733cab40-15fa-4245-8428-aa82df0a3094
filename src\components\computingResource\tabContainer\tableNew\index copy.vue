<template>
  <div>
    <Modal
      v-model="initialmodal"
      width="800"
      :mask-closable="false"
    >
      <p slot="header">
        <span>新建容器</span>
      </p>
      <div class="vm_new_dialog">
        <Menu theme="light" :active-name="activeNumber" @on-select="modalMenuItem">
          <Menu-item :name="i" v-for="(item, i) in menniuItemData" :key="i">
            <span>{{ item }}</span>
          </Menu-item>
        </Menu>
        <div class="modalcontent">
          <Basic v-show="activeNumber == 0" :times='time0' @returnOK='returnOK'></Basic>
          <Spec v-show="activeNumber == 1" :times='time1' @returnOK='returnOK'></Spec>
          <Volumes v-show="activeNumber == 2" :times='time2' @returnOK='returnOK'></Volumes>
          <Net v-show="activeNumber == 3" :times='time3' @returnOK='returnOK'></Net>
          <Ports v-show="activeNumber == 4" :times='time4' @returnOK='returnOK'></Ports>
          <Security v-show="activeNumber == 5" :times='time5' @returnOK='returnOK'></Security>
          <Miscellaneous v-show="activeNumber == 6" :times='time6' @returnOK='returnOK'></Miscellaneous>
          <Labels v-show="activeNumber == 7" :times='time7' @returnOK='returnOK'></Labels>
        </div>
      </div>
      <div slot="footer">
        <Button v-if="activeNumber>0" type="primary" @click="activeNumber--">上一步</Button>
        <Button type="text" @click="initialmodal = false">取消</Button>
        <Button v-if="activeNumber<7" type="primary" @click="activeNumber++">下一步</Button>
        <Button v-if="activeNumber==7" type="primary" :loading="loading" @click="modalOK">
          <span v-if="!loading">确认</span>
          <span v-else>创建中</span>
        </Button>
      </div>
    </Modal>
  </div>
</template>
<script>
import Basic from "./Basic.vue";
import Spec from "./Spec.vue";
import Volumes from "./Volumes.vue";
import Net from "./Net.vue";
import Ports from "./Ports.vue";
import Security from "./Security.vue";
import Miscellaneous from "./Miscellaneous.vue";
import Labels from "./Labels.vue";
import {

} from '@/api/virtualMachine';
export default {
  components: {
    Basic,
    Spec,
    Volumes,
    Net,
    Ports,
    Security,
    Miscellaneous,
    Labels,
  },
  props: {
    newTime: String,
  },
  watch: {
    newTime(news){
      this.initialmodal = true
      this.loading = false
      this.activeNumber = 0
    },
    activeNumber(news) {
      console.log('aaaa',news)
    }
    // activeNumber:{
    //   handler(news, old) {
    //     let carousel = document.querySelector("#lunbo");
    //     carousel.style.transform = `translateX(-${news * 600}px)`;
    //   },
    //   // deep:true,
    //   // immediate:true,
    // },
  },
  data(){
    return {
      initialmodal: false,
      loading: false, // 禁止多次触发
      menniuItemData: ['基本信息','Spec','Volumes','网络','Ports','安全组','Miscellaneous','Labels'],
      activeNumber: 0,

      time0: '',
      time1: '',
      time2: '',
      time3: '',
      time4: '',
      time5: '',
      time6: '',
      time7: '',
    }
  },
  methods: {
    // 导航点击
    modalMenuItem(item) {
      this.activeNumber = item;
    },
    modalOK() {

    },
    // 返回参数
    returnOK(data) {
      if(data.page == 0) {

      }
    },
  }
}
</script>
<style scoped lang="less">
.vm_new_dialog {
  height: 500px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  .ivu-menu-light {
    width: 160px!important;
    min-width: 160px!important;
    max-width: 160px!important;
  }
}
.modalcontent {
  width: calc(100% - 180px);
  height: 100%;
  overflow: hidden;
}
</style>
