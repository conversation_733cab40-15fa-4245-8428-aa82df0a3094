<style lang="less">
@import "./physicsTab.less";
</style>>
<template>
  <div class="compute_cluster">
    <!-- 侧边虚拟机文件夹组 -->
    <Menu theme="light" :active-name="activeName" @on-select="menuSelect">
      <p>计算集群组</p>
      <div class="physical_unit">
        <Tooltip content="新建集群组" placement="top-start" v-show="powerAcitons.jisuanjiqunzutianjia">
          <Button type="text" @click="newJQclick" style="padding: 0px" icon="md-add-circle"></Button>
        </Tooltip >
        <Tooltip content="编辑集群组" placement="top" v-show="powerAcitons.jisuanjiqunzubianji">
          <Button type="text" @click="editJQclick" style="padding: 0px" icon="ios-create" :disabled="disableButton"></Button>
        </Tooltip >
        <Tooltip content="刷新集群组" placement="top" v-show="powerAcitons.jisuanjiqunzushuaxin">
          <Button type="text" @click="refreshJQ" style="padding: 0px" :loading="loadRefresh">
            <span v-if="!loadRefresh"><Icon type="md-refresh" /></span>
            <span v-else></span>
          </Button>
        </Tooltip >
        <Tooltip content="删除集群组" placement="top" v-show="powerAcitons.jisuanjiqunzushanchu">
          <Button type="text" @click="deletJQ" style="padding: 0px" icon="ios-trash" :disabled="disableButton"></Button>
        </Tooltip >
      </div>
      <MenuItem name='0'>所有主机</MenuItem>
      <Menu-item
        v-for="(item, i) in colonyData"
        :name="item.id.toString()"
        :key="i"
        class="mennuclass"
      >
        <Tooltip :content="item.name" placement="right">
          <span class="text_overflow menuspan">{{ item.name }}</span>
        </Tooltip >
        <!-- 版本隐藏 -->
        <Tooltip placement="bottom" v-if="moduleShow('migrate')">
          <Icon :class='item.id==activeName?"allow":"prohibit"' type="md-flower" @click.stop='migrateClick(item)'/>
           <template #content>
              <span>迁移配置</span>
           </template>
        </Tooltip>
      </Menu-item>
    </Menu>
    <!-- 主机列表数据 -->
    <div class="host_table_area">
      <p>主机列表</p><span style="display:block;border-top:1px solid #ccc;padding-bottom:6px;margin-left:-10px"></span>
      <div class="table-button-area">
        <div>
          <Button class="move_in" :disabled="disableButton" @click="moveClick" v-show="powerAcitons.yiruzhuji"><span class="icon iconfont icon-a-17Edaoru2"></span> 移入主机</Button>
          <Button class="remove" :disabled="disableButton" @click="removClick" v-show="powerAcitons.yichuzhuji"><span class="icon iconfont icon-a-17Hdaochu"></span> 移出主机</Button>
        </div>
        <div></div>
      </div>
      <div v-show="powerAcitons.zhujiliebiao" class="host_table_box">
        <Spin fix v-if="spinShowTable" size="large" style="color:#ef853a">
          <Icon type="ios-loading" size=50 class="demo-spin-icon-load"></Icon>
          <div style="font-size:16px;padding:20px">Loading...</div>
        </Spin>
        <Table :columns="tableColumn" :data="tableData" @on-selection-change="tableChange">
          <!-- 名称 -->
          <template v-slot:hypervisor_hostname="{row}">
            <span v-if="row.state == 'down'|| row.status == 'disabled'">{{ row.hypervisor_hostname }}</span>
            <a v-else @click="nameClick(row)">{{ row.hypervisor_hostname }}</a>
          </template>
          <!-- CPU使用率 -->
          <template v-slot:cpu_percent="{row}">
            <span v-if="row.state == 'down'">-</span>
            <Progress v-else :percent="progressDataConversion('number',row.cpu_percent)" :stroke-color="progressDataConversion('color',row.vcpus)" />
          </template>
          <!-- 内存使用率 -->
          <template v-slot:memory_mb_used="{row}">
            <span v-if="row.state == 'down'">-</span>
            <Progress v-else :percent="progressDataConversion('number',row.memory_mb_used/row.memory_mb*100)" :stroke-color="progressDataConversion('color',row.memory_mb_used/row.memory_mb*100)" />
          </template>
          <!-- 运行时间 -->
          <template v-slot:time="{row}">
            <span v-if="row.state == 'up'&& row.status == 'enabled'">{{ row.time }}</span>
            <span v-else>-</span>
          </template>
          <!-- CPU -->
          <template v-slot:vcpus="{row}">
            <span v-if="row.state == 'up'&& row.status == 'enabled'">{{ row.vcpus }}</span>
            <span v-else>-</span>
          </template>
          <!-- 内存 -->
          <template v-slot:memory_mb="{row}">
            <span v-if="row.state == 'up'&& row.status == 'enabled'">{{ byteUnitConversion("",row.memory_mb) }}</span>
            <span v-else>-</span>
          </template>
          <!-- 虚拟机 -->
          <template v-slot:running_vms="{row}">
            <span v-if="row.state == 'up'&& row.status == 'enabled'">{{ row.running_vms }}</span>
            <span v-else>-</span>
          </template>
          <!-- 健康状态 -->
          <template v-slot:state="{row}">
            <span v-if="row.state == 'up'">良好</span>
            <a v-else-if="row.state == 'down'" @click="downtimeClick(row)">宕机迁移</a>
            <span v-else>-</span>
          </template>
          <!-- 终端 -->
          <template v-slot:terminal="{row}">
            <Button v-if="row.state == 'up'&& row.status == 'enabled'" class="plus_btn" @click="terminalClick(row)">终端</Button>
            <span v-else>-</span>
          </template>
          <!-- 运行状态 -->
          <template v-slot:status="{row}">
            <span v-if="row.state == 'up'&& row.status == 'enabled'">已启动</span>
            <span v-else-if="row.state == 'down'&& row.status == 'enabled'">未启动</span>
            <span v-else style="color:red">已宕机</span>
          </template>
          <!-- 操作 -->
          <template v-slot:operation="{row}">
            <Dropdown class="dropdstyle"  @on-click="dropdownClick($event,row)">
              <Button>配置 ▼</Button>
              <template #list>
                <DropdownMenu>
                  <DropdownItem name="kj">开机</DropdownItem>
                  <DropdownItem name="gj">关机</DropdownItem>
                  <DropdownItem name="ss">疏散</DropdownItem>
                  <DropdownItem name="pz">配置</DropdownItem>
                  <DropdownItem name="wl">网络</DropdownItem>
                  <DropdownItem name="sj">时间</DropdownItem>
                  
                </DropdownMenu>
              </template>
            </Dropdown>
          </template>
        </Table>
      </div>
    </div>
    <!-- 迁移配置  -->
    <GroupMigrate
      :migrateTime="migrateTime"
      :groupSelect="groupSelect"
      @custom-error="customError"
      @custom-ok="customOK"
    ></GroupMigrate>
    <!-- 新建集群组弹框 -->
    <GroupNew
      :newTime="newTime"
      @custom-error="customError"
      @custom-ok="customOK"
    ></GroupNew>
    <!-- 编辑集群组弹框 -->
    <GroupEdit
      :editTime="editTime"
      :groupSelect="groupSelect"
      @custom-error="customError"
      @custom-ok="customOK"
    ></GroupEdit>
    <!-- 主机移入 -->
    <HostMove
      :moveTime="moveTime"
      :groupSelect="groupSelect"
      @host-error="hostError"
      @host-ok="hostOK"
    ></HostMove>
    </Modal>
    <!-- 主机移出 -->
    <HostRemove
      :removeTime="removeTime"
      :groupSelect="groupSelect"
      :tableSelect="tableSelect"
      @host-error="hostError"
      @host-ok="hostOK"
    ></HostRemove>
    </Modal>
    <!-- 主机详情抽屉 -->
    <Physicsetails :detailstime="detailstime" :tableRow="tableRow"></Physicsetails>
    <!-- 终端弹框 -->
    <Physicsterminator :terminaltime="terminaltime"></Physicsterminator>
  </div>
</template>
<script>
import { powerCodeQuery } from '@/api/other'; // 权限可用性 查询

import {
  physicsTableAllQuery, // 物理机全部 查询
  manualDowntimeMigration, // 手动宕机迁移
  hostListQuery, // 主机列表 查询
  clusterHostQuery, // 集群主机 查询
  clusterGroupDelete, // 集群组 删除
} from '@/api/physics';
import GroupMigrate from './GroupMigrate.vue'; // 组 迁移
import GroupNew from './GroupNew.vue'; // 组 新建
import GroupEdit from './GroupEdit.vue'; // 组 编辑
import HostMove from './HostMove.vue'; // 主机 移入
import HostRemove from './HostRemove.vue'; // 主机 移出

import Physicsetails from './Physicsetails.vue'; // 详情
import Physicsterminator from './Physicsterminator.vue'; // 终端弹框
export default {
  components: {
    GroupMigrate,
    GroupNew,
    GroupEdit,
    HostMove,
    HostRemove,
    Physicsetails,
    Physicsterminator,
  },
  props: {
    tabSelected: String,
  },
  watch: {
    tabSelected(value) {
      if(value=='物理资源'){
        this.actionQuery()
      }
    },
  },
  data() {
    return {
      groupSelect: {}, // 组 选中
      migrateTime: '', // 组 迁移
      newTime: '', // 组 新建
      editTime: '', // 组 编辑
      loadRefresh:false, // 组 刷新
      moveTime: '', // 主机 移入
      removeTime: '', // 主机 移出
      tableRow: {}, // 主机 表格行数据
      detailstime: '', // 主机抽屉
      terminaltime: '', // 终端

      activeName: "0", // 集群组 选中数据
      colonyData: [], // 集群组数据
      disableButton: true, // 禁用规则
     
      // 主机表数据
      spinShowTable:false,
      tableColumn: [],
      tableData: [],
      tableSelect: [],
      
      powerAcitons: {}, // 操作权限数据
    };
  },
  mounted() {
    if(this.$store.state.power.computingResourceTab == '物理资源') {
      this.actionQuery()
    }
  },
  methods: {
     cloumnManage(){
      this.tableColumn=[
        { type: 'selection',width: 30,align: 'center' },
        { title: "主机名", sortable: true, minWidth: 100, key: "hypervisor_hostname", slot: 'hypervisor_hostname' },
        { title: "IP", key: "host_ip",align: "center",sortable: true,minWidth: 120 },
        { title: "CPU使用率", key: "cpu_percent", align: "center", minWidth: 90, slot: 'cpu_percent' },
        { title: "内存使用率", key: "memory_mb_used", align: "center", minWidth: 90, slot: 'memory_mb_used' },
        { title: "运行时间", key: "time",align: 'center',minWidth: 95, slot: 'time' },
        { title: "CPU", key: "vcpus",align: 'center',minWidth: 40, slot: 'vcpus' },
        { title: "内存", key: "memory_mb",align: 'center',minWidth: 95, slot: 'memory_mb' },
        { title: "虚拟机", key: "running_vms",align: 'center' ,minWidth: 60, slot: 'running_vms' },
        { title: "健康状态", key: "state",align: 'center',width: 90, slot: 'state' },
        ...(this.powerAcitons.zhongduan?[{ title: "终端", key: "terminal",align: 'center',width: 70, slot: 'terminal' }]:[]),
        { title: "运行状态", key: "status",align: 'center',width: 70, slot: 'status' },
        // { title: "操作", key: "operation",width:120,  slot: 'operation' }
      ]
    },
    // 获取集群组
    refreshJQ() {
      this.loadRefresh = true
      physicsTableAllQuery().then(em=>{
        setTimeout(() => {
          this.loadRefresh = false
          this.colonyData = em.data
        }, 500);
      })
    },
    // 集群组选中事件
    menuSelect(item) {
      this.activeName = item
      if(item=="0") {
        this.hostList()
        this.disableButton = true
      }else{
        this.hostTable(item)
        this.disableButton = false
      }
    },
    // 获取全部主机（所有主机）
    hostList() {
      this.spinShowTable =true;
      hostListQuery()
      .then(callback=>{
        this.spinShowTable =false;
        this.tableData = callback.data
      })
      .catch((error) => {
        this.spinShowTable =false;
      });
    },
    
    // 获取集群组选中后的主机列表
    hostTable(item){
      this.spinShowTable =true;
      clusterHostQuery(item)
      .then(callback=>{
        this.spinShowTable =false;
        this.tableData = callback.data
        this.tableSelect = new Array()
      })
    },
    // 主机选中数据
    tableChange(item) {
      this.tableSelect = item
    },
    // 迁移配置
    migrateClick(item){
      if(this.activeName == item.id &&this.tableData.length>0) {
        this.groupSelect = item
        this.migrateTime = "" + new Date()
      }
    },
    // 新建集群组 按钮
    newJQclick(){
      this.newTime = "" + new Date()
    },
    // 编辑集群组 按钮
    editJQclick() {
      this.selectCluster().then(result=>{
        this.groupSelect = result
        this.editTime = "" + new Date()
      })
    },
    // 删除集群组 按钮
    deletJQ() {
      let list = ""
      this.colonyData.forEach(em=>{
        if(em.id==this.activeName) {
          list = em.name
        }
      })
      this.$Modal.confirm({
        title: '删除集群组',
        content: '<p>是否删除 <span style="font-weight: 600;color:red;word-wrap: break-word;">'+list+'</span> 集群组?</p>',
        onOk: () => {
          clusterGroupDelete({data:{id:this.activeName,name:list}})
          .then(callback=>{
            this.refreshJQ()
            this.menuSelect("0")
          })
        },
        onCancel: () => {}
      });
    },
    // 移入主机按钮事件
    moveClick() {
      this.selectCluster().then(result=>{
        this.groupSelect = result
        this.moveTime = "" + new Date()
      })
    },
    // 移出选中主机
    removClick() {
      if(this.tableSelect.length!==0) {
        this.selectCluster().then(result=>{
          this.groupSelect = result
          this.removeTime = "" + new Date()
        })
      }else{
        this.hostError('请先选择要移出的主机，再进行移出主机操作')
      }
    },
    // 主机详情
    nameClick(row) {
      this.tableRow = row;
      this.detailstime = ""+new Date();
    },
    // 进度条数据转换
    progressDataConversion(type,data){
      let number = parseFloat(data.toFixed(1))
      if(type == 'number') {
        return number>100?99:number
      }else {
        return number>70?["#f45d3f","#f45d3f"]:["#2ebe76","#2ebe76"]
      }
    },
    // 宕机迁移
    downtimeClick(row){
      this.$Modal.confirm({
        title: '手动宕机迁移操作',
        content:'<p>是否手动对云主机 <span style="font-weight: 600;color:green;">'+row.hypervisor_hostname+'</span> 进行<span style="font-weight: 600;color:red;">宕机迁移</span>操作？</p>',
        onOk: () => {
          manualDowntimeMigration({host:row.hypervisor_hostname})
          .then(callback =>{
            if (callback.data.msg == 'ok') {
              message.success('手动宕机迁移操作已运行')
            } else {
              message.error('手动宕机迁移操作操作失败:' + callback.msg)
            }
          })
          .catch((error) => {
            message.error('手动宕机迁移操作操作失败')
          })
        }
      });
    },
    // 终端
    terminalClick(row) {
      this.terminaltime = row.host_ip+"/"+new Date()
    },
    // 表格列操作
    dropdownClick(event,row){
      switch (event) {
        case "kj":
          console.log("未开发")
          break;
        case "gj":
          console.log("未开发")
          break;
        case "ss":
          console.log("未开发")
          break;
        case "pz":
          // 重定向组
          console.log("未开发")
          break;
        case "wl":
          console.log("未开发")
          break;
        case "sj":
          console.log("未开发")
          break;
      }
    },
    // 判断选中集群
    selectCluster(){
      return new Promise((resolve, reject) => {
        let list = {
          name:"",
          id:null,
        }
        this.colonyData.forEach(em=>{
          if(em.id == this.activeName) {
            list.name = em.name
            list.id = em.id
          }
        })
        resolve(list)
      })
    },
    // 字节+单位转换
    byteUnitConversion(type,size) {
      const units = ['MB','GB', 'TB', 'PB'];
      let unitIndex = 0;
      while (size >= 1024 && unitIndex < units.length - 1) {
          size /= 1024;
          unitIndex++;
      }
      if(type=="byte") {
        return Math.floor(size * 100) / 100 
      }else if(type=="unit") {
        return units[unitIndex] 
      }else {
        return Math.floor(size * 100) / 100 + ' ' + units[unitIndex];
      }
    },
    // 主机移入移出返回
    hostError(data) {
      this.$Message.error({
        background: true,
        closable: true,
        duration: 5,
        content: data,
      });
    },
    // 主机移入移出返回
    hostOK(data) {
      this.hostTable(this.activeName)
      this.$Message.success({
        background: true,
        closable: true,
        duration: 5,
        content: data,
      });
    },
    // 子组件返回错误
    customError(data) {
      this.$Message.error({
        background: true,
        closable: true,
        duration: 5,
        content: data,
      });
    },
    // 子组件返回成功
    customOK(data) {
      this.refreshJQ();
      this.$Message.success({
        background: true,
        closable: true,
        duration: 5,
        content: data,
      });
    },
    // 操作权限获取
    actionQuery(){
      powerCodeQuery({
        module_code:[
          'zhujiliebiao',
          'zhongduan',
          'jisuanjiqunzutianjia',
          'jisuanjiqunzubianji',
          'jisuanjiqunzushuaxin',
          'jisuanjiqunzushanchu',
          'yiruzhuji',
          'yichuzhuji',
        ]        
      }).then(callback=>{
        this.powerAcitons = callback.data.data
        this.cloumnManage()
        if(this.powerAcitons.zhujiliebiao) {
          this.refreshJQ()
          if(this.activeName==0) {
            this.hostList()
            this.disableButton = true
          }else{
            this.hostTable(this.activeName)
            this.disableButton = false
          }
        }
      })
    },
    // 模块显示隐藏判断
    moduleShow(item){
      return window.gurl.PageModule.indexOf(item) == -1?false:true
    },
  },
};
</script>
