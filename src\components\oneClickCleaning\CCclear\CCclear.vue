<style lang="less">
@import "./CCclear.less";
</style>
<template>
  <div class="one_click_clear">
    <div class="one_click_title"><p>一键存储清理</p></div>
    <div v-if="textBox" class="one_click_describe">
      <img src="../../../assets/rws.png" alt="">
      <ul>
        <li><h1>一键存储清理</h1></li>
        <li><p>存储清理用于扫描和检测虚拟机和后端存储文件的关联关系，对于未被任何虚拟机挂载的存储文件，同时又不是虚拟机操作系统安装包(ISO)以及虚拟化驱动程序的文件，我们将其视为空闲存储文件，您可以对扫描出的空闲的存储文件进行选择性的清理，以释放存储空间。请单击[开始扫描]按钮，开始扫描系统中所有存储内的空闲文件。</p></li>
        <li>
          <Button class="plus_btn" @click="startSM">开始扫描</Button>
        </li>
      </ul>
    </div>
    <div v-if="imgBox" class="one_click_picture">
      <img src="../../../assets/leidatu.gif" alt="">
    </div>
    <div v-if="selectedBox" class="one_click_slected">
      <div class="clear_slected_area">
        <img src="../../../assets/rws.png" alt="">
        <div class="clear_data_area">
          <div class="release_storage">
            <p><span>{{releaseTotal}}MB</span> </br> 总共可释放存储</p>
            <p><span>{{releaseAvailable}}MB</span> </br> 已选可释放存储</p>
          </div>
          <div class="clear_btn_area">
            <Button class="plus_btn" @click="clearClick" :disabled="clearStatus">一键清理</Button>
            <Button class="plus_btn" @click="allClick">
              <span v-if="selectStatus">全选</span>
              <span v-else>清空</span>
            </Button>
            <Button class="plus_btn" @click="startSM">重新扫描</Button>
          </div>
        </div>
      </div>
      <div class="clear_data">
        <CheckboxGroup v-model="clearCheckbox" @on-change="groupClick">
          <div v-for="item in arrayCheckbox" :key="item.key" class="clear_result_area">
            <Checkbox :label="item.storage">
              <span>{{item.title}}</span>
            </Checkbox>
            <div>
              {{item.title}}+测试数据, <span>占用空间：{{item.storage}} MB</span>
            </div>
          </div>
        </CheckboxGroup>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      textBox:true, // 第1页
      imgBox:false, // 第2页
      selectedBox:false, // 第3页
      clearStatus:true, // 一键清理是否禁用
      selectStatus:true, // 全选/反选默认显示
      releaseTotal:0, // 总共可释放存储
      releaseAvailable:0, // 已选可释放存储

      arrayCheckbox:[],
      clearCheckbox:[],
    }
  },
  watch: {
    clearCheckbox: {
      handler(news) {
        if(news.length>0){
          this.clearStatus = false
        }else {
          this.clearStatus = true
        }
      }
    }
  },
  methods: {
    // 开始扫描/重新扫描
    startSM(){
      this.textBox = false // 第1页隐藏
      this.imgBox = true // 第2页显示
      this.selectedBox = false // 第3页隐藏
      this.smTime()
      this.clearCheckbox = new Array()
    },
    // 扫描页面过度
    smTime(){
      setTimeout(() =>{
        this.imgBox = false // 第2页隐藏
        this.selectedBox = true // 第3页显示
        this.selectStatus = true // 初始全选按钮
        this.clearStatus = true // 初始一键清理按钮
        this.clearData()
      },300)
    },
    // 一键清理
    clearClick(){
      // 清理完成之后跳转至默认界面
      this.textBox = true // 第1页显示
      this.selectedBox = false // 第3页隐藏
    },
    // 全选/反选
    allClick(){
      this.selectStatus = !this.selectStatus
      if(this.selectStatus){
        // [反选]状态 一键清理 按钮 ×
        this.releaseAvailable = 0
        this.clearCheckbox = new Array()
      }else {
        // [全选]状态 一键清理 按钮 √
        this.clearCheckbox = new Array()
        this.arrayCheckbox.forEach(em=>{
          this.clearCheckbox.push(em.storage)
        })
        this.releaseAvailable=this.releaseTotal
      }
    },
    // 选框勾选事件
    groupClick(item){
      let numbers = 0
      item.forEach(em=>{
       numbers=numbers+parseFloat(em)
      })
      this.releaseAvailable = numbers.toFixed(2)
    },
    // 一键存储清理 可选数据
    clearData(){
      this.arrayCheckbox = new Array()
      let lists = (Math.random()*100).toFixed(0)
      for(var i=0;i<lists;i++){
        this.arrayCheckbox.push(
          { title:'题目'+i,key:i,storage:(Math.random()*1000).toFixed(2) }
        )
      }
      let listNumber = 0
      this.arrayCheckbox.forEach(em=>{
        listNumber=listNumber+parseFloat(em.storage)
      })
      this.releaseTotal=listNumber.toFixed(2)
      this.releaseAvailable = 0
    }
  },
}
</script>