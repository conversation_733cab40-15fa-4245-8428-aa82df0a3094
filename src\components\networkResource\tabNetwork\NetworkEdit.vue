<template>
  <Modal v-model="model" width="600" :mask-closable="false">
    <template #header><p>编辑网络</p></template>
    <Form
      :model="formItem"
      ref="formItem"
      :rules="rulesForm"
      :label-width="120"
    >
      <FormItem label="当前网络名称" prop="name">
        <Input
          v-model="tableRow.name" disabled
        ></Input>
      </FormItem>
      <FormItem label="网络新名称" prop="name">
        <Input
          v-model="formItem.name"
          placeholder="输入网络新名称"
        ></Input>
      </FormItem>
    </Form>
    <div slot="footer">
      <Button type="text" @click="model = false">取消</Button>
      <Button type="primary" @click="modelOK" :disabled="disabled">确认</Button>
    </div>
  </Modal>
</template>
<script>
import {networkTableModify} from '@/api/network';

export default {
  props: {
    tableRow: Object,
    nameAll: Array,
    nameTime: String,
  },
  watch: {
    nameTime(news){
      this.formItem.id = this.tableRow.id
      // this.formItem.title = this.tableRow.name+' 网络修改名称'
      this.$refs.formItem.resetFields();
      this.model = true
      this.disabled = false
    }
  },
  data(){
    // 网络名称
    const propName = (rule, value, callback) => {
      let list = /^[\u4e00-\u9fa5_a-zA-Z0-9@_.-]{2,32}$/;
      if (list.test(value)) {
        this.nameAll.indexOf(value) == -1
          ? callback()
          : callback(new Error("名字已使用"));
      } else {
        callback(new Error("请输入2-32个中文或字符（特殊字符可用@_.-）"));
      }
    };
    return {
      model: false,
      disabled: false,
      formItem:{
        title:'',
        id: '',
        name:'',
      },
      
      // 正则验证
      rulesForm: {
        name: [
          { required: true, message: "必填项", trigger: "change" },
          { validator: propName, trigger: "change" },
        ],
      },
    }
  },
  methods: {
    // 编辑网络确认事件
    modelOK() {
      this.$refs.formItem.validate((valid) => {
        if (valid) {
          this.disabled = true;
          networkTableModify({
            id: this.formItem.id,
            name: this.formItem.name,
          })
          .then(callback => {
            this.$emit("return-ok",'修改网络名称完成');
            this.model = false;
          })
          .catch((error) => {
            this.$emit("return-error",'修改网络名称失败');
            this.disabled = false;
          });
        }
      });
    },
    
  }
}
</script>
