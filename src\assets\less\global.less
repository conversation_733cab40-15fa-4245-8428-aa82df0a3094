
/*单行限制文本*/
.ellipsis {
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  word-break: keep-all;
}

/*多行限制文本，具体限制行数根据具体情况设置 -webkit-line-clamp */
.m-ellipsis {
  overflow: hidden;
  white-space: pre-wrap;
  word-wrap: break-word;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
  // -webkit-line-clamp: 3;
  display: -webkit-box;
}

.hand {
  cursor: pointer!important;
}

.hide {
  display: none;
}

.emoji-char {
  font-size: 18px;
}

.colorWeak {
  filter: invert(80%);
}

.ant-layout {
  background-color: white;
  transition: all ease 300ms;
}

.ant-layout.layout-basic {
  height: 100vh;
  min-height: 100vh;
}

canvas {
  display: block;
}


.ant-layout-footer {
  display: none;
}

/******************************Vue Transition 动画定义 开始**********************************/

.scale-top-enter-active,
.scale-top-leave-active {
  transition: all ease 300ms;
}

.scale-top-enter,
.scale-top-leave-active {
  transform: scaleY(0.1);
  transform-origin: center top;
}


.scale-enter-active,
.scale-leave-active {
  transition: all ease 300ms;
}

.scale-enter,
.scale-leave-active {
  transform: scale(0.1, 0.1);
}


.slide-enter-active,
.slide-leave-active {
  transition: all ease 200ms;
}

.slide-enter,
.slide-leave-to {
  transform: translate3d(100%, 0, 0);
}


.fade-enter-active,
.fade-leave-active {
  transition: all ease 200ms;
}

.fade-enter,
.fade-leave-to {
  opacity: 0;
}

/******************************************************************************************************************************/
.back-white {
  background-color: #fff;
}

.padding20 {
  padding: 20px;
  padding-left: 32px;
  padding-right: 32px;
}

.display-flex {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flex1 {
  flex: 1;
}

.flex-top {
  display: flex;
  align-items: flex-start;
}

.color-white {
  color: white;
}
.color-red {
  color: red;
}
.font-weight {
  font-weight: bold;
}
.t-right{
  text-align:right
}
.t-center{
  text-align: center;
}
.blue-input {
  background-color: #f5f7f7;
  border-color: #f5f7f7;
}
.m-left5 {
  margin-left: 5px;
}

.m-top20 {
  margin-top: 20px;
}
.m-top10 {
  margin-top: 10px;
}
.m-top50{
  margin-top:50px;
}
.m-top5 {
  margin-top: 5px;
}
.m-left8 {
  margin-left: 8px;
}
.filter-tab .ant-tabs-bar {
  background-color: #fff;
  margin-bottom: 0px;
  padding-left: 20px;
  padding-top: 20px;
}

.font16{
  font-size: 16px;
}
.font18 {
  font-size: 18px;
}
.font24{
  font-size: 24px;
}
.font30{
  font-size: 30px;
}

.pointer{
  cursor: pointer;
}

@i:0;
.loop(@i)when(@i<10)
{
  .z-@{i} {
    z-index: @i * 10;
  }

  .loop(@i+1);
}
.loop(@i);

@maxVal:250;//最大上限
@step:5;//渐变值
// margin-left 
.sizeFun(@maxVal);
.sizeFun(@n, @i: 0) when (@i =< @n) {
  .m-@{i}{
    margin: 0px + @i;
  }
  .mt-@{i}{
    margin-top: 0px + @i;
  }
  .mr-@{i}{
    margin-right: 0px + @i;
  }
  .mb-@{i}{
    margin-bottom: 0px + @i;
  }
  .ml-@{i}{
    margin-left: 0px + @i;
  }

  .p-@{i}{
    padding: 0px + @i;
  }
  .pt-@{i}{
    padding-top: 0px + @i;
  }
  .pr-@{i}{
    padding-right: 0px + @i;
  }
  .pb-@{i}{
    padding-bottom: 0px + @i;
  }
  .pl-@{i}{
    padding-left: 0px + @i;
  }

  .w-@{i}{
    width: 0px + @i;
  }

  .min-w-@{i}{
    min-width: 0px + @i;
  }

  .h-@{i}{
    height: 0px + @i;
  }

  .min-h-@{i}{
    min-height: 0px + @i;
  }

  .fs-@{i} {
    font-size: 0px + @i;
  }
  .sizeFun(@n, (@i+@step));
}

.fs-12 {
  font-size: 12px;
}

.fs-14 {
  font-size: 14px;
}

.fs-16 {
  font-size: 16px;
}

.fs-18 {
  font-size: 18px;
}

.fs-22 {
  font-size: 12px;
}

.fs-24 {
  font-size: 24px;
}

.bold {
  font-weight: 600;
}

.w {
  width: 100%;
}

.h {
  height: 100%;
}

.no-padding {
  padding: 0px!important;
}


.required::before {
  opacity: 0.7;
}


.z-0 {
  z-index: 0;
}

.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-h-center {
  display: flex;
  justify-content: center;
}

.flex-column {
  flex-flow: column;
}

.flex-middle {
  display: flex;
  align-items: center;
}

.flex-between {
  justify-content: space-between;
}


