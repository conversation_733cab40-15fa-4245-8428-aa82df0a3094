.ivu-icon-md-add-circle {
  padding: 0 7px;
  cursor: pointer;
}

// 外部云管理
.multi_cloud_management_area {
  position: relative;
  display: flex;
  height: calc(100% - 70px);
  border-radius: 10px;
  background-color: #FFF;
  padding: 15px 5px;
  overflow: auto;

  .multi_cloud_management_group {
    width: 200px;
    border-right: 1px solid #ccc;

    .multi_cloud_management_title {
      border-bottom: 1px solid #ccc;
      height: 32px;
    }

    .nanotube_navigation {
      display: flex;
      flex-direction: row;
      justify-content: space-evenly;
      height: 40px;

      >.ivu-tooltip {
        line-height: 40px;

      }

      .ivu-icon {
        font-size: 20px;
      }
    }

    /* 树形图 */
    .vm_tree_area {
      min-width: 200px;
      overflow: auto;
      height: 80%;
    }
  }

  /* 多云纳管表格 */
  .multi_cloud_table_area {
    width: calc(100% - 200px);
    height: 100%;
    border-radius: 10px;
    background-color: #FFF;
    position: relative;

    .multi_cloud_table_title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid #ccc;
      height: 32px;
    }
  }
}

.multi_cloud_management_area p {
  font-weight: 600;
  border-left: 4px solid #fb6129;
  margin: 0 0 5px 5px;
  padding-left: 10px;
  color: #333;
  font-size: 14px;
}

// .ivu-icon {
//   line-height: 12px;
//   font-size: 12px;
// }

// .ivu-btn:focus {
//   box-shadow: none !important;
// }

.cloudOS {
  display: flex;
  justify-content: space-around;
}