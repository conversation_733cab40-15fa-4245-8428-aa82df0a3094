<template>
  <div class="creat-nfs-wrap">
    <Modal
      v-model="dialogVisible"
      :title="`${selectType}ISCSI`"
      @on-ok="submitForm('formData')"
      @on-cancel="resetForm('formData', false)"
      :mask-closable="false"
    >
      <div v-if="dialogVisible">
        <Form :model="formData" :label-width="170" :rules="rules" ref="formData">
          <FormItem label="选择所在集群" prop="cluster_id">
            <Select v-model="formData.cluster_id">
              <Option  v-for="(item, index) in clusterArr" :key="index" :label="item.cluster_id" :value="item.cluster_id">
                {{ item.cluster_id }}
              </Option>
            </Select>
          </FormItem>
          <FormItem
            label="实例所在节点"
            prop="target_iqn"
            v-if="selectType != operation.edit"
          >
            <Input
              v-model="daemonsStr"
              placeholder="请输入实例所在节点"
            ></Input>
          </FormItem>
          <FormItem label="选择后端存储类型" prop="fsal.name">
            <Select v-model="formData.fsal.name">
              <Option
                v-for="(item, index) in BACK_END_STORAGE_TYPE"
                :key="item.value"
                :disabled="item.disabled"
                :value="item.value"
              >
                {{ item.label }}
              </Option>
            </Select>
          </FormItem>
          <FormItem label="设置后端存储路径" prop="path">
            <Input
              v-model="formData.path"
              placeholder="请输入后端存储路径"
            ></Input>
          </FormItem>
          <FormItem label="设置输出目录别名" prop="pseudo">
            <Input
              v-model="formData.pseudo"
              placeholder="请输入输出目录别名"
            ></Input>
          </FormItem>
          <FormItem label="访问控制类型" prop="access_type">
            <Select v-model="formData.access_type">
              <Option
                v-for="(item, index) in AUTHENTICATION_METHOD"
                :value="item.value"
                :key="index"
                >{{ item.value }}</Option
              >
            </Select>
          </FormItem>
        </Form>
      </div>
    </Modal>
  </div>
</template>

<script>
export default {
  props: {
    selectType: {
      type: String,
      default: '添加'
    }
  },
  data() {
    var validator1 = (rule, val, callback) => {
      if (!val) {
        callback(new Error("请输入"));
      } else {
        const reg =/^\/(.*)$/
        if (reg.test(val)) {
          return callback();
        } else {
          callback(new Error("请输入以/开头"));
        }
      }
    };
    return {
      dialogVisible: false,
      operation: {
        add: '添加',
        edit: '编辑'
       },
      formData: {
        cluster_id: "",
        compress: true,
        fsal: {
          name: "CEPH",
        },
        daemons: [],
        access_type: "RW",
      },
      oldFormData: {
        cluster_id: "",
        fsal: {
          name: "CEPH",
          user_id: "admin",
          fs_name: "css-fs",
          sec_label_xattr: null,
        },
        access_type: "RW",
        tag: null,
        squash: "no_root_squash",
        clients: [],
        daemons: [],
        security_label: false,
        protocols: [4],
        transports: ["TCP", "UDP"],
      },
      clusterArr: [],
      daemonArr: [],
      daemonsStr: "",
      rules: {
        cluster_id: [
          { required: true, message: '请输入选择类型', trigger: 'change' },
        ],
        daemons: [
          { required: true, message: '请输入选择实例', trigger: 'change' },
        ],
        'fsal.name': [
          { required: true, message: '请输入选择存储类型', trigger: 'change' },
        ],
        path: [
          { required: true, message: '请输入/开头路径', trigger: 'change' },
          { validator: validator1, trigger: 'change' }
        ],
        pseudo: [
          { required: true, message: '请输入/开头别名', trigger: 'change' },
          { validator: validator1, trigger: 'change' }
        ],
        access_type: [
           { required: true, message: '请输入选择访问类型', trigger: 'change' },
        ]
      },
      AUTHENTICATION_METHOD: [
        {
          label: 'RW',
          value: 'RW',
        },
        {
          label: 'RO',
          value: 'RO'
        }
      ],
      BACK_END_STORAGE_TYPE : [
        {
          label: '文件系统',
          value: 'CEPH',
          disabled: false
        },
        {
          label: '对象存储',
          value: 'RGW',
          disabled: true,
        }
      ]
    };
  },
  mounted() {
    // this.getGaneshaDaemonFun();
  },
  methods: {
    getGaneshaDaemonFun() {
      this.$axios.get("/thecephapi/api/nfs-ganesha/daemon").then((res) => {
        const map = new Map();
        // 集群里clusterId 需要去重
        this.clusterArr = res.data.filter(
          (v) => !map.has(v.cluster_id) && map.set(v.cluster_id, 1)
        );
        this.daemonArr = res.data;
      });
    },
    submitForm(formName ) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.selectType === this.operation.add) {
            this.addNfsGaneshaFun()
          } else {
            delete this.formData.daemonsList
            let data = this.formData
            this.$axios.put(`/thecephapi/api/nfs-ganesha/export/${data.cluster_id}/${data.export_id}`).then((res) => {
              this.$Message.success({
                background: true,
                closable: true,
                duration: 5,
                content: "修改成功",
              });
              this.getTableList()
            })
          }
        } else {
          return false;
        }
      });
      this.dialogVisible = false
    },
    resetForm(formName ) {
      this.$refs[formName].resetFields();
      this.dialogVisible = false
    },
    addNfsGaneshaFun() {
      this.$axios.post(`/thecephapi/api/api/nfs-ganesha/export`,this.formData).then((res)=> {
        this.$Message.success({
          background: true,
          closable: true,
          duration: 5,
          content: "添加成功",
        });
        this.daemonsStr = ''
        this.getTableList()
      })
    }
  },
  watch: {
    "formData.cluster_id"(newVal, oldVal) {
      if (!newVal) {
        this.formData.daemons = "";
      } else {
        // 找到该集群名字下相同集群下所有节点
        let daemonsList = [];
        this.daemonArr.forEach((item) => {
          if (item.cluster_id == newVal) {
            daemonsList.push(item.daemon_id);
          }
        });
        this.$set(this.formData, "daemons", daemonsList);
        this.daemonsStr = daemonsList.join(",");
      }
    },
    selectType(newVal) {
      if (newVal === this.operation.add ) {
        this.formData = JSON.parse(JSON.stringify(this.oldFormData));
      }
    }
  },
};
</script>

<style scoped lang='less'>
</style>