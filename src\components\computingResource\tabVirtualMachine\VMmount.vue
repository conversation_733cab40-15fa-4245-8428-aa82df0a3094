<template>
  <div>
    <Modal
      v-model="model"
      :mask-closable="false"
      width="800"
    >
      <template #header><p><span style="color:green">{{vmRow.name}}</span>虚拟机挂载云硬盘</p></template>
      <Table :columns="tableColumn" :data="tableData" height="460">
        <!-- 云硬盘容量 -->
        <template v-slot:size="{row}">
          <span>{{ row.size}} GB</span>
        </template>
        <!-- 状态 -->
        <template v-slot:status="{row}">
          <span>{{ renwu(row) }}</span>
        </template>
      </Table>
      <div slot="footer">
        <Button type="text" @click="model = false">取消</Button>
        <Button type="primary" @click="modalOK" :disabled="disabled">确认</Button>
      </div>
    </Modal>
  </div>
</template>
<script>
import {
  cloudHardDriveQuery, // 挂载云硬盘 查询
  loudHardDriveMount, // 挂载云硬盘 挂载
} from '@/api/virtualMachine';
export default {
  props: {
    vmRow: Object,
    mountTime: String,
  },
  watch: {
    mountTime(news){
      this.formItem.vmid = this.vmRow.id
      this.formItem.vmName = this.vmRow.name
      this.mounttable()
      this.model = true
      this.disabled = false
    }
  },
  data(){
    return {
      disabled:false,
      model:false,
      tableColumn:[
        { title: "单选",width: 60, key: "radio",align: "center",
          render: (h, params) => {
            let flag = false;
            if (this.formItem.mountID == params.row.id) {
              flag = true
            }else {
              flag = false
            }
            return h('div',[
              h('Radio',{
                props: {
                  value:flag,
                },
                on:{
                  'on-change':()=>{
                    this.formItem.mountID = params.row.id
                    this.formItem.mountName = params.row.name
                  },
                }
              })
            ])
          }
        },
        { title: "云硬盘名称", key: "name",tooltip:true,},
        { title: "云硬盘容量", key: "size",align: "center", slot: "size" },
        { title: "状态", key: "status",align: "center", slot:"status" },
      ],
      tableData:[],
      formItem:{
        mountID: '',
        mountName: '',
        vmid: '',
        vmName: '',
      },
    }
  },
  methods: {
    // 获取挂载表数据
    mounttable(){
      cloudHardDriveQuery()
      .then((callback) => {
        this.tableData=callback.data.filter(em =>em.status == 'available')
        this.formItem.mountID = callback.data[0].id
        this.formItem.mountName = callback.data[0].name
      })
    },
    // 弹框确认
    modalOK(){
      if(this.formItem.mountID !== '') {
        this.disabled = true
        loudHardDriveMount({
          volumeid: this.formItem.mountID,
          volume_name: this.formItem.mountName,
          vm_name: this.formItem.vmName,
          vmid: this.formItem.vmid,
        })
        .then(callback=>{
          if(callback.data.msg == "ok") {
            this.model=false
            this.$Message.success({
              background: true,
              closable: true,
              duration: 5,
              content: "云硬盘挂载操作完成",
            });
          }else {
            this.disabled = false
            this.$emit("return-error",'云硬盘挂载操作失败');
          }
        }).catch((error) => {
          this.disabled = false
          this.$emit("return-error",'云硬盘挂载操作失败');
        })
      }else {
        this.$Message.warning({
          background: true,
          closable: true,
          duration: 5,
          content: "未勾选有效数据",
        });
      }
    },
    // 表格任务条封装
    renwu(row) {
      let text = '使用中'
      switch (row.status) {
        case "in-use":
          text = '使用中'
        break;
        case "available":
          text = '空闲中'
        break;
        case "creating":
          text = '创建中'
        break;
        case "attaching":
          text = '连接中'
        break;
        case "detaching":
          text = '分离中'
        break;
        case "deleting":
          text = '删除'
        break;
        case "error":
          text = '错误'
        break;
        case "error_deleting":
          text = '删除错误'
        break;
        case "downloading":
          text = '下载镜像中'
        break;
        case "extending":
          text = '扩展中'
        break;
        case "reserved":
          text = '保留'
        break;
      }
      return text
    },
  }
}
</script>