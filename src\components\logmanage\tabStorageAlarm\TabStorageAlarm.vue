<style lang="less">
@import "../logmanage.less";
</style>
<template>
  <div class="alarm_page_area">
    <Spin fix v-if="spinShow" size="large" style="color: #ef853a">
      <Icon type="ios-loading" size="50" class="demo-spin-icon-load"></Icon>
      <div style="font-size: 16px; padding: 20px">Loading...</div>
    </Spin>
    <div class="alarm_page_btn">
      <!-- <CheckboxGroup v-model="gjCheck" @on-change="groupClick">
        <Checkbox label="critical" border><Icon type="ios-notifications-outline" color="#ff7842" />严重<span style="color: #ff7842">{{critical}}</span></Checkbox>
        <Checkbox label="major" border><Icon type="ios-notifications-outline" color="#ffa12d" />重要<span style="color: #ffa12d">{{major}}</span></Checkbox>
        <Checkbox label="warning" border><Icon type="ios-notifications-outline" color="#fff82d" />次要<span style="color: #fff82d">{{warning}}</span></Checkbox>
        <Checkbox label="info" border><Icon type="ios-notifications-outline" color="#656eea" />提示<span style="color: #656eea">{{info}}</span></Checkbox>
      </CheckboxGroup> -->
      <Button class="plus_btn" style="margin-bottom: 10px"  @click="ignoreClick(tableSelec)"><Icon type="md-more" /> 忽略</Button>
    </div>
    <div class="table_currency_area">
      <Table :columns="eventColumn" :data="eventData" @on-selection-change="tableChange">
        <!-- 类型 -->
        <template v-slot:severity="{row}">
          <span :style="{color:typeConversion('color',row.severity)}">{{typeConversion('text',row.severity)}}</span>
        </template>
        <!-- 操作 -->
        <template v-slot:operation="{ row }">
          <Button @click="ignoreClick([row])">忽略</Button>
        </template>
      </Table>
      <div class="pages" v-if="this.eventData.length>0">
        <Page :total="httpTableData.tableTotal" show-sizer :page-size="httpTableData.pagecount" :page-size-opts="[10, 20, 30]" @on-change="onPage" @on-page-size-change="pageSizeChange" />
      </div>
    </div>
  </div>
</template>
<script>
import {storageAlarmQuery,storageAlarmIgnore} from '@/api/log';

export default {
  props: {
    tabName: String,
  },
  watch: {
    tabName(value) {
      if(value=='存储告警'){
        this.gjCheck=['critical','major','warning','info','page','none']
        this.giveANalarmGET()
      }
    },
  },
  data() {
    return {
      spinShow:false, 
      eventColumn:[
        { type: "selection", width: 30, align: "center"},
        { title: "告警名称", key: "summary" ,minWidth: 200,tooltip:true },
        { title: "告警时间", key: "activeAt",align: "center" ,minWidth: 200 },
        { title: "告警详情", key: "description",align: "center",minWidth: 400,tooltip:true },
        { title: "告警类型", key: "severity",align: "center",width: 120, slot:"severity" },
        { title: "操作", key: "operation",align: "center",width: 120, slot:"operation" }
      ],
      eventData:[],
      eventAll:[],
      tableSelec:[],
      totalData:0,
      critical:0,
      major:0,
      warning:0,
      info:0,
      gjCheck:['critical','major','warning','info','page','none'],
      // 分页
      httpTableData: {
        tableTotal:0,
        page: 1, // 当前页
        pagecount: this.$store.state.power.pagecount, // 每页条
      },
    }
  },
  
  created() {},
  mounted() {
    if(this.$store.state.power.logManagementTab == '存储告警') {
        this.gjCheck=['critical','major','warning','info','page','none']
      this.giveANalarmGET()
    }
  },
  methods: {
    giveANalarmGET(){
      this.spinShow = true
      this.eventData= new Array()
      this.httpTableData.tableTotal = 0
      this.httpTableData.page = 1
      storageAlarmQuery()
      .then(callback=>{
        this.spinShow = false
        this.tableSelec = new Array()
        this.totalData=callback.data.length
        let yanzhong =0
        let zhongyao =0
        let ciyao =0
        let tishi =0
        callback.data.forEach(item=>{
          if(item.severity=="critical"){
            yanzhong=yanzhong+1
          }
          if(item.severity=="major"){
            zhongyao=zhongyao+1
          }
          if(item.severity=="warning"){
            ciyao=ciyao+1
          }
          if(item.severity=="info"){
            tishi=tishi+1
          }
        })
        this.eventAll= callback.data
        this.httpTableData.tableTotal = callback.data.length
        this.eventData= this.eventAll.slice(this.httpTableData.page-1, this.httpTableData.page*this.httpTableData.pagecount)
        his.critical=yanzhong
        this.major=zhongyao
        this.warning=ciyao
        this.info=tishi
      }).catch((error) => {
        this.spinShow=false;
      });
    },
    groupClick(item){
      this.eventData= new Array()
      this.httpTableData.tableTotal = 0
      this.httpTableData.page = 1
      setTimeout(() => {
        this.eventData=this.selec_checjbox(item,this.httpTableData.page,this.httpTableData.pagecount)
      }, 100);
    },
    // 判定勾选
    selec_checjbox(item,page,size){
      let arr = new Array()
      item.forEach(el => {
        this.eventAll.forEach(em=>{
          if(em.severity==el){
            arr.push({
              summary:em.summary,
              activeAt:em.activeAt,
              description:em.description,
              severity:em.severity,
            })
          }
        })
      })
      this.httpTableData.tableTotal = arr.length
      return arr.slice((page-1)*size, this.httpTableData.page*size)
    },
    // 点击分页器
    onPage(item) {
      this.httpTableData.page = item;
      this.eventData=this.selec_checjbox(this.gjCheck,item,this.httpTableData.pagecount)
    },
    // 切换 条数
    pageSizeChange(item) {
      this.$store.state.power.pagecount = item
      this.httpTableData.pagecount = this.$store.state.power.pagecount
      this.httpTableData.page = 1
      this.eventData=this.selec_checjbox(this.gjCheck,1,item)
    },
    // 表选中数据
    tableChange(item){
      this.tableSelec = item
    },
    // 忽略 操作
    ignoreClick(data) {
      if(data == 0) {
        this.$Message.warning({
          background: true,
          closable: true,
          duration: 5,
          content: "未选择表格数据",
        });
      } else {
        let ids = new Array()
        let names = new Array()
        data.forEach(em=>{
          ids.push(em.fingerprint)
          names.push(em.summary)
        })
        storageAlarmIgnore({
          ids: ids,
          names: names,
        })
        .then(callback=>{
          this.gjCheck=['critical','major','warning','info','page','none']
          this.giveANalarmGET()
        })
      }
    },
    // 类型转换
    typeConversion(type,severity){
      let color = '#000'
      let text = '-'
      switch (severity) {
        case 'critical':
          color = '#ff7842'
          text = '严重'
          break;
        case 'major':
          color = '#ffa12d'
          text = '重要'
          break;
        case 'warning':
          color = '#fff82d'
          text = '次要'
          break;
        case 'info':
          color = '#dbb35f'
          text = '提示'
          break;
        case 'page':
          color = '#da4c18'
          text = '紧急通知'
          break;
        case 'none':
          color = '#ccc'
          text = '-'
          break;
      }
      if(type=='color') {
        return color
      }else {
        return text
      }
    },    
  }
}
</script>
