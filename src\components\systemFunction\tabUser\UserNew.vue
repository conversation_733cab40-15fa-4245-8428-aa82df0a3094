<template>
  <Modal v-model="model" width="600" :mask-closable="false">
    <template #header><p>新建用户</p></template>
    <Form
      :model="formItem"
      ref="formItem"
      :rules="rulesForm"
      :label-width="120"
    >
      <FormItem label="登录账号" prop="username">
        <Input v-model="formItem.username" placeholder="请输入登录的用户名"></Input>
      </FormItem>
      <FormItem label="登录密码"  prop="password">
        <Input v-model="formItem.password" type="password" password placeholder="请输入登录的密码"></Input>
      </FormItem>
      <FormItem label="确认密码"  prop="confirmPW">
        <Input v-model="formItem.confirmPW" type="password" password placeholder="请重新入输登录密码"></Input>
      </FormItem>
      <FormItem label="用户名称" prop="name">
        <Input v-model="formItem.name" placeholder="请输入用户名"></Input>
      </FormItem>
      <FormItem label="用户角色" v-if="moduleShow('tripleManage')">
        <Select v-model="formItem.role">
          <Option v-for="item in roleData" :value="item.value" :key="item.value">{{ item.label }}</Option>
        </Select>
      </FormItem>
      <FormItem label="有效期">
        <DatePicker v-model="listTime" type="date" :clearable="false" :options="optionsTime" @on-change="timeChange"></DatePicker>
      </FormItem>
    </Form>
    <div slot="footer">
      <Button type="text" @click="model = false">取消</Button>
      <Button type="primary" @click="modelOK" :disabled="disabled">确认</Button>
    </div>
  </Modal>
</template>
<script>
import {
  userTableNewBuilt, // 用户新建
} from '@/api/system';

export default {
  props: {
    newTime: String,
  },
  watch: {
    newTime(news){
      this.$refs.formItem.resetFields();
      this.formItem.time = this.defaultTime()
      this.listTime = this.defaultTime()
      this.model = true
      this.disabled = false
    }
  },
  data(){
    const propUser =(rule,value,callback)=>{
      let list=/^[a-z0-9@_.]{2,32}$/;
      if(list.test(value)){
        callback()
      }else{
        callback(new Error('2-32 个英文、数字、特殊字符(@_.)'))
      }
    }
    const propName =(rule,value,callback)=>{
      let list = /^[\u4e00-\u9fa5_a-zA-Z0-9@_.]{2,32}$/;
      if(list.test(value)){
        callback()
      }else{
        callback(new Error("2-32 个中文、英文、数字、特殊字符(@_.)"));
      }
    }
    const propPwd =(rule,value,callback)=>{
      let list = /^[a-zA-Z0-9@_.]{8,32}$/;
      if(list.test(value)){
        callback()
      }else{
        callback(new Error("8-32 个英文、数字、特殊字符(@_.)"))
      }
    }
    const propCpwd =(rule,value,callback)=>{
      if(value == this.formItem.password){
        callback()
      }else{
        callback(new Error('两次输入的密码不匹配'))
      }
    }
    
    return {
      model: false,
      disabled: false,
      formItem: {
        name: '',
        username: '',
        password: '',
        confirmPW: '',
        role: 'operator',
        time: '',
      },
      listTime:null,
      // 禁止选择过去时间
      optionsTime: {
        disabledDate (date) {
          return date && date.valueOf() < Date.now() - 86400000;
        }
      },
      roleData:[
        { label:'审计员',value:'adtadm' },
        { label:'安全员',value:'secadm' },
        { label:'操作员',value:'operator' }
      ],
      // 正则验证
      rulesForm: {
        username:[
          { required: true, message: '必填项', trigger: 'change' },
          { validator: propUser, trigger:'change' }
        ],
        name:[
          { required: true, message: '必填项', trigger: 'change' },
          { validator: propName, trigger:'change' }
        ],
        password:[
          { required: true, message: '必填项', trigger: 'change' },
          { validator: propPwd, trigger:'change' }
        ],
        confirmPW:[
          { required: true, message: '必填项', trigger: 'change' },
          { validator: propCpwd, trigger:'change' }
        ],
      },
    }
  },
  methods: {
    
    // 时间选择 
    timeChange(item){
      this.formItem.time = item
      this.listTime = item
    },
    // 编辑网络确认事件
    modelOK() {
      this.$refs.formItem.validate((valid) => {
        if (valid) {
          this.disabled = true;
          userTableNewBuilt({
            name:this.formItem.name,
            username:this.formItem.username,
            password:this.formItem.password,
            role:this.moduleShow('tripleManage')?this.formItem.role:'sysadm',
            expiredday:this.formItem.time
          })
          .then(callback => {
            if(callback.data.msg == "ok") {
              this.$emit("custom-ok",'新建用户操作完成');
              this.model = false;
            }else {
              this.$emit("custom-error",callback.data.msg);
              this.disabled = false;
            }
          })
          .catch((error) => {
            this.$emit("custom-error",'新建用户失败');
            this.disabled = false;
          });
        }
      });
    },
    // 30天之后的时间
    defaultTime(){
      let currentDate = new Date();
      currentDate.setDate(currentDate.getDate() + 30);
      let year = currentDate.getFullYear();
      let month = currentDate.getMonth() + 1; // 月份是从0开始的，所以要加1
      let day = currentDate.getDate();
      return  year + "-" + (month < 10 ? '0' : '') + month + "-" + (day < 10 ? '0' : '') + day
    },
    // 模块显示隐藏判断
    moduleShow(item){
      return window.gurl.PageModule.indexOf(item) == -1?true:false
    },
  }
}
</script>
