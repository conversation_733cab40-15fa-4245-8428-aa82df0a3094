<template>
  <div class="general_page_area">
    <Form :model="formItem" ref="formItem" :rules="ruleValidate" :label-width="120">
			<FormItem label="工作目录">
        <Input v-model="formItem.directory" placeholder="请输入运行命令的工作目录"></Input>
    	</FormItem>
      <FormItem label="环境变量">
        <Input v-model="formItem.variables" type="textarea" :rows="4" placeholder="请输入环境变量。例：KEY1=VALUE1,KEY2=VALUE2..."></Input>
    	</FormItem>
      <FormItem label="启用交互模式">
        <Checkbox v-model="formItem.interactive"></Checkbox>
    	</FormItem>
		</Form>
  </div>
</template>
<script>
export default {
  props: {
    times:String,
  },
  watch: {
    times(value) {
      this.$emit("returnOK",{
        page: 3,
        data: this.formItem,
      });
      // this.$refs.formItem.validate((valid) => {
      //   if (valid) {
      //     this.$emit("returnOK",{
      //       page: 3,
      //       data: this.formItem,
      //     });
      //   }
      // })
      
    }
  },
  data(){
    return {
      formItem: {
        directory: '',
        variables: '',
        interactive: true
      },
      ruleValidate:{
        directory:[{ required: true, message: '必填项', trigger: 'change' }],
        variables:[{ required: true, message: '必填项', trigger: 'change' }],
      }
    }
  },
  methods: {

  },
}
</script>
<style lang="less" scoped>
  .general_page_area {
    width: 100%;
    height: 100%;
    overflow: auto;
  }
</style>