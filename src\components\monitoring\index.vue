<style lang="less">
@import "./monitoring.less";
</style>
<template>
  <div class="monitor_tab">
    <Tabs class="tabs_template" name="1" v-model="tabName"  @on-click="tabsClick">
      <TabPane
        v-if="item.show"
        v-for="item in tabsData"
        :key="item.name"
        :name="item.name"
        tab="1"
        :label="renderTabLabel(item.name)">
        <TabHostMonitor v-if="item.name === '物理机监控'" :tabName="tabName" />
        <TabVMmonitor v-if="item.name === '虚拟机监控'" :tabName="tabName" />
        <TabStorageMonitor v-if="item.name === '存储监控'" :tabName="tabName" />
        <CeshiLine v-if="item.name === '测试'" />
      </TabPane>
    </Tabs>
  </div>
</template>
<script>
import { powerCodeQuery } from '@/api/other'; // 权限可用性 查询

import TabHostMonitor from "./tabHostMonitor/TabHostMonitor.vue"
import TabVMmonitor from "./tabVMmonitor/TabVMmonitor.vue"
import TabStorageMonitor from "./tabStorageMonitor/TabStorageMonitor.vue"
import CeshiLine from "./cheshi/CeshiLine.vue"
export default {
  components: {
    TabHostMonitor,
    TabVMmonitor,
    TabStorageMonitor,
    CeshiLine,
  },
  data() {
    return {
      tabName: '',
      tabsData: [
        { name: '物理机监控', code: 'wulijijiankong', show: false },
        { name: '虚拟机监控', code: 'xunijijiankong', show: false },
        { name: '存储监控', code: 'cunchujiankong', show: false },
      ],
    }
  },
  watch: {
    '$store.state.power.itMonitoringTab'(news){
      this.tabName = news
    }
  },
  mounted(){
    this.tabsQuery()
  },
  
  methods: {
    renderTabLabel(item){
      return (h) => {
        return h('div', [
          h('span', {
            class: 'select_tab_border',
            style: { background: this.tabName === item ? '#fb6129' : '' }
          }),
          h('span', item)
        ]);
      };
    },
    // 查询标签页权限
    tabsQuery(){
      powerCodeQuery({
        module_code:[
          'wulijijiankong',
          'xunijijiankong',
          'cunchujiankong'
        ]
      }).then(callback=>{
        this.tabsData.forEach(item => {
          if (callback.data.data.hasOwnProperty(item.code)) {
            item.show = callback.data.data[item.code];
          }
        });
        if(this.$store.state.power.itMonitoringTab == '存储监控') {
          this.tabName = '存储监控'
        }else {
          if(callback.data.data.wulijijiankong) {
            this.tabName = '物理机监控'
          }else if(callback.data.data.xunijijiankong) {
            this.tabName = '虚拟机监控'
          }else if(callback.data.data.cunchujiankong) {
            this.tabName = '存储监控'
          }
          this.$store.state.power.itMonitoringTab = this.tabName
        }
      })
    },
    tabsClick(name){
      this.$store.state.power.itMonitoringTab = name
    },
  }
}
</script>