<template>
  <div>
    <Modal v-model="model" width="900" :mask-closable="false">
      <template #header
        ><p @click="listDatas()">虚拟机动态资源扩展</p></template
      >
      <div>
        <Table :columns="tableColumns" :data="tableData" height="500">
          <!-- IP -->
          <template v-slot:ip="{ row }">
            <Tooltip :content="convertIP(row.addresses)" style="width: 100%">
              <span class="text_overflow">{{ convertIP(row.addresses) }}</span>
            </Tooltip>
          </template>
          <!-- 内存 -->
          <template v-slot:ram="{ row }">
            <span>{{ (row.ram / 1024).toFixed(1) }} GB</span>
          </template>
          <!-- 硬盘 -->
          <template v-slot:disk="{ row }">
            <span>{{ row.disk }} GB</span>
          </template>
          <!-- 操作 -->
          <template v-slot:operation="{ row }">
            <Tooltip content="动态资源扩展" placement="top">
              <Button
                @click="editClick(row)"
                type="text"
                icon="ios-create-outline"
              ></Button>
            </Tooltip>
            <Tooltip content="重置动态资源扩展" placement="top">
              <Button
                @click="resetClick(row)"
                type="text"
                icon="ios-backspace-outline"
              ></Button>
            </Tooltip>
          </template>
        </Table>
      </div>
      <div slot="footer">
        <Button type="text" @click="model = false">取消</Button>
      </div>
    </Modal>
    <!-- 动态资源扩展 -->
    <VMdynamicResource
      :vmRow="vmRow"
      :dynamicResourceTime="dynamicResourceTime"
      @return-ok="returnOK"
      @return-error="returnError"
    ></VMdynamicResource>
    <!-- 重置动态资源扩展 -->
    <Modal v-model="resetModel" width="600" :mask-closable="false">
      <template #header><p>重置动态资源扩展</p></template>
      <div style="padding: 5px">
        <span>是否对下列虚拟机进行重置动态资源扩展操作？</span>
        <p style="color: red; word-wrap: break-word">{{ vmRow.name }}</p>
      </div>
      <div slot="footer">
        <Button type="text" @click="resetModel = false">取消</Button>
        <Button type="primary" @click="modelOK">确认</Button>
      </div>
    </Modal>
  </div>
</template>
<script>
import VMdynamicResource from "./VMdynamicResource.vue"; // 动态资源扩展
import {
  ddispatchQuery,
  dynamicResourceExtendEdit,
} from "@/api/virtualMachine"; // 动态资源扩展
export default {
  props: {
    dispatchTime: String,
  },
  watch: {
    dispatchTime(news) {
      this.queryData();
      this.model = true;
    },
  },
  components: {
    VMdynamicResource,
  },
  data() {
    return {
      model: false,
      tableColumns: [
        { title: "名称", key: "name", tooltip: true },
        { title: "IP", key: "ip", align: "center", slot: "ip" },
        { title: "主机名", key: "hostname", align: "center", tooltip: true },
        { title: "VCPU", key: "vcpus", align: "center" },
        { title: "内存", key: "ram", align: "center", slot: "ram" },
        { title: "硬盘", key: "disk", align: "center", slot: "disk" },
        { title: "操作", key: "operation", width: 90, slot: "operation" },
      ],
      tableData: [],
      vmRow: {},
      dynamicResourceTime: "",
      resetModel: false,
    };
  },
  methods: {
    // 查询数据
    queryData() {
      ddispatchQuery()
        .then((callback) => {
          this.tableData = callback.data.data;
        })
        .catch((error) => {
          this.tableData = new Array();
        });
    },
    listDatas() {
      this.tableData = [
        {
          id: "c51f7988-279e-456e-99ec-6eabaeabeec6",
          name: "tt",
          hostname: "controller3",
          addresses: [
            {
              version: 4,
              addr: "************",
              "OS-EXT-IPS:type": "fixed",
              "OS-EXT-IPS-MAC:mac_addr": "fa:16:3e:be:ce:32",
            },
          ],
          status: "ACTIVE",
          imageid: "c59308af-036f-480c-92b2-07963d549207",
          vcpus: 1,
          ram: 2048,
          disk: 10,
          volumes: [
            {
              id: "38b12d99-940e-4f10-b97f-756e3387d777",
              delete_on_termination: false,
            },
          ],
          mountpoint: "/dev/vda",
          description: "tt",
          task_state: null,
          vm_state: "active",
          os_type: "linux",
        },
      ];
    },
    // IP转换
    convertIP(item) {
      let ips = item.map((em) => {
        return em.addr;
      });
      return ips.toString();
    },
    // 动态资源扩展
    editClick(row) {
      this.vmRow = row;
      this.dynamicResourceTime = "" + new Date();
    },
    // 重置动态资源扩展
    resetClick(row) {
      this.vmRow = row;
      this.resetModel = true;
    },
    modelOK() {
      this.resetModel = false;
      dynamicResourceExtendEdit({
        vm_id: row.id,
        vm_name: row.name,
        enabled: "false",
      }).then((callback) => {
        if (callback.data.msg == "ok") {
          this.model = false;
          this.returnOK("虚拟机动态资源扩展操作完成");
        } else {
          this.returnError(callback.dta.msg);
        }
      });
    },
    returnOK(data) {
      this.$Message.success({
        background: true,
        closable: true,
        duration: 5,
        content: data,
      });
    },
    returnError(data) {
      this.$Message.error({
        background: true,
        closable: true,
        duration: 5,
        content: data,
      });
    },
  },
};
</script>
<style scoped>
</style>