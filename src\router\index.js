import Vue from 'vue'
import Router from 'vue-router'
// 导入登录组件
import Login from '@/components/login'
// 导入 导航组件
import NewNavi from "@/components/NewNavi";
// 全屏
import ScreenPage from "@/components/ScreenPage";

import Overview from '@/components/overview'
import ComputingResource from '@/components/computingResource'
import StorageResources from '@/components/storageResources'
import NetworkResource from "@/components/networkResource";
import Monitoring from "@/components/monitoring";
import SystemFunction from '@/components/systemFunction'
import Logmanage from '@/components/logmanage'
import ResourceAllocation from '@/components/resourceAllocation'
import OneClickCleaning from '@/components/oneClickCleaning'
import StoreService from '@/components/storeService'



Vue.use(Router)

export default new Router({
  routes: [
    {
      path: "/",
      redirect: "/login", // 如果用户访问的 / 根路径，则 重定向到 /login 页面
    },
    { path: "/login", component: Login }, // 登录页面的路由规则
    { path: "/newNavi", component: NewNavi },
    { path: "/screenPage", component: ScreenPage },
    {
      path: "/newNavi",
      component: NewNavi,
      redirect: "/overview", // 只要进入了 overview 页面，就立即重定向到 welcome 欢迎页
      children: [
        {
          path: "/overview",
          component: Overview,
          //meta: { requireAuth: true } // 配置此条，进入页面前判断是否需要登陆
        },
        { path: "/computingResource", component: ComputingResource },
        { path: "/storageResources", component: StorageResources },
        { path: "/networkResource", component: NetworkResource },
        { path: "/monitoring", component: Monitoring },
        { path: "/systemFunction", component: SystemFunction },
        { path: "/logmanage", component: Logmanage },
        { path: "/resourceAllocation", component: ResourceAllocation },
        { path: "/oneClickCleaning", component: OneClickCleaning },
        { path: "/storeService", component: StoreService },
        ,
      ],
    }, // 后台主页的路由规则
  ],
});

// 路由导航守卫的语法   router对象.beforeEach((to, from, next) => {})
// router.beforeEach((to, from, next) => {
//   // 如果用户访问的是 登录页面，则直接放行
//   if (to.path === '/login') return next()
//   // 获取 token
//   axios.interceptors.response.use((err) => {
//     // 如果状态码不是401则放行，否者跳转登录页面
//     if (err.response.status !== 401) return next()
//     next('/login')
//   })
//   // const tokenStr = sessionStorage.getItem('token')
//   // // 如果 token 存在， 直接放行
//   // if (tokenStr) return next()
//   // // 否则，强制跳转到登录页
//   // next('/login')
// })

// export default router
