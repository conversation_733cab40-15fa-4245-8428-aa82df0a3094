<style>
@import "../assets/css/global.css";
</style>
<template>
  <div class="layout">
    <Layout>
      <Header class="layout-header-bar">
        <div class="logo_thxh">
          <div>
          <img src="@/assets/loginCRH.png" alt="THXH" v-if="THElogoTitle=='CRH'" >
          <img src="@/assets/loginYGL.png" alt="THXH" v-if="THElogoTitle!=='CRH'" >
          </div>
        </div>
        <div class="user_setup">
          <!-- 大屏 -->
          <span @click="screenClick">
            <Tooltip placement="bottom">
              <Icon class="nav_large_screen" type="md-expand" />
              <div slot="content">
                大屏展示
              </div>
            </Tooltip>
          </span>
          <!-- 迁移 -->
          <span @click="noticeClick" style="margin-left: 20px" v-if="moduleShow('migrate')">
            <Tooltip placement="bottom">
              <Badge class="messages" :count='moveTotal'>
                <!-- <Icon type="ios-alert-outline" /> -->
                <Icon type="md-alert" />
              </Badge>
              <div slot="content">
                <p>共有 {{moveTotal}} 条待迁移数据</p>
              </div>
            </Tooltip>
          </span>
          <!-- 动态资源调度 -->
          <span  @click="schedulingClick" style="margin-left: 20px" v-show="powerNavi.dongtaiziyuandiaodubiao">
            <Tooltip placement="bottom">
              <Badge class="messages" :count='schedulingTotal'>
                <Icon type="ios-alert-outline" />
              </Badge>
              <div slot="content">
                <p>共有 {{ schedulingTotal }} 条动态资源调度数据</p>
              </div>
            </Tooltip>
          </span>
          <!-- 虚机动态资源扩展 -->
          <span  @click="expansionClick" style="margin-left: 20px" v-show="powerNavi.dongtaiziyuankuozhanbiao">
            <Tooltip placement="bottom">
              <Badge class="messages" :count='expansionTotal'>
                <Icon type="md-alert" />
                <!-- <Icon type="md-open" /> -->
              </Badge>
              <div slot="content">
                <p>共有 {{ expansionTotal }} 条虚机资源调度数据</p>
              </div>
            </Tooltip>
          </span>
          <!-- 集群告警 -->
          <span @click="alertingClick" style="margin-left: 20px" v-show="powerNavi.jiqungaojing">
            <Tooltip placement="bottom" max-width="600">
              <Badge class="messages" :count='totalData'>
                  <Icon
                    style="color: #dc6511"
                    type="ios-notifications"
                  ></Icon>
              </Badge>
              <div slot="content">
                <p>共有 {{totalData}} 条集群告警信息</p>
                <p><i>严重 {{critical}} 条，重要 {{major}} 条，次要 {{warning}} 条，提示 {{info}} 条</i></p>
              </div>
            </Tooltip>
          </span>
          <!-- 助手 -->
          <span style="margin-left: 20px" @click="YJclick" class="assistant" v-show="powerNavi.yunweizhushou"><Icon style="color:#999" type="logo-android" /><span style="font-size:14px">助手</span></span>
          <!-- 用户 -->
          <Icon style="margin-left: 20px;color:#999;border-left: 2px solid #f6f4f4;" class="nav_icon" type="md-person" />
          <span style="font-size: 12px">{{usernames}}</span>
          <!-- 设置 -->
          <!-- <Dropdown trigger="click" style="margin-left: 20px" class="nav_icon" @on-click="mennuTopClick" v-show="powerNavi.shezhi"> -->
          <Dropdown trigger="click" style="margin-left: 20px" class="nav_icon" @on-click="mennuTopClick">
            <a href="javascript:void(0)" style="color: #999">
              <Icon type="md-settings" />
            </a>
            <DropdownMenu slot="list">
              <DropdownItem v-show="powerNavi.tuichudenglu" name="signOut"><span style="color:red">退出登录</span></DropdownItem>
              <DropdownItem v-show="powerNavi.xiugaimima" name="modifyPASS">修改密码</DropdownItem>
              <DropdownItem v-show="powerNavi.mimaguize" name="pawRules">密码规则</DropdownItem>
              <DropdownItem v-show="powerNavi.dongtaiziyuandiaodu" name="resourceScheduling">动态资源调度</DropdownItem>
              <DropdownItem v-show="powerNavi.guanyuwomen" name="aboutUs">关于我们</DropdownItem>
            </DropdownMenu>
          </Dropdown>
        </div>
      </Header>
      <Layout>
        <Sider hide-trigger collapsible :collapsed-width="200">
          <Menu :active-name="naviActive" theme="light" width="200px" class="menu_area" @on-select="pushUrl" >
            <MenuItem name="overview" to="overview" v-show="powerNavi.gailan">
              <!-- <Icon type="ios-home-outline" /><br /> -->
              <span class="icon iconfont icon-erji-ziyuanzonglan"></span>
              <span>概览</span>
            </MenuItem>
            <MenuItem name="computingResource" to="computingResource" v-show="powerNavi.jisuanziyuan">
              <span class="icon iconfont icon-erji-fuwuqi"></span>
              <span>计算资源</span>
            </MenuItem>
            <MenuItem name="storageResources" to="storageResources" v-show="powerNavi.cunchuziyuan">
              <span class="icon iconfont icon-erji-yunziyuanchi"></span>
              <span>存储资源</span>
            </MenuItem>
            <MenuItem name="networkResource" to="networkResource" v-show="powerNavi.wangluoziyuan">
              <span class="icon iconfont icon-erji-wangluotuopu"></span>
              <span>网络资源</span>
            </MenuItem>
            <MenuItem name="monitoring" to="monitoring" v-show="powerNavi.ziyuanjiankong">
              <span class="icon iconfont icon-erji-jiankongtubiao"></span>
              <span>资源监控</span>
            </MenuItem>
            <MenuItem name="resourceAllocation" to="resourceAllocation" v-show="powerNavi.xunijifenpei">
              <span class="icon iconfont icon-erji-xuniziyuan"></span>
              <span>资源分配</span>
            </MenuItem>
            <MenuItem name="logmanage" to="logmanage" v-show="powerNavi.rizhiguanli">
              <span class="icon iconfont icon-yiji-baobiaoguanli"></span>
              <span>日志告警</span>
            </MenuItem>
            <MenuItem name="systemFunction" to="systemFunction" v-show="powerNavi.xitonggongneng">
              <span class="icon iconfont icon-yiji-xitongguanli"></span>
              <span>系统功能</span>
            </MenuItem>
            <!-- <MenuItem name="storeService" to="storeService" v-show="powerNavi.cunchufuwu">
              <span class="icon iconfont icon-erji-yunziyuanchi"></span>
              <span>存储服务</span>
            </MenuItem> -->
          </Menu>
          <!-- 导航时间 -->
          <div class="nav_time">
            <span>{{todayTime.day}}</span>
            <span>{{todayTime.years}}</span>
            <span class="nav_time_week">{{todayTime.week}}</span>
          </div>
        </Sider>
        <Content class="content_area">
          <router-view></router-view>
        </Content>
      </Layout>
    </Layout>
    <!-- 关于我们弹框 -->
    <AboutUS :datas="aboutModal"></AboutUS>
    <!-- 动态资源调度 -->
    <Dispatch :datas="transferModal"></Dispatch>
    <!-- 密码规则 -->
    <PasswordRules :datas="rulesModal"></PasswordRules>
    <!-- 调度确认 -->
    <SchedulingConfirmation :datas="schedulingModal" @scheduling-ok="schedulingOK"></SchedulingConfirmation>
    <!-- 扩展确认 -->
    <ExpansionConfirmation :datas="expansionModal" @expansion-ok="expansionOK"></ExpansionConfirmation>
  </div>
</template>
<script>
import { logout } from '@/api/login'; // 退出登录
import { alarmDataQuery } from '@/api/log'; // 日志查询
import { 
  vmGroupTableMigrationConfirm, // 迁移确认
  vmextendQuery, // 动态资源扩展表查询
 } from '@/api/virtualMachine';
import {
  notificationInformation, // 通知信息
  notifyMigrationConfirmation, // 通知迁移确认
  powerCodeQuery, // 权限可用性 查询
  dispatchTableQuery, // 动态资源调度表查询
} from '@/api/other';
import {automaticDowntimeMigrationQuery} from '@/api/system';
import AboutUS from './public/AboutUS.vue'; // 关于我们
import Dispatch from './public/Dispatch.vue'; // 动态资源调度
import PasswordRules from './public/PasswordRules.vue'; // 密码规则
import SchedulingConfirmation from './public/SchedulingConfirmation.vue'; // 调度确认
import ExpansionConfirmation from './public/ExpansionConfirmation.vue'; // 扩展确认

export default {
  components: {
    AboutUS,
    Dispatch,
    PasswordRules,
    SchedulingConfirmation,
    ExpansionConfirmation,
  },
  data() {
    return {
      usernames: '', // 登录用户名
      role: '', // 角色
      naviActive: 'overview', // 侧边导航默认选中
      powerNavi: {},
      THElogoTitle: "CRH",
      transferModal: 0, // 动态资源调度
      aboutModal: 0, // 关于我们
      moveTotal: 0, // 迁移提醒
      totalData: 0, // 告警数据 总
      critical: 0, // 告警数据 严重
      major: 0, // 告警数据 重要
      warning: 0, // 告警数据 次要
      info: 0, // 告警数据 提示
      gaojingData: null, // 告警刷新 5分钟
      calll: [
        { status:"migrating", vm:"192.168.0.0", source:"主机A", target:"主机B" },
        { status:"wait", vm:"192.168.1.0", source:"主机C", target:"主机D" },
        { status:"success", vm:"192.168.2.0", source:"主机E", target:"主机F" },
        { status:"error", vm:"192.168.3.0", source:"主机G", target:"主机H" }
      ],
      rulesModal: 0, // 密码规则
      schedulingModal: 0, // 调度确认
      schedulingTotal: 0, // 调度数据
      expansionModal: 0, // 扩展确认
      expansionTotal: 0, // 扩展数据
      todayTime: {
        day:undefined, // 导航时间 日
        years:undefined, // 导航时间 年月
        week:undefined, // 导航时间 周
      },
    };
  },
  created(){
    this.load()
    this.timeQuery()
  },
  watch:{
    $route:{
      handler(news , old) {
        this.naviActive = this.$route.path.split('/')[1]
        if(news.path!=='/systemFunction'){
          this.$store.state.power.systemFunctionTab = ''
        }
        if(news.path!=='/logmanage'){
          this.$store.state.power.logManagementTab = ''
        }
        if(news.path!=='/computingResource') {
          this.$store.state.power.computingResourceTab = ''
        }
        if(news.path!=='/monitoring') {
          this.$store.state.power.itMonitoringTab = ''
        }
      }
    },
  },
  mounted() {
    this.$Notice.config({
      top: 50,
    });
    // 获取关于我们弹框数据
    this.THElogoTitle = window.gurl.THElogoTitle
    this.naviActive = this.$route.path.split('/')[1]
    this.menusQuery()
    
    // 版本隐藏
    this.moduleShow('migrate')?this.noticeClick():null
    this.gaojingData=setInterval(() => {
      this.powerNavi.jiqungaojing?this.giveANalarmGET():null
      this.load()
      // 版本隐藏
     this.moduleShow('migrate')?this.noticeClick():null
     this.powerNavi.dongtaiziyuankuozhanbiao?this.queryExpansion():null
     this.powerNavi.dongtaiziyuandiaodubiao?this.queryDispatch():null
    },30000)
  },
  methods: {
    // 获取时间
    timeQuery(){
      let currentDate = new Date();
      let year = currentDate.getFullYear();
      let month = currentDate.getMonth() + 1;
      let day = currentDate.getDate()
      let dayOfWeek = currentDate.getDay()
      let daysOfWeek = ["星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"]
      this.todayTime.day = day
      this.todayTime.years = year+"-"+(month < 10?'0' + month:month)
      this.todayTime.week = daysOfWeek[dayOfWeek]
    },
    // 获取权限
    load() {
      if(document.cookie.length > 0) {
        let arr = document.cookie.split('; ');
        let arrData = new Object()
        arr.forEach(item=>{
          let li = item.split('=')
          arrData[li[0]]=li[1]
        })
        this.usernames = arrData.username
        this.role = arrData.role
        window.sessionStorage.setItem('username',arrData.username)
      }
    },
    // 运维助手跳转
    YJclick() {
      if(this.naviActive!=="oneClickCleaning") {
        this.$router.push("/oneClickCleaning");
      }
    },
    // 全屏跳转
    screenClick(){
      let routeInfo = this.$router.resolve({path:"/screenPage"});
      window.open(routeInfo.href,'_blank')
      // this.$router.push("/screenPage");
    },
    // 邮件跳转
    warningMessage(){
      this.$router.push("/logmanage");
    },
    // 导航点击事件
    pushUrl(item) {
      if(this.naviActive!==item) {
        this.$router.push("/" + item);
      }
    },
    // 导航权限查询
    menusQuery(){
      powerCodeQuery({
        module_code:[
          'gailan',
          'jisuanziyuan',
          'cunchuziyuan',
          'wangluoziyuan',
          'ziyuanjiankong',
          'xunijifenpei',
          'rizhiguanli',
          'xitonggongneng',
          'jiqungaojing',
          'yunweizhushou',
          'shezhi',
          'tuichudenglu',
          'xiugaimima',
          'mimaguize',
          'dongtaiziyuandiaodu',
          'guanyuwomen',
          'dongtaiziyuankuozhanbiao',
          'dongtaiziyuandiaodubiao',
        ]
      }).then(callback=>{
        this.powerNavi = callback.data.data
        this.powerNavi.jiqungaojing?this.giveANalarmGET():null
        this.powerNavi.dongtaiziyuankuozhanbiao?this.queryExpansion():null
        this.powerNavi.dongtaiziyuandiaodubiao?this.queryDispatch():null
      })
    },
    // 告警信息获取
    giveANalarmGET(){
      var path = window.location.href;
      var parts = path.split("/");
      var lastPart = parts[parts.length - 1]; 
      if (lastPart === "") {
        lastPart = parts[parts.length - 2];
      }
      if(lastPart !== "login"){
        alarmDataQuery()
        .then(callback=>{
          if(callback.data.length!==0) {
            this.totalData=callback.data.length
            let yanzhong =0
            let zhongyao =0
            let ciyao =0
            let tishi =0
            callback.data.forEach(item=>{
              if(item.severity=="critical"){
                yanzhong=yanzhong+1
              }
              if(item.severity=="major"){
                zhongyao=zhongyao+1
              }
              if(item.severity=="warning"){
                ciyao=ciyao+1
              }
              if(item.severity=="info"){
                tishi=tishi+1
              }
            })
            this.critical=yanzhong
            this.major=zhongyao
            this.warning=ciyao
            this.info=tishi
          }
        })
      }else {
        console.log('登录页禁止调用告警')
      }
    },
    schedulingClick(){
      this.schedulingModal = Date.now()
    },
    expansionClick(){
      this.expansionModal = Date.now()
    },
    // 告警信息跳转
    alertingClick(){
      this.$store.state.power.logManagementTab = "集群告警"
      if(this.naviActive!=="logmanage") {
        this.$router.push('/logmanage')
      }
    },
    mennuTopClick(item){
      if(item == 'signOut') {
        logout()
        .then(callback=>{
          if(callback.data.msg == 'ok') {
            this.$Notice.destroy()
            clearInterval(this.gaojingData)
            sessionStorage.clear()
            this.$router.push('/login')
            this.$Message.success({
              background: true,
              closable: true,
              duration: 5,
              content: "用户已退出登录",
            })
          }else {
            this.$Message.error({
              background: true,
              closable: true,
              duration: 5,
              content: callback.data.msg,
            })
          }
        })
        .catch((error) => {
          this.$emit("return-error",'退出登录失败');
        })
        
      }else if(item == 'modifyPASS') {
        this.$store.state.power.systemFunctionTab = "修改密码"
        if(this.naviActive!=="systemFunction") {
          this.$router.push('/systemFunction')
        }
      }else if(item == 'pawRules'){
        this.rulesModal = Date.now()
      }else if(item == 'resourceScheduling') {
        this.downtimeState()
      }else if(item == 'aboutUs') {
        this.aboutModal = Date.now()
      }
    },
    // 通知提醒
    noticeClick() {
      // setInterval(() => {
        this.$Notice.destroy()
        notificationInformation()
        .then(callback=>{
          this.moveTotal = 0
          callback.data.result.forEach((em,index)=>{
            em.status==null?this.moveTotal++:''
            em.status==null?
            this.$Notice.open({
              duration: 0,
              name:em.domain,
              title:em.domain +'虚拟机迁移推荐',
              render:h=>{
                return h("div",{style:{display: 'flex',justifyContent: 'space-between',alignItems:'center'}},[
                  h('span',{style:{padding:'10px 0'}},"从"+em.from_host+' 迁移到 '+em.in_host),
                  h('div',{style:{display: 'flex',alignItems:'center'}},[
                    h('span',[
                      h('i-button',{
                        style:{margin:"0 10px"},
                        on:{
                          click:()=>{
                            this.$Modal.confirm({
                              title: '不参与迁移',
                              content: '<span style="font-weight: 600;color:green">'+em.domain+'</span>虚拟机将不参与迁移操作。',
                              onOk: () => {
                                vmGroupTableMigrationConfirm({vmid:em.id,enable:'false'})
                                .then(noCal=>{
                                  noCal.data.msg == "ok"?
                                  this.$Message.success({
                                    background: true,
                                    closable: true,
                                    duration: 5,
                                    content: "迁移设置已完成",
                                  }):
                                  this.$Message.error({
                                    background: true,
                                    closable: true,
                                    duration: 5,
                                    content: "迁移设置失败",
                                  })
                                  setTimeout(() => {
                                    this.noticeClick()
                                  }, 1000)
                                })
                              },
                              onCancel: () => {}
                            });
                          }
                        }
                      },'不迁移'),
                      h('i-button',{
                        class:'move_in',
                        on:{
                          click:()=>{
                            this.$Modal.confirm({
                              title: '迁移确认',
                              content: '<p>是否将虚拟机<span style="font-weight: 600;color:green">'+em.domain+'</span><br>从<span style="font-weight: 600">'+em.from_host+'</span>迁移到<span style="font-weight: 600">'+em.in_host+'</span>?</p>',
                              onOk: () => {
                                notificationInformation(em)
                                .then(okCal=>{
                                  setTimeout(() => {
                                    this.noticeClick()
                                  }, 1000)
                                })
                              },
                              onCancel: () => {}
                            });
                          }
                        }
                      },'迁移'),
                    ])
                  ])
                ])
              }
            })
            :""
          })
        })
      // }, 300000);
       
      // this.$Notice.open({
      //   duration: 0,
      //   title: '全部提醒操作',
      //   render:h=>{
      //     return h("div",{style:{display: 'flex', justifyContent: 'flex-end'}},[
      //       h('i-button',{
      //         style:{margin:"0 10px"},
      //         props: {
      //           disabled: false,
      //         },
      //         on:{
      //           click:()=>{
      //             this.$Notice.destroy()
      //           }
      //         }
      //       },'全部清空')
      //     ])
      //   }
      // })
      // this.calll.forEach((em,index)=>{
      //   this.$Notice.open({
      //     duration: 0,
      //     name:em.vm,
      //     title: '虚拟机'+ em.vm +'迁移确认',
      //     render:h=>{
      //       return h("div",{style:{display: 'flex',justifyContent: 'space-between',alignItems:'center'}},[
      //         h('h2',{style:{padding:'10px 0'}},em.source+' 迁移 '+em.target),
      //         h('div',{style:{display: 'flex',alignItems:'center'}},[
      //           // h('span',{style:{fontSize:'20px',}},[
      //           //   h('Icon',{
      //           //     props: {
      //           //       type: em.status=="migrating"?"md-move":em.status=="wait"?"md-information-circle":em.status=="success"?"md-checkmark-circle":"md-close-circle"
      //           //     },
      //           //     style:{color: em.status=="migrating"?"#000":em.status=="wait"?"#ccc":em.status=="success"?"green":"red",marginRight:"10px"}
      //           //   }),
      //           //   h('span',{style:{color: em.status=="migrating"?"#000":em.status=="wait"?"#ccc":em.status=="success"?"green":"red"}},em.status=="migrating"?"迁移中":em.status=="wait"?"等待":em.status=="success"?"成功":"失败"),
      //           // ]),
      //           h('span',[
      //             h('i-button',{
      //               style:{margin:"0 10px"},
      //               props: {
      //                 // disabled: em.status=="wait"? false:true,
      //               },
      //               on:{
      //                 click:()=>{
      //                   this.$Modal.confirm({
      //                     title: '不参与迁移',
      //                     content: '<p>虚拟机</p><span style="font-weight: 600;color:green">'+em.vm+'</span>将不参与迁移操作。',
      //                     onOk: () => {
      //                       this.$Notice.close(this.calll[index].vm)
      //                       // this.calll.splice(index,1)
      //                       // this.noticeClick()
      //                     },
      //                     onCancel: () => {}
      //                   });
      //                 }
      //               }
      //             },'不迁移'),
      //             h('i-button',{
      //               class:'move_in',
      //               props: {
      //                 // disabled: em.status=="wait"? false:true,
      //               },
      //               // class:'move_in',
      //               on:{
      //                 click:()=>{
      //                   this.$Modal.confirm({
      //                     title: '迁移确认',
      //                     content: '<p>是否将虚拟机<span style="font-weight: 600;color:green">'+em.vm+'</span><br>由<span style="font-weight: 600;font-size:25px">'+em.source+'</span>迁移至<span style="font-weight: 600;font-size:25px">'+em.target+'</span>?</p>',
      //                     onOk: () => {
      //                       this.$Notice.close(this.calll[index].vm)
      //                       // this.calll.splice(index,1)
      //                       // this.noticeClick()
      //                     },
      //                     onCancel: () => {}
      //                   });
      //                 }
      //               }
      //             },'确认'),
      //           ])
      //         ])
      //       ])
      //       // return h("div",[
      //       //   ('h1',{},em.source+'迁移'+em.target),
      //       //   ('div',[
      //       //     ('span',{},em.status=="wait"?"等待":em.status=="success"?"成功":"失败"),
      //       //     ('i-button',{
      //       //       class:'move_in',
      //       //       on:{
      //       //         click:()=>{
      //       //           console.log(em)
      //       //         }
      //       //       }
      //       //     },确认),
      //       //   ])
      //       // ])
      //     }
      //   })
      // })
    },
    // 模块显示隐藏判断
    moduleShow(item){
      return window.gurl.PageModule.indexOf(item) == -1?false:true
    },
    // 查询自动宕机状态
    downtimeState(){
      automaticDowntimeMigrationQuery().then(callback =>{
        console.log('callback',callback)
        if(callback.data.enabled_auto){
          if(callback.data.enabled_auto=='on'){
            this.transferModal = Date.now()
          }else {
            this.$Message.error({
              background: true,
              closable: true,
              duration: 5,
              content: '请先开启安全配置中的自动宕机迁移',
            });
          }
        }else{
          this.$Message.error({
              background: true,
              closable: true,
              duration: 5,
              content: '请先开启安全配置中的自动宕机迁移',
            });
        }
      })
    },
    // 动态资源扩展表查询
    queryExpansion(){
      vmextendQuery().then(callback=>{
        if(callback.data.value!==undefined){
          this.expansionTotal = callback.data.value.length
        }else {
          this.expansionTotal = 0          
        }
      })
    },
    // 动态资源调度表查询
    queryDispatch(){
      dispatchTableQuery().then(callback=>{
        if(callback.data.msg == 'ok') {
          this.schedulingTotal = 1
        }else {
          this.schedulingTotal = 0
        }
      })
    },
    schedulingOK(){
      this.queryDispatch()
    },
    expansionOK(){
      this.queryExpansion()
    },
  },
  beforeDestroy() {
    clearInterval(this.gaojingData)
  },
};
</script>