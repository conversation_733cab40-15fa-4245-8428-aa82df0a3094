<template>
  <Modal v-model="model" width="600" :mask-closable="false">
    <template #header><p>主机移出<span style="red">{{groupSelect.name}}</span>集群组</p></template>
    <p>是否移出主机 <span style="font-weight: 600">{{ hostname.toString() }}</span> ？</p>
    <div slot="footer">
      <Button type="text" @click="model = false">取消</Button>
      <Button type="primary" @click="modelOK" :disabled="disabled">确认</Button>
    </div>
  </Modal>
</template>
<script>
import {
  clusterGroupRemove, // 集群组 移出
} from '@/api/physics';
export default {
  props: {
    removeTime: String,
    groupSelect: Object,
    tableSelect: Array,
  },
  watch: {
    removeTime(news){
      // this.formItem.title = '主机移出 '+this.groupSelect.name+ ' 集群组'
      this.hostname = new Array()
      this.tableSelect.forEach(em=>{
        this.hostname.push(em.hypervisor_hostname)
      })
      this.formItem.groupID = this.groupSelect.id
      this.model = true
      this.disabled = false
    }
  },
  data() {
    return {
      model: false,
      disabled: false,
      formItem:{
        title: '',
        groupID: null,
      },
      hostname: [],
    };
  },
  methods: {
    modelOK() {
      this.disabled = true
      for(var i=0; i<this.hostname.length;i++) {
        if(i==this.hostname.length-1){
          clusterGroupRemove({data:{id:this.formItem.groupID,name:this.groupSelect.name,hostname: this.hostname[i]}})
          .then(callback=>{
            this.model = false
            this.$emit("host-ok",'移出主机操作完成');
          })
        }else {
          clusterGroupRemove({data:{id:this.formItem.groupID,name:this.groupSelect.name,hostname: this.hostname[i]}})
        }
      }
      
    },
  },
};
</script>