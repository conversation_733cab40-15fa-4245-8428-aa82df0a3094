<template>
  <div class="line-echarts-wrap">
    <div class="title-wraps mb-10">客户端</div>
    <div class="new-box-wrap">
      <div class="line-title-box">
      <div class="read-write-wrap">
        <div class="title-name-size-wrap">
          <span class="name-size-wrap">客户端读写</span>
          <div class="color-name-wrap">
            <div>
              读: 
              <span class="text-color1-wrap">{{readBytesEnd}}KB</span>
            </div>
            <div class="ml-10">
              写: 
              <span class="text-color2-wrap">{{ writeBytesEnd }}KB</span>
            </div>
          </div>
          <div class="color-name-wrap">
            <div class="color1-box-wraps mr-5"></div>
            <span>读</span>
            <div class="color2-box-wraps ml-10 mr-5"></div>
            <span>写</span>
          </div>
        </div>
      </div>
      <div class="line-charts-wrap" id="line-charts-wrap"></div>
    </div>
    <div class="line-title-box">
      <div class="read-write-wrap">
        <div class="title-name-size-wrap">
          <span class="name-size-wrap">客户端吞吐</span>
          <div class="color-name-wrap">
            <div>
              读: 
              <span class="text-color3-wrap">{{ readOpsEnd }}KB</span>
            </div>
            <div class="ml-10">
              写: 
              <span class="text-color4-wrap">{{ writeOpsEnd }}KB</span></div>
          </div>
          <div class="color-name-wrap">
            <div class="color3-box-wraps mr-5"></div>
            <span>读</span>
            <div class="color4-box-wraps ml-10 mr-5"></div>
            <span>写</span>
          </div>
        </div>
      </div>
      <div class="line-charts-wrap" id="line-charts-wrap2"></div>
    </div>
    </div>
    <div class="line-title-box1">
      <div class="read-write-wrap">
        <div class="title-name-size-wrap">
          <span class="name-size-wrap">恢复吞吐量</span>
          <div class="recover-num-wrap">{{ RecoveryBytesEnd }}MB /S</div>
          <div class="color-name-wrap">
            <div class="color5-box-wraps mr-5"></div>
            <span>吞吐量</span>
          </div>
        </div>
      </div>
      <div class="line-charts-wrap" id="line-charts-wrap3"></div>
    </div>
  </div>
</template>

<script>
  import * as echarts from 'echarts';
  export default {
    props: {
      datas: {
        type: Object,
        default: () => {}
      }
    },
   data() {
     return{
       setIntervalFun: null,
        queryList: [
          'ceph_client_io_read_bytes{}[120s]',
          'ceph_client_io_write_bytes{}[120s]',
          'ceph_client_io_read_ops{}[120s]',
          'ceph_client_io_write_ops{}[120s]'
        ],
        readBytesList: [],
        writeBytesList:[],
        readOpsList: [],
        writeOpsList: [],
        timeList: [],
        readBytesEnd: '',
        writeBytesEnd: '',
        RecoveryBytesEnd: 0,
        readOpsEnd: '',
        writeOpsEnd: '',
        myChart1: null,
        myChart2: null,
     }
    },
    computed:{
      readBytesListUpdate(){
        return this.readBytesList.pop()
      },
      writeBytesListUpdate() {
        return this.writeBytesList.pop()
      }
    },
    mounted(){
      this.forQueryList()
      this.getCephRecoveryFun()
      this.setIntervalFun = setInterval(() => {
        this.forQueryList()
        this.getCephRecoveryFun()
      }, 10000);
    },
    methods:{
       async forQueryList() {
        this.readBytesList = []
        this.writeBytesList =[]
        this.readOpsList = []
        this.writeOpsList= []
        this.timeList = []
        let time = new Date().getTime() /1000
        for(let i =0;i<4; i++) {
          let obj = {
            query: this.queryList[i],
            time: time
          }
           await this.getLineData(obj, i)
        }
          this.lineCharts()
          this.lineCharts2()
      },
      async getLineData(obj, type) {
        await this.$axios.get(`/apiquery/api/v1/query?query=${obj.query}&time=${obj.time}`).then(res => {
            if(type === 0) { // 客户端读
             res.data.data.result.length && res.data.data.result[0].values.map(item => {
                let num = this.toTimeString(item[0])
                this.timeList.push(num)
                this.readBytesList.push(item[1])
              })
              this.readBytesEnd = this.readBytesList[this.readBytesList.length-1]
              // this.lineCharts()
            } 
            if(type ===1) { // 客户端写
              res.data.data.result.length && res.data.data.result[0].values.map(item => {
                this.writeBytesList.push(item[1])
              })
              this.writeBytesEnd = this.writeBytesList[this.writeBytesList.length-1]
            }
            if(type ===2) { // 客户端吞吐读
              res.data.data.result.length && res.data.data.result[0].values.map(item => {
                this.readOpsList.push(item[1])
              })
              this.readOpsEnd = this.readOpsList[this.readOpsList.length-1]
            }
            if(type ===3) { // 客户端吞吐写
              res.data.data.result.length && res.data.data.result[0].values.map(item => {
                this.writeOpsList.push(item[1])
              })
              this.writeOpsEnd = this.writeOpsList[this.writeOpsList.length-1]
            }
          }
        );
      },
      toTimeString(time) {
        // 比如需要这样的格式 yyyy-MM-dd hh:mm:ss
        var date = new Date(time * 1000);
        var y=date.getFullYear();
        var m=date.getMonth()+1;
        var d=date.getDate();
        var h=date.getHours();
        var m1=date.getMinutes();
        var s=date.getSeconds();
        m = m<10?("0"+m):m;
        d = d<10?("0"+d):d;
        return h+':'+m1 + ':' +s
      },
      lineCharts() {
        var chartDom = document.getElementById('line-charts-wrap');
        if (this.myChart1 == null) { 
          this.myChart1 = echarts.init(chartDom);
        }
        const colors = ['#00cdc5', '#fe6500'];
        var option;
        option = {
          color: colors,
          tooltip: {
            trigger: 'axis'
          },
          legend: {
            data: ['读', '写']
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: this.timeList
          },
          yAxis: {
            type: 'value'
          },
         
          series: [
            {
              name: '读',
              type: 'line',
              data: this.readBytesList,
              lineStyle: {
                colors:'#00cdc5'
              },
              smooth: true,
              areaStyle: {
                normal: {
                  color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    { offset: 1, color: "#ffffff" },
                    { offset: 0.5, color: "#ecfbfb" },
                    { offset: 0, color: "#e6faf9" }
                  ])
                }
              }, //填充区域样式
            },
            {
              name: '写',
              type: 'line',
              data: this.writeBytesList,
              lineStyle: {
                colors:'#fe6500'
              },
              smooth: true,
              areaStyle: {
                normal: {
                  color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    { offset: 1, color: "#ffffff" },
                    { offset: 0.5, color: "#fff8f3" },
                    { offset: 0, color: "#fff0e6" }
                  ])
                }
              }, //填充区域样式
            },
            
          ]
        };

        option && this.myChart1.setOption(option);
      },
      lineCharts2() {
        var chartDom = document.getElementById('line-charts-wrap2');
        if (this.myChart2 == null) { 
          this.myChart2 = echarts.init(chartDom);
        }
        const colors = ['#366bff', '#ffaf0f'];
        var option;
        option = {
          color: colors,
          tooltip: {
            trigger: 'axis'
          },
          legend: {
            data: ['读', '写']
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: this.timeList
          },
          yAxis: {
            type: 'value'
          },
          series: [
            {
              name: '读',
              type: 'line',
              data: this.readOpsList,
              lineStyle: {
                colors:'#366bff'
              },
              smooth: true,
              areaStyle: {
                normal: {
                  color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    { offset: 1, color: "#ffffff" },
                    { offset: 0.5, color: "#f1f5fe" },
                    { offset: 0, color: "#eaf0fd" }
                  ])
                }
              }, //填充区域样式
            },
            {
              name: '写',
              type: 'line',
              data: this.writeOpsList,
              lineStyle: {
                colors:'#ffaf0f'
              },
              smooth: true,
              areaStyle: {
                normal: {
                  color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    { offset: 1, color: "#ffffff" },
                    { offset: 0.5, color: "#f6f6f5" },
                    { offset: 0, color: "#fff8e9" }
                  ])
                }
              }, //填充区域样式
            },
          ]
        };

        option && this.myChart2.setOption(option);

      },
      lieCharts3(dataArr, timeListArr) {
        var chartDom = document.getElementById('line-charts-wrap3');
        var myChart = echarts.init(chartDom);
        const colors = ['#ff9b61', '#ff9b61'];
        var option;
        option = {
          color: colors,
          tooltip: {
            trigger: 'axis'
          },
          grid: {
            left: '3%',
            right: '3%',
            bottom: '6%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: timeListArr
          },
          yAxis: {
            type: 'value'
          },
          series: [
            {
              name: '读',
              type: 'line',
              stack: 'Total',
              data: dataArr,
              lineStyle: {
                colors:'#ff9b61'
              },
              smooth: true,
              areaStyle: {
                normal: {
                  color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    { offset: 1, color: "#ffffff" },
                    { offset: 0.5, color: "#fffbf8" },
                    { offset: 0, color: "#ffede3" }
                  ])
                }
              }, //填充区域样式
            },
          ]
        };

        option && myChart.setOption(option);
      },
      getCephRecoveryFun(){
        let time = new Date().getTime() / 1000
        this.$axios.get(`/apiquery/api/v1/query?query=ceph_recovery_io_bytes%5B2m%5D&time=${time}`).then(res => {
          let timeListArr = []
          let dataArr = []
          res.data.data.result.length &&  res.data.data.result[0].values.forEach(item => {
            let num = this.toTimeString(item[0])
            timeListArr.push(num)
            dataArr.push(item[1])
          });
          this.RecoveryBytesEnd = dataArr[dataArr.length - 1]
          this.lieCharts3(dataArr, timeListArr)
        })
      }
    },
    destroyed () {
      /**
       * 清除定时器
       */
      clearInterval(this.setIntervalFun)
    }
  }
</script>

<style scoped lang='less'>
  .line-echarts-wrap {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .title-wraps {
      font-size: 20px;
      font-weight: 600;
    }
    .new-box-wrap {
      display: flex;
      align-items: center;
      width: 100%;
      height: 47%;
      justify-content: space-between;
      margin-bottom: 18px;
      .line-title-box {
        width: 49.2%;
        height: 100%;
        border-radius: 8px;
        background: white;
        display: flex;
        flex-direction: column;
      .read-write-wrap {
        width: 100%;
        height: 35px;
        padding: 10px 0 0 10px;
        display: flex;
        flex-direction: column;
        .title-name-size-wrap {
          width: 100%;
          display: flex;
          height: 25px;
          align-items: center;
          justify-content: space-between;
          padding-right: 18px;
          .capacity-name-wrap {
            height: 100%;
            width: 5px;
            background: #e40413;
          }
          .name-size-wrap {
            color: #101010;
            font-size: 16px;
            font-weight: 600;
            margin-left: 10px;
          }
          .color-name-wrap {
            display: flex;
            align-items: center;
            justify-content: center;
            color: #4f4f4f;
            .color1-box-wraps {
              width: 11px;
              height: 11px;
              background: #00cdc5;
            }
            .text-color1-wrap {
              color: #00cdc5;
            }
            .text-color2-wrap {
              color: #fe6500;
            }
            .text-color3-wrap {
              color: #366bff;
            }
            .text-color4-wrap {
              color: #ffaf0f;
            }
            .color2-box-wraps {
              width: 11px;
              height: 11px;
              background: #fe6500;
            }
            .color3-box-wraps {
              width: 11px;
              height: 11px;
              background: #366bff;
            }
            .color4-box-wraps {
              width: 11px;
              height: 11px;
              background: #ffaf0f;
            }
          }
        }
        .read-write-icon-wrap {
          flex: 1;
          display: flex;
          justify-content: space-between;
          padding: 0 10%;
          .left-wrap {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            .read-number-wrap {
              display: flex;
              align-items: center;
              div{
                width: 20px;
                height: 20px;
                background: #66e361;
              }
              span {
                margin-left: 15px;
              }
            }
            .write-number-wrap {
              display: flex;
              align-items: center;
              margin-top: 20%;
              div{
                width: 20px;
                height: 20px;
                background: #ea5858;
              }
              span {
                margin-left: 15px;
              }
            }
          }
          .right-wrap {
            width: 40%;
            height: 100%;
            color: #ea5858;
            display: flex;
            align-items: center;
            flex-direction: column;
            justify-content: center;
            font-size: 20px;
            padding-bottom: 3%;
          }
        }
      }
      .line-charts-wrap {
        flex: 1;
      }
    }
    }
    
    .line-title-box1 {
      flex: 1;
      border-radius: 8px;
      background: white;
      display: flex;
      flex-direction: column;
      .read-write-wrap {
        width: 100%;
        height: 35px;
        padding: 10px 0 0 10px;
        display: flex;
        flex-direction: column;
        .title-name-size-wrap {
          width: 100%;
          display: flex;
          height: 25px;
          align-items: center;
          justify-content: space-between;
          padding-right: 18px;
          .capacity-name-wrap {
            height: 100%;
            width: 5px;
            background: #e40413;
          }
          .name-size-wrap {
            color: #101010;
            font-size: 16px;
            font-weight: 600;
            margin-left: 10px;
          }
          .color-name-wrap {
            display: flex;
            align-items: center;
          }
          .color5-box-wraps {
            width: 11px;
            height: 11px;
            background: #ff9b61;
          }
          .recover-num-wrap {
            color: #4f4f4f;
          }
        }
        .read-write-icon-wrap {
          flex: 1;
          display: flex;
          justify-content: space-between;
          padding: 0 10%;
          .left-wrap {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            .read-number-wrap {
              display: flex;
              align-items: center;
              div{
                width: 20px;
                height: 20px;
                background: #66e361;
              }
              span {
                margin-left: 15px;
              }
            }
            .write-number-wrap {
              display: flex;
              align-items: center;
              margin-top: 20%;
              div{
                width: 20px;
                height: 20px;
                background: #ea5858;
              }
              span {
                margin-left: 15px;
              }
            }
          }
          .right-wrap {
            width: 40%;
            height: 100%;
            color: #ea5858;
            display: flex;
            align-items: center;
            flex-direction: column;
            justify-content: center;
            font-size: 20px;
            padding-bottom: 3%;
          }
        }
      }
      .line-charts-wrap {
        flex: 1;
      }
    }
  }
</style>