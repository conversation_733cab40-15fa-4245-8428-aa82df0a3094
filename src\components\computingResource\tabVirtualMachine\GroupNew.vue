<template>
  <div>
    <Modal
      v-model="model"
      width="600"
      :mask-closable="false"
    >
      <template #header><p>新建虚拟机组</p></template>
      <Form :model="formItem" ref="formItem" :rules="ruleValidate" :label-width="150">
        <FormItem label="父级虚拟机组">
          <Input
            v-model="formItem.parent"
            disabled
          ></Input>
        </FormItem>
        <FormItem>
          <h4>
            父级虚拟机组分组下创建新的子级虚拟机组，<br />请确认好父级虚拟机组
          </h4>
        </FormItem>
        <FormItem label="子级虚拟机组" prop="name">
          <Input
            v-model="formItem.name"
            placeholder="请输入虚拟机组名称"
          ></Input>
        </FormItem>
      </Form>
      <div slot="footer">
        <Button type="text" @click="model = false">取消</Button>
        <Button type="primary" @click="modalOK" :disabled="disabled">确认</Button>
      </div>
    </Modal>
  </div>
</template>
<script>
import {  vmGroupNewBuilt } from '@/api/virtualMachine';  // 虚拟机组 新建
export default {
  props: {
    groupSelect:Object,
    newGroupTime:String,
  },
  watch: {
    newGroupTime(news){
      this.formItem.parent = this.groupSelect.name
      this.formItem.id = this.groupSelect.id
      this.model = true
      this.disabled = false
      this.$refs.formItem.resetFields()
    }
  },
  data(){
    const propName = (rule, value, callback) => {
      let list = /^[\u4e00-\u9fa5_a-zA-Z0-9@_.-]{2,32}$/;
      if (list.test(value)) {
        callback();
      } else {
        callback(new Error("2-32 个中文、英文、数字、特殊字符(@_.-"));
      }
    };
    return {
      model:false,
      disabled: false,
      formItem:{
        parent: '',
        name: '',
        id: ''
      },
      ruleValidate:{
        name:[
          { required: true, message: '必填项', trigger: 'change' },
          { validator: propName, trigger: "change" },
        ]
      }
    }
  },
  methods: {
    modalOK() {
      this.$refs.formItem.validate((valid) => {
        if(valid) {
          this.disabled = true
          vmGroupNewBuilt({
            name: this.formItem.name,
            id: this.formItem.id,
          })
          .then((callback) => {
            this.model = false;
            this.$emit("group-ok",'新建虚拟机组完成',"");
          })
          .catch((error) => {
            this.disabled = false
            this.$emit("group-error",'新建虚拟机组失败');
          })
        }
      })
    },
  }
}
</script>