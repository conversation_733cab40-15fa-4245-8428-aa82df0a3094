<template>
  <div class="iscsi-wrap">
    <div class="table-button-area">
      <div>
        <Button class="plus_btn" @click="createButs"><span class="icon iconfont icon-plus-circle"></span>创建</Button>
        <Button class="plus_btn" @click="overallButs"><span class="icon iconfont el-icon-finished"></span> 全局认证</Button>
      </div>
      <div>
        <!-- <Input v-model="tablePageForm.search_str" search enter-button placeholder="请输入名称" style="width: 300px" @on-search="tableSearchInput"/> -->
      </div>
    </div>
     <div style="position: relative">
      <Spin fix v-if="loading" size="large" style="color:#ef853a">
        <Icon type="ios-loading" size=50 class="demo-spin-icon-load"></Icon>
        <div style="font-size:16px;padding:20px">Loading...</div>
      </Spin>
      <Table :columns="journalColumn" :data="tableData" ref="table"></Table>
    </div>
    <div class="pages" v-if="tableData.length>0">
      <Pagination  :total="tableTotal" :page-size="tablePageForm.pagecount"  @page-change="getTableList" @page-size-change="onPageSizeChange"/>
    </div>
    <Modal v-model="dialogVisible" :title="`${selectType}ISCSI`" @on-ok="submitForm('formData')" @on-cancel="resetForm('formData', false)" :mask-closable="false">
      <div>
        <Form :model="formData" :label-width="170" :rules="rules" ref="formData">
          <FormItem label="后端设备名称(iqn)" prop="target_iqn">
            <Input v-model="formData.target_iqn" placeholder="请输入后端设备名称(iqn)"></Input>
          </FormItem>
          <FormItem label="选择入口" prop="portals">
            <Select v-model="formData.portals">
              <Option 
                v-for="(item, index) in portalsList" :value="`${item.name}_${item.ip}`" :key="`${item.name}_${item.ip}`" >{{ item.name}}_{{ item.ip }}
              </Option>
            </Select>
          </FormItem>
          <FormItem label="选择块设备" prop="disks">
            <Select v-model="formData.disks">
              <Option v-for="(item, index) in poolImageList" :value="`${item.pool_name}_${item.name}`" :key="`${item.pool_name}_${item.name}`" >{{ `${item.pool_name}_${item.name}` }}</Option>
            </Select>
          </FormItem>
          <FormItem label="认证方式" prop="acl_enabled">
            <Select v-model="formData.acl_enabled">
              <Option v-for="(item, index) in AUTHENTICATION_METHOD" :value="item.value" :key="index" >{{ item.label }}</Option>
            </Select>
          </FormItem>
          <FormItem label="后端设备用户名设置" prop="auth.user">
            <Input v-model="formData.auth.user" placeholder="请输入后端设备用户名设置"></Input>
          </FormItem>
          <FormItem label="后端设备密码设置" prop="auth.password">
           <Input v-model="formData.auth.password" placeholder="请输入后端设备密码设置"></Input>
          </FormItem>
          <FormItem label="客户端用户名设置">
            <Input v-model="formData.auth.mutual_user" placeholder="请输入客户端用户名设置"></Input>
          </FormItem>
          <FormItem label="客户端密码设置">
            <Input v-model="formData.auth.mutual_password" placeholder="请输入客户端密码设置"></Input>
          </FormItem>
        </Form>
      </div>
    </Modal>
    <GlobalAuthentication v-model="dialogVisible1" ref="refGlobalAuthentication"></GlobalAuthentication>
  </div>
</template>

<script>
  import Pagination from '@/components/public/Pagination.vue';
  import IscsiTree from '@/components/storeService/Iscsi/IscsiTree'
  import GlobalAuthentication from './GlobalAuthentication.vue'
  export default {
    components: {
     IscsiTree,
     Pagination,
     GlobalAuthentication
   },
   data() {
     var validator1 = (rule, val, callback) => {
        if (!val) {
          callback(new Error("请输入"));
        } else {
          const reg =/^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{8,20}$/
          if (val.length < 8) {
            callback(new Error("请输入至少8位"));
            return
          }
          if (reg.test(val)) {
            return callback();
          } else {
            callback(new Error("请输入至少8位字母加数字组合"));
          }
        }
      };
      var validator2 = (rule, val, callback) => {
        if (!val) {
          callback(new Error("请输入"));
        } else {
          const reg = /^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{12,20}$/;
          if (val.length < 12) {
            callback(new Error("请输入至少12位"));
            return
          }
          if (reg.test(val)) {
            return callback();
          } else {
            callback(new Error("请输入至少12位字母加数字组合"));
          }
        }
      };
     return{
       AUTHENTICATION_METHOD: [
        {
          label: 'CHAP',
          value: 'false'
        },
        {
          label: 'ACL',
          value: 'true'
        }
      ],
       selectType: '添加',
       operation: {
        add: '添加',
        edit: '编辑'
      },
      portalsList: [],
      poolImageList: [],
      tableTotal: 0,
      tableData:[
        {
          target_iqn: '测试数据',
          osdid: 1111111,
          path: '/test/dev',
          client: 'jjjj-ssss'
        },
        {
          target_iqn: '测试数据2222',
          osdid: 2222222,
          path: '/test/dev/obj',
          client: 'jjjj-ssss-96666'
        },
      ],
      loading:false,
      journalColumn: [
        { type: "expand", width: 50,
          render: (h, params) => {
            return h("div",{style:{width:'100%'}},[
              h(IscsiTree,{
                props: {infoData:params.row} 
              }),
            ]);
          },
        },
        { title: "后端设备名称", key: "target_iqn", sortable: true },
        { title: "块设备路径", key: "osdid", sortable: true,align: "center"  },
        { title: "入口", key: "path", sortable: true,align: "center" },
        { title: "客户端", key: "client", sortable: true ,align: "center" },
        { title: "操作",key: "action",width:200,
          render: (h, params) => {
            return h("div", [
              h("Button", {
                props:{
                  icon:"ios-create",
                  class:"close_btn"
                },
                on: {
                  click: () => {
                    this.tableAction(params.row,"edit")
                  },
                },
              },"编辑"),
              h("Button", {
                style:{color:'red',marginLeft:'10px'},
                props:{
                  icon:"ios-trash",
                  class:"plus_btn"
                },
                on: {
                  click: () => {
                    this.tableAction(params.row,"deleted")
                  },
                },
              },"删除"),
            ])
          }
        },
      ],
       tablePageForm: {
        page: 1, // 当前页
        pagecount: this.$store.state.power.pagecount, // 每页条
        search_str: "", // 收索
        order_type: "asc", // 排序规则
        order_by: "", // 排序列
      },
      dialogVisible: false,
      dialogVisible1: false,
       formData: {
         auth: {},
         target_controls: {},
         target_iqn: '',
         portals: [],
         disks: [],
         acl_enabled: 'false',
         auth: {
           user: '',
           password: '',
           mutual_user: '',
           mutual_password: ''
         }
       },
       oldFormData: {
        auth: {},
        target_controls: {},
        target_iqn: '',
        portals: [],
        disks: [],
        acl_enabled: 'false',
        auth: {
          user: '',
          password: '',
          mutual_user: '',
          mutual_password: ''
        }
       },
       rules: {
         target_iqn: [
           { required: true, message: '请输入后端设备名称(iqn)', trigger: 'blur' },
          ],
          portals: [
            { required: true, message: '请选择入口', trigger: 'blur' },
          ],
          disks: [
            { required: true, message: '请选择块设备', trigger: 'blur' },
          ],
          acl_enabled: [
            { required: true, message: '请选择认证方式', trigger: 'change' },
          ],
         'auth.user': [
            { required: true, message: '请输入后端设备用户名设置', trigger: 'blur' },
            { min: 8, validator: validator1, trigger: 'blur' }
          ],
         'auth.password': [
            { required: true, message: '请输入后端设备密码设置', trigger: 'blur' },
            { min: 12, validator: validator2, trigger: 'blur' }
          ],
          'auth.mutual_user': [
            { required: true, message: '请输入客户端用户名设置', trigger: 'blur' },
            { min: 8, validator: validator1, trigger: 'blur' }
          ],
          'auth.mutual_password': [
            { required: true, message: '请输入客户端密码设置', trigger: 'blur' },
            { min: 12, validator: validator2, trigger: 'blur' }
          ],
       },
     }
    },
    mounted(){
      this.getIscsiTargeList()
    },
    methods:{
      setFormData(type, datas) { // 添加或者编辑初始化数据
        if (type == this.operation.add) { // 添加操作
         this.formData = JSON.parse(JSON.stringify(this.oldFormData)) 
        } else { // 编辑操作
          const data =  JSON.parse(JSON.stringify(datas))
          const portalsList = []
          data.portals && data.portals.map(item => {
            let str = `${item.host}_${item.ip}`
            portalsList.push(str)
          })
          this.lodTargetName = data.target_iqn
          data.portals = portalsList
          const setdisksList = []
          data.disks && data.disks.map(item => {
            let str = `${item.pool}_${item.image}`
            setdisksList.push(str)
            item.pool_name = item.pool
            item.name = item.image
            return item
          })
          this.endDisks =  data.disks
          data.disks = setdisksList
          this.formData = data
        }
        this.getUsesTargetFun()
      },
       /**
       * 查询已经使用的image数据,过滤掉已使用的image
      */
      getUsesTargetFun() {
        this.$axios.get('/thecephapi/api/iscsi/target').then((res) => {
          let disksList = []
          res.forEach((item) => {
            disksList = [...disksList, ...item.disks]
          })
          this.getBlockImageList(disksList)
        })
      },
      /**
       * 获取入口选择
      */
      getIscsiTargeList() {
        this.$axios.get('/thecephapi/ui-api/iscsi/portals').then((res) => {
          this.portalsList = []
          res.data.length && res.data[0].ip_addresses.forEach(item => {
            let obj = {
              label: item,
              ip: item,
              name: res[0].name
            }
            this.portalsList.push(obj)
          });
        })
      },
      getBlockImageList(disksList) { // 过滤所有的块设备
        this.poolImageList = []
        this.$axios.get('/thecephapi/api/block/image').then((res) => {
          res.forEach(item => {
            this.poolImageList = [...this.poolImageList, ...item.value]
          })
          disksList.map((item) => {
            for(var i =0; i<this.poolImageList.length;) {
               if (item.image ==this.poolImageList[i].name || this.poolImageList[i].namespace) {
                this.poolImageList.splice(i, 1)
                i = 0
              } else {
                i++
              }
            }
          })
          if (this.selectType == this.operation.edit) { // 把自己本身的disks加入到里面
            this.poolImageList = [...this.poolImageList, ...this.endDisks]
          }
        })
      },
      createButs() {
        this.selectType = this.operation.add
        this.setFormData(this.operation.add)
        this.dialogVisible =!this.dialogVisible
      },
      overallButs() { // 全局认证
        // this.dialogVisible1 = true
        this.$refs.refGlobalAuthentication.showModal = true
      },
      // 获取卷池管理表数据
      getTableList() {
        this.loading = !this.loading
        this.$axios.get("/thecephapi/api/iscsi/target").then((res)=> {
          this.tableData= res.data.map(item => {
            // item.client = `${item.target_iqn}_${item.info.stat ? '正常' : '未连接'}`
            item.label = item.target_iqn
            return item
          })
          this.loading = !this.loading
        })
      },
       // 当前分页
      onPageChange(item) {
        this.tablePageForm.page = item
        this.volumePoolData();
      },
      // 每页条数
      onPageSizeChange(item) {
        this.$store.state.power.pagecount = item
        this.tablePageForm.pagecount = this.$store.state.power.pagecount
        this.tablePageForm.page = 1
        this.getTableList();
      },
      // 表格操作
      tableAction(row,action){
        switch (action) {
        case "edit":
          this.dialogVisible =!this.dialogVisible
          this.selectType = this.operation.edit
          this.setFormData(this.operation.edit,row)
          break;   
        case "deleted":
          this.$Modal.confirm({
            title: "删除卷池",
            content:
              '<p>是删除名称为<span style="font-weight: 600;color:red;word-wrap: break-word;">' +
              row.target_iqn +
              "</span>的设备？</p>",
            onOk: () => {
              this.$axios.delete(`/thecephapi/api/iscsi/target/${row.target_iqn}`).then((callback) => {
                  this.$Message.success({
                    background: true,
                    closable: true,
                    duration: 5,
                    content: "删除设备完成",
                  });
                  this.getTableList()
              }).catch((error) => {
                this.$Message.error({
                    background: true,
                    closable: true,
                    duration: 5,
                    content: "删除设备失败",
                  });
              });
            },
          });
          break;
        }
      },
      resetForm(formName, type) {
        this.$refs[formName].resetFields();
      },
      submitForm(formName) {
        this.$refs[formName].validate((valid) => {
          if (valid) {
            this.newIscsiTargetSubmit()
          } else {
            this.resetForm(formName)
            return false;
          }
        });
      },
      newIscsiTargetSubmit() {
        const formData = JSON.parse(JSON.stringify(this.formData))
        const portals = []
        formData.portals.forEach(item => {
          let itemSplit = item.split('_')
          let obj = {
            host: itemSplit[0],
            ip: itemSplit[1],
          }
          portals.push(obj)
        })
        const setDisks = []
        formData.disks.forEach((item, index) => {
          let disksSplit = item.split('_')
          let obj = {
            pool: disksSplit[0],
            image: disksSplit[1],
            backstore: "user:rbd",
            controls: {},
            lun: index 
          }
          setDisks.push(obj)
        })
        formData.portals = portals
        formData.disks = setDisks
        if (this.selectType == this.operation.add) {
          this.$axios.post("/thecephapi/api/iscsi/target",formData).then((res) => {
            this.$Message.success({
              background: true,
              closable: true,
              duration: 5,
              content: "后端设备创建成功",
            });
            this.getTableList()
            this.resetForm('formData', false)
          })
        } else {
          formData.new_target_iqn = formData.target_iqn
          formData.target_iqn = this.lodTargetName
          delete formData.info
          delete formData.label
          delete formData.client
          this.$axios.put( `/thecephapi/api/iscsi/target/${formData.target_iqn}`,formData).then((res) => {
          this.$Message.success({
            background: true,
            closable: true,
            duration: 5,
            content: "后端设备修改成功",
          });
          this.getTableList()
          this.resetForm('formData', false)
        })
        }
      }
    },
  }
</script>

<style scoped lang='less'>
  .iscsi-wrap {
    width: 100%;
    height: 100%;
  }
</style>