<template>
  <div>
    <Modal
      v-model="model"
      width="600"
      :mask-closable="false"
    >
    <template #header><p><span style="color:green"></span>修改配置-群操作</p></template>
    <div class="chongse">
      <Form :model="formItem" :label-width="100" class="resetTooltip">
        <FormItem label="VCPU核数">
          <div class="slider_area">
            <div style="width:370px">
              <Slider
                v-model="formItem.vcpus"
                :min="1"
                :max="64"
                :tip-format="formCpu"
              ></Slider>
            </div>
            <div style="width:80px">
              <InputNumber :min="1" :max="64" v-model="formItem.vcpus" :formatter="value => `${value}核`" :parser="value => value.replace('核', '')"></InputNumber>
            </div>
          </div>
        </FormItem>
        <FormItem label="内存容量">
          <div class="slider_area">
            <div style="width:370px">
              <Slider
                v-model="formItem.ram"
                :min="1"
                :max="256"
                :tip-format="formMemory"
              ></Slider>
            </div>
            <div style="width:80px">
              <InputNumber :min="1" :max="256" v-model="formItem.ram" :formatter="value => `${value}GB`" :parser="value => value.replace('GB', '')"></InputNumber>
            </div>
          </div>
        </FormItem>
        <FormItem label="硬盘容量">
          <div class="slider_area">
            <div style="width:370px">
              <Slider
                v-model="formItem.disk"
                :min="1"
                :max="2048"
                :tip-format="formDisk"
              ></Slider>
            </div>
            <div style="width:80px">
              <InputNumber :min="1" :max="2048" v-model="formItem.disk" :formatter="value => `${value}GB`" :parser="value => value.replace('GB', '')"></InputNumber>
            </div>
          </div>
        </FormItem>
      </Form>
    </div>
    <div slot="footer">
        <Button type="text" @click="model = false">取消</Button>
        <Button type="primary" @click="modelOK" :disabled="disabled">确认</Button>
      </div>
    </Modal>
  </div>
</template>
<script>
import {
  vmGroupTableConfig, // 虚拟机 修改配置 群操作
} from '@/api/virtualMachine';

export default {
  props: {
    tableArr: Array,
    configGroupTime: String,
  },
  watch: {
    configGroupTime(news){
      this.ids = new Array()
      this.names = new Array()
      this.tableArr.forEach(em=>{
        this.ids.push(em.id)
        this.names.push(em.name)
      })
      this.formItem.vcpus = 2;
      this.formItem.ram = 4;
      this.formItem.disk = 100;
      this.disabled = false
      this.model = true
    }
  },
  data(){
    return {
      disabled:true,
      model:false,
      // 虚拟机 修改配置弹框 
      formItem: {
        name: "",
        vcpus: 2,
        ram: 4,
        disk: 100,
        extdisk: 0,
      },
      ids: [],
      names: [],
    }
  },
  methods: {
    // 虚拟机表操作（设置）确认事件
    modelOK() {
      this.disabled = true
      this.$Modal.confirm({
        title: '提示',
        content: `<p>是否对勾选的虚拟机,进行相同的修改配置操作?</p>`,
        onOk: () => {
          vmGroupTableConfig({
            actions: ["resize"],
            vcpus: this.formItem.vcpus,
            ram: this.formItem.ram,
            disk: this.formItem.disk,
            ids: this.ids,
            names: this.names
          })
          .then((callback) => {
            this.$emit("return-ok",'修改配置虚拟机，群操作完成');
            this.model = false
          }).catch((error) => {
            this.disabled=false
            this.$emit("custom-event",'重新配置虚拟机，群操作失败');
          })
        },
        onCancel: () => {this.disabled=false}
      });
    },
    // cpu选择
    formCpu(val) {
      return val + "核";
    },
    // 内存选择
    formMemory(val) {
      return val + " GB";
    },
    // 硬盘选择
    formDisk(val) {
      return val + " GB";
    },
  }
}
</script>