<template>
  <div>
    <Modal v-model="model" width="600" :mask-closable="false">
      <template #header
        ><p>{{ type }}容器</p></template
      >
      <div style="padding: 5px">
        <span>是否{{ type }}下列容器？</span>
        <p style="color: red; word-wrap: break-word">
          {{ tableNames.toString() }}
        </p>
      </div>
      <div slot="footer">
        <Button type="text" @click="model = false">取消</Button>
        <!-- <Button type="primary" :loading="disabled" @click="modelOK">
          <span v-if="!disabled">确认</span>
          <span v-else>删除中</span>
        </Button> -->
        <Button type="primary" @click="modelOK" :disabled="disabled"
          >确认</Button
        >
      </div>
    </Modal>
  </div>
</template>
<script>
import { containerDeleted } from "@/api/container"; // 容器表 查询

export default {
  props: {
    tableArr: Array,
    deletionTime: String,
  },
  watch: {
    deletionTime(news) {
      this.type = news.split("/")[0];
      this.tableNames = this.tableArr.map((em) => {
        return em.name;
      });
      this.tableIDS = this.tableArr.map((em) => {
        return em.uuid;
      });
      this.model = true;
      this.disabled = false;
    },
  },
  data() {
    return {
      model: false,
      disabled: false,
      type: "",
      tableNames: [],
      tableIDS: [],
    };
  },
  methods: {
    modelOK() {
      this.disabled = true;
      containerDeleted({
        ids: this.tableIDS,
        names: this.tableNames,
        ...(this.type == "停止并删除" ? { stop: true } : null),
        ...(this.type == "强制删除" ? { force: true } : null),
      })
        .then((callback) => {
          this.model = false;
          if (callback.data.msg == "ok") {
            setTimeout(() => {
              this.$emit("return-ok", {
                msg: this.type + "容器操作完成",
                type: "ok",
              });
            }, 500);
          } else {
            this.$emit("return-ok", {
              msg: this.type + "容器操作失败",
              type: "error",
            });
          }
        })
        .catch((error) => {
          this.disabled = false;
          this.$emit("return-ok", {
            msg: this.type + "容器操作失败",
            type: "error",
          });
        });
    },
  },
};
</script>