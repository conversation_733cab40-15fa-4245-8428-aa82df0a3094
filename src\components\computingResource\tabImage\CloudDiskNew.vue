<template>
  <div>
    <Modal
      v-model="model"
      width="600"
      :mask-closable="false"
    >
      <template #header><p>创建云硬盘</p></template>
      <Form :model="formItem" ref="formItem" :rules="ruleValidate" :label-width="120">
        <FormItem label="当前镜像">
          <Input
            v-model="formItem.oldname"
            disabled
          ></Input>
        </FormItem>
        <FormItem label="云硬盘名称" prop="name">
          <Input
            v-model="formItem.name"
            placeholder="请输入云硬盘名称"
          ></Input>
        </FormItem>
        <FormItem label="云硬盘容量">
          <div class="slider_area">
            <div style="width:350px">
              <Slider v-model="formItem.size" :min='1' :max='2048' :tip-format="formMemory" ></Slider>
            </div>
            <div style="width:80px">
              <InputNumber :min='1' :max="2048" v-model="formItem.size" :formatter="value => `${value}GB`" :parser="value => value.replace('GB', '')"></InputNumber>
            </div>
          </div>
        </FormItem>
        <FormItem label="备注" >
          <Input v-model="formItem.description" placeholder="请输入备注信息"></Input>
        </FormItem>
      </Form>
      <div slot="footer">
        <Button type="text" @click="model = false">取消</Button>
        <Button type="primary" @click="modalOK" :disabled="disabled">确认</Button>
      </div>
    </Modal>
  </div>
</template>
<script>
import {
  cloudHardDriveNew, // 云硬盘 新建
} from '@/api/image'; 
export default {
  props: {
    tableRow:Object,
    cloudDiskTime:String,
  },
  watch: {
    cloudDiskTime(news){
      this.formItem.oldname = this.tableRow.name
      this.formItem.size = Math.ceil(this.tableRow.size/1024/1024/1024)
      this.formItem.oldsize = Math.ceil(this.tableRow.size/1024/1024/1024)
      this.formItem.id = this.tableRow.id
      this.formItem.description = ''
      this.$refs.formItem.resetFields()
      this.model = true
      this.disabled = false
    }
  },
  data(){
    const propName = (rule, value, callback) => {
      let list = /^[\u4e00-\u9fa5_a-zA-Z0-9@_.-]{2,32}$/;
      if (list.test(value)) {
        callback();
      } else {
        callback(new Error("2-32 个中文、英文、数字、特殊字符(@_.-"));
      }
    };
    return {
      model:false,
      disabled: false,
      formItem:{
        oldname: '',
        name: '',
        oldsize: 0,
        size: 0,
        description: '',
        id: '',
      },
      ruleValidate:{
        name:[
          { required: true, message: '必填项', trigger: 'change' },
          { validator: propName, trigger: "change" },
        ]
      }
    }
  },
  methods: {
    // 云硬盘大小
    formMemory (val) {
      return val + ' GB';
    },
    
    modalOK(){
      this.$refs.formItem.validate((valid) => {
        if(valid){
          if(this.formItem.size>=this.formItem.oldsize) {
            this.disabled = true
            cloudHardDriveNew({
              imageRef: this.formItem.id,
              name: this.formItem.name,
              size: this.formItem.size,
              description: this.formItem.description
            })
            .then((callback) => {
              if(callback.data.id!==undefined) {
                this.model=false
                this.$emit("return-ok",'镜像创建云硬盘操作完成');
              }else {
                this.disabled = false
                this.$emit("return-error",'镜像创建云硬盘操作失败');
              }
            }).catch((error) => {
              this.disabled = false
              this.$emit("return-error",'镜像创建云硬盘操作失败');
            })
          }else {
            this.$Message.warning({
              background: true,
              closable: true,
              duration: 5,
              content: '云硬盘容量不能小于镜像容量。'
            });
          }
        }
      })
    },
  }
}
</script>