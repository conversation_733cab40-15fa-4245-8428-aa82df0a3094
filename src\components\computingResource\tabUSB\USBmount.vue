<template>
  <div class="usb_area">
    <!-- 挂载虚拟机 -->
    <Modal width="900" v-model="model" :title="title">
      <Spin fix v-if="tableSpin" size="large" style="color:#ef853a">
        <Icon type="ios-loading" size=50 class="demo-spin-icon-load"></Icon>
        <div style="font-size:16px;padding:20px">Loading...</div>
      </Spin>
      <Input v-model="vmPageForm.search_str" search enter-button placeholder="请输入名称" @on-search="tableVMsearchInput" />
      <Table :columns="vmColumn" :data="vmData" @on-sort-change="sortColumn" ></Table>
      <div style="padding-bottom:0" class="pages" v-if="this.vmData.length>0">
        <Pagination  :total="vmTableTotal" :page-size="vmPageForm.pagecount"  @page-change="onPageChange" @page-size-change="onPageSizeChange"/>
      </div>
      <div slot="footer">
        <Button type="text" @click="model = false">取消</Button>
        <Button type="primary" @click="modalOK">确认</Button>
      </div>
    </Modal>
    <!-- 提示 -->
     <Modal width="600" v-model="promptModel" title="USB挂载虚拟机">
      <P>USB: {{this.mountrow.product_name}} 挂载虚拟机，如需生效请手动重启虚拟机。</P>
      <div slot="footer">
        <Button type="text" @click="promptModel = false">取消</Button>
        <Button type="primary" @click="promptOK" :disabled="disabled">确认</Button>
      </div>
    </Modal>
  </div>
</template>
<script>
import Pagination from '@/components/public/Pagination.vue';
import {usbVMmount} from '@/api/other';  // USB挂载虚拟机
export default {
  components: {
    Pagination,
  },
  props: {
    mounttime: String,
    mountrow: Object,
  },
  watch: {
    mounttime(news) {
      this.title = "USB: "+this.mountrow.product_name +" 挂载虚拟机"
      this.formItem.host_ip = this.mountrow.host_ip
      this.formItem.host_port = this.mountrow.host_port
      this.formItem.host_name = this.mountrow.hostname
      this.formItem.sn = this.mountrow.sn
      this.formItem.vid = this.mountrow.vendor_id
      this.formItem.pid = this.mountrow.product_id
      this.formItem.bus = this.mountrow.bus.toString()
      this.formItem.device = this.mountrow.device.toString()
      this.model= true
      this.tableData()
    },
  },
  data() {
    return {
      tableSpin:false,
      model:false,
      title:"", // 标题
      vmColumn:[
        { title: "单选",width: 60, key: "radio",align: "center",
          render: (h, params) => {
            return h('div',[
              h('Radio',{
                props: {
                  value:this.formItem.vm_id == params.row.id,
                },
                on:{
                  'on-change':()=>{
                    this.formItem.vm_id = params.row.id
                    this.formItem.vm_name = params.row.name
                  },
                }
              })
            ])
          }
        },
        { title: "名称", key: "name", sortable: 'custom', minWidth: 100 },
        { title: "IP", key: "ip", tooltip:true,align: "center",minWidth: 120,sortable: 'custom'},
        { title: "主机名",align: "center", minWidth: 100,key: "hostname" },
        { title: "状态",align: "center", key: "status",minWidth: 80,
          render: (h, params) => {
            switch (params.row.status) {
              case "ACTIVE":
                return h(
                  "span",
                  {
                    style: { color: "#000" },
                  },
                  "已开机"
                );
                break;
              case "BUILD":
                return h(
                  "span",
                  {
                    style: { color: "#000" },
                  },
                  "构建"
                );
                break;
              case "DELETED":
                return h(
                  "span",
                  {
                    style: { color: "#000" },
                  },
                  "删除"
                );
                break;
              case "ERROR":
                return h(
                  "span",
                  {
                    style: { color: "#000" },
                  },
                  "错误"
                );
                break;
              case "HARD_REBOOT":
                return h(
                  "span",
                  {
                    style: { color: "#000" },
                  },
                  "重启"
                );
                break;
              case "MIGRATING":
                return h(
                  "span",
                  {
                    style: { color: "#000" },
                  },
                  "迁移"
                );
                break;
              case "PASSWORD":
                return h(
                  "span",
                  {
                    style: { color: "#000" },
                  },
                  "密码"
                );
                break;
              case "PAUSED":
                return h(
                  "span",
                  {
                    style: { color: "#000" },
                  },
                  "已暂停"
                );
                break;
              case "REBOOT":
                return h(
                  "span",
                  {
                    style: { color: "#000" },
                  },
                  "重启"
                );
                break;
              case "REBUILD":
                return h(
                  "span",
                  {
                    style: { color: "#000" },
                  },
                  "重建"
                );
                break;
              case "RESCUE":
                return h(
                  "span",
                  {
                    style: { color: "#000" },
                  },
                  "救援"
                );
                break;
              case "RESIZE":
                return h(
                  "span",
                  {
                    style: { color: "#a2e3a1" },
                  },
                  "准备调整大小/迁移"
                );
                break;
              case "REVERT_RESIZE":
                return h(
                  "span",
                  {
                    style: { color: "#e58fab" },
                  },
                  "已取消调整大小/迁移"
                );
                break;
              case "SHELVED":
                return h(
                  "span",
                  {
                    style: { color: "#000" },
                  },
                  "搁置"
                );
                break;
              case "SHELVED_OFFLOADED":
                return h(
                  "span",
                  {
                    style: { color: "#000" },
                  },
                  "搁置卸载"
                );
                break;
              case "SHUTOFF":
                return h(
                  "span",
                  {
                    style: { color: "#000" },
                  },
                  "已关机"
                );
                break;
              case "SOFT_DELETED":
                return h(
                  "span",
                  {
                    style: { color: "#000" },
                  },
                  "软删除"
                );
                break;
              case "SUSPENDED":
                return h(
                  "span",
                  {
                    style: { color: "#000" },
                  },
                  "已挂起"
                );
                break;
              case "UNKNOWN":
                return h(
                  "span",
                  {
                    style: { color: "#000" },
                  },
                  "未知"
                );
                break;
              case "VERIFY_RESIZE":
                setTimeout(() => {
                  this.actionPackage(params, "confirm")
                }, 1000);
                return h(
                  "span",
                  {
                    style: { color: "#ed9e5b" },
                  },
                  "正在调整大小/迁移"
                );
                break;
            }
          },
        },
      ], // 虚拟机表列
      vmData:[], // 虚拟机数据
      formItem:{
        vm_id:"",
        vm_name:"",
        host_ip:"",
        host_port:"",
        host_name:"",
        sn:"",
        vid:undefined,
        pid:undefined,
        bus:undefined,
        device:undefined,
      },
      vmPageForm:{ // 虚拟机查询条件
        id: 1,
        page: 1, // 当前页
        pagecount: 10, // 每页条
        search_str: "", // 收索
        order_type: "desc", // 排序规则
        order_by: "", // 排序列
      },
      vmTableTotal:0, // 虚拟机总数

      // 提示
      promptModel:false,
      disabled:false,
    };
  },

  methods: {
    tableData(){
      this.tableSpin = true
      vmGroupTableQuery(this.vmPageForm)
        .then((callback) => {
          this.tableSpin = false
          this.vmTableTotal = callback.data.total;
          this.vmData = callback.data.data;
          this.formItem.vm_id = this.vmData[0].id
          this.formItem.vm_name = this.vmData[0].name
        })
        .catch((error) => {
          this.tableSpin = false
        });
    },
    // 搜索虚拟机列表
    tableVMsearchInput(){
      this.vmPageForm.page = 1
      this.tableTotal = 0
      this.tableData();
    },
     // 当前分页
    onPageChange(item) {
      this.vmPageForm.page = item;
      this.tableData();
    },
    // 每页条数
    onPageSizeChange(item) {
      this.vmPageForm.pagecount = item
      this.vmPageForm.page = 1
      this.tableTotal = 0
      this.tableData();
    },
    // 虚拟机列表 列排序
    sortColumn(column, key, order) {
      if (column.key !== "normal") {
        this.vmPageForm.order_by = column.key;
        this.vmPageForm.order_type = column.order;
        this.vmPageForm.page = 1
        this.tableTotal = 0
        this.tableData();
      }
    },
    // 挂载返回
    modalOK(){
      this.promptModel = true
      this.disabled = false
    },
    promptOK(){
      this.disabled = true
      usbVMmount(this.formItem )
      .then((callback) => {
        this.model = false
        this.promptModel = false
        if(callback.data.msg == "ok") {
          
          this.$Message.success({
            background: true,
            closable: true,
            duration: 5,
            content: "US挂载虚拟机操作完成",
          });
          
          this.$emit("custom-succe",'US挂载虚拟机操作完成');
        }else {
          this.$Message.error({
            background: true,
            closable: true,
            duration: 5,
            content: callback.data.msg,
          });
        }
      })
      .catch((error) => {
        this.$Message.error({
          background: true,
          closable: true,
          duration: 5,
          content: "US挂载虚拟机操作失败",
        });
      });
    },
  }
};
</script>
