.health_examination {
  height: 100%;
  width: 100%;
  padding: 5px;

  .inspection_area {
    width: 100%;
    padding: 0 5%;
    height: 150px;
    margin: 0 auto;
    display: flex;
    justify-content: space-around;
    align-items: center;
    background-image: url(../../../assets/xnjbg.png);
    background-size: 100% 100%;

    h3 {
      font-size: 24px;
      font-weight: 600;

      #fraction {
        padding-left: 20px;
        color: green;
      }
    }

    ul {
      display: block;

      li {
        float: left;
        padding: 5px;
      }
    }

    .inspection_btn {
      height: 50%;

      .inspect_btn {
        height: 60px;
        padding: 0 20px;
      }
    }
  }

  .inspection_content {
    padding-top: 15px;
    height: 80%;
    width: 100%;
    display: flex;
    justify-content: space-evenly;
    position: relative;

    .export_check_btn {
      position: absolute;
      right: 0;
      top: 3px;
    }

    .detection_navigation {
      width: 19%;
      height: 100%;
      border-right: 1px solid #ccc;

      p {
        font-weight: 600;
        border-left: 4px solid #fb6129;
        margin: 0 0 5px 5px;
        padding-left: 10px;
        color: #333;
        font-size: 16px;
      }

      >div {
        height: 90%;
        overflow: auto;
        padding-bottom: 30px;
      }
    }

    .check_list {
      height: 100%;
      width: 79%;
      overflow: auto;

      p {
        font-weight: 600;
        border-left: 4px solid #fb6129;
        margin: 0 0 5px 5px;
        padding-left: 10px;
        color: #333;
        font-size: 16px;
      }

      .radar_chart {
        overflow: hidden;
        width: 300px;
        height: 300px;
        /* background-color: rgb(38, 76, 146); */
        background-image: linear-gradient(to left, #36d1dc8a, #5b87ec8a);
        border-radius: 50%;
        position: relative;
        left: 50%;
        top: 100px;
        transform: translateX(-50%);
        box-shadow: 0 0 100px #5b87ecc7;

        .scanogram {
          padding: 35px;
          border: 3px solid #568de5bf;
          border-radius: 50%;
          width: 100%;
          height: 100%;

          #radar:after {
            position: absolute;
            left: 0;
            top: 0;
            content: "";
            display: block;
            background-image: linear-gradient(45deg,
                rgba(235, 79, 6, 0) 50%,
                #64ffd0 100%);
            width: 150px;
            height: 150px;
            animation: radar-beam 5s infinite;
            animation-timing-function: linear;
            transform-origin: bottom right;
            border-radius: 100% 0 0 0;
          }
        }
      }
    }
  }
}

@keyframes radar-beam {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}