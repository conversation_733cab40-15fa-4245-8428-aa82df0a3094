// The Vue build version to load with the `import` command
// (runtime-only or standalone) has been set in webpack.base.conf with an alias.
import Vue from 'vue'
import App from './App'
import $ from 'jquery';
import "../src/static/ztree/js/jquery-3.2.1.min";
import "../src/static/ztree/js/jquery.ztree.core.js";
import "../src/static/ztree/js/jquery.ztree.excheck";
import "../src/static/ztree/js/jquery.ztree.exedit";
import "../src/static/ztree/css/zTreeStyle/zTreeStyle.css";
import Vuex from 'vuex'
import ViewUI from 'view-design'
import router from './router'
import store from './store/store'
import axios from './utils/axiosConfig'
// import axios from 'axios'
import Antd from "ant-design-vue";
import "ant-design-vue/dist/antd.css";
import vcolorpicker from 'vcolorpicker'
import 'view-design/dist/styles/iview.css';
import echarts from 'echarts'
import "./assets/less/global.less";
// 导入自己的全局样式
import './assets/css/global.css'
import "xterm/dist/xterm.css";
// ElementUI;
import ElementUI from "element-ui";
import "element-ui/lib/theme-chalk/index.css";

Vue.config.productionTip = false
Vue.prototype.$echarts = echarts

Vue.use(ViewUI)
Vue.use(Antd);
Vue.use(vcolorpicker);
Vue.use(Vuex)
Vue.use(ElementUI);
Vue.prototype.$axios = axios
// 拦截request,设置全局请求为ajax请求
axios.interceptors.request.use((config) => {
    config.headers['X-Requested-With'] = 'XMLHttpRequest'
    config.headers["Content-Type"] = "application/json";
    config.headers["Accept"] = 'application/vnd.ceph.api.v1.0+json'
    
    return config
})
new Vue({
    el: '#app',
    router,
    store,
    render: h => h(App),
})