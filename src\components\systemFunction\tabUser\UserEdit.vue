<template>
  <Modal v-model="model" width="600" :mask-closable="false">
    <template #header><p>编辑用户</p></template>
    <Form
      :model="formItem"
      ref="formItem"
      :rules="rulesForm"
      :label-width="120"
    >
      <FormItem label="登录账号">
        <Input v-model="formItem.username" disabled></Input>
      </FormItem>
      <FormItem label="当前用户" >
        <Input v-model="formItem.oldname" disabled></Input>
      </FormItem>
      <FormItem label="用户新名称" prop="name">
        <Input v-model="formItem.name" placeholder="请输入用户名"></Input>
      </FormItem>
      <FormItem label="有效期">
        <DatePicker v-model="listTime" type="date" :clearable="false" :options="optionsTime" @on-change="timeChange"></DatePicker>
      </FormItem>
    </Form>
    <div slot="footer">
      <Button type="text" @click="model = false">取消</Button>
      <Button type="primary" @click="modelOK" :disabled="disabled">确认</Button>
    </div>
  </Modal>
</template>
<script>
import {
  userTableModify, // 用户修改
} from '@/api/system';
export default {
  props: {
    editTime: String,
    tableRow: Object,
  },
  watch: {
    editTime(news){
      this.$refs.formItem.resetFields();
      this.formItem.username = this.tableRow.username
      this.formItem.oldname = this.tableRow.name
      this.formItem.name = this.tableRow.name
      this.formItem.time = this.tableRow.expiredday!==null?this.tableRow.expiredday.split(' ')[0]:''
      this.listTime = this.tableRow.expiredday!==null?this.tableRow.expiredday.split(' ')[0]:''
      this.model = true
      this.disabled = false
    }
  },
  data(){
    const propName =(rule,value,callback)=>{
      let list = /^[\u4e00-\u9fa5_a-zA-Z0-9@_.]{2,32}$/;
      if(list.test(value)){
        callback()
      }else{
        callback(new Error("2-32 个中文、英文、数字、特殊字符(@_.)"));
      }
    }
    return {
      model: false,
      disabled: false,
      formItem: {
        username: '',
        oldname: '',
        name: '',
        time: '',
      },
      listTime:null,
      // 禁止选择过去时间
      optionsTime: {
        disabledDate (date) {
          return date && date.valueOf() < Date.now() - 86400000;
        }
      },
      // 正则验证
      rulesForm: {
        name:[
          { required: true, message: '必填项', trigger: 'change' },
          { validator: propName, trigger:'change' }
        ],
      },
    }
  },
  methods: {
    // 时间选择 
    timeChange(item){
      this.formItem.time = item
      this.listTime = item
    },
    // 编辑网络确认事件
    modelOK() {
      this.$refs.formItem.validate((valid) => {
        if (valid) {
          this.disabled = true;
          userTableModify({
            name:this.formItem.name,
            username:this.formItem.username,
            expiredday:this.formItem.time,
          }).then(callback=>{
            if(callback.data.msg == "ok"){
              this.$emit("custom-ok",'编辑用户完成');
              this.model = false;
            }else {
              this.$emit("custom-error",callback.data.msg);
              this.disabled = false;
            }
          })
          .catch((error) => {
            this.$emit("custom-error",'编辑用户失败');
            this.disabled = false;
          });
        }
      });
    },
  }
}
</script>
