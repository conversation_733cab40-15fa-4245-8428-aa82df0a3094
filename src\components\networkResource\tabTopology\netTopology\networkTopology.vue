<style lang="less">
  @import "../../networkResource.less";
</style>
<template>
<div class="net_chart_area">
  <div class="topology_module_btn">
      <Button class="remove" @click="shuaxin" ><span class="icon iconfont icon-a-15Jhuanyuan"></span> 刷新</Button>
    </div>
  <div class="container">
    <div class="box left">
      <Spin fix v-if="tableSpin" size="large" style="color:#ef853a">
        <Icon type="ios-loading" size=50 class="demo-spin-icon-load"></Icon>
        <div style="font-size:16px;padding:20px">Loading...</div>
      </Spin>
      <div v-for="(item, index) in netData" :key="index" class="item net_topology_area">
        <h2><Icon type="ios-globe" /> {{ item.subname }}</h2>
        <p>子网IP: {{ item.sub_gateway_ip }}</p>
        <h3 @click="vmExpand(index,item.type)"><Icon :type="item.type"/> 虚拟机（{{ item.vms?item.vms.length:0 }}）</h3>
        <div v-for="(em,i) in item.vms" :key="i" class="vm_tamp_area" v-if="item.type == 'ios-arrow-down'">
          <div class="vm_addr_area">
            <h3 @click="addrExpand(index,i,em.type)"><span class="vm_addr_lable"> {{ em.name }} </span> <Icon :type="em.type" /></h3>
            <Icon type="md-browsers" :color="em.power_state=='RUNNING'?'green':'#ccc'" />
          </div>
          <div v-if="em.type == 'md-arrow-dropdown'">
            <p style="padding-left:10px">所属物理机：{{ em.hostname }}</p>
            <p style="padding-left:10px">虚拟机ID: {{ em.id }}</p>
            <p style="padding-left:10px">状态：{{ em.power_state }}</p>
            <p style="padding-left:10px">虚机状态： {{ em.vm_state }}</p>
          </div>
        </div>
      </div>
    </div>
    <canvas ref="canvas" class="canvas"></canvas>
    <div class="box right">
      <div class="item host_topology_area">
        <h2>{{hostObj.hostname}}</h2>
        <p>网卡名称：{{hostObj.device}} <Icon type="md-browsers" style="float:right" :color="hostObj.status=='1'?'green':'#ccc'" /></p>
        <p>{{hostObj.ipaddress}} <a @click="switchNodes(hostObj.hostname)" style="float:right">. . .</a></p>
      </div>
    </div>
</div>
<!-- 切换节点弹框 -->
    <Modal
      v-model="model"
      width="600"
      :mask-closable="false"
    >
      <template #header><p>切换节点</p></template>
      <Form :model="formItem" ref="formItem" :label-width="150">
        <FormItem label="更换节点">
          <Select v-model="formItem.hostname">
            <Option
              v-for="item in hostSelect"
              :value="item.hostname"
              :key="item.hostname"
              >{{ item.hostname }}</Option
            >
          </Select>
        </FormItem>
      </Form>
      <div slot="footer">
        <Button type="text" @click="model = false">取消</Button>
        <Button type="primary" @click="modalOK">确认</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
import {networkTopologyNode,networkTopologyVM} from '@/api/network';
export default {
   props: {
    tabTuopu: String,
  },
 data() {
    return {
      tableSpin: false,
      spinTopology: true,
      hostData: [],
      hostObj: {
        hostname: 'contorller1',
        ipaddress: '***********',
        device: 'ETH0',
        status: '1'
      },
      netData: [],

      model: false, // 选择节点弹框
      formItem: {
        hostname: '',
      },
      hostSelect: [],
    };
  },
  watch: {
    tabTuopu(value) {
      value=='network'?this.hostGET():""
    },
  },
  mounted() {
    this.hostGET();
  },
  methods: {
    drawLines() {
      const canvas = this.$refs.canvas;
      const ctx = canvas.getContext('2d');
      const leftBox = this.$el.querySelector('.left');
      const rightBox = this.$el.querySelector('.right .item');
      const netData = this.$el.querySelectorAll('.left .item');

      canvas.width = this.$el.clientWidth;
      canvas.height = this.$el.clientHeight;
      // canvas.height = this.$el.clientHeight+5000;

      ctx.clearRect(0, 0, canvas.width, canvas.height);
      ctx.strokeStyle = 'black';
      ctx.lineWidth = 2;

      netData.forEach(leftItem => {
        const startX = leftBox.offsetWidth;
        const startY = leftItem.offsetTop + leftItem.offsetHeight / 2;
        const endX = canvas.width - rightBox.offsetWidth;
        const endY = rightBox.offsetTop + rightBox.offsetHeight / 2;

        ctx.beginPath();
        ctx.moveTo(startX, startY);
        ctx.lineTo((startX + endX) / 2, startY);
        ctx.lineTo((startX + endX) / 2, endY);
        ctx.lineTo(endX, endY);
        ctx.stroke();
      });
    },
    shuaxin(){
      this.hostObj.hostname = "查询中"
      this.hostObj.ipaddress = "查询中"
      setTimeout(()=>{
        this.hostGET();
      },200)
    },
    // 获取节点
    hostGET(){
      networkTopologyNode()
      .then(callback=>{
        this.hostData = callback.data
        this.hostObj.hostname = this.hostData[0].hostname
        this.hostObj.ipaddress = this.hostData[0].ipaddress
        this.hostObj.device = this.hostData[0].device
        this.hostObj.status = this.hostData[0].net_status
        this.netGET(this.hostData[0].hostname)
      })
      .catch(err=>{})
    },
    // 确认切换节点
    modalOK(){
      this.hostData.forEach(em=>{
        if(em.hostname == this.formItem.hostname) {
          this.hostObj.hostname = em.hostname
          this.hostObj.ipaddress = em.ipaddress
          this.hostObj.device = em.device
          this.hostObj.status = em.net_status
          this.formItem.ipaddress = em.ipaddress
          this.netGET(em.hostname)
        }
      })
      this.model = false
    },
    netGET(data){
      this.tableSpin = true
      networkTopologyVM({
        hostname:data
      }).then(callback=>{
        this.tableSpin = false
        let arr = callback.data
        arr.forEach(item => {
          item.type = "ios-arrow-down"
          if(item.vms){
            item.vms.forEach(em=>{
              em.type = "md-arrow-dropright"
            })
          }
        });
        this.netData = callback.data
        this.drawLines();
        setTimeout(()=>{
          this.drawLines();
        },20)
      })
    },
    // 切换节点
    switchNodes(item){
      let arr = new Array()
      this.hostData.forEach(em=>{
        if(em.hostname !== item) {
          arr.push(em)
        }
      })
      this.hostSelect = arr
      this.formItem.hostname = arr[0].hostname
      this.model = true
    },
    // 虚拟机展开收起
    vmExpand(index,type){
      this.netData[index].type = type == "ios-arrow-down"?"ios-arrow-forward":"ios-arrow-down"
      setTimeout(()=>{
        this.drawLines();
      },20)
    },
    addrExpand(index,i,type) {
      this.netData[index].vms[i].type = type == "md-arrow-dropdown"?"md-arrow-dropright":"md-arrow-dropdown"
      setTimeout(()=>{
        this.drawLines();
      },20)
    }
  }
};
</script>

<style scoped>
.container {
  display: flex;
  justify-content: space-between;
  position: relative;
}

.box {
  width: 30%;
}

.left {
  background-color: #f6f7fb;
}

.right {
  background-color: #f6f7fb;
}

.item {
  padding: 10px;
  margin: 10px;
  background-color: white;
  border: 1px solid black;
}

.canvas {
  position: absolute;
  top: 0;
  left: 0;
  z-index: -1;
}
</style>
