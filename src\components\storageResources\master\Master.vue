<template>
  <div style="overflow: auto; height: 100%; padding: 0 0 150px 0">
    <div class="table-button-area">
      <div>
        <Button class="plus_btn" @click="increaseClick"
          ><span class="icon iconfont icon-plus-circle"></span> 添加卷池</Button
        >
        <Button
          class="plus_btn"
          @click="masterGET"
          ><span class="icon iconfont icon-plus-circle"></span>刷新</Button
        >
      </div>
      <div >
        <!-- <Input v-model="httpTableData.search_str" search enter-button placeholder="请输入名称" style="width: 300px" @on-search="tableSearchInput" /> -->
      </div>
    </div>
    <div style="position: relative">
      <Spin fix v-if="spinShow" size="large" style="color: #ef853a">
        <Icon type="ios-loading" size="50" class="demo-spin-icon-load"></Icon>
        <div style="font-size:16px; padding: 20px">Loading...</div>
      </Spin>
      <Table :columns="tableColumns" :data="tableData"></Table>
      <div class="pages" v-if="this.tableData.length > 0">
        <Pagination
          :total="tableTotal"
          :page-size="tablePageForm.pagecount"
          @page-change="onPageChange"
          @page-size-change="onPageSizeChange"
        />
      </div>
    </div>
    <!-- 添加主机 -->
    <Modal v-model="increaseModal" width="600" title="添加主机" :loading="BJloadWL" @on-ok="increaseOK" @on-cancel="cancel" :mask-closable="false">
			<Form :model="formItem" :rules="ruleform" :label-width="120">
				<FormItem label="主机名称" props="hostname">
          <Input v-model="formItem.hostname" placeholder="输入主机名称"></Input>
      	</FormItem>
			</Form>
		</Modal>
  </div>
</template>
<script>
import Pagination from "@/components/public/Pagination.vue";
export default {
  components: {
    Pagination,
  },
  props: {
    tabName: String,
  },
  watch: {
    tabName(value) {
      value == "jiedian" ? this.masterGET() : "";
    },
  },
  updated() {
    this.tablePageForm.pagecount = this.$store.state.power.pagecount;
  },
  data() {
    const propPoolname = (rule, value, callback) => {
      let list = /^[\u4e00-\u9fa5_a-zA-Z0-9@_.-]{2,32}$/;
      if (list.test(value)) {
        callback();
      } else {
        callback(new Error("请输入2-32个中文或字符（特殊字符可用@_.-）"));
      }
    };
    return {
      spinShow: false,
      tableColumnsP:[
        { title: "存储节点",key: "hostname" },
				{ title: "IP地址",key: "addr",align: "center" },
				{ title: "版本",key: "ceph_version",align: "center" },
				{ title: "运行的服务",key: "servicesList",align: "center" },
				{ title: "操作",key: "operation", width: 120,
          render: (h, params) => {
            return h("Dropdown", { props: { trigger: "click" } }, [
              h("Button", "配置 ▼"),
              h("DropdownMenu", { slot: "list" }, [
                h("DropdownItem",{
                  nativeOn: {
                    click: () => {
                      this.$Modal.confirm({
                        title: "重启节点",
                        content:
                          '<p>是否重启<span style="font-weight: 600;color:green;word-wrap: break-word;">' +
                          params.row.hostname +
                          "</span>存储节点？</p>",
                        onOk: () => {
                          this.$axios.post("/thecss/v2/rebootNodeByHost?hostName="+params.row.hostname+"&usrname="+params.row.root).then((callback) => {
                            this.$Message.success({
                              background: true,
                              closable: true,
                              duration: 5,
                              content: "重启节点操作完成",
                            });
                            this.blokGET()
                          }).catch((error) => {
                            this.$Message.error({
                                background: true,
                                closable: true,
                                duration: 5,
                                content: "重启节点操作失败",
                              });
                          });
                        },
                        onCancel: () => {},
                      });
                    },
                  },
                },"重启"),
                h("DropdownItem",{
                  nativeOn: {
                    click: () => {
                      this.$Modal.confirm({
                        title: "关闭节点",
                        content:
                          '<p>是否关闭<span style="font-weight: 600;color:red;word-wrap: break-word;">' +
                          params.row.hostname +
                          "</span>存储节点？</p>",
                        onOk: () => {
                          this.$axios.post("/thecss/v2/shutNodeByHost?hostName="+params.row.hostname+"&usrname="+params.row.root).then((callback) => {
                            this.$Message.success({
                              background: true,
                              closable: true,
                              duration: 5,
                              content: "关闭节点操作完成",
                            });
                            this.blokGET()
                          }).catch((error) => {
                            this.$Message.error({
                                background: true,
                                closable: true,
                                duration: 5,
                                content: "关闭节点操作失败",
                              });
                          });
                        },
                        onCancel: () => {},
                      });
                    },
                  },
                },"关闭"),
                h("DropdownItem",{
                  style: { color: "red" },
                  nativeOn: {
                    click: () => {
                      this.$Modal.confirm({
                        title: "删除节点",
                        content:
                          '<p>是否删除节点名称为<span style="font-weight: 600;color:red;word-wrap: break-word;">' +
                          params.row.hostname +
                          "</span>存储节点？</p>",
                        onOk: () => {
                          this.$axios.delete("/thecephapi/api/block/image/"+params.row.pool_name+"%2F"+params.row.name+"/move_trash").then((callback) => {
                            this.$Message.success({
                              background: true,
                              closable: true,
                              duration: 5,
                              content: "删除节点操作完成",
                            });
                            this.blokGET()
                          }).catch((error) => {
                            this.$Message.error({
                                background: true,
                                closable: true,
                                duration: 5,
                                content: "删除节点操作失败",
                              });
                          });
                        },
                        onCancel: () => {},
                      });
                    },
                  },
                },"删除"),
              ]),
            ]);
          },
        },
      ],
      tableData:[],
      tablekAllData:[],
      // 分页
      tableTotal:0,
      tablePageForm: {
        page: 1, // 当前页
        pagecount: this.$store.state.power.pagecount, // 每页条
        search_str: "", // 收索
        order_type: "asc", // 排序规则
        order_by: "", // 排序列
      },

      // 添加节点
      increaseModal:false,
      formItem:{
        hostname:""
      },
      ruleform: {
        hostname: [
          { required: true, message: "必填项"},
          { validator: propPoolname, trigger: "change" },
          { validator: propPoolname, trigger: "blur" },
        ],
      },
    };
  },
  methods: {
    // 获取节点表格
    masterGET(){
      this.spinShow = true;
      this.$axios.get("/thecephapi/api/host").then((callback) => {
        this.spinShow = false;
        this.tableTotal = callback.data.length
        this.tablekAllData = callback.data
        if(this.tablekAllData.length>0){
          this.tableData = this.tablekAllData.slice(
            this.tablePageForm.page - 1,
            this.tablePageForm.page * this.$store.state.power.pagecount
          );
        }
      })
    },
    // 添加节点
    increaseClick(){
      this.increaseModal = true
      this.formItem.hostname= ""
    },
    increaseOK(){
      this.$axios.post("/thecephapi/api/host",this.formItem).then((callback) => {
        this.$Message.success({
          background: true,
          closable: true,
          duration: 5,
          content: "添加节点操作完成",
        });
        this.increaseClick()
      })
    },
    cancel(){},
  },
};
</script>