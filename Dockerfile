FROM tianwen1:5000/node:14 as builder

WORKDIR /code
COPY . .
RUN rm -rf node_modules && \
    npm config set registry https://registry.npmmirror.com && \
    npm install && \
    npm run build

FROM tianwen1:5000/node:14-alpine

ARG ARG_VERSION=latest
ARG ARG_VERSION
ENV COMMIT_VERSION=$ARG_VERSION

ARG ARG_COMMIT_DATE=2000-01-01-01:01:01
ARG ARG_COMMIT_DATE
ENV COMMIT_DATE=$ARG_COMMIT_DATE

ARG ARG_TAG=v2.1
ARG ARG_TAG
ENV LATEST_TAG=$ARG_TAG

ARG ARG_OS=linux
ARG ARG_OS
ENV OS_VERSION=$ARG_OS

WORKDIR /code
COPY docker/package.json ./
#RUN npm config set registry https://registry.npm.taobao.org
#RUN npm install express
COPY docker/node_modules ./node_modules/

COPY docker/app.js ./
COPY  --from=builder /code/dist /code/dist/

EXPOSE 8066
CMD [ "node", "app.js" ]
