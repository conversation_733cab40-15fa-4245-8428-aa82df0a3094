<template>
  <div>
    <Modal
      v-model="model"
      width="600"
      :mask-closable="false"
    >
      <template #header><p>移出虚拟机</p></template>
      <div style="padding: 5px">
        <span>是否从</span>
        <span style="font-weight: 600;color:red;;word-break: break-all">{{ this.groupSelect.name }}</span>
        <span>虚拟机组中移出下列虚拟机？</span>
        <p style="color:red;word-wrap: break-word">{{tableNames.toString()}}</p>
      </div>
      <div slot="footer">
        <Button type="text" @click="model = false">取消</Button>
        <Button type="primary" :loading="disabled" @click="modelOK">
          <span v-if="!disabled">确认</span>
          <span v-else>移出中</span>
        </Button>
        <!-- <Button type="primary" @click="modelOK" :disabled="disabled">确认</Button> -->
      </div>
    </Modal>
  </div>
</template>
<script>
import { vmGroupTableRemoveVM } from '@/api/virtualMachine';  // 虚拟机组表格 移出虚拟机
export default {
  props: {
    groupSelect: Object,
    tableArr: Array,
    remveTime: String,
  },
  watch: {
    remveTime(news){
      this.tableNames = this.tableArr.map(em=>{ return em.name})
      this.tableIDS = this.tableArr.map(em=>{ return em.id})
      this.model = true
      this.disabled = false
    }
  },
  data(){
    return {
      model:false,
      disabled: false,
      tableNames: [],
      tableIDS: [],
    }
  },
  methods: {
    modelOK() {
      this.disabled = true
      vmGroupTableRemoveVM({data:{
        group_id: this.groupSelect.id,
        group_name: this.groupSelect.name,
        vmids: this.tableIDS,
        vm_names: this.tableNames
      }})
      .then((callback) => {
        this.model = false;
        if(callback.data.msg == 'ok') {
          this.$emit("return-ok",'删除虚拟机操作完成');
        }else {
          this.$emit("return-error",'删除虚拟机操作失败');
        }
      })
      .catch((error) => {
        this.disabled = false
        this.$emit("return-error",'删除虚拟机操作失败');
      })
    }
  }
}
</script>