<template>
<!-- 柱状图 -->
  <div ref="chart" style="width: 100%;height:100%;"></div>
</template>
<script>
  export default {
    // 接受父组件传值
    props: {
      datas: Object,
    },
    watch: {
      datas(value) {
        this.renderChart(value)
      },
    },
    mounted() {},
    methods: {
      renderChart(vls) {
        let chart = this.$echarts.init(this.$refs.chart);
        let color = [
          "#00ff22","#8B5CFF","#004cff","#00ffc6","#ff8400",
          "#f4ff5c","#5f5449","#fd4882","#2bc199","#00ffc6",
          "#8fff00","#fff900","#b0ff5c","#ffd65c","#00b6ff",
          "#ffa95c","#f37542","#f34242","#f348fd","#4d00ff",
          "#b148fd","#5548fd","#b36464","#c12b31","#c12b9f",
          "#892bc1","#3c2bc1","#2b66c1","#2ba4c1","#bfbdbb",
          "#2bc154","#71c12b","#b6c12b","#c1a92b","#c16b2b",
          "#c13a2b","#ff0015","#ff0097","#fd00ff",
          ];
        const hexToRgba = (hex, opacity) => {
          let rgbaColor = "";
          let reg = /^#[\da-f]{6}$/i;
          if (reg.test(hex)) {
            rgbaColor = `rgba(${parseInt("0x" + hex.slice(1, 3))},${parseInt(
              "0x" + hex.slice(3, 5)
            )},${parseInt("0x" + hex.slice(5, 7))},${opacity})`;
          }
          return rgbaColor;
        };

        let arr = new Array
        vls.data.forEach((item,index) => {
          arr.push({
              name: item.title,
              type: "line",
              smooth: true,
              symbolSize: 8,
              zlevel: 3,
              lineStyle: {
                normal: {
                  color: color[index],
                  shadowBlur: 3,
                  shadowColor: hexToRgba(color[index], 0.5),
                  shadowOffsetY: 8,
                },
              },
              symbol: "circle", //数据交叉点样式
              areaStyle: {
                normal: {
                  color: new this.$echarts.graphic.LinearGradient(
                    0,0,0,1,
                    [
                      {
                        offset: 0,
                        color: hexToRgba(color[index], 0.3),
                      },
                      {
                        offset: 1,
                        color: hexToRgba(color[index], 0.1),
                      },
                    ],
                    false
                  ),
                  shadowColor: hexToRgba(color[index], 0.1),
                  shadowBlur: 10,
                },
              },
              data: item.list,
            })
        });

        chart.setOption ({
          backgroundColor: "#fff",
          color: color,
          legend: {
            top: 20,
          },
          tooltip: {
            trigger: "axis",
            formatter: function (params) {
              let html = "";
              params.forEach((v) => {
                // let nub = 0
                // if(v.name<0){
                //     nub = v.name *-1
                // }
                // html += `<div style="color: #666;font-size: 14px;line-height: 24px">
                //         <span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${
                //           color[v.componentIndex]
                //         };"></span>
                //         ${v.seriesName}${nub}
                //         <span style="color:${
                //           color[v.componentIndex]
                //         };font-weight:700;font-size: 18px;margin-left:5px">${
                //   v.value
                // }</span>
                //         MB`;
                  html += `<div style="color: #666;font-size: 14px;line-height: 24px">
                        <span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${
                          color[v.componentIndex]
                        };"></span>
                        ${v.seriesName}
                        <span style="color:${
                          color[v.componentIndex]
                        };font-weight:700;font-size: 18px;margin-left:5px">${
                  v.value
                }</span>`;
              });
              return html;
            },
            extraCssText:
              "background: #fff; border-radius: 0;box-shadow: 0 0 3px rgba(0, 0, 0, 0.2);color: #333;",
            axisPointer: {
              type: "shadow",
              shadowStyle: {
                color: "#ffffff",
                shadowColor: "rgba(225,225,225,1)",
                shadowBlur: 5,
              },
            },
          },
          grid: {
            top: 100,
            containLabel: true,
          },
          xAxis: [
            {
              type: "category",
              boundaryGap: false,
              axisLabel: {
                formatter: "{value}",
                textStyle: {
                  color: "#333",
                },
              },
              axisLine: {
                lineStyle: {
                  color: "#D9D9D9",
                },
              },
              data: vls.time,
            },
          ],
          yAxis: [
            {
              type: "value",
              // name: "单位（MB）",
              name: "",
              axisLabel: {
                textStyle: {
                  color: "#666",
                },
              },
              nameTextStyle: {
                color: "#666",
                fontSize: 12,
                lineHeight: 40,
              },
              // 分割线
              splitLine: {
                lineStyle: {
                  type: "dashed",
                  color: "#E9E9E9",
                },
              },
              axisLine: {
                show: false,
              },
              axisTick: {
                show: false,
              },
            },
          ],
          series: arr,
        })
      }
    }
  }
</script>
