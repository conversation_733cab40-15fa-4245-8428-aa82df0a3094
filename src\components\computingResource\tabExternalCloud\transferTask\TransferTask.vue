<template>
  <div>
    <div style="padding:10px;display: flex; justify-content: space-around;">
      <Input v-model="tablePageForm.search_str" search enter-button placeholder="请输入名称" style="width: 400px" @on-search="taskSearchInput" />
    </div>
    <Table :columns="transferColumn" :data="transferData" @on-sort-change="taskColumn" no-data-text="暂无有效迁移任务"></Table>
    <Spin fix v-if="spinShowTask" size="large" style="color:#ef853a">
      <Icon type="ios-loading" size=50 class="demo-spin-icon-load"></Icon>
      <div style="font-size:16px;padding:20px">Loading...</div>
    </Spin>
     <!--迁移任务表格 分页  -->
      <div class="pages" v-if="this.transferData.length>0">
        <!-- <Page
          :total="tableTotal"
          show-total
          show-sizer
          :page-size="tablePageForm.pagecount"
          placement="top"
          :page-size-opts="[10, 20, 30]"
          @on-change="onPageChange"
          @on-page-size-change="onPageSizeChange"
        /> -->
      <Pagination  :total="tableTotal" :page-size="tablePageForm.pagecount"  @page-change="onPageChange" @page-size-change="onPageSizeChange"/>
      </div>
  </div>
</template>
<script>
import {
  migrateTaskQuery, // 迁移任务查询
} from '@/api/externalCloud';
import Pagination from '@/components/public/Pagination.vue';
export default {
  components: {
    Pagination
  },
  props: {
    taskBoolean: Boolean,
  },
  watch: {
    taskBoolean(value) {
      if(value == true) {
        this.taskTableData()
      }else {
        this.taskNumber = 0
      }
    },
    taskNumber(news){
      if(news== 0 ){
        clearInterval(this.taskTime)
      }else {
        this.taskTime = setInterval(() => {
          migrateTaskQuery(this.tablePageForm)
          .then((callback) => {
            let listNumber = 0
            callback.data.data.forEach(callback=>{
              if(callback.status.substr(-5) !== "error" || callback.status !=="complete"){
                listNumber++
              }
            })
            if(listNumber== 0){
              clearInterval(this.taskTime)
            }
          })
        },3000)
      }
    }
  },
  data(){
    return {
      spinShowTask:false,
      transferColumn:[
        { title:"源虚拟机名称",key:"vm_name",align: 'center' },
        { title:"外部云",key:"cloudy_name",align: 'center' },
        { title:"外部云id",key:"cloudy_id",align: 'center' },
        { title:"目标虚拟机名称",key:"vm_name",align: 'center' },
        { title:"目标主机",key:"target_zone",align: 'center' },
        { title:"迁移状态",key:"status",align: 'center',
          render:(h, params)=>{
            let list = 0
            if(params.row.status .substr(-5) !== "error" || params.row.status !=="complete") {
              list++
            }
            this.taskNumber = list
            let statusList = this.tasksStatus(params.row.status)
            return h("span",{
              style:{
                color:statusList=='迁移错误'?"red":"#666"
              }
            },statusList)
          }
        },
        // { title:"操作",key:"operation",align: 'center',
        //   render:(h, params)=>{
        //     return h('div',[
        //       h('Button',"aaa"),
        //       h('Button',"bbb")
        //     ])
        //   }
        // },
      ],
      transferData:[],
      // 总条数 
      tableTotal:0,
      tablePageForm: {
        page: 1, // 当前页
        pagecount: this.$store.state.power.pagecount, // 每页条
        search_str: "", // 收索
        order_type: "asc", // 排序规则
        order_by: "", // 排序列
      },
      // 计时器 
      taskTime:null,
      taskNumber:0,
    }
  },
  mounted() {},
  updated() {
    this.tablePageForm.pagecount=this.$store.state.power.pagecount
  },
  methods: {
    // 获取任务表数据
    taskTableData(){
      this.spinShowTask = true
      migrateTaskQuery(this.tablePageForm)
      .then((callback) => {
        this.spinShowTask = false
        this.tableTotal = callback.data.total
        this.transferData = callback.data.data
      }).catch((error) => {
        this.spinShowTask = false
      });
    },
    // 当前分页
    onPageChange(item) {
      this.tablePageForm.page = item;
      this.taskTableData()
    },
    // 每页条数
    onPageSizeChange(item){
      this.$store.state.power.pagecount = item
      this.tablePageForm.pagecount = this.$store.state.power.pagecount
      this.tablePageForm.page = 1
      this.tablePageForm.page = 1
      this.tableTotal = 0
      this.tablePageForm.search_str = ""
      this.taskTableData()
    },
    // 虚拟机列表 列排序
    taskColumn(column, key, order) {
      if (column.key !== "normal") {
        this.tablePageForm.order_by = column.key;
        this.tablePageForm.order_type = column.order;
        this.tablePageForm.page = 1
        this.tableTotal = 0
        this.tablePageForm.search_str = ""
        this.taskTableData()
      }
    },
    taskSearchInput(){
      this.tablePageForm.page = 1
      this.tableTotal = 0
      this.taskTableData()
    },
    // 迁移任务状态封装
    tasksStatus(state){
      let list = ''
      state == "wait"? list="等待运行":state == "complete"?list="已完成":state.substr(-5) == "error"?list="迁移错误":list="迁移中"
      // if(state == "wait") {
      //   return "等待运行"
      // }else if(state == "complete"){
      //   return "已完成"
      // }else if(state.substr(-5) == "error"){
      //   return "迁移错误"
      // }else {
      //   return "迁移中"
      // }
      return list
    },
  },
  beforeDestroy() {
    clearInterval(this.taskTime)
  },
}
</script>