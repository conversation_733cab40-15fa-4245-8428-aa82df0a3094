<template>
  <div>
    <Modal
      v-model="model"
      width="600"
      :mask-closable="false"
    >
      <template #header><p>编辑镜像</p></template>
      <Form :model="formItem" ref="formItem" :rules="ruleValidate" :label-width="150">
        <FormItem label="当前镜像名称">
          <Input
            v-model="formItem.oldname"
            disabled
          ></Input>
        </FormItem>
        <FormItem label="镜像新名称" prop="name">
          <Input
            v-model="formItem.name"
            placeholder="输入镜像新名称"
          ></Input>
        </FormItem>
      </Form>
      <div slot="footer">
        <Button type="text" @click="model = false">取消</Button>
        <Button type="primary" @click="modalOK" :disabled="disabled">确认</Button>
      </div>
    </Modal>
  </div>
</template>
<script>
import {
  imageTableEdit, // 镜像表 编辑
} from '@/api/image'; 
export default {
  props: {
    tableRow:Object,
    editTime:String,
  },
  watch: {
    editTime(news){
      this.formItem.oldname = this.tableRow.name
      this.formItem.id = this.tableRow.id
      this.model = true
      this.disabled = false
      this.$refs.formItem.resetFields()
    }
  },
  data(){
    const propName = (rule, value, callback) => {
      let list = /^[\u4e00-\u9fa5_a-zA-Z0-9@_.-]{2,32}$/;
      if (list.test(value)) {
        callback();
      } else {
        callback(new Error("2-32 个中文、英文、数字、特殊字符(@_.-"));
      }
    };
    return {
      model:false,
      disabled: false,
      formItem:{
        oldname:"",
        name:"",
        id:""
      },
      ruleValidate:{
        name:[
          { required: true, message: '必填项', trigger: 'change' },
          { validator: propName, trigger: "change" },
        ]
      }
    }
  },
  methods: {
    modalOK(){
      this.$refs.formItem.validate((valid) => {
        if(valid){
          this.disabled = true
          imageTableEdit({
            id: this.formItem.id,
            name: this.formItem.name,
          })
          .then((callback) => {
            if(callback.data.id!==undefined) {
              this.model=false
              this.$emit("return-ok",'编辑镜像操作已完成');
            }else {
              this.disabled = false
              this.$emit("return-error",'编辑镜像操作失败');
            }
          }).catch((error) => {
            this.disabled = false
            this.$emit("return-error",'编辑镜像操作失败');
          })
        }
      })
    },
  }
}
</script>