<template>
  <div>
    <!-- 封装分页 -->
    <Page
      :total="total"
      show-total
      show-sizer
      :page-size="pageSize"
      placement="top"
      :page-size-opts="[10, 20, 30]"
      @on-change="handleChange"
      @on-page-size-change="handlePageSizeChange"
    />
  </div>
</template>
<script>
export default {
  props: {
    total:Number,
    pageSize:Number,
  },
  
  methods: {
    handleChange(page) {
      this.$emit('page-change', page);
    },
    handlePageSizeChange(size) {
      // 处理每页显示数量改变事件
      this.$emit('page-size-change', size);
    }
  }
}
</script>