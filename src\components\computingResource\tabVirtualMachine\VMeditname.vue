<template>
  <div>
    <Modal
      v-model="model"
      width="600"
      :mask-closable="false"
    >
      <template #header><p><span style="color:green">{{vmRow.name}}</span>虚拟机编辑名称</p></template>
      <Form :model="formItem" ref="formItem" :rules="ruleValidate" :label-width="150">
        <FormItem label="当前虚拟机名称">
          <Input
            v-model="formItem.oldname"
            disabled
          ></Input>
        </FormItem>
        <FormItem label="虚拟机新名称" prop="name">
          <Input
            v-model="formItem.name"
            placeholder="请输入虚拟机新名称"
          ></Input>
        </FormItem>
      </Form>
      <div slot="footer">
        <Button type="text" @click="model = false">取消</Button>
        <Button type="primary" @click="modelOK" :disabled="disabled">确认</Button>
      </div>
    </Modal>
  </div>
</template>
<script>
import {
  vmGroupTableModify, // 虚拟机组表格 修改
} from '@/api/virtualMachine';

export default {
  props: {
    vmRow:Object,
    editvmTime:String,
  },
  watch: {
    editvmTime(news){
      this.formItem.oldname = this.vmRow.name
      this.formItem.id = this.vmRow.id
      this.model = true
      this.disabled = false
      this.$refs.formItem.resetFields()
    }
  },
  data(){
    const propName = (rule, value, callback) => {
      let list = /^[\u4e00-\u9fa5_a-zA-Z0-9@_.-]{2,32}$/;
      if (list.test(value)) {
        callback();
      } else {
        callback(new Error("2-32 个中文、英文、数字、特殊字符(@_.-"));
      }
    };
    return {
      model:false,
      disabled:false,
      formItem:{
        oldname:"",
        name:"",
        id:""
      },
      ruleValidate:{
        name:[
          { required: true, message: '必填项', trigger: 'change' },
          { validator: propName, trigger: "change" },
        ]
      }
    }
  },
  methods: {
    // 确认事件
    modelOK(){
      this.$refs.formItem.validate((valid) => {
        if(valid){
          this.disabled = true
          vmGroupTableModify({name:this.formItem.name,vmid:this.formItem.id})
          .then((callback) => {
            this.model=false
            this.$emit("return-ok",'编辑虚拟机名称操作完成')
          }).catch((error) => {
            this.disabled = false
            this.$emit("return-error",'编辑虚拟机名称操作失败');
          })
        }else {
          this.disabled = false
        }
      })
      
    },
  }
}
</script>