const jsonServer = require('json-server')
const server = jsonServer.create()
const router = jsonServer.router('db.json')  // db.json 包含你的默认json数据
const middlewares = jsonServer.defaults()

server.use(middlewares)

// 添加自定义的中间件，使特定的请求返回HTTP 500错误
server.use((req, res, next) => {
  if (req.method === 'GET' && req.path === '/error') {
    res.status(404).jsonp({
      error: "Internal Server Error"
    })
  } else {
    next()
  }
})

server.use(router)
server.listen(3000, () => {
  console.log('JSON Server is running')
})