<template>
  <div>
    <!-- 此页面为和JB联调更改的页面 -->
    <Modal v-model="model" fullscreen>
      <template #header
        ><p>动态资源调度待确认表格</p></template
      >
      <div>
        <Table :columns="tableColumns" :data="tableData">
          <!-- 调度方式 -->
          <template v-slot:auto="{ row }">
            <span>{{ row.auto == "true" ? "自动" : "手动" }}</span>
          </template>
          <!-- 因素 -->
          <template v-slot:factor="{ row }">
            <Tag v-if="row.cpu_enabled" checkable color="success"
              >CPU</Tag
            >
            <Tag v-if="row.mem_enabled" checkable color="success"
              >内存</Tag
            >
          </template>
          <!-- 主机CPU -->
          <template v-slot:host_cpu="{ row }">
            <span>{{ row.host_cpu }}</span>
          </template>
          <!-- 主机内存 -->
          <template v-slot:host_mem="{ row }">
            <span>{{ row.host_mem }}</span>
          </template>
          <!-- 分级策略 -->
          <template v-slot:strategy="{ row }">
            <span
              >主机负载大于<span style="color: #b97">
                {{ row.migration_threshold }}</span
              >，且主机负载差值超过<span style="color: #b97">
                {{ row.host_diff }}</span
              ></span
            >
          </template>
          <!-- 推荐节点 -->
          <template v-slot:recommend_data="{ row }">
            <span>{{ row.recommend_data }}</span>
            <!-- <Select v-model="row.recommend_node">
              <Option
                v-for="item in row.recommend_data"
                :value="item"
                :key="item"
                >{{ item }}</Option
              >
            </Select> -->
          </template>
          <!-- 操作 -->
          <template v-slot:operation="{ row }">
            <Button @click="tablesConfirm(row)">迁移</Button>
          </template>
        </Table>
      </div>
      <div slot="footer">
        <Button type="text" @click="model = false">取消</Button>
      </div>
    </Modal>
  </div>
</template>
<script>
import { dispatchTableQuery, dispatchTableConfirm } from "@/api/other"; // 动态资源调度

export default {
  props: {
    datas: Number,
  },
  watch: {
    datas(news) {
      this.model = true;
      this.tablesQuery();
    },
    model(news) {
      if(news) {
        this.timer=setInterval(() => {
          this.tablesQuery();
        },20000)
      }else {
        clearInterval(this.timer)
      }
    }
  },
  data() {
    return {
      model: false,
      tableColumns: [
        { title: "调度方式", key: "auto", slot: "auto", width: 70 },
        { title: "衡量因素", key: "factor", slot: "factor", width: 70 },
        { title: "主机CPU", key: "host_cpu", slot: "host_cpu", width: 70 },
        { title: "主机内存", key: "host_mem", slot: "host_mem", width: 70 },
        { title: "虚拟机", key: "vm_name", tooltip: true },
        { title: "虚拟机CPU", key: "vm_cpu", width: 90 },
        { title: "虚拟机内存", key: "vm_mem", width: 90 },
        { title: "分级策略", key: "strategy", slot: "strategy", width: 300 },
        { title: "当前节点", key: "node" },
        { title: "推荐节点", key: "recommend_data", slot: "recommend_data" },
        { title: "过期时间", key: "expiry_date" },
        { title: "操作", key: "operation", width: 80, slot: "operation" },
      ],
      tableData: [],
      timer: null,
    };
  },
  methods: {
    tablesQuery() {
      this.tableData = []
      dispatchTableQuery().then((callback) => {
        if(callback.data.msg == 'ok') {
          let arr = new Array()
          let list = callback.data.data
          arr.push({
            auto: list.drs.auto, // 调度方式
            mem_enabled: list.drs.mem_enabled, // 是否内存超标
            cpu_enabled: list.drs.cpu_enabled, // 是否CPU超标
            migration_threshold: list.drs.migration_threshold+'%', // 迁移阈值
            host_diff: list.drs.host_diff+'%', // 主机负载差值
            host_ip: list.host.ip, // 主机IP
            host_cpu: list.host.cpu_average_load.toFixed(2)+'%', // 主机CPU
            host_mem: list.host.mem_average_load.toFixed(2)+'%', // 主机内存
            vm_name: list.vm.vm_name, // 虚拟机名称
            vm_id: list.vm.id, // 虚拟机ID
            vm_cpu: list.vm.cpu_usage_for_host.toFixed(2)+'%', // 虚拟机CPU
            vm_mem: list.vm.mem_usage_for_host.toFixed(2)+'%', // 虚拟机内存
            node:  list.vm.host, // 当前节点
            recommend_data: list.recommended_hosts[0], // 推荐节点组，顺序推荐
            expiry_date: list.expiry_date, // 过期时间
          })
          this.tableData = arr
        }else {
          // this.$Message.error({
          //   background: true,
          //   closable: true,
          //   duration: 5,
          //   content: callback.msg,
          // });
        }
      });
    },
    tablesConfirm(row) {
      const time = new Date(row.expiry_date.replace(' ', 'T'))
      const targetTimestamp = time.getTime()
      const currentTimestamp = Date.now(); 
      if (targetTimestamp < currentTimestamp) {
        return this.$Message.error({
          background: true,
          closable: true,
          duration: 5,
          content: "迁移时间已过期",
        });
      }
      this.$Modal.confirm({
        title: "迁移确认",
        content: `是否将节点从 ${row.node} 节点迁移至 ${row.recommend_data} 节点?`,
        onOk: () => {
          dispatchTableConfirm({
            node: row.node,
            recommend_node: row.recommend_data,
            vm_name: row.vm_name,
            vm_id: row.vm_id,
          }).then((callback) => {
            if (callback.data.msg == "ok") {
              this.$emit("scheduling-ok");
              this.tablesQuery();
              this.$Message.success({
                background: true,
                closable: true,
                duration: 5,
                content: "节点迁移完成",
              });
            } else {
              this.$Message.error({
                background: true,
                closable: true,
                duration: 5,
                content: "节点迁移失败",
              });
            }
          });
        },
        onCancel: () => {},
      });
    },
  },
};
</script>