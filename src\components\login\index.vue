<style>
 @import './login.css';
</style>
<template>
<div :class="loginClass">
  <img class="login_page_logo" src="../../assets/loginCRH.png" alt="THXH" v-if="THElogoTitle=='CRH'" >
  <img class="login_page_logo" src="../../assets/loginYGL.png" alt="THXH" v-if="THElogoTitle!=='CRH'" >
  <div class="login_container">
    <h2>用户登录</h2>
    <!-- 登录的表单 -->
    <Form ref="loginFormRef" :model="loginForm" :rules="loginFormRules" class="loginform_style">
        <!-- 登录名字 -->
        <FormItem prop="username">
          <div style="display: flex;align-items: center;">
            <Icon type="md-person" style="font-size:20px" />
            <Input type="text" v-model="loginForm.username" placeholder="请输入登录账号"></Input>
          </div>
        </FormItem>
        <!-- 登录密码 -->
        <FormItem prop="password">
          <div style="display: flex;align-items: center;">
            <Icon type="md-lock" style="font-size:20px" />
            <Input type="password" v-model="loginForm.password" password placeholder="请输入登录密码"></Input>
          </div>
        </FormItem>
        <FormItem>
            <Button @click="login" class="login_but">登录</Button>
            <!-- <Button type="info" @click="resetForm">重置</Button> -->
            <!-- <Button style="margin-left:30%" type="warning" ghost @click="register">新用户注册</Button> -->
            <!-- 注册对话框 -->
            <Modal title="新用户注册框" v-model="registerModa" :mask-closable="false" height="90%" width="70%" @on-ok="registerOK" @on-cancel="cancel">
              <div class="new_user_moda">
                <h1>新用户注册</h1>
                <Form ref="newUserRef" :model="newUserRegister" :rules="newUserRules" :label-width="80">
                  <FormItem label="用户名" prop="newUser" style="margin-bottom:20%">
                      <Input type="text" v-model="newUserRegister.newUser" placeholder="请输入注册用户名" >
                      </Input>
                  </FormItem>
                  <FormItem label="密码" prop="newPassword">
                      <Input type="password" v-model="newUserRegister.newPassword" placeholder="请输入新密码" @keyup.enter.native="login" >
                      </Input>
                  </FormItem>
                </Form>
              </div>
            </Modal>
        </FormItem>
    </Form>
    <Modal v-model="empowerModal" title="温馨提示" :footer-hide="true" :mask-closable="false">
      <div class="reminder">
        <div class="dow_file">
          <a :href="fileUrl">
            <Button type="primary">
              <Icon type="ios-download-outline"></Icon>下载授权环境文件
            </Button>
          </a>
        </div>
        <ul>
          <li>尊敬的用户您好，您的产品已过试用期，如需继续使用请联系：</li>
          <li><a target="_blank" href="http://www.thedatasys.com/">北京天华星航科技有限公司</a></li>
          <li>或使用授权文件进行授权</li>
        </ul>
        <Upload
          multiple
          type="drag"
          action="/thelicense/v1/authentication/analysis"
          :on-success="empowerOK"
          :on-error="empowerError"
        >
          <div style="padding: 20px 0">
              <Icon type="ios-cloud-upload" size="52" style="color: #3399ff"></Icon>
              <p>上传授权文件</p>
          </div>
        </Upload>
      </div>
    </Modal>
    <!-- 修改密码 -->
    <Modal v-model="change_password" width="600" :footer-hide="true" :mask-closable="false">
      <template #header>
        修改密码
      </template>
      <div style="display: flex;justify-content: center;padding:10px;height:50px">
        <img src="../../assets/loginCRH.png" alt="THXH" v-if="THElogoTitle=='CRH'" >
        <img src="../../assets/loginYGL.png" alt="THXH" v-if="THElogoTitle!=='CRH'" >
      </div>
      <Form ref="formss" :model="formItem" :label-width="100" :rules="rulesPW">
        <!-- <FormItem label="登录密码" style="padding: 10px 0" prop="loginPW"> -->
        <FormItem label="登录账号">
          <Input
            v-model="loginForm.username"
            disabled
          ></Input>
        </FormItem>
        <FormItem label="登录密码" prop="loginPW">
          <Input
            v-model="formItem.loginPW"
            type="password"
            password
            placeholder="请输入登录密码"
          ></Input>
        </FormItem>
        <FormItem label="新密码" prop="pwd_new">
          <Input
            v-model="formItem.pwd_new"
            type="password"
            password
            placeholder="请输入新密码"
          ></Input>
        </FormItem>
        <FormItem label="确认密码" prop="pwd_confirm">
          <Input
            v-model="formItem.pwd_confirm"
            type="password"
            password
            placeholder="请重新输入新密码"
          ></Input>
        </FormItem>
        <FormItem style="padding: 10px 0">
          <Button type="warning" ghost style="width: 100px" @click="resetClick('formss')">重置</Button>
          <Button class="plus_btn" style="width: 100px; margin-left: 100px" @click="confirmClick">确认</Button>
        </FormItem>
      </Form>
    </Modal>
  </div>
</div>

</template>
<script>
import { powerCodeQuery } from '@/api/other'; // 权限可用性 查询

import {logon,queryCodeRule,loginExpiredChangePassword} from '@/api/login';
import {automaticExitTimeQuery} from '@/api/system';
export default {
   data() {
    const propName = (rule, value, callback)=>{
      let list = /^[a-zA-Z0-9@_.]{2,32}$/
      if (list.test(value)) {
          callback()
      } else {
        callback(new Error("2-32 个英文、数字、特殊字符(@_.)"));
      }
    };
    const propPwd = (rule, value, callback)=>{
      let list = /^[a-zA-Z0-9@_.]{8,32}$/
      if (list.test(value)) {
          callback()
      } else {
        callback(new Error("8-32 个英文、数字、特殊字符(@_.)"));
      }
    };
    
    const validateLoginPW = (rule, value, callback) => {
      let list = window.sessionStorage.getItem('userPW')
      value!==""?callback():callback(new Error("登录密码有误,请重新输入"))
    };
    const validateNewPW = (rule, value, callback) => {
      let pwd_length = this.formItem.pwd_length;
      let guize = new RegExp(`^(?=.*[a-zA-Z])(?=.*\\d)(?=.*[@._])[a-zA-Z\\d@._]{${pwd_length},32}$`);
      if(value == this.formItem.loginPW){
        callback(new Error("新密码与登录密码重复"))
      }else if (!this.formItem.pwd_status) {
        guize = new RegExp(`^(?=.*[a-zA-Z0-9@._])[a-zA-Z0-9@._]{8,32}$`);
        guize.test(value)?callback():callback(new Error('请输入8-32个英文数字特殊符号@_.'))
      }else if (this.formItem.pwd_class == "l") {
        guize = new RegExp(`^(?=.*[a-zA-Z0-9@._])[a-zA-Z0-9@._]{${pwd_length},32}$`);
        guize.test(value)?callback():callback(new Error('请输入'+pwd_length+'-32个英文数字特殊符号@_.'))
      } else if (this.formItem.pwd_class == "m") {
        guize = new RegExp(`^(?=.*[a-zA-Z])(?=.*\\d)[a-zA-Z\\d@._]{${pwd_length},32}$`)
        guize.test(value)?callback():callback(new Error('请输入'+pwd_length+'-32个英文数字特殊符号@_. ，必须有英文和数字。'))
      } else if (this.formItem.pwd_class == "h") {
        guize = new RegExp(`^(?=.*[a-zA-Z])(?=.*\\d)(?=.*[@._])[a-zA-Z\\d@._]{${pwd_length},32}$`);
        guize.test(value)?callback():callback(new Error('请输入'+pwd_length+'-32个英文数字及特殊符号@_.'))
      }else {
        guize = new RegExp(`^(?=.*[a-zA-Z])(?=.*\\d)(?=.*[@._])[a-zA-Z\\d@._]{${pwd_length},32}$`);
        guize.test(value)?callback():callback(new Error('请输入'+pwd_length+'-32个英文数字及特殊符号@_.'))
      }
    };
    const validateConfirmPW = (rule, value, callback) => {
      if (value==this.formItem.pwd_new) {
        callback();
      } else {
        callback(new Error("两次输入的密码不匹配"));
      }
    };
    return {
      loginClass: "login_form logo_crh",
      empowerModal: false,
      fileUrl: '#',
      // fileUrl: 'http://***********:8055/v1/authentication/generate',
      THElogoTitle: "CRH",
      registerModa: false,
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      // 登录的表单绑定到的数据对象
      loginForm: {
        username: '',
        password: ''
      },
      // 登录表单的验证规则
      loginFormRules: {
        username: [
          { required: true, message: "登录名称不能为空", trigger: "blur" },
          { validator: propName, trigger: "change" },
        ],
        password: [
          { required: true, message: "登录密码不能为空", trigger: "blur" },
          { validator: propPwd, trigger: "change" },
        ],
      },
      // 新用户注册
      newUserRegister: {
        newUser: '',
        newPassword: ''
      },
      // 新注册用户表单验证
      newUserRules: {
        newUser: [
          { required: true, message: "登录名称不能为空", trigger: "blur" },
          { validator: propName, trigger: "change" },
        ],
        newPassword: [
          { required: true, message: "登录名称不能为空", trigger: "blur" },
          { validator: propPwd, trigger: "change" },
        ]
      },
      // 修改密码
      change_password: false,
      formItem: {
        loginPW: "",
        pwd_new: "",
        pwd_confirm: "",
        pwd_length: null,
        pwd_class: "l",
        pwd_status: true,
        token: "",
      },
      rulesPW: {
        loginPW: [
          { required: true, message: "密码不能为空"},
          { validator: validateLoginPW, trigger: 'blur' },
        ],
        pwd_new: [
          { required: true, message: "密码不能为空"},
          { validator: validateNewPW, trigger: 'blur' }
        ],
        pwd_confirm: [
          { required: true, message: "密码不能为空"},
          { validator: validateConfirmPW, trigger: 'change' }
        ],
      },
    }
  },
  created(){
    this.THElogoTitle = window.gurl.THElogoTitle
    if(this.THElogoTitle =="CRH") {
      this.loginClass="login_form logo_crh"
    }else {
      this.loginClass="login_form logo_ygl"
    }
    // 使用Enter按键直接登录
    let that = this;
    document.onkeydown = function (e) {
         e = window.event || e;
      if(that.$route.path=='/login'&&(e.code=='Enter'||e.code=='Num Enter')){//验证在登录界面和按得键是回车键enter
        that.login()
      }
    }
    // 禁止文本选中
    // this.$nextTick(() => {
    //   // 禁用右键
    //   document.oncontextmenu = new Function("event.returnValue=false");
    //   // 禁用选择
    //   document.onselectstart = new Function("event.returnValue=false");
    // });
  },
  methods: {
    // 点击重置按钮，重置表单项
    resetForm() {
      window.sessionStorage.setItem('user', 1)
      this.$refs.loginFormRef.resetFields()
    },
    // 点击按钮登录
    login() {
      this.$refs.loginFormRef.validate((valid) => {
        if(valid) {
          logon({username:this.loginForm.username,password:this.loginForm.password}).then(em=>{
          if(em.data.msg === 'ok') {
            this.menusQuery()
            window.sessionStorage.setItem('username',this.loginForm.username)
            window.sessionStorage.setItem('userPW',this.loginForm.password)
            automaticExitTimeQuery().then(callback =>{
              this.$store.state.power.exitTimeSave = callback.data.auto_exit_time
            })
          }else if(em.data.msg === 'expire'){
            this.formItem.token =em.data.data
            this.$Message.warning({
              background: true,
              closable: true,
              duration: 10,
              content: '用户密码安全危险，请重新设置密码。'
            });
            this.query_password_rules()
            this.change_password = true;
          }else if(em.data.msg === 'nolicense'){
            let protocol = window.location.protocol; // http:
            let hostname = window.location.hostname; // *************
            this.fileUrl = protocol+"//"+hostname+"/v1/authentication/generate"
            this.empowerModal = true
          }
        }).catch(error=>{
          if(error.response){
            this.$Message.error({
              background: true,
              closable: true,
              duration: 5,
              content: error.response.data.msg
            });
          }
        })
        }
      })
    },
    // 点击注册新用户
    register() {
      this.$refs.newUserRef.resetFields()
      this.registerModa = true;
    },
    // 用户注册确认
    registerOK () {
      this.$refs.newUserRef.validate((valid) => {
        if(valid){
          this.$axios.post('register/',{username:this.newUserRegister.newUser,password:this.newUserRegister.newPassword}).then(res=>{
            if(res.data.status_code==1) {
              this.$Message.success({ content: '创建用户成功', duration: 5, closable: true })
            }else{
              this.$Message.error({ content: '创建用户失败', duration: 5, closable: true })
            }
          })
        }
      })
    },
    cancel () {
      this.$Message.info('操作已取消');
    },
    // 查询密码规则
    query_password_rules(){
      this.$refs["formss"].resetFields();
      queryCodeRule().then(callback=>{
        this.formItem.pwd_length = callback.data.pwd_length
        this.formItem.pwd_class = callback.data.pwd_class
        this.formItem.pwd_status = callback.data.status=="on"?true:false
      })
    },
    // 重置表单
    resetClick(name){
      this.$refs[name].resetFields();
    },
    // 确认提交
    confirmClick(){
      this.$refs.formss.validate((valid) => {
        if (valid) {
          loginExpiredChangePassword({
            username: this.loginForm.username,
            password: this.formItem.loginPW,
            new_password: this.formItem.pwd_new,
            token: this.formItem.token,
          }).then(callback=>{
            if(callback.data.msg == "ok") {
              this.change_password = false
              this.$Message.success({
                background: true,
                closable: true,
                duration: 5,
                content:'密码修改成功，请重新登录'
              });
            }else {
              this.$Message.error({
                background: true,
                closable: true,
                duration: 5,
                content:callback.data.msg,
              });
            }
          })
        }else {
          this.$Message.warning({
            background: true,
            closable: true,
            duration: 5,
            content: "输入内容中有不符合规定的值",
          });
        }
      })
    },
    empowerOK(response,file,fileList){
      if(response.msg=="ok") {
        this.$Message.success({
          background: true,
          closable: true,
          duration: 5,
          content: "用户授权操作已完成,请重新登录",
        });
        this.empowerModal = false
      }
    },
    empowerError(error,file,fileList){
      console.log('上传失败',error,file,fileList)
    },
    // 查询导航权限
    menusQuery(){
      powerCodeQuery({
        module_code:[
          'gailan',
          'jisuanziyuan',
          'cunchuziyuan',
          'wangluoziyuan',
          'ziyuanjiankong',
          'xunijifenpei',
          'rizhiguanli',
          'xitonggongneng',
          'jiqungaojing',
          'yunweizhushou',
          'shezhi',
          'tuichudenglu',
          'xiugaimima',
          'mimaguize',
          'guanyuwomen',
        ]
      }).then(callback=>{
        if(callback.data.data.gailan) {
          this.$router.push('/overview')
        }else if(callback.data.data.jisuanziyuan) {
          this.$router.push('/computingResource')
        }else if(callback.data.data.cunchuziyuan) {
          this.$router.push('/storageResources')
        }else if(callback.data.data.wangluoziyuan) {
          this.$router.push('/networkResource')
        }else if(callback.data.data.ziyuanjiankong) {
          this.$router.push('/monitoring')
        }else if(callback.data.data.xunijifenpei) {
          this.$router.push('/resourceAllocation')
        }else if(callback.data.data.rizhiguanli) {
          this.$router.push('/logmanage')
        }else if(callback.data.data.xitonggongneng) {
          this.$router.push('/systemFunction')
        }else {
          this.$Message.error({
            background: true,
            closable: true,
            duration: 5,
            content: '该账号无可用权限'
          });
        }
        this.$Message.success({
          background: true,
          closable: true,
          duration: 5,
          content: '登录成功'
        });
      })
    },
  }
}
</script>
