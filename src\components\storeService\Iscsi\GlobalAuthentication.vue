<template>
  <div class="global-authentication-wrap">
    <Modal v-model="showModal" title="全局认证":mask-closable="false"  @on-ok="submitForm('formData')" @on-cancel="resetForm('formData', false)">
      <div v-loading="loading" element-loading-text="加载中..." class="pb-20">
         <Form :model="formData" :label-width="170" :rules="rules" ref="formData">
          <FormItem label="后端设备用户名设置" prop="target_iqn">
            <Input v-model="formData.user" placeholder="后端设备用户名设置"></Input>
          </FormItem>
          <FormItem label="后端设备密码设置" prop="target_iqn">
            <Input v-model="formData.password" placeholder="后端设备密码设置"></Input>
          </FormItem>
          <FormItem label="客户端用户名设置" prop="target_iqn">
            <Input v-model="formData.mutual_user" placeholder="客户端用户名设置"></Input>
          </FormItem>
          <FormItem label="客户端密码设置" prop="target_iqn">
            <Input v-model="formData.mutual_password" placeholder="客户端密码设置"></Input>
          </FormItem>
         </Form> 
      </div>
      <!-- <span slot="footer" class="dialog-footer mb-20 mt-20">
        <el-button @click="resetForm('formData')" icon="el-icon-circle-close">取消</el-button>
        <el-button type="primary" @click="submitForm('formData')" icon="el-icon-circle-check">确定</el-button>
      </span> -->
    </Modal>
  </div>
</template>

<script>
  // import { discoveryauthIscsi } from '@/api/storageService/ISCSI'
  export default {
    props: {
      value: {
        type: Boolean,
      }
    },
   data() {
      var validator1 = (rule, val, callback) => {
        if (!val) {
          callback(new Error("请输入"));
        } else {
          const reg = /^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{8,20}$/
          if (val.length < 8) {
            callback(new Error("请输入至少8位"));
            return
          }
          if (reg.test(val)) {
            return callback();
          } else {
            callback(new Error("请输入至少8位字母加数字组合"));
          }
        }
      };
      var validator2 = (rule, val, callback) => {
        if (!val) {
          callback(new Error("请输入"));
        } else {
          const reg = /^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{12,20}$/
          if (val.length < 12) {
            callback(new Error("请输入至少12位"));
            return
          }
          if (reg.test(val)) {
            return callback();
          } else {
            callback(new Error("请输入至少12位字母加数字组合"));
          }
        }
      };
     return{
       showModal: false,
       loading: false,
       formData: {
        user: '',
        password: '',
        mutual_user: '',
        mutual_password: '',
       },
       oldFormData: {
          user: '',
          password: '',
          mutual_user: '',
          mutual_password: '',
       },
       rules: {
         'auth.user': [
            { required: true, message: '请输入后端设备用户名', trigger: 'blur' },
            { min: 8, validator: validator1, trigger: 'blur' }
          ],
         'auth.password': [
            { required: true, message: '请输入后端设备密码', trigger: 'blur' },
            { min: 12, validator: validator2, trigger: 'blur' }
          ],
         'auth.mutual_user': [
            { required: true, message: '请输入客户端用户名', trigger: 'blur' },
            { min: 8, validator: validator1, trigger: 'blur' }
          ],
         'auth.mutual_password': [
            { required: true, message: '请输入客户端密码', trigger: 'blur' },
            { min: 12, validator: validator2, trigger: 'blur' }
          ],
       },
       labelPosition: 'right',
     }
    },
    mounted(){},
    methods:{
      resetForm(formName) {
        this.$refs[formName].resetFields();
        this.$emit('input', false)
      },
      submitForm(formName) {
        this.$refs[formName].validate((valid) => {
          if (valid) {
            this.$axios.put( `/thecephapi/api/iscsi/discoveryauth `,this.formData).then( res => {
               this.$Message.success({
                background: true,
                closable: true,
                duration: 5,
                content: "全局认证成功",
              });
              this.showModal = false
              // this.$emit('input', false)
            }).catch(error => {
              this.showModal = false
              // this.$emit('input', false)
            })
          } else {
            this.$Message.error();({
              background: true,
              closable: true,
              duration: 5,
              content: "全局认证失败",
            });
            // this.$emit('input', false)
            this.showModal = false 
            return false;
          }
        });
      },
    },
    watch: {
      value(newVal, oldVal) {
        if (newVal) {
         this.showModal = newVal
         this.formData = JSON.parse(JSON.stringify(this.oldFormData))
        }
      }
    }
  }
</script>

<style scoped lang='less'>
  .global-authentication-wrap{

  }
</style>