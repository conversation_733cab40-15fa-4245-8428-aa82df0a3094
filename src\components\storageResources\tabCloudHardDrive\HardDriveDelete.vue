<template>
  <div>
    <Modal
      v-model="model"
      width="600"
      :mask-closable="false"
    >
      <template #header><p>删除云硬盘</p></template>
      <div style="padding: 5px">
        <span>是否删除下列云硬盘？</span>
        <p style="color:red;word-wrap: break-word">{{tableNames.toString()}}</p>
      </div>
      <div slot="footer">
        <Button type="text" @click="model = false">取消</Button>
        <Button type="primary" @click="modelOK" :disabled="disabled">确认</Button>
      </div>
    </Modal>
  </div>
</template>
<script>
import {
  cloudDiskDelete, // 云硬盘 删除
} from '@/api/storage'; 
export default {
  props: {
    tableDelet: Array,
    deleteTime: String,
  },
  watch: {
    deleteTime(news){
      this.tableNames = this.tableDelet.map(em=>{ return em.name})
      this.tableIDS = this.tableDelet.map(em=>{ return em.id})
      this.model = true
      this.disabled = false
    }
  },
  data(){
    return {
      model:false,
      disabled: false,
      tableNames: [],
      tableIDS: [],
    }
  },
  methods: {
    modelOK() {
      this.disabled = true
      this.model = false;
      this.tableNames.forEach((em,index)=>{
        cloudDiskDelete({data:{
          id: this.tableIDS[index],
          name: this.tableNames[index]
        }})
        .then(callback=>{
          if(callback.data.msg=="snap") {
            this.$emit("return-error",this.tableNames[index]+' 云硬盘存在快照，无法删除该云硬盘');
          }else if(callback.data.msg=="error") {
            this.$emit("return-error",this.tableNames[index]+' 云硬盘删除操作失败');
          }else {
            this.$emit("return-ok",this.tableNames[index]+' 云硬盘删除操作完成');
          }
        })
        .catch((error) => {
          this.$emit("return-error",this.tableNames[index]+' 云硬盘删除操作失败');
        })
      })
    },
    
  },
}
</script>