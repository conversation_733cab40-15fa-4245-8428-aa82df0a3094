<style lang="less">
@import "../logmanage.less";
</style>
<template>
  <div class="alarm_rules_area">
    <Spin fix v-if="spinShow" size="large" style="color:#ef853a">
      <Icon type="ios-loading" size=50 class="demo-spin-icon-load"></Icon>
      <div style="font-size:16px;padding:20px">Loading...</div>
    </Spin>
    <div class="table-button-area">
      <div>
        <!-- <Button class="plus_btn" @click="newClick"><span class="icon iconfont icon-plus-circle"></span> 新建规则</Button > -->
        <Button class="plus_btn" v-show="powerAcitons.shezhiyuzhi" @click="tableCheckClick(tableSelec,'启用')"> 启用</Button>
        <Button class="close_btn" v-show="powerAcitons.shezhiyuzhi" @click="tableCheckClick(tableSelec,'禁用')"> 禁用</Button>
        <!-- <Button class="close_btn" @click="tableCheckClick(tableSelec,'删除')"><span class="icon iconfont icon-close-circle"></span> 删除</Button> -->
      </div>
      <div>
        <!-- <Input v-model="tablePageForm.search_str" search enter-button placeholder="请输入名称" style="width: 300px" @on-search="tableVMsearchInput" /> -->
      </div>
    </div>
    <div class="table_currency_area" v-show="powerAcitons.gaojingguizeliebiao">
      <Table :columns="tableColumn" :data="tableData" @on-sort-change="sortColumn" @on-selection-change="tableChange">
        <!-- 类别 -->
        <template v-slot:job="{ row }">
          <span>{{ row.job==null?"集群告警":"存储告警" }}</span>
        </template>
        <!-- 严重告警 -->
        <template v-slot:critical_value="{ row }">
          <template v-if="row.expr_code=='0'">
            <Icon v-if="row.critical_value=='-1'" type="md-checkmark" color="green" />
            <span v-else>-</span>
          </template>
          <template v-if="row.expr_code!=='0'">
            <span v-if="row.critical_value=='0'">-</span>
            <span v-else>{{'>=' }}{{row.critical_value}}{{ row.unit}}</span>
          </template>
        </template>
        <!-- 重要告警 -->
        <template v-slot:major_value="{ row }">
          <template v-if="row.expr_code=='0'">
            <Icon v-if="row.major_value=='-1'" type="md-checkmark" color="green" />
            <span v-else>-</span>
          </template>
          <template v-if="row.expr_code!=='0'">
            <span v-if="row.major_value=='0'">-</span>
            <span v-else>{{'>=' }}{{row.major_value}}{{ row.unit}}</span>
          </template>
        </template>
        <!-- 次要告警 -->
        <template v-slot:warning_value="{ row }">
          <template v-if="row.expr_code=='0'">
            <Icon v-if="row.warning_value=='-1'" type="md-checkmark" color="green" />
            <span v-else>-</span>
          </template>
          <template v-if="row.expr_code!=='0'">
            <span v-if="row.warning_value=='0'">-</span>
            <span v-else>{{'>=' }}{{row.warning_value}}{{ row.unit}}</span>
          </template>
        </template>
        <!-- 提示告警 -->
        <template v-slot:info_value="{ row }">
          <template v-if="row.expr_code=='0'">
            <Icon v-if="row.info_value=='-1'" type="md-checkmark" color="green" />
            <span v-else>-</span>
          </template>
          <template v-if="row.expr_code!=='0'">
            <span v-if="row.info_value=='0'">-</span>
            <span v-else>{{'>=' }}{{row.info_value}}{{ row.unit}}</span>
          </template>
        </template>
        
        <!-- 持续时间 -->
        <template v-slot:for_interval="{ row }">
          <span>{{row.for_interval.split('m')[0]}}分钟</span>
        </template>
        <!-- 状态 -->
        <template v-slot:status="{ row }">
          <span v-if="row.status == 'enabled'" style="color:green">启用</span>
          <span v-else style="color:red">禁用</span>
        </template>
        <!-- 操作 -->
        <template v-slot:operation="{ row }">
          <Dropdown @on-click="dropdownClick($event, row)">
            <Button>配置 ▼</Button>
            <template #list>
              <DropdownMenu>
                <DropdownItem name="bj" >编辑</DropdownItem>
                <DropdownItem name="qy" >启用</DropdownItem>
                <DropdownItem name="jy" >禁用</DropdownItem>
                <!-- <DropdownItem name="sc" :disabled="row.is_default==1" style="color: red" divided
                  >删除</DropdownItem
                > -->
              </DropdownMenu>
            </template>
          </Dropdown>
        </template>
      </Table>
      <!-- 虚拟机表格 分页  -->
      <div class="pages" v-if="this.tableData.length>0">
        <Pagination  :total="tableTotal" :page-size="tablePageForm.pagecount"  @page-change="onPageChange" @page-size-change="onPageSizeChange"/>
      </div>
    </div>
    <!-- 新建规则 -->
    <RulesNew
      :newTime="newTime"
      @return-error="returnError"
      @return-ok="returnOK"
    ></RulesNew>
    <!-- 修改规则 -->
    <RulesEdit
      :editTime="editTime"
      :tableRow="tableRow"
      @return-error="returnError"
      @return-ok="returnOK"
    ></RulesEdit>
    <!-- 表格操作启用、禁用、删除 -->
    <RulesAction
      :tableCheckTime="tableCheckTime"
      :tableCheckText="tableCheckText"
      :tableCheckData="tableCheckData"
      @return-error="returnError"
      @return-ok="returnOK"
    ></RulesAction>
  </div>
</template>
<script>
import { powerCodeQuery } from '@/api/other'; // 权限可用性 查询

import {
  alarmRulesQuery, // 告警规则 查询
} from '@/api/log';
import Pagination from '@/components/public/Pagination.vue';

import RulesNew from "./RulesNew.vue"; // 新建
import RulesEdit from "./RulesEdit.vue"; // 新建
import RulesAction from "./RulesAction.vue"; // 表格操作


export default {
  components: {
    Pagination,
    RulesNew,
    RulesEdit,
    RulesAction
  },
  props: {
    tabName: String,
  },
  watch: {
    tabName(value) {
      if(value=='告警规则'){
        this.actionQuery()
      }
    }
  },
  mounted() {
    if(this.$store.state.power.logManagementTab == '告警规则') {
      this.actionQuery()
    }
  },
  data() {
    return {
      spinShow: false, // 加载动画
      tableColumn: [],
      tableData: [],
      tableSelec: [],
      tableTotal: 0,
      tablePageForm: {
        page: 1,
        pagecount: 10,
        search_str: '',
        order_type: 'desc',
        order_by: '',
      },

      tableRow: {},
      newTime: '', // 新增规则
      editTime: '', // 编辑规则
      // 禁用、启用、删除规则
      tableCheckData: [],
      tableCheckTime: '',
      tableCheckText: '',

      powerAcitons: {}, // 操作权限数据
      
    };
  },
  updated() {
    this.tablePageForm.pagecount=this.$store.state.power.pagecount
  },
  methods: {
    cloumnManage(){
      this.tableColumn=[
        { type: "selection", width: 30, align: "center"},
        { title: "名称", key: "name", tooltip:true },
        { title: "类别", key: "job", align: "center", slot: 'job' },
        { title: "严重告警", align: "center", key: "critical_value", slot: 'critical_value' },
        { title: "重要告警", align: "center", key: "major_value",slot: 'major_value' },
        { title: "次要告警", align: "center", key: "warning_value",slot: 'warning_value' },
        { title: "提示告警", align: "center", key: "info_value",slot: 'info_value' },
        { title: "持续时间", align: "center", key: "for_interval",slot: 'for_interval' },
        { title: "状态", align: "center", key: "status",width: 80, slot: 'status' },
        ...(this.powerAcitons.shezhiyuzhi?[{ title: "操作", key: "operation",width:120,slot: "operation" }]:[]),
      ]
    },
    // 表格数据查询
    tableQuery() {
      this.spinShow = true
      alarmRulesQuery(this.tablePageForm)
      .then(callback=>{
        this.tableSelec = new Array()
        this.spinShow = false
        // let list = callback.data.map(em=>{
        //   let newEm = {...em}
        //   if(newEm.status) {
        //     newEm['_disabled'] = true
        //   }else {
        //     newEm['_disabled'] = false
        //   }
        //   return newEm
        // })
        this.tableTotal = callback.data.total
        this.tableData = callback.data.data
      })
    },
    // 表格选中数据
    tableChange(data) {
      this.tableSelec = data
    },
    // 表格下拉操作
    dropdownClick(event, row){
      switch (event) {
        case "bj":
          this.tableRow = row;
          this.editTime = '' + new Date();
          break;
        case "qy":
          this.tableCheckClick([row],'启用')
          break;
        case "jy":
          this.tableCheckClick([row],'禁用')
          break;
        case "sc":
          this.tableCheckClick([row],'删除')
          break;
      }
    },
    // 添加告警规则
    newClick(){
      this.newTime = '' + new Date();
    },
    // 禁用、启用、删除
    tableCheckClick(data,text) {
      if(data == 0) {
        this.$Message.warning({
          background: true,
          closable: true,
          duration: 5,
          content: "未选择表格数据",
        });
      }else {
        this.tableCheckData = data
        this.tableCheckTime = '' + new Date();
        this.tableCheckText = text
      }
    },
    // 当前分页
    onPageChange(item) {
      this.tablePageForm.page = item;
      this.tableQuery();
    },
    // 每页条数
    onPageSizeChange(item) {
      this.$store.state.power.pagecount = item;
      this.tablePageForm.pagecount = this.$store.state.power.pagecount;
      this.tablePageForm.page = 1;
      this.tableTotal = 0;
      this.tableQuery();
    },
    // 虚拟机列表 列排序
    sortColumn(column, key, order) {
      if (column.key !== "normal") {
        this.tablePageForm.order_by = column.key;
        this.tablePageForm.order_type = column.order;
        this.tablePageForm.page = 1;
        this.tableTotal = 0;
        this.tableQuery();
      }
    },
    // 搜索虚拟机列表
    tableVMsearchInput() {
      this.tablePageForm.page = 1;
      this.tableTotal = 0;
      this.tableQuery();
    },
    // 子组件返回错误
    returnError(data) {
      this.$Message.error({
        background: true,
        closable: true,
        duration: 5,
        content: data,
      });
    },
    // 子组件返回成功
    returnOK(data) {
      this.tableQuery();
      this.$Message.success({
        background: true,
        closable: true,
        duration: 5,
        content: data,
      });
    },
    // 操作权限获取
    actionQuery(){
      powerCodeQuery({
        module_code:[
          'gaojingguizeliebiao',
          'shezhiyuzhi',
        ]
      }).then(callback=>{
        this.powerAcitons = callback.data.data
        this.cloumnManage()
        this.powerAcitons.gaojingguizeliebiao?this.tableQuery():null
      })
    },
  },
};
</script>