<template>
  <div>
    <Modal v-model="model" width="600" :mask-closable="false">
      <template #header><p>新建云硬盘</p></template>
      <Form
        :model="formItem"
        ref="formItem"
        :rules="rulesForm"
        :label-width="120"
      >
        <FormItem label="云硬盘名称" prop="name">
          <Input v-model="formItem.name" placeholder="请输入云硬盘名称"></Input>
        </FormItem>
        <FormItem label="云硬盘容量">
          <div class="slider_area">
            <div style="width:350px">
              <Slider v-model="formItem.size" :min='1' :max='10240' :tip-format="formMemory" ></Slider>
            </div>
            <div style="width:80px">
              <InputNumber :min='1' :max="10240" v-model="formItem.size" :formatter="value => `${value}GB`" :parser="value => value.replace('GB', '')"></InputNumber>
            </div>
          </div>
        </FormItem>
        <FormItem label="类型选择" prop="type">
          <Select v-model="formItem.type">
            <Option v-for="item in typeData" :value="item.value" :key="item.id" >{{ item.name }}</Option>
          </Select>
        </FormItem>
        <FormItem label="备注" >
          <Input v-model="formItem.description" placeholder="请输入备注信息"></Input>
        </FormItem>
      </Form>
      <div slot="footer">
        <Button type="text" @click="model = false">取消</Button>
        <Button type="primary" @click="modelOK" :disabled="disabled">确认</Button>
      </div>
    </Modal>
  </div>
</template>
<script>
import {
  cloudDiskNewBuilt, // 云硬盘 新建
  typeQuery, // 类型管理 查询
} from '@/api/storage';
export default {
  props: {
    newTime: String,
  },
  watch: {
    newTime(news){
      this.TypeManagementData()
      this.formItem.description = ''
      this.formItem.size = 1
      this.$refs.formItem.resetFields();
      this.model = true
      this.disabled = false
    }
  },
  data(){
    const propName =(rule,value,callback)=>{
      let list = /^[\u4e00-\u9fa5_a-zA-Z0-9@_.-]{2,32}$/;
      if(list.test(value)){
        callback()
      }else{
        callback(new Error("2-32 个中文、英文、数字、特殊字符(@_.-)"));
      }
    }
    return {
      model: false,
      disabled: false,
      formItem: {
        name: '',
        size: 1,
        type: '',
        description: '',
      },
      typeData: [],
      // 正则验证
      rulesForm: {
        name:[
          { required: true, message: '必填项', trigger: 'change' },
          { validator: propName, trigger:'change' }
        ],
      },
    }
  },
  methods: {
    // 云硬盘大小
    formMemory (val) {
      return val + ' GB';
    },
    // 查询类型
    TypeManagementData() {
      typeQuery().then(callback=>{
        let arr = new Array()
        callback.data.forEach(callback=>{
          arr.push({
            id: callback.id,
            value: callback.name,
            name: callback.name=="__DEFAULT__"?"默认类型":callback.name,
            extra_specs: callback.extra_specs,
            description: callback.description,
          })
        })
        this.typeData = arr
        this.formItem.type = arr[0].value
      })
    },
    // 确认事件
    modelOK() {
      this.$refs.formItem.validate((valid) => {
        if (valid) {
          this.disabled = true;
          cloudDiskNewBuilt({
            name: this.formItem.name,
            size: this.formItem.size,
            volume_type: this.formItem.type,
            description: this.formItem.description,
          }).then(callback=>{
            if(callback.data.msg !== "error"){
              this.$emit("return-ok",'新建云硬盘操作已完成','init');
              this.model = false;
            }else {
              this.$emit("return-error","新建云硬盘操作失败");
              this.disabled = false;
            }
          })
          .catch((error) => {
            this.$emit("return-error",'新建云硬盘操作失败');
            this.disabled = false;
          });
        }
      });
    },
  }
}
</script>
