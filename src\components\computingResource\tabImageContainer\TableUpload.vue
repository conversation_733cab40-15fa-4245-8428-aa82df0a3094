<template>
  <Modal
    v-model="show"
    title="上传容器镜像"
    :mask-closable="false"
    :closable="true"
    width="500"
  >
    <div class="upload-container">
      <Form :model="formData" :label-width="100">
        <FormItem label="目标主机" prop="host">
          <Select v-model="formData.host" placeholder="请选择主机">
            <Option
              v-for="item in hostData"
              :value="item.hostname"
              :key="item.uuid"
              >{{ item.hostname }}</Option
            >
          </Select>
        </FormItem>
        <FormItem label="镜像仓库" prop="repo">
          <Input v-model="formData.repo" placeholder="请输入仓库，如：tianwen1:5000/ubuntu" />
        </FormItem>
        <FormItem label="标签" prop="tag">
          <Input v-model="formData.tag" placeholder="请输入标签，如：20.05" />
        </FormItem>
        <FormItem label="镜像文件" prop="file">
          <Upload
            ref="upload"
            :before-upload="handleBeforeUpload"
            :show-upload-list="false"
            :max-size="10240"
            action=""
            accept=".tar"
          >
            <Button icon="ios-cloud-upload-outline">选择镜像文件</Button>
            <div class="tip" style="margin-top: 8px;">只能上传 tar 文件，且大小不超过 10GB</div>
          </Upload>
          <div v-if="file" class="selected-file-info">
            <span class="file-name">{{ file.name }}</span>
            <span class="file-size">{{ formatFileSize(file.size) }}</span>
            <Button type="text" icon="ios-close" @click="removeFile" class="remove-file"></Button>
          </div>
        </FormItem>
      </Form>
    </div>
    <div slot="footer">
      <Button @click="cancelHandle">取消</Button>
      <Button type="primary" @click="submitUpload" :loading="uploading">确认上传</Button>
    </div>
  </Modal>
</template>

<script>
import axios from 'axios';
import { hostQuery } from '@/api/container'; // 导入 hostQuery 函数

export default {
  name: "TableUpload",
  props: {
    uploadTime: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      show: false,
      formData: {
        host: "controller1",
        repo: "tianwen1:5000/ubuntu",
        tag: "",
        file: null
      },
      file: null,
      uploading: false,
      hostData: [] // 添加主机数据
    };
  },
  watch: {
    uploadTime: {
      handler(newVal, oldVal) {
        if (newVal !== "") {
          this.show = true;
          // 打开弹窗时获取主机数据
          this.fetchHostData();
        }
      },
    },
  },
  methods: {
    // 关闭弹窗
    cancelHandle() {
      this.show = false;
      this.formData = {
        host: "controller1",
        repo: "tianwen1:5000/ubuntu",
        tag: "",
        file: null
      };
      this.file = null;
      if (this.$refs.upload) {
        this.$refs.upload.clearFiles();
      }
    },
    // 文件上传前的处理
    handleBeforeUpload(file) {
      this.file = file;
      return false; // 阻止自动上传
    },
    // 格式化文件大小
    formatFileSize(size) {
      if (size < 1024) {
        return size + ' B';
      } else if (size < 1024 * 1024) {
        return (size / 1024).toFixed(2) + ' KB';
      } else if (size < 1024 * 1024 * 1024) {
        return (size / (1024 * 1024)).toFixed(2) + ' MB';
      } else {
        return (size / (1024 * 1024 * 1024)).toFixed(2) + ' GB';
      }
    },
    // 移除已选择的文件
    removeFile() {
      this.file = null;
      if (this.$refs.upload) {
        this.$refs.upload.clearFiles();
      }
    },
    // 提交上传
    submitUpload() {
      if (!this.file) {
        this.$Message.warning('请选择要上传的镜像文件');
        return;
      }
      if (!this.formData.host) {
        this.$Message.warning('请输入主机名');
        return;
      }
      if (!this.formData.repo) {
        this.$Message.warning('请输入镜像仓库');
        return;
      }
      if (!this.formData.tag) {
        this.$Message.warning('请输入标签');
        return;
      }

      this.uploading = true;
      
      // 创建FormData对象
      const formData = new FormData();
      formData.append('host', this.formData.host);
      formData.append('repo', this.formData.repo);
      formData.append('tag', this.formData.tag);
      formData.append('file', this.file);

      // 发送请求
      axios({
        method: 'post',
        url: '/upload/v1/images',  // 使用已配置好的代理路径
        data: formData,
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })
      .then(response => {
        this.uploading = false;
        this.$emit('return-ok', { type: 'ok', msg: '容器镜像上传成功' });
        this.cancelHandle();
      })
      .catch(error => {
        this.uploading = false;
        this.$emit('return-ok', { type: 'error', msg: '容器镜像上传失败: ' + ((error.response && error.response.data && error.response.data.message) || error.message) });
      });
    },
    // 获取主机数据
    fetchHostData() {
      hostQuery()
        .then(response => {
          this.hostData = response.data.hosts;
          if (this.hostData && this.hostData.length > 0) {
            this.formData.host = this.hostData[0].hostname;
          }
        })
        .catch(error => {
          this.$Message.error('获取主机数据失败: ' + error.message);
        });
    }
  },
  created() {
    // 创建时获取主机数据
    this.fetchHostData();
  }
};
</script>

<style lang="less" scoped>
.upload-container {
  padding: 10px;
}

.selected-file-info {
  margin-top: 10px;
  padding: 8px 12px;
  background-color: #f8f8f9;
  border-radius: 4px;
  display: flex;
  align-items: center;
  
  .file-name {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-right: 10px;
  }
  
  .file-size {
    color: #999;
    margin-right: 10px;
  }
  
  .remove-file {
    padding: 0;
    color: #999;
    &:hover {
      color: #ed4014;
    }
  }
}
</style>