<template>
  <div>
    <Modal
      v-model="model"
      width="650"
      :mask-closable="false"
    >
      <template #header><p>{{vmRow.oldnam}}虚拟机动态资源扩展</p></template>
      <Form :model="formItem" :label-width="120">
        <FormItem label="动态资源添加">
          <Checkbox v-model="formItem.addCheck"></Checkbox>
          <span class="span-color-info">启用动态资源添加后,系统将自动评估虚拟机的性能，当虚拟机性能长期不足时自动为虚拟机添加CPU和内存资源，确保业务持续高效运行。</span>
        </FormItem>
        <FormItem label="CPU">
          <Checkbox v-model="formItem.cpuCheck" :disabled="!formItem.addCheck"></Checkbox>
          <span class="span-color-info">当虚拟机CPU利用率 大于 <InputNumber :max="99" :min="1" v-model="formItem.cpuValue" @on-blur="cpuInput" :disabled="!formItem.addCheck||!formItem.cpuCheck" />%时，将为虚拟机添加CPU,每次增加一个插槽的核数，上限为虚拟机原配置两倍。</span>
        </FormItem>
        <FormItem label="内存">
          <Checkbox v-model="formItem.memCheck" :disabled="!formItem.addCheck"></Checkbox>
          <span class="span-color-info">当虚拟机内存阈值 大于 <InputNumber :max="99" :min="1" v-model="formItem.memValue" @on-blur="memInput" :disabled="!formItem.addCheck||!formItem.memCheck"/>%时，将为虚拟机添加内存,每次增加原配置1/8(取整)，上限为虚拟机原配置两倍。</span>
        </FormItem>
        <FormItem label="敏感度">
          <Select v-model="formItem.interval" :disabled="!formItem.addCheck">
            <Option v-for="item in intervalData" :value="item.value" :key="item.value">{{ item.label }}</Option>
          </Select>
          <span class="span-color-info">在该模式下，系统检测到衡量因素达到阈值并持续<span class="span-color-keynote"> {{formItem.interval}}分钟 </span>，将对虚拟机进行动态资源添加。</span>
        </FormItem>
      </Form>
      <div slot="footer">
        <Button type="text" @click="model = false">取消</Button>
        <Button type="primary" @click="modalOK" :disabled="disabled">确认</Button>
      </div>
    </Modal>
  </div>
</template>
<script>
import { dynamicResourceExtendQuery,dynamicResourceExtendEdit } from '@/api/virtualMachine';  // 动态资源扩展
export default {
  props: {
    vmRow:Object,
    dynamicResourceTime:String,
  },
  watch: {
    dynamicResourceTime(news){
      this.queryData()
      this.model = true
      this.disabled = false
    }
  },
  data(){
    return {
      model: false,
      disabled: false,
      formItem:{
        addCheck: false,
        cpuCheck: false,
        cpuValue: 1,
        memCheck: false,
        memValue: 1,
        interval: '3',
      },
      intervalData: [
        { value: '3',label: '3分钟' },
        { value: '5',label: '5分钟' },
        { value: '10',label: '10分钟' },
        { value: '15',label: '15分钟' },
      ]
    }
  },
  methods: {
    // 查询数据
    queryData(){
      dynamicResourceExtendQuery(this.vmRow.id)
      .then(callback => {
        if(callback.data.data==null){
          this.formItem.addCheck = false
          this.formItem.cpuCheck = false
          this.formItem.cpuValue = 1
          this.formItem.memCheck = false
          this.formItem.memValue = 1
          this.formItem.interval = '3'
        }else {
          this.formItem.addCheck = callback.data.data.enabled=="true"
          this.formItem.cpuCheck = callback.data.data.cpu_enabled=="true"
          this.formItem.cpuValue = callback.data.data.cpu==null?1:callback.data.data.cpu*1
          this.formItem.memCheck = callback.data.data.mem_enabled=="true"
          this.formItem.memValue = callback.data.data.mem==null?1:callback.data.data.mem*1
          this.formItem.interval = callback.data.data.interval
        }
      }).catch((error) => {})
    },
    modalOK(){
      this.disabled = true
      let data = {
        vm_id: this.vmRow.id,
        vm_name: this.vmRow.name,
        enabled: this.formItem.addCheck?'true':'false',
        ...(this.formItem.addCheck?{interval: this.formItem.interval}:null),
        ...(this.formItem.addCheck?{cpu_enabled: this.formItem.cpuCheck?'true':'false'}:null),
        ...(this.formItem.addCheck&&this.formItem.cpuCheck?{cpu: this.formItem.cpuValue.toString()}:null),
        ...(this.formItem.addCheck?{mem_enabled: this.formItem.memCheck?'true':'false'}:null),
        ...(this.formItem.addCheck&&this.formItem.memCheck?{mem: this.formItem.memValue.toString()}:null),
      }
      dynamicResourceExtendEdit(data)
      .then((callback) => {
        if(callback.data.msg=='ok'){
          this.model=false
          this.$Message.success({
            background: true,
            closable: true,
            duration: 5,
            content: '虚拟机动态资源扩展操作完成',
          });
        }else {
          this.disabled = false
          this.$emit("return-error",callback.dta.msg);
        }
      })
    },
    cpuInput(){
      this.formItem.cpuValue = Math.floor(this.formItem.cpuValue);
    },
    memInput(){
      this.formItem.memValue = Math.floor(this.formItem.memValue);
    },
  }
}
</script>
<style scoped>
  .span-color-info{
    color: #ccc
  }
  .span-color-keynote{
    color: #b97
  }
</style>