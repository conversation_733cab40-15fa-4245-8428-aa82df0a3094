<style scoped>
@import "../computingResource.less";
</style>
<template>
  <div class="recycle_bin_area">
    <Spin fix v-if="spinShow" size="large" style="color: #ef853a">
      <Icon type="ios-loading" size="50" class="demo-spin-icon-load"></Icon>
      <div style="font-size: 16px; padding: 20px">Loading...</div>
    </Spin>
    <div class="table-button-area">
      <div>
        <Button class="rollback" @click="recoveryVMclick(tableSelec)" v-show="powerAcitons.huishouzhanhuanyuan"
          ><span class="icon iconfont icon-rollback"></span> 还原</Button
        >
        <Button class="close_btn" @click="deletVMclick(tableSelec)" v-show="powerAcitons.huishouzhanshanchu"
          ><span class="icon iconfont icon-close-circle"></span> 删除</Button
        >
      </div>
      <div v-show="powerAcitons.huishouzhantiaojianchaxun">
        <Input
          v-model="tablePageForm.search_str"
          search
          enter-button
          placeholder="请输入名称"
          style="width: 300px"
          @on-search="tableVMsearchInput"
        />
      </div>
    </div>
    <div class="table_currency_area">
      <Table
        :columns="tableColumn"
        :data="tableData"
        @on-selection-change="tableChange"
        @on-sort-change="sortColumn"
      >
        <!-- IP -->
        <template v-slot:ip="{row}">
          <Tooltip :content="convertIP(row.ip)" style="width:100%">
            <span class="text_overflow">{{ convertIP(row.ip) }}</span>
          </Tooltip>
        </template>
        <!-- 销毁时间 -->
        <template v-slot:deadline="{row}">
          <span>{{ row.deadline }} 天</span>
        </template>
        <!-- 内存 -->
        <template v-slot:ram="{row}">
          <span>{{ (row.ram/1024).toFixed(1) }} GB</span>
        </template>
        <!-- 硬盘 -->
        <template v-slot:disk="{row}">
          <span>{{ row.disk }} GB</span>
        </template>
        
        <template v-slot:operation="{ row }">
          <Dropdown @on-click="dropdownClick($event, row)">
            <Button>配置 ▼</Button>
            <template #list>
              <DropdownMenu>
                <DropdownItem name="hy" v-show="powerAcitons.huishouzhanhuanyuan">还原</DropdownItem>
                <DropdownItem name="sc" v-show="powerAcitons.huishouzhanshanchu" style="color: red" divided
                  >删除</DropdownItem
                >
              </DropdownMenu>
            </template>
          </Dropdown>
        </template>
      </Table>
      <!-- 虚拟机表格 分页  -->
      <div class="pages" v-if="this.tableData.length > 0">
        <Pagination
          :total="tableTotal"
          :page-size="tablePageForm.pagecount"
          @page-change="onPageChange"
          @page-size-change="onPageSizeChange"
        />
      </div>
    </div>

    <!-- 还原虚拟机 -->
    <VMreduction
      :tableReduction="tableReduction"
      :reductionTime="reductionTime"
      @return-ok="returnOK"
      @return-error="returnError"
    ></VMreduction>
    <!-- 删除虚拟机 -->
    <VMdelete
      :tableDelet="tableDelet"
      :deleteTime="deleteTime"
      @return-ok="returnOK"
      @return-error="returnError"
    ></VMdelete>
    
  </div>
</template>
<script>
import { powerCodeQuery } from '@/api/other'; // 权限可用性 查询

import {
  recycleBinTableQuery, // 回收站 查询
} from "@/api/virtualMachine";
import Pagination from "@/components/public/Pagination.vue";
import VMreduction from "./VMreduction.vue"; // 还原虚拟机
import VMdelete from "./VMdelete.vue"; // 删除虚拟机

export default {
  components: {
    Pagination,
    VMreduction,
    VMdelete,
  },
  props: {
    tabSelected: String,
  },
  watch: {
    tabSelected(value) {
      if (value == "回收站") {        
        this.actionQuery()
      }
    },
  },
  mounted() {
    if(this.$store.state.power.computingResourceTab == '回收站') {      
      this.actionQuery()
    }
  },
  data() {
    return {
      spinShow: false,
      // 镜像表格数据
      tableColumn: [],
      tableData: [],
      tableSelec: [],
      // 分页
      tablePageForm: {
        id: 1,
        page: 1, // 当前页
        pagecount: this.$store.state.power.pagecount, // 每页条
        search_str: "", // 收索
        order_type: "asc", // 排序规则
        order_by: "", // 排序列
      },
      // 虚拟机分页总条数
      tableTotal: 0,

      tableReduction: [],
      reductionTime: '', // 还原虚拟机
      tableDelet: [],
      deleteTime: "", // 删除虚拟机

      powerAcitons: {}, // 操作权限数据
    };
  },
  updated() {
    this.tablePageForm.pagecount = this.$store.state.power.pagecount;
  },
  methods: {
    cloumnManage(){
      this.tableColumn=[
        { type: "selection", width: 30, align: "center" },
        {
          type: "expand",
          width: 50,
          render: (h, params) => {
            return h("div", { style: { width: "100%" } }, [
              h(
                "span",
                { style: { width: "45%", display: "inline-block" } },
                "ID：" + params.row.id
              ),
              // h("span",'名称：'+params.row.name)
            ]);
          },
        },
        { title: "名称", key: "name" },
        { title: "IP", key: "ip", align: "center", slot: 'ip' },
        // { title: "名称", key: "name",sortable: 'custom' },
        // { title: "IP", key: "ip",align: "center",sortable: 'custom' },
        { title: "主机", key: "hostname", align: "center" },
        { title: "销毁时间", key: "deadline", align: "center", slot: 'deadline' },
        { title: "VCPU", key: "vcpus", align: "center" },
        { title: "内存", key: "ram", align: "center", slot: 'ram' },
        { title: "硬盘", key: "disk", align: "center", slot: 'disk' },
        ...(this.operationShow()?[{ title: "操作", key: "operation",width:120,slot: "operation" }]:[]),
      ]
    },
    // 表格操作
    dropdownClick(event, row) {
      switch (event) {
        case "hy":
          this.recoveryVMclick([row])
          break;
        case "sc":
          this.deletVMclick([row]);
          break;
      }
    },
   
    // 表格数据
    recoveryTablePost() {
      this.spinShow = true;
      this.tableTotal = 0
      recycleBinTableQuery(this.tablePageForm).then((callback) => {
        this.spinShow = false;
        this.tableTotal = callback.data.total;
        this.tableSelec = new Array();
        this.tableData = callback.data.data
      });
    },

    // 表格选中数据
    tableChange(item) {
      this.tableSelec = item;
    },
    // 当前分页
    onPageChange(item) {
      this.tablePageForm.page = item;
      this.recoveryTablePost();
    },
    // 每页条数
    onPageSizeChange(item) {
      this.$store.state.power.pagecount = item;
      this.tablePageForm.pagecount = this.$store.state.power.pagecount;
      this.tablePageForm.page = 1;
      this.tableTotal = 0;
      this.tablePageForm.search_str = "";
      this.recoveryTablePost();
    },
    // 虚拟机列表 列排序
    sortColumn(column, key, order) {
      if (column.key !== "normal") {
        this.tablePageForm.order_by = column.key;
        this.tablePageForm.order_type = column.order;
        this.tablePageForm.page = 1;
        this.tableTotal = 0;
        this.tablePageForm.search_str = "";
        this.recoveryTablePost();
      }
    },
    // 搜索
    tableVMsearchInput() {
      this.tablePageForm.page = 1;
      this.tableTotal = 0;
      this.recoveryTablePost();
    },
    // 还原虚拟机
    recoveryVMclick(data) {
      if (data== 0) {
        this.$Message.warning({
          background: true,
          closable: true,
          duration: 5,
          content: "未选择表格数据",
        });
      } else {
        this.tableReduction = data
        this.reductionTime = '' + new Date()

      }
    },
    // 删除虚拟机
    deletVMclick(data) {
      if (data == 0) {
        this.$Message.warning({
          background: true,
          closable: true,
          duration: 5,
          content: "未选择表格数据",
        });
      } else {
        this.tableDelet = data
        this.deleteTime = '' + new Date()
      }
    },
    
    // 子组件返回 更新表数据
    returnError(data){
      this.$Message.error({
        background: true,
        closable: true,
        duration: 5,
        content: data,
      });
    },
    returnOK(data) {
      setTimeout(() => {
        this.recoveryTablePost()
      }, 1000);
      this.$Message.success({
        background: true,
        closable: true,
        duration: 5,
        content: data,
      });
    },
    // IP转换
    convertIP(item){
      let ips = item.map(em=>{return em.addr})
      return ips.toString()
    },
     // 字节+单位转换
    byteUnitConversion(type, size) {
      const units = ["GB", "TB", "PB"];
      let unitIndex = 0;
      while (size >= 1024 && unitIndex < units.length - 1) {
        size /= 1024;
        unitIndex++;
      }
      if (type == "byte") {
        return Math.floor(size * 100) / 100;
      } else if (type == "unit") {
        return units[unitIndex];
      } else {
        return Math.floor(size * 100) / 100 + "（" + units[unitIndex] + "）";
      }
    },
    // 操作列
    operationShow(){
      let list = false
      if(this.powerAcitons.huishouzhanhuanyuan || this.powerAcitons.huishouzhanshanchu){
        list = true
      }
      return list
    },
    // 操作权限获取
    actionQuery(item){
      powerCodeQuery({
        module_code:[
          'huishouzhanliebiao',
          'huishouzhanhuanyuan',
          'huishouzhanshanchu',
          'huishouzhantiaojianchaxun',
        ]
      }).then(callback=>{
        this.powerAcitons = callback.data.data
        this.cloumnManage()
        this.powerAcitons.huishouzhanliebiao?this.recoveryTablePost():null
      })
    },
  },
};
</script>
