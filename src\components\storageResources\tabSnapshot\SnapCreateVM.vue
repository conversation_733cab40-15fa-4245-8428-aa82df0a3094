<template>
  <div>
    <!-- <Modal
      v-model="model"
      width="600"
      :styles="{ top: '20px' }"
      :mask-closable="false"
    > -->
    <Modal
      v-model="model"
      width="600"
      :mask-closable="false"
    >
      <template #header><p>创建虚拟机</p></template>
      <Form :model="formItem" ref="formItem" :rules="ruleValidate" :label-width="150">
        <FormItem label="所选快照">
          <Input v-model="tableRow.name" disabled></Input>
        </FormItem>
        <FormItem label="虚拟机名称" prop="name">
          <Input v-model="formItem.name" placeholder="请输入虚拟机名称"></Input>
        </FormItem>
        <FormItem label="计算资源" prop="host">
          <Select v-model="formItem.host" :label-in-value='true'>
            <Option v-for="item in hostData" :value="item.availability_zone" :key="item.id" >{{ item.name }}</Option>
          </Select>
        </FormItem>
        <FormItem label="网络名称" prop="netID">
          <Select :disabled="formItem.presetsIP" v-model="formItem.netID" :label-in-value="true" @on-change="netChange">
            <Option v-for="item in netData" :value="item.id" :key="item.id" >{{ item.name }}</Option>
          </Select>
        </FormItem>
        <FormItem label="系统版本" v-if="architecture">
          <Select v-model="formItem.version" :label-in-value="true" style="width: 300px">
            <Option v-for="item in versionData" :value="item.value" :key="item.value">{{ item.value }}</Option>
          </Select>
        </FormItem>
        <FormItem label="虚拟机数量">
          <div class="slider_area">
            <div style="width:350px">
              <Slider
                v-model="formItem.vmNumber"
                :disabled="formItem.presetsIP"
                :min="1"
                :max="200"
                :tip-format="formNUM"
              ></Slider>
            </div>
            <div style="width:80px">
              <InputNumber :min="1" :max="200" :disabled="formItem.presetsIP" v-model="formItem.vmNumber" :formatter="value => `${value}个`" :parser="value => value.replace('个', '')"></InputNumber>
            </div>
          </div>
        </FormItem>
        <FormItem label="VCPU核数">
          <div class="slider_area">
            <div style="width:350px">
              <Slider
                v-model="formItem.cpu"
                :min="1"
                :max="64"
                :tip-format="formCPU"
              ></Slider>
            </div>
            <div style="width:80px">
              <InputNumber :min="1" :max="64" v-model="formItem.cpu" :formatter="value => `${value}核`" :parser="value => value.replace('核', '')"></InputNumber>
            </div>
          </div>
        </FormItem>
        <FormItem label="内存容量">
          <div class="slider_area">
            <div style="width:350px">
              <Slider
                v-model="formItem.mem"
                :min="1"
                :max="256"
                :tip-format="formMEM"
              ></Slider>
            </div>
            <div style="width:80px">
              <InputNumber :min="1" :max="256" v-model="formItem.mem" :formatter="value => `${value}GB`" :parser="value => value.replace('GB', '')"></InputNumber>
            </div>
          </div>
        </FormItem>
        <FormItem label="预设IP">
          <div style="display: flex;">
            <Checkbox v-model="formItem.presetsIP"></Checkbox>
            <FormItem v-if="formItem.presetsIP" prop="ipPash">
              <div style="display: flex;">
                <Input v-model="formItem.ipPash" placeholder="请输入IP地址" style="width: 300px"></Input>
                <Button class="plus_btn" :disabled="formItem.presetsBtn" style="display: flex; align-items: center; justify-content: center;" icon="ios-add-circle-outline"  @click="addIPpool"></Button>
              </div>
            </FormItem>
          </div>
        </FormItem>
        <div v-if="formItem.presetsIP" style="display: flex;flex-wrap: wrap; width: 100%;height: 80px;overflow: auto;">
          <span style="width:135px;display: flex;justify-content: flex-end;align-items: center;" v-for="(itm,index) in formItem.ipPoolData">{{itm}}<Icon type="md-trash" style="font-size:18px;color:red;padding-left: 5px" @click="deleIPpool" /></span>
        </div>
      </Form>
      <div slot="footer">
        <Button type="text" @click="model = false">取消</Button>
        <Button type="primary" @click="modelOK" :disabled="disabled">确认</Button>
      </div>
    </Modal>
  </div>
</template>
<script>
import {
  snapshotVMnewBuilt, // 快照 新建虚拟机
} from '@/api/storage';
import { physicsTableAllQuery } from '@/api/physics'; // 查询集群
import { networkTableQuery,networkCheckip } from '@/api/network'; // 查询网络
import { vmTemplateNewBuilt } from '@/api/virtualMachine'; // 虚拟机模板
export default {
  props: {
    vmTime: String,
    tableRow: Object,
  },
  watch: {
    vmTime(news){
      this.$refs.formItem.resetFields();
      if(window.gurl.architecture == "ARM") {
        this.formItem.version = "linux";
        this.architecture = false
      }else {
        this.formItem.version = "windows 其它版本";
        this.architecture = true
      }
      this.dropdownData() // 初始化下拉选择
      this.formItem.presetsIP = false; // 初始化预设IP
      this.formItem.presetsBtn = true; // 初始化预设ip添加按钮控制
      this.formItem.ipPoolData = []; // 初始化预设ip池
      
      this.formItem.vmNumber = 1;
      this.formItem.cpu = 2;
      this.formItem.mem = 4;
      this.formItem.disk = parseInt(this.tableRow.size)

      this.model = true
      this.disabled = false
    },
    'formItem.presetsIP':{
      handler (news,old) {
        if(news) {
          this.formItem.vmNumber=this.formItem.ipPoolData.length>0?this.formItem.ipPoolData.length:1
        }else {
          this.formItem.ipPoolData=[]
          this.formItem.vmNumber=1
        }
      }
    }
  },
  data(){
    const propName = (rule, value, callback) => {
      let list = /^[\u4e00-\u9fa5_a-zA-Z0-9@_.-]{2,32}$/;
      if (list.test(value)) {
        callback();
      } else {
        callback(new Error("2-32 个中文、英文、数字、特殊字符(@_.-"));
      }
    };
    const propipPash = (rule, value, callback) => {
      let list = /^(25[0-4]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-4]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-4]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-4]|2[0-4][0-9]|[01]?[1-9][0-9]?|1[0-9]{2}|2[0-4][0-9])$/
      let subnet = this.formItem.netName.split(':')[1]
      if(list.test(value)) {
        if(this.ipInSubnet(value, subnet)) {
          this.formItem.presetsBtn = false
          callback()
        }else {
          this.formItem.presetsBtn = true
          callback(new Error("IP地址与所选网段不匹配"))
        }
      }else {
        this.formItem.presetsBtn = true
        callback(new Error("该ip不可用"))
      }
    };
    return {
      architecture: false, // 架构是否启用
      model:false,
      disabled:false,
      formItem:{
        name: '',
        host: '',
        netID: '',
        netName: '',
        presetsIP: false,
        ipPash: '',
        ipPoolData: '',
        version: 'windows 其它版本',
        vmNumber: 1,
        cpu: 2,
        mem: 4,
        disk: 0,
        presetsBtn: false, // 预设IP添加控制
      },

      hostData: [], // 计算资源
      netData: [], // 网络
      versionData: [
        {value:"windows 其它版本"},
        {value:"windows 2003"},
        {value:"windows 2008"},
        {value:"windows 2012"},
        {value:"windows 2016"},
        {value:"linux"},
      ],  
      ruleValidate:{
        name:[
          { required: true, message: '必填项', trigger: 'change' },
          { validator: propName, trigger: "change" },
        ],
        host:[{ required: true, message: "必选项", trigger: "change" }],
        netID:[{ required: true, message: "必选项", trigger: "change" }],
        ipPash:[
          { required: true, message: " "},
          { validator: propipPash, trigger: "change" },
        ],
      }
    }
  },
  methods: {
    dropdownData () {
      // 获取计算资源下拉数据
      physicsTableAllQuery()
      .then((callback) => {
        this.hostData = callback.data;
        this.formItem.host = callback.data[0].availability_zone
      });
      // 获取网络下拉框数据
      networkTableQuery()
      .then((callback) => {
        let arr = new Array();
        callback.data.forEach(item=>{
          item.cidr.forEach((em,i) => {
            arr.push({
              id: item.id+":"+item.subnets[i],
              name: em,
            })
          })
        })
        this.netData = arr;
        this.formItem.netID = arr[0].id
        this.formItem.netName = arr[0].name
      });
    },
    // 网络选择
    netChange(item) {
      this.formItem.netID = item.value;
      this.formItem.netName = item.label;
    },
    // 虚拟机选择
    formNUM(val) {
      return val + "个";
    },
    // cpu选择
    formCPU(val) {
      return val + "核";
    },
    // 内存选择
    formMEM(val) {
      return val + " GB";
    },
    // 预设IP 加如池  
    addIPpool(){
      networkCheckip({
        now_ipv4:'',
        new_ipv4:this.formItem.ipPash
      }).then((callback)=>{
        if(callback.data.msg == 'ok') {
          this.formItem.ipPoolData.push(this.formItem.ipPash)
          this.formItem.ipPash = ""
          this.formItem.yuseDisabled = true
          this.formItem.vmNumber= this.formItem.ipPoolData.length
        }else {
          this.$Message.error({
            background: true,
            closable: true,
            duration: 10,
            content: callback.data.msg
          });
        }
      })
    },
    // 预设IP 去除池 
    deleIPpool(index){
      this.formItem.ipPoolData.splice(index, 1)
      this.formItem.vmNumber= this.formItem.ipPoolData.length
    },
    // 确认事件
    modelOK(){
      let list = /^[\u4e00-\u9fa5_a-zA-Z0-9@_.-]{2,32}$/;
      if(list.test(this.formItem.name)) {
        this.disabled = true
        vmTemplateNewBuilt({
          name: this.formItem.cpu+'-'+this.formItem.mem+'-'+this.formItem.disk,
          vcpus: this.formItem.cpu,
          ram: this.formItem.mem,
          disk: this.formItem.disk,
          extdisk: 0,
          ...(this.formItem.version=="windows 其它版本"||this.formItem.version=="linux"?"":{metadata_sys_versionthis:this.formItem.version})
        })
        .then((callback) => {
          snapshotVMnewBuilt({
            flavorRef: callback.data.id, // 模板id
            name: this.formItem.name, // 名称
            availability_zone: this.formItem.host, // 计算资源
            networkRef: this.formItem.netID.split(":")[0], // 网络ID
            subnet_id: this.formItem.netID.split(":")[1], // 子网ID
            ipv4: this.formItem.presetsIP?this.formItem.ipPoolData.join(","):"", // 使用预设IP
            snapshotRef: this.tableRow.id, // 快照表ID
            count: this.formItem.vmNumber, // 创建数量
            os_type: this.formItem.version=="linux"?"linux":"windows" // 系统类型默认填充
          })
          .then(res=>{
            if(res.data.msg == "ok"){
              this.$emit("return-ok",'快照创建虚拟机操作完成');
              this.model = false;
            }else {
              this.$emit("return-error","快照创建虚拟机操作失败");
              this.disabled = false;
            }
          })
          .catch((error) => {
            this.$emit("return-error",'快照创建虚拟机操作失败');
            this.disabled = false;
          });

        })
        .catch((error) => {
          this.$emit("return-error",'虚拟机模板创建失败');
          this.disabled = false
        })
      }else {
        this.disabled = false
        this.$Message.error({
          background: true,
          closable: true,
          duration: 5,
          content: "虚拟机名称不符合规定",
        });
      }
      
    },
    // 判断网络属于子网
    ipInSubnet(ip, subnet) {
      let [subnetIp, maskBits] = subnet.split('/');
      maskBits = parseInt(maskBits, 10);
      let ipBinary = ip.split('.').map(octet => parseInt(octet, 10).toString(2).padStart(8, '0')).join('');
      let subnetBinary = subnetIp.split('.').map(octet => parseInt(octet, 10).toString(2).padStart(8, '0')).join('');
      for(let i=0; i<maskBits; i++){
          if(ipBinary[i] != subnetBinary[i]){
              return false;
          }
      }
      return true;
    },
  }
}
</script>