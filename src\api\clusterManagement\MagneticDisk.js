// import request from "@/utils/request";

// 查询存储单元列表
export function getGetall(query) {
  return request({
    url: "/v2/v2/getall",
    method: "get",
    params: query
  });
}
// 查询已有存储单元列表（创建时使用）
export function getAvailDevicesByShellJson(query) {
  return request({
    url: "/v2/v2/getAvailDevicesByShellJson",
    method: "get",
    params: query
  });
}

// 创建存储单元
export function addMagneticDisk(data) {
  return request({
    url: `/v2/v2/createOsdNode`,
    method: "post",
    data: data
  });
}

// 删除存储单元（删除与更换硬盘为用一个接口，preserve=false是删除，true是更换硬盘）
export function delOsd(data, type) {
  return request({
    url:
      type === OPERATE_TYPE.del
        ? `/api/api/osd/${data.osdid}?preserve_id=false&force=true`
        : `/api/api/osd/${data.osdid}?preserve_id=true&force=true`,
    method: "delete"
  });
}

// 编辑存储单元
export function editOsd(osd_id, data) {
  return request({
    url: `/api/api/osd/${osd_id}`,
    method: "post",
    data: data
  });
}

// 停用存储单元
export function markOutOsd(data) {
  return request({
    url: `/api/api/osd/${data.osdid}/mark_out`,
    method: "post",
    data: data
  });
}
// 启用存储单元
export function markInOsd(data) {
  return request({
    url: `/api/api/osd/${data.osdid}/mark_in`,
    method: "post",
    data: data
  });
}
// 下线存储单元
export function markDownOsd(data) {
  return request({
    url: `/api/api/osd/${data.osdid}/mark_down`,
    method: "post",
    data: data
  });
}

//存储类型
export const OSD_TYPE = [
  {
    name: "HDD",
    value: "HDD",
    label: "HDD"
  },
  {
    name: "SSD",
    value: "SSD",
    label: "SSD"
  },
  {
    name: "NVME",
    value: "NVME",
    label: "NVME"
  }
];

//接口操作类型
export const OPERATE_TYPE = {
  del: "删除",
  change: "更换"
};
