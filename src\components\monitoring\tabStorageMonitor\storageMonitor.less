// 存储监控
.storage_monitor_area {
  width: 100%;
  height: calc(100% - 70px);
  overflow: auto;

  // 集群管理
  .cluster_management_area {
    width: 100%;
    height: 38%;
    border-radius: 10px;
    margin-bottom: 1%;

    .cluster_management_template {
      width: 100%;
      height: 84%;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      // 警报
      .cluster_management_alarm {
        width: 20%;
        height: 100%;
        border-radius: 10px;
        background-color: #fff;

        .alarm_module_area {
          height: 88%;
          display: flex;
          justify-content: space-evenly;
          align-items: center;

          img {
            height: 60%;
          }

          .alarm_information_area {
            display: flex;
            align-items: flex-start;
            flex-direction: column;
            font-size: 30px;
            font-weight: 800;
            color: #323232;

            span:first-child {
              font-size: 15px;
              font-weight: 400;
              color: #747980;
              padding-bottom: 20px;
            }

            .storage_alarm_number:hover {
              text-shadow: 2px 2px 3px #CCC;
              cursor: pointer;
            }
          }
        }
      }

      // 存储容量
      .cluster_management_storage_capacity {
        width: 26%;
        height: 100%;
        border-radius: 10px;
        background-color: #fff;

        .storage_capacity_area {
          height: 88%;
          display: flex;
          justify-content: space-evenly;
          align-items: center;

          .demo-Circle-custom {
            p {
              font-weight: 800;
              font-size: 14px;
              color: #7a7a7a;
            }

            p:first-child {
              font-weight: 800;
              font-size: 18px;
              color: #323232;
              padding-bottom: 12px;
            }
          }

          .storage_capacity_information {

            /* display: flex;
            justify-content: space-evenly */
            .capacity_information_common {
              display: flex;
              align-items: center;
              padding: 15px 0;

              span:nth-child(1) {
                display: inline-block;
                width: 10px;
                height: 10px;
                border-radius: 3px;
              }

              span:nth-child(2) {
                display: inline-block;
                width: 45px;
                text-align: right;
              }

              span:nth-child(3) {
                display: inline-block;
                width: 80px;
                text-align: right;
              }
            }
          }
        }
      }

      // 存储健康状态 I/O读写速率
      .cluster_management_storage_status {
        width: 25%;
        height: 100%;
        border-radius: 10px;
        background-color: #fff;

        .io_charts_area {
          width: 100%;
          height: 88%;
          display: flex;
          justify-content: space-evenly;

          .io_charts_reading,
          .io_charts_writing {
            width: 48%;
            height: 100%;
          }
        }
      }
    }
    
  }

  .node_xiangqing_area {
    // 节点表
      .node_radio_area,
      .node_radio_table {
        margin-bottom: 1%;
      }
    
      // OSD状态
      .osd_state_area {
        width: 100%;
        height: 120px;
        display: flex;
        justify-content: space-between;
        margin-bottom: 20px;
    
        .osd_state_template {
          width: 400px;
          height: 120px;
          border-radius: 10px;
          padding: 15px 50px;
          background-color: #fff;
          display: flex;
          flex-direction: column;
          justify-content: space-evenly;
    
          p:nth-child(1) {
            font-size: 16px;
            color: #7a7a7a;
          }
    
          p:nth-child(2) {
            font-size: 26px;
            color: #333;
          }
        }
    
        .osd_div_0 {
          background-image: url("../../../assets/monitorIMG/osdinup.png");
        }
    
        .osd_div_1 {
          background-image: url("../../../assets/monitorIMG/osdindown.png");
        }
    
        .osd_div_2 {
          background-image: url("../../../assets/monitorIMG/osdoutup.png");
        }
    
        .osd_div_3 {
          background-image: url("../../../assets/monitorIMG/osdoutdown.png");
        }
      }
  }
  // 性能统计分析
  .system_performance {
    width: 100%;
  }

  .cluster_node_switch {
    display: inline-block;
    width: 108px;
    height: 30px;
    font-size: 14px;
    margin-left: 10px;
    background-color: #dedede;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: space-evenly;

    span {
      display: inline-block;
      width: 50px;
      height: 22px;
      border-radius: 4px;
      text-align: center;
      cursor: pointer;
    }
  }

  .performance_statistical_analysis {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;

    .universal_chart_module {
      width: 49%;
      height: 330px;
      border-radius: 10px;
      background-color: #fff;
      margin-bottom: 20px;
    }
  }
}

// 通用大标题
.storage_big_title {
  font-size: 20px;
  font-weight: 800;
  height: 10%;
  color: #323232;
  margin-bottom: 1%;
  display: flex;
  align-items: center;
}

// 通用小标题
.storage_module_title {
  padding: 1% 0 0 20px;
  height: 10%;
  font-size: 15px;
  font-weight: 800;
  color: #323232;
}