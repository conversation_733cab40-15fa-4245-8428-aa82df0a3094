<style scoped lang='less'>
  .diskUnitBox {
    position: relative;
    display: flex;
    flex-direction: row;
    justify-content: space-evenly;
    .selectedTable {
      border:1px solid #ccc;
      h5 {
        padding:3px;
        font-size:18px;
      }
    }
    .buttonbox {
      padding: 5px;
      display: flex;
      flex-direction: column;
      justify-content: center;
    }
    .unselectedTable {
      border:1px solid #ccc;
      h5 {
        padding:3px;
        font-size:18px;
      }
    }
  }
</style>
<template>
  <!-- 管理磁盘单元 -->
  <div>
    <Modal v-model="diskUnitModal" :loading="hdfload" width="1200" title="管理磁盘单元" @on-ok="diskUnitOK" @on-cancel="cancel" :mask-closable="false">
      <div class="diskUnitBox">
        <Spin fix v-if="spinShow" size="large" style="color: #ef853a">
          <Icon type="ios-loading" size="50" class="demo-spin-icon-load"></Icon>
          <div style="font-size:16px; padding: 20px">Loading...</div>
        </Spin>
        <div class="selectedTable">
          <h5>存储节点/存储单元</h5>
          <Table :columns="allColumns" :data="allData" height="500" @on-selection-change="allTableChange" ></Table>
        </div>
        <div  class="buttonbox">
          <Button class="plus_btn"  @click="addclick" :disabled="add">添加 <Icon type="ios-arrow-forward" /></Button>
          <Button class="plus_btn"  @click="remclick"  :disabled="rem"><Icon type="ios-arrow-back" /> 删除</Button>
        </div> 
        <div  class="unselectedTable">
          <h5>{{rows.poolName}}: 已选择的存储节点/存储单元</h5>
          <Table :columns="allColumns" :data="selecData" height="500" @on-selection-change="selecTableChange"></Table>
        </div>
      </div>
    </Modal>
  </div>
</template>
<script>
  export default {
  props: {
    manage: Array,
  },
  data() {
    return{
      spinShow:false,
      diskUnitModal:false,
      hdfload:false,
      // 全部管理磁盘单元
      allColumns:[
        { type: 'selection',align: 'center',width: 30 },
        { title: "存储单元id", key: "osdid",width:90 },
        { title: "存储单元类型", key: "type",width:100 },
        { title: "所在存储节点", key: "saveNodeName",width:100 },
        { title: "磁盘路径", key: "path",width:90 },
        { title: "硬盘域", key: "poolName",tooltip:true,width:90 },
      ],
      allData:[],
      allSlectTable:[],
      // 添加、删除操作 
      add:true,
      rem:true,
      // 已选管理磁盘单元
      selecData:[],
      selecSlectTable:[],
      rows:{},
      saveAll:[],
      saveSelce:[],
    }
  },
  watch: {
    manage(value) {
      this.diskUnitModal = true;
      this.rows = value[0]
      this.tableGET()
    },
  },
  methods: {
    // 获取表格数据
    tableGET(){
      this.spinShow = true
      this.$axios.get("/thecss/v2/getYingPanYv").then((callback) => {
        this.spinShow = false
        let arrAll = new Array();
        let arrSelec = new Array();
        let arrsave = new Array()
        callback.data.forEach(item => {
          if(item.poolName == this.rows.poolName && item.osdid) {
            arrAll.push(item)
          }else{
            arrSelec.push(item)
            arrsave.push(item)
          }
        });
        this.allData = arrAll
        this.selecData = arrSelec
        this.saveSelce = arrsave
      })
    },
    
    // 全部管理磁盘单元 表格选中数据
    allTableChange(data){
      this.add = data.length === 0
      this.allSlectTable = data
    },
    // 已选管理磁盘单元 表格选中数据
    selecTableChange(data){
      this.rem = data.length === 0
      this.selecSlectTable = data
    },
    // 添加
    addclick() {
      for(let row of this.allSlectTable) {
        let index  = this.allData.findIndex(item => item.osdid === row.osdid)
        index !== -1?this.allData.splice(index, 1):""
        this.selecData.push(row)
      }
      this.allTableChange([])
    },
    // 删除
    remclick() {
      for (let row of this.selecSlectTable) {
        let index = this.selecData.findIndex(item => item.osdid === row.osdid);
        index !== -1? this.selecData.splice(index, 1):""
        this.allData.push(row);
      }
      this.selecTableChange([])
    },
    // 确定
    diskUnitOK(){
      this.hdfload=true,
      setTimeout(() => {
          this.createYYPmodel = false;
          this.hdfload = false;
      }, 500);
      let form=new Object()
      form.currentRecode= this.rows
      form.currentYvArray= new Array()
      form.finalYvArray= new Array()
      for(let row of this.selecData){
        let index = this.saveSelce.findIndex(item => item.osdid === row.osdid)
        index !== -1?this.saveSelce.splice(index, 1):""
      }
      form.finalYvArray= this.selecData
      form.currentYvArray = this.saveSelce
      this.$axios.post("/thecss/v2/addOsd2RootHost",form).then((callback) => {
        this.$emit("custom-event",callback);
        this.$Message.success({
          background: true,
          closable: true,
          duration: 5,
          content: "管理硬盘域操作完成",
        });
      })
      this.diskUnitModal=false
    },
    cancel(){},
  },
      
  }
</script>