{"name": "twv", "version": "2.3.0", "description": "THE cloud web vue", "author": "", "private": false, "scripts": {"dev": "webpack-dev-server --inline --progress --config build/webpack.dev.conf.js", "start": "npm run dev", "build": "node --max_old_space_size=4096 build/build.js"}, "dependencies": {"@antv/g6": "^4.6.15", "ant-design-vue": "^1.7.8", "axios": "^0.19.0", "core-js": "^3.6.5", "echarts": "^4.9.0", "element-ui": "^2.15.13", "file-saver": "^2.0.5", "iview": "^3.4.2", "jquery": "^3.6.0", "js-cookie": "^3.0.1", "json-server": "^0.17.3", "vcolorpicker": "^1.1.0", "view-design": "^4.7.0", "view-ui-plus": "^1.2.0", "vue": "^2.5.2", "vue-jsonp": "^0.1.8", "vue-router": "^3.0.1", "xlsx": "^0.18.5", "xterm": "^3.14.5"}, "devDependencies": {"autoprefixer": "^7.1.2", "babel-core": "^6.22.1", "babel-helper-vue-jsx-merge-props": "^2.0.3", "babel-loader": "^7.1.1", "babel-plugin-component": "^1.1.1", "babel-plugin-syntax-jsx": "^6.18.0", "babel-plugin-transform-runtime": "^6.22.0", "babel-plugin-transform-vue-jsx": "^3.5.0", "babel-preset-env": "^1.3.2", "babel-preset-stage-2": "^6.22.0", "chalk": "^2.0.1", "copy-webpack-plugin": "^4.0.1", "css-loader": "^0.28.0", "extract-text-webpack-plugin": "^3.0.0", "file-loader": "^1.1.4", "friendly-errors-webpack-plugin": "^1.6.1", "html-webpack-plugin": "^2.30.1", "iview-loader": "^1.2.2", "less": "^3.13.1", "less-loader": "^5.0.0", "mockjs": "^1.1.0", "node-notifier": "^5.1.2", "optimize-css-assets-webpack-plugin": "^3.2.0", "ora": "^1.2.0", "portfinder": "^1.0.13", "postcss-import": "^11.0.0", "postcss-loader": "^2.0.8", "postcss-url": "^7.2.1", "rimraf": "^2.6.0", "semver": "^5.3.0", "shelljs": "^0.7.6", "uglifyjs-webpack-plugin": "^1.1.1", "url-loader": "^0.5.8", "vue-loader": "^13.3.0", "vue-style-loader": "^3.0.1", "vue-template-compiler": "^2.5.2", "vuex": "^3.0.1", "webpack": "^3.8.1", "webpack-bundle-analyzer": "^2.9.0", "webpack-dev-server": "^2.9.1", "webpack-merge": "^4.1.0"}, "engines": {"node": ">= 6.0.0", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"]}