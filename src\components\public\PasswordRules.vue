<template>
  <div>
    <Modal v-model="frame" width="700" :footer-hide="true" :mask-closable="false">
      <template #header>
        <p>密码规则</p>
      </template>
      <div style="position: relative">
          <Spin fix v-if="spinShow" size="large" style="color:#ef853a">
            <Icon type="ios-loading" size=50 class="demo-spin-icon-load"></Icon>
            <div style="font-size:16px;padding:20px">Loading...</div>
          </Spin>
          <Form :model="formItem" :label-width="120">
            <FormItem label="密码长度">
              <InputNumber :disabled="!formItem.status" :max="31" :min="8" v-model="formItem.pwd_length" />
              <Icon class="prompt" type="ios-information-circle-outline" />
              <span class="describe">范围最小值为{{formItem.pwd_length}}-31位，最大值为固定32位。</span>
            </FormItem>
            <FormItem label="安全等级">
              <RadioGroup v-model="formItem.pwd_class" type="button" button-style="solid">
                <Radio label="l" :disabled="!formItem.status">低</Radio>
                <Radio label="m" :disabled="!formItem.status">中</Radio>
                <Radio label="h" :disabled="!formItem.status">高</Radio>
              </RadioGroup>
              <Icon style="font-size:20px" :style="formItem.color" type="ios-information-circle-outline" />
              <span :style="formItem.color">{{formItem.grade_title}}</span>
            </FormItem>
            <FormItem label="有效期">
              <InputNumber :disabled="!formItem.status" :min="7" v-model="formItem.pwd_expired_day" /> 天
              <Icon class="prompt" type="ios-information-circle-outline" />
              <span class="describe">有效期为密码定期修改时间。</span>
            </FormItem>
            <FormItem label="是否启用规则">
              <i-switch size="large" v-model="formItem.status">
                <template #open>
                  <span>启用</span>
                </template>
                <template #close>
                  <span>禁用</span>
                </template>
              </i-switch>
              <Icon class="prompt" type="ios-information-circle-outline" />
              <span class="describe">规则禁用后将使用默认规则，规则：8-32低等级。</span>
            </FormItem>
          </Form>
          <div style="display: flex;justify-content: space-around">
            <!-- <Button @click="initialization">初始化</Button> -->
            <Button @click="frame=false">取消</Button>
            <Button type="info" class="plus_btn" @click="rulesPWSmodify " :disabled="loading">确定</Button>
          </div>
      </div>
    </Modal>
  </div>
</template>
<script>
import {queryCodeRule,modifyCodeRule} from '@/api/login';
export default {
  props: {
    datas:Number,
  },
  watch: {
    datas(news){
      this.frame = true
      this.pawRulesQuery()
    },
    'formItem.pwd_class':{
      handler (news,old) {
        if(news=="l") {
          this.formItem.color = "color:#ff7474"
          this.formItem.grade_title = "低级密码英文、数字、部分特殊符号，可单独或混搭使用。"
        }else if(news=="m") {
          this.formItem.color = "color:#ffb91b"
          this.formItem.grade_title = "中级密码必须包含英文及数字，还可以使用部分特殊符号。"
        }else if(news=="h") {
          this.formItem.color = "color:#87bf87"
          this.formItem.grade_title = "高级密码必须包含英文数字及部分特殊符号。"
        }
      },
      deep:true,
      immediate: true
    },
  },
  data(){
    return {
      frame:false,
      spinShow:false,
      loading:false,
      formItem:{
        id:null,
        pwd_length:8,
        pwd_class:"m",
        pwd_expired_day:30,
        status:true,
        color:"color:#ffb91b",
        grade_title:"中级密码必须包含英文及数字，还可以使用一部分特殊符号。",
      },
    }
  },
  methods: {
    // 查询密码规则
    pawRulesQuery(){
      this.spinShow = true
      queryCodeRule().then(callback=>{
        this.spinShow = false
        this.formItem.id = callback.data.id
        this.formItem.pwd_length = callback.data.pwd_length
        this.formItem.pwd_class = callback.data.pwd_class
        this.formItem.pwd_expired_day = callback.data.pwd_expired_day
        this.formItem.status = callback.data.status=="on"?true:false
      }).catch((error) => {
        this.spinShow = false
      });
    },
    // 初始密码规则
    initialization(){
      this.$Modal.confirm({
        title: '初始化密码规则',
        content: '<p>初始化设置后将恢复初始默认值，是否确认进行初始化密码规则操作?</p>',
        onOk: () => {
          modifyCodeRule({
            id:this.formItem.id,
            pwd_length:this.formItem.pwd_length,
            pwd_class:this.formItem.pwd_class,
            pwd_expired_day:this.formItem.pwd_expired_day,
            status:this.formItem.status?"on":"off",
          }).then(callback =>{
            if(callback.data.msg == "ok") {
              this.frame = false
              this.$Message.success({
                background: true,
                closable: true,
                duration: 5,
                content: '设置密码规则操作已完成'
              });
            }else {
              this.$Message.error({
                background: true,
                closable: true,
                duration: 5,
                content: '设置密码规则操作失败'
              });
            }
          })
        },
        onCancel: () => {}
      });
    },
    // 修改密码规则
    rulesPWSmodify(){
      this.loading = true;
      setTimeout(() => {
        this.loading = false;
      }, 1000);
      this.$Modal.confirm({
        title: '设置密码规则',
        content: '<p>是否进行设置密码规则操作?</p>',
        onOk: () => {
          modifyCodeRule({
            id:this.formItem.id,
            pwd_length:this.formItem.pwd_length,
            pwd_class:this.formItem.pwd_class,
            pwd_expired_day:this.formItem.pwd_expired_day,
            status:this.formItem.status?"on":"off",
          }).then(callback =>{
            if(callback.data.msg == "ok") {
              this.frame = false
              this.$Message.success({
                background: true,
                closable: true,
                duration: 5,
                content: '设置密码规则操作已完成'
              });
              this.$store.state.power.pwdRules= ""+new Date()
            }else {
              this.$Message.error({
                background: true,
                closable: true,
                duration: 5,
                content: '设置密码规则操作失败'
              });
            }
          })
        },
        onCancel: () => {}
      });
    },
  },
}
</script>
<style>
.prompt {
  font-size:20px;
  color:#ccc
}
.describe {
  color:#ccc
}
</style>