.vm_monitor_area {
  width: 100%;
  height: calc(100% - 70px);
  overflow: auto;

  // 虚拟机总量
  .vm_big_title {
    font-size: 20px;
    color: #323232;
    margin-bottom: 10px;
    display: flex;
    align-items: center;

    >span {
      color: #fe6902;
    }
  }

  // 虚机状态
  .node_vm_area {
    width: 100%;
    height: 33%;
    margin-bottom: 1.5%;
    display: flex;
    justify-content: space-between;

    .vm_status_area {
      width: 38%;
      height: 100%;
      padding: 0.5%;
      border-radius: 10px;
      background-color: #fff;

      // 虚拟机标题
      .vm_status_title {
        font-size: 15px;
        color: #121529;
        display: flex;
        justify-content: space-between;

        span:nth-child(1) {
          font-weight: 800;
        }

        .title_vm_count {
          width: 20px;
          color: #fe6902;
          display: inline-block;
          text-align: right;
        }
      }

      // 虚拟状态
      .node_vm_status {
        width: 100%;
        height: calc(100% - 25px);
        display: flex;
        align-items: center;
        justify-content: space-between;

        >img {
          width: 28%;
        }

        .vm_status_number {
          height: 100%;
          width: 71%;
          padding: 3% 0;
          display: flex;
          flex-wrap: wrap;
          align-content: space-around;
          justify-content: space-evenly;
          > .ivu-tooltip {
            width: 46%;
            height: 30%;
            > .ivu-tooltip-rel {
              width: 100%;
              height: 100%;
              .vm_status_public {
                width: 100%;
                height: 100%;
                border-radius: 20px;
                font-size: 14px;
                color: #333;
                display: flex;
                align-items: center;
                justify-content: flex-end;

                .vm_public_number {
                  display: inline-block;
                  width: 20px;
                  font-weight: 800;
                  text-align: right;
                  margin-right: 20px;
                }
              }

              .vm_open {
                background-image: url("../../../assets/monitorIMG/vmOpen.png");
                background-size: 100% 100%;
              }

              .vm_fault {
                background-image: url("../../../assets/monitorIMG/vmFault.png");
                background-size: 100% 100%;
              }

              .vm_close {
                background-image: url("../../../assets/monitorIMG/vmClose.png");
                background-size: 100% 100%;
              }

              .vm_warn {
                background-image: url("../../../assets/monitorIMG/vmWarn.png");
                background-size: 100% 100%;
              }
            }
          }
        }

      }
    }

    // 虚拟机图表区
    .vm_chart_area {
      width: 60%;
      height: 100%;
      border-radius: 10px;
      background-color: #fff;
    }
  }
}