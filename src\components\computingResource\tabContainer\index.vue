
<style>
@keyframes move {
  0% {
    background-position: -28px 0;
  }
  100% {
    background-position: 0 0;
  }
}
.table_schedule {
  width: 100%;
  height: 15px;
  border-radius: 5px;
  border: 1px solid red;
  background-image: repeating-linear-gradient(
    -45deg,
    #f67f45,
    #f67f45 11px,
    #eee 10px,
    #eee 20px
  );
  background-size: 2000px 28px;
  animation: move 1s linear infinite;
}
</style>
<template>
  <div class="container_area">
    <!-- 局部加载装置 -->
    <Spin fix v-if="spinShow" size="large" style="color: #ef853a">
      <Icon type="ios-loading" size="50" class="demo-spin-icon-load"></Icon>
      <div style="font-size: 16px; padding: 20px">Loading...</div>
    </Spin>
    <!-- 侧边虚拟机分组 -->
    <div class="table-button-area">
      <div>
        <Button class="plus_btn" @click="newClick"
          ><span class="icon iconfont icon-plus-circle"></span> 新建容器</Button
        >
        <Button class="close_btn" @click="deletClick('删除/',tableSelec)"
          ><span class="icon iconfont icon-close-circle"></span>
          删除容器</Button
        >
        <Button class="close_btn" @click="tableQuery">刷新</Button>
      </div>
      <div>
        <Input
          v-model="tablePageForm.search_str"
          search
          enter-button
          placeholder="请输入名称"
          style="width: 300px"
          @on-search="tableRQsearchInput"
        />
      </div>
    </div>
    <Table
      :columns="columns"
      :data="data"
      @on-sort-change="sortChange"
      @on-selection-change="slectChange"
    >
      <!-- 名称 -->
      <template v-slot:name="{ row }">
        <Tooltip :content="row.name" style="width: 100%">
          <a class="text_overflow" @click="nameClick(row)">{{ row.name }}</a>
        </Tooltip>
      </template>
      <!-- 内存 -->
      <template v-slot:memory="{ row }">
        <span>{{ (row.memory / 1024).toFixed(1) }} GB</span>
      </template>
      <!-- 硬盘 -->
      <template v-slot:disk="{ row }">
        <span>{{ row.disk }} GB</span>
      </template>
      <!-- IP地址 -->
      <template v-slot:ip="{ row }">
        <span>{{ row.ip.toString() }} </span>
      </template>
      <!-- 状态 -->
      <template v-slot:status="{ row }">
        <span>{{ statusConvert(row) }}</span>
      </template>
      <!-- 任务 -->
      <template v-slot:task_state="{ row }">
        <div v-if="row.task_state!=='None'" class="table_schedule">
          {{ convertTask(row.task_state) }}
        </div>
        <span v-else>无</span>
      </template>
      <!-- 操作 -->
      <template v-slot:operation="{ row }">
        <Dropdown
          @on-click="dropdownClick($event, row)"
        >
          <Button>配置 ▼</Button>
          <template #list>
            <DropdownMenu>
              <DropdownItem name="bj" v-show="row.status!=='Error'">编辑</DropdownItem>
              <!-- <DropdownItem name="aqz" v-show="row.status!=='Error'">管理安全组</DropdownItem> -->
              <DropdownItem name="qd" v-show="row.status=='Stopped'||row.status=='Error'">启动</DropdownItem>
              <DropdownItem name="tz" v-show="row.status=='Running'">停止</DropdownItem>
              <DropdownItem name="cq" v-show="row.status!=='Paused'">重启</DropdownItem>
              <DropdownItem name="cj" v-show="row.status!=='Paused'">重建</DropdownItem>
              <DropdownItem name="zt" v-show="row.status=='Running'">暂停</DropdownItem>
              <DropdownItem name="hf" v-show="row.status=='Paused'">恢复</DropdownItem>
              <DropdownItem name="zxml" v-show="row.status=='Running'">执行命令</DropdownItem>
              <DropdownItem name="swxh" v-show="row.status=='Running'">发送死亡信号</DropdownItem>
              <DropdownItem name="sc" v-show="row.status=='Stopped'" style="color: red">删除</DropdownItem>
              <DropdownItem name="tzsc" v-show="row.status!=='Paused'" style="color: red">停止并删除</DropdownItem>
              <DropdownItem name="qzsc" v-show="row.status!=='Paused'" style="color: red">强制删除</DropdownItem>
            </DropdownMenu>
          </template>
        </Dropdown>
      </template>
    </Table>
    <!-- 虚拟机表格 分页  -->
    <div class="pages" v-if="this.data.length > 0">
      <!-- <Page
        :total="tableTotal"
        show-total
        show-sizer
        :page-size="tablePageForm.pagecount"
        placement="top"
        :page-size-opts="[10, 20, 30]"
        @on-change="onPageChange"
        @on-page-size-change="onPageSizeChange"
      /> -->
      <Pagination
        :total="tableTotal"
        :page-size="tablePageForm.pagecount"
        @page-change="onPageChange"
        @page-size-change="onPageSizeChange"
      />
    </div>
    <TableNew :newTime="newTime" @return-ok="returnOK"></TableNew>
    <TableEdit :editTime="editTime" :row="row" @return-ok="returnOK"></TableEdit>
    <TableGroup :groupTime="groupTime" :row="row" @return-ok="returnOK"></TableGroup>
    <TableMany :manyTime="manyTime" :tableArr="tableArr" @return-ok="returnOK"></TableMany>
    <TableRestart :restartTime="restartTime" :row="row" @return-ok="returnOK"></TableRestart>
    <TableRebuild :rebuildTime="rebuildTime" :row="row" @return-ok="returnOK"></TableRebuild>
    <TableCommand :commandTime="commandTime" :row="row" @return-ok="returnOK"></TableCommand>
    <TableSignal :signalTime="signalTime" :row="row" @return-ok="returnOK"></TableSignal>
    <TableDeleted
      :deletionTime="deletionTime"
      :tableArr="tableArr"
      @return-ok="returnOK"
    ></TableDeleted>
  </div>
</template>
<script>
import {
  containerQuery, // 容器表 查询
} from '@/api/container'; 
import Pagination from "@/components/public/Pagination.vue";
// import TableNew from "./TableNew.vue";
import TableNew from "./tableNew/index.vue";
import TableEdit from "./TableEdit.vue";
import TableGroup from "./TableGroup.vue";
import TableMany from "./TableMany.vue";
import TableRestart from "./TableRestart.vue";
import TableRebuild from "./TableRebuild.vue";
import TableCommand from "./TableCommand.vue";
import TableSignal from "./TableSignal.vue";
import TableDeleted from "./TableDeleted.vue";

export default {
  components: {
    Pagination,
    TableNew,
    TableEdit,
    TableGroup,
    TableMany,
    TableRestart,
    TableRebuild,
    TableCommand,
    TableSignal,
    TableDeleted,
  },

  props: {
    tabSelected: String,
  },
  data() {
    return {
      spinShow: false,
      columns: [],
      data: [],
      tableSelec: [],
      selectName: [],
      selectID: [],
      tableTotal: 0,
      tablePageForm: {
        page: 1, // 当前页
        pagecount: this.$store.state.power.pagecount, // 每页条
        search_str: "", // 收索
        order_type: "asc", // 排序规则
        order_by: "", // 排序列
      },
      row: {},
      newTime: "", // 新建容器
      editTime: "", // 编辑容器
      groupTime: "", // 安全组
      manyTime: "", // 多操作
      restartTime: "", // 重启
      rebuildTime: "", // 重建
      commandTime: "", // 执行命令
      signalTime: "", // 死亡信号
      
      deletionTime: "", // 删除容器
      tableArr: [],
      detailsTime: "", // 容器详情
      containerDetailData: "", // 容器日志数据
      // 计时器
      tableTask: null,
      taskList: false,
    };
  },
  watch: {
    tabSelected(value) {
      if (value == "容器") {
        this.tableColumn(); // 表列
        this.tableQuery(); // 表数据
      } else {
        this.tableColumn(); // 表列
        clearInterval(this.tableTask);
      }
    },
    taskList(news) {
      if (news == true) {
        this.tableTask = setInterval(() => {
          containerQuery(this.tablePageForm)
            .then((callback) => {
              let tasknuber = 0;
              this.tableTotal = callback.data.total;
              this.data = callback.data.data;
              callback.data.data.forEach((item) => {
                if (item.task_state !== "None") {
                  tasknuber++;
                }
              });
              if (tasknuber == 0) {
                clearInterval(this.tableTask);
                this.taskList = false;
              }
            });
        }, 5000);
      }
    },
  },
  methods: {
    // 获取容器表格数据
    tableQuery() {
      this.spinShow = true;
      if(!true){
        setTimeout(() => {
          this.spinShow = false;
          this.tableSelec = new Array();
          // this.data = [{id:'aaa',name:'aaaa'},{id:'bb',name:'bbbb'}];
          this.tableTotal = this.data.length;
        }, 1000);

      }else {
        containerQuery(this.tablePageForm)
        .then((callback) => {
          this.spinShow = false;
          this.tableSelec = new Array();
          this.tableTotal = callback.data.total;
          this.data = callback.data.data;
        })
        .catch((error) => {
          this.spinShow = false;
        });
      }
    },
    // 表格选中数据
    slectChange(item) {
      this.tableSelec = item;
      let Name = new Array();
      let ID = new Array();
      item.forEach((it) => {
        Name.push(it.name);
        ID.push(it.uuid);
      });
      this.selectName = Name;
      this.selectID = ID;
    },
    // 当前分页
    onPageChange(item) {
      this.tablePageForm.page = item;
      this.tableQuery();
    },
    // 每页条数
    onPageSizeChange(item) {
      this.$store.state.power.pagecount = item;
      this.tablePageForm.pagecount = this.$store.state.power.pagecount;
      this.tablePageForm.page = 1;
      this.tableTotal = 0;
      this.tablePageForm.search_str = "";
      this.tableQuery();
    },
    // 列表 列排序
    sortChange(column, key, order) {
      if (column.key !== "normal") {
        this.tablePageForm.order_by = column.key;
        this.tablePageForm.order_type = column.order;
        this.tablePageForm.page = 1;
        this.tableTotal = 0;
        this.tablePageForm.search_str = "";
        this.tableQuery();
      }
    },
    // 列表 搜索
    tableRQsearchInput() {
      this.tablePageForm.page = 1;
      this.tableTotal = 0;
      this.tableQuery();
    },
    // 新建容器
    newClick() {
      this.newTime = "" + new Date();
    },
    // 新建返回
    returnOK(item) {
      if(item.type == 'ok') {
        this.packageOK(item.msg)
      }else{
        this.packageError(item.msg)
      }
    },
    // 详情
    nameClick(row) {
      this.row = row;
      this.detailsTime = "" + new Date();
      this.$axios
        .post(
          "/theapi/v1/containers/logs",
          { id: row.id },
          { headers: { "Content-type": "application/json" } }
        )
        .then((callback) => {
          if (callback.data.msg == "ok") {
            this.containerDetailData = JSON.parse(callback.data.logs);
          }
        });
    },

    // 删除容器弹框
    deletClick(tepy,data) {
      if (data == 0) {
        this.packageWarning("未选择表格数据");
      } else {
        this.tableArr = data;
        this.deletionTime = tepy + new Date();
      }
    },
    
    // 状态
    statusConvert(row) {
      let list = "未知";
      switch (row.status) {
        case "Creating":
          list = "创建中";
          break;
        case "Created":
          list = "已创建";
          break;
        case "Running":
          list = "运行中";
          break;
        case "Stopped":
          list = "停止";
          break;
        case "Restarting":
          list = "重启中";
          break;
        case "Paused":
          list = "暂停";
          break;
        case "Error":
          list = "错误";
          break;
      }
      return list 
    },
    // 任务转换
    convertTask(item) {
      if (item !== 'None') {
        this.taskList = true;
      }
    },
    // 操作
    dropdownClick(event, row) {
      this.row = row;
      switch (event) {
        case "bj":
          this.editTime = "" + new Date();
          break;
        case "aqz":
          this.groupTime = "" + new Date();
          break;
        case "qd":
          this.manyTime = "启动/" + new Date();
          this.tableArr = [row]
          break;
        case "tz":
          this.manyTime = "停止/" + new Date();
          this.tableArr = [row]
          break;
        case "cq":
          this.restartTime = "" + new Date();
          break;
        case "cj":
          this.rebuildTime = "" + new Date();
          break;
        case "zt":
          this.manyTime = "暂停/" + new Date();
          this.tableArr = [row]
          break;
        case "hf":
          this.manyTime = "恢复/" + new Date();
          this.tableArr = [row]
          break;
        case "zxml":
          this.commandTime = "" + new Date();
          this.tableArr = [row]
          break;
        case "swxh":
          this.signalTime = "" + new Date();
          this.tableArr = [row]
          break;
        case "sc":
          this.deletClick('删除/',[row]);
          break;
        case "tzsc":
          this.deletClick('停止并删除/',[row]);
          break;
        case "qzsc":
          this.deletClick('强制删除/',[row]);
          break;
      }
    },
    // 表格列获取
    tableColumn() {
      this.columns = [
        { type: "selection", width: 30, align: "center" },
        {
          type: "expand",
          width: 50,
          render: (h, params) => {
            return h("div", { style: { width: "100%" } }, [
              h(
                "span",
                { style: { width: "45%", display: "inline-block" } },
                "ID：" + params.row.uuid
              ),
              // h("span",'名称：'+params.row.name)
            ]);
          },
        },
        // { title: "容器名称", key: "name", sortable: "custom", slot: "name" },
        { title: "容器名称", key: "name", sortable: "custom",tooltip:true, },
        { title: "镜像", key: "image", sortable: "custom",tooltip:true, align: "center" },
        { title: "CPU", key: "cpu", align: "center" },
        { title: "内存", key: "memory", align: "center", slot: "memory" },
        { title: "硬盘", key: "disk", align: "center", slot: "disk" },
        { title: "IP地址", key: "ip", align: "center", slot: "ip"},
        { title: "状态", key: "status", align: "center", slot: "status" },
        { title: "任务状态", key: "task_state", align: "center", slot: "task_state" },
        { title: "操作", key: "operation", width: 120, slot: "operation" },
      ];
    },
    // 封装告警提示
    packageWarning(data) {
      this.$Message.warning({
        background: true,
        closable: true,
        duration: 5,
        content: data,
      });
    },
    packageOK(data) {
      this.tableQuery();
      this.$Message.success({
        background: true,
        closable: true,
        duration: 5,
        content: data,
      });
    },
    packageError(data) {
      this.$Message.error({
        background: true,
        closable: true,
        duration: 5,
        content: data,
      });
    },
  },
  beforeDestroy() {
    clearInterval(this.tableTask);
  },
};
</script>
<style lang="less">
.container_area {
  height: 100%;
  border-radius: 10px;
  background-color: #fff;
  padding: 15px 5px;
  position: relative;
}
</style>
