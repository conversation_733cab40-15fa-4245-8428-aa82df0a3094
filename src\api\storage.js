// 存储管理
import axios from "axios";
import * as basic_proxy from "@/api/config";

// 类型管理 查询
export async function typeQuery() {
  return await axios.get(basic_proxy.theapi + "/v1/volumes/types/listall");
}
// 类型管理 新建
export async function typeNewBuilt(params) {
  return await axios.post(
    basic_proxy.theapi + "/v1/volumes/types/create",
    // 我也忘记为什么用V2了，但是后台说重来没有过v2。
    // basic_proxy.theapi + "/v2/volumes/types/create",
    params
  );
}
// 类型管理 删除
export async function typeDelete(params) {
  return await axios.delete(
    basic_proxy.theapi + "/v1/volumes/types/delete",
    params
  );
}

// 直通设备 查询
export async function directDeviceQuery() {
  return await axios.get(basic_proxy.theapi + "/v1/volumes/passthrough/all");
}
// 直通管理 查询
export async function throughQuery() {
  return await axios.get(
    basic_proxy.theapi + "/v1/volumes/passthrough/connect"
  );
}
// 直通管理 连接直通
export async function connectStraightThrough(params) {
  return await axios.post(
    basic_proxy.theapi + "/v1/volumes/passthrough/createconnect",
    params
  );
}
// 直通管理 连接直通
export async function throughDelete(params) {
  return await axios.delete(
    basic_proxy.theapi + "/v1/volumes/passthrough/deleteconnect",
    params
  );
}

// 云硬盘 查询
export async function cloudDiskQuery(params) {
  // return await axios.post(basic_proxy.theapi + "/v1/volumes", params);
  // return await axios.post(basic_proxy.theapi + "/v2/volumes", params); // 查询数据变快
  return await axios.post(basic_proxy.theapi + "/v3/volumes", params); // MJJ
}
// 云硬盘 新建
export async function cloudDiskNewBuilt(params) {
  return await axios.post(basic_proxy.theapi + "/v2/volumes/create", params);
}
// 云硬盘 修改
export async function cloudDiskModify(params) {
  return await axios.put(basic_proxy.theapi + "/v1/volumes/edit", params);
}
// 云硬盘 删除
export async function cloudDiskDelete(params) {
  return await axios.delete(basic_proxy.theapi + "/v1/volumes/delete", params);
}
// 云硬盘 扩容
export async function cloudDiskExpansion(params) {
  return await axios.post(basic_proxy.theapi + "/v1/volumes/action", params);
}
// 云硬盘 快照
export async function cloudDiskSnapshot(params) {
  return await axios.post(
    basic_proxy.theapi + "/v1/volumes/snapshot/create",
    params
  );
}
// 云硬盘 启动
export async function cloudDiskFiring(params) {
  return await axios.post(basic_proxy.theapi + "/v1/volumes/bootable", params);
}
// 云硬盘 连接
export async function cloudDiskConnect(params) {
  return await axios.post(basic_proxy.theapi + "/v1/volumes/attach", params);
}
// 云硬盘 分离
export async function cloudDiskSeparate(params) {
  return await axios.post(basic_proxy.theapi + "/v1/instances/detach", params);
}
// 云硬盘 创建虚拟机
export async function cloudDiskVMnewBuilt(params) {
  return await axios.post(
    basic_proxy.theapi + "/v3/instance/fromvolumecreate",
    params
  );
}
// 云硬盘 管理连接
export async function cloudDiskManageConnect() {
  return await axios.get(basic_proxy.theapi + "/v1/volumes/detail");
}
// 管理连接云硬盘
export async function manageConnectCloudDisk() {
  return await axios.get(basic_proxy.theapi + "/v1/instances/detail");
}

// 快照 查询
export async function snapshotQuery(params) {
  return await axios.post(
    basic_proxy.theapi + "/v1/volumes/snapshot/list",
    params
  );
}
// 快照 修改
export async function snapshotModify(params) {
  return await axios.post(
    basic_proxy.theapi + "/v1/volumes/snapshot/edit",
    params
  );
}
// 快照 删除
export async function snapshotDelete(params) {
  return await axios.delete(
    basic_proxy.theapi + "/v1/volumes/snapshot/delete",
    params
  );
}
// 快照 创建虚拟机
export async function snapshotVMnewBuilt(params) {
  return await axios.post(
    basic_proxy.theapi + "/v3/instance/createfromsnapshot",
    params
  );
}
// 快照 创建云硬盘
export async function snapshotDriveCreate(params) {
  return await axios.post(
    basic_proxy.theapi + "/v1/volumes/snapshot/to/create",
    params
  );
}

// 云硬盘get 查询
export async function cloudDiskGETQuery() {
  return await axios.get(basic_proxy.theapi + "/v1/volumes");
}
// 云硬盘类型 查询
export async function cloudDiskTypeQuery() {
  return await axios.get(basic_proxy.theapi + "/v1/volumes/type");
}
// 云硬盘类型 新建
export async function cloudDiskTypeNew(params) {
  return await axios.post(basic_proxy.theapi + "/v1/volumes/snapshot", params);
}