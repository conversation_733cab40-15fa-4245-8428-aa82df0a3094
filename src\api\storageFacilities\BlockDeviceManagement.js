// import request from '@/utils/request'

// 查询卷池table列表
export function getBlockImage(query) {
  return request({
    url: '/api/api/block/image',
    method: 'get',
    params: query
  })
}

// 查询空间命名管理列表
export function getBlockPool(query,data) {
  return request({
    url: `/api/api/block/pool/${data}/namespace/`,
    method: 'get',
    params: query
  })
}

// 查询块设备管理的命名空间
export function getnmBypoolNameByThread(query) {
  return request({
    url: '/v2/v2/getnmBypoolNameByThread',
    method: 'get',
    params: query
  })
}

// 查询回收站列表
export function getBlockTrash(query) {
  return request({
    url: '/api/api/block/image/trash',
    method: 'get',
    params: query
  })
}

// 查询卷池列表
export function getPoolList(query) {
  return request({
    url: '/api/api/pool?attrs=pool_name,type,application_metadata',
    method: 'get',
    params: query
  })
}

// 根据卷池名字查询空间命名
export function getPoolNameSpace(query) {
  return request({
    url: `/api/api/block/pool/${query}/namespace/`,
    method: 'get',
    params: ''
  })
}

// 分片与条带大小 ( 需要换算成bytes)
export const STRIPE_UNIT_SIZE = [
  {
    name: '4KiB',
    value: 4096,
    label: '4KiB',
    disabled: false,
  },
  {
    name: '8KiB',
    value: 8192,
    label: '8KiB',
    disabled: false,
  },
  {
    name: '16KiB',
    value: 16384,
    label: '16KiB',
    disabled: false,
  },
  {
    name: '32KiB',
    value: 32768,
    label: '32KiB',
    disabled: false,
  },
  {
    name: '64KiB',
    value: 65536,
    label: '64KiB',
    disabled: false,
  },
  {
    name: '128KiB',
    value: 131072,
    label: '128KiB',
    disabled: false,
  },
  {
    name: '512KiB',
    value: 524288,
    label: '512KiB',
    disabled: false,
  },
  {
    name: '1MiB',
    value: 1048576,
    label: '1MiB',
    disabled: false,
  },
  {
    name: '2MiB',
    value: 2097152,
    label: '2MiB',
    disabled: false,
  },
  {
    name: '4MiB',
    value: 4194304,
    label: '4MiB',
    disabled: false,
  },
  {
    name: '8MiB',
    value: 8388608,
    label: '8MiB',
    disabled: false,
  },
  {
    name: '16MiB',
    value: 16777216,
    label: '16MiB',
    disabled: false,
  },
  {
    name: '32MiB',
    value: 33554432,
    label: '32MiB',
    disabled: false,
  },
]

// 创建块设备保存
export function addBlockImage(data) {
  return request({
    url: '/api/api/block/image',
    method: 'post',
    data: data
  })
}

//修改块设备保存
export function updateBlockImage(data, poolObj) {
  return request({
    url: poolObj.namespace ? `/api/api/block/image/${poolObj.pool_name}%2F${poolObj.namespace}%2F${data.name}` : `/api/api/block/image/${poolObj.pool_name}%2F${data.name}`,
    method: 'put',
    data: data
  })
}

//删除块设备
export function deleteImage(data) {
  return request({
    url: data.namespace ? `/api/api/block/image/${data.pool_name}%2F${data.namespace}%2F${data.name}/move_trash` : `/api/api/block/image/${data.pool_name}%2F${data.name}/move_trash` ,
    method: 'post',
    data: {
      delay: 2592000
    }
  })
}

//新建命名空间
export function addNameSpace(data, pool_name) {
  return request({
    url: `/api/api/block/pool/${pool_name}/namespace`,
    method: 'post',
    data: data
  })
}

//删除块设备
export function deleteNameSpace(data) {
  return request({
    url: data.namespace ? `/api/api/block/pool/${data.pool_name}/namespace/${data.namespace}` : `/api/api/block/pool/${data.pool_name}/namespace` ,
    method: 'delete',
    data:data
  })
}

//回收站删除
export function deleteTrash(data) {
  return request({
    url: data.namespace ? `/api/api/block/image/trash/${data.pool_name}%2F${data.namespace}%2F${data.id}/?force=true` : `/api/api/block/image/trash/${data.pool_name}%2F${data.id}/?force=true` ,
    method: 'delete',
    data:data
  })
}

//恢复回收站的块设备
export function reTrash(data) {
  return request({
    url: `/api/api/block/image/trash/${data.pool_name}%2F${data.namespace}%2F${data.id}/restore`,
    method: 'post',
    data: {
      new_image_name: `${data.name}_restored`
    }
  })
}

//清空回收站
export function emptyTrash(data) {
  return request({
    url: `/api/api/block/image/trash/purge/?pool_name=`,
    method: 'post',
    data: data
  })
}

