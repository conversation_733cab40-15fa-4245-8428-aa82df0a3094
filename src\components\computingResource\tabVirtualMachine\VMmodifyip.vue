<template>
  <div>
    <Modal
      v-model="model"
      width="600"
      :mask-closable="false"
    >
      <template #header><p><span style="color:green">{{vmRow.name}}</span>虚拟机修改IP</p></template>
      <Form :model="formItem" ref="formItem" :rules="ruleValidate"  :label-width="150">
        <FormItem label="当前IP">
          <Select v-model="formItem.oldID" :label-in-value="true" @on-change="nowIPChange">
            <Option
              v-for="item in currentIP"
              :value="item.mac"
              :key="item.mac"
              >{{ item.addr }}</Option
            >
          </Select>
        </FormItem>
        <FormItem label="选择网络" prop="networkId">
          <Select v-model="formItem.networkId" :label-in-value="true" @on-change="slectChange">
             <Option
              v-for="item in netOption"
              :value="item.id"
              :key="item.id"
              >{{ item.name }}</Option
            >
          </Select>
        </FormItem>
        <FormItem label="新IP地址" prop="newIP">
          <Input v-model="formItem.newIP"></Input>
        </FormItem>
      </Form>
      <div slot="footer">
        <Button type="text" @click="model = false">取消</Button>
        <Button type="primary" @click="modelOK" :disabled="disabled">确认</Button>
      </div>
    </Modal>
  </div>
</template>
<script>
import {networkTableQuery,networkCheckip} from '@/api/network';
import {vmGroupTableReplaceIP} from '@/api/virtualMachine';
export default {
  props: {
    vmRow: Object,
    modifyipTime: String,
  },
  watch: {
    modifyipTime(news){
      let arr = new Array()
      this.vmRow.addresses.forEach(em=>{
        arr.push({
          mac:em['OS-EXT-IPS-MAC:mac_addr'],
          addr:em.addr
        })
      })
      this.currentIP = arr
      this.formItem.oldID = arr[0].mac
      this.formItem.oldIP = arr[0].addr
      this.networkData()
      this.formItem.vmid = this.vmRow.id
      this.formItem.vmName = this.vmRow.name
      this.model = true
      this.disabled = false
      this.$refs['formItem'].resetFields()
    }
    
  },
  data(){
    const propipv4 = (rule, value, callback) => {
      let list = /^(25[0-4]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-4]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-4]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-4]|2[0-4][0-9]|[01]?[1-9][0-9]?|1[0-9]{2}|2[0-4][0-9])$/
      let subnet = this.formItem.networkName.split(':')[1]
      if(list.test(value)) {
        if(this.ipInSubnet(value, subnet)) {
          callback()
        }else {
          callback(new Error("IP地址与所选网段不匹配"))
        }
      }else {
        callback(new Error("该ip不可用"))
      }
    };
    return {
      model:false,
      disabled:  false,
      rowips: [],
      currentIP: [], // 当前已有IP
      netOption: [], // 选择网络
      formItem: {
        oldID: '', // 当前IP选中
        oldIP: '', // 当前IP选中
        networkId: '', // 选择网络选中id
        networkName: '', // 选择网络选中名称
        newIP: '', // 新IP地址
        vmid: '', // 虚拟机id
        vmName: '', // 虚拟机名称
      },
      ruleValidate: {
        networkId: [{ required: true, message: "必选项", trigger: "blur" }],
        newIP: [
          { required: true, message: "必填项"},
          { validator: propipv4, trigger: "blur" },
        ],
      }
    }
  },
  methods: {
    // 获取网络下拉数据
    networkData(){
      networkTableQuery().then((callback) => {
        let arr = new Array();
        callback.data.forEach(item=>{
          item.cidr.forEach((em,i) => {
            arr.push({
              id: item.id+":"+item.subnets[i],
              name: em,
            })
          })
        })
        this.netOption = arr;
        this.formItem.networkId = this.netOption[0].id
        this.formItem.networkName = this.netOption[0].name
      });
    },
    // 当前ip
    nowIPChange(item){
      this.formItem.oldIP = item.label
      this.formItem.oldID = item.value;
    },
    // 选择网络
    slectChange(item){
      if(item!==undefined) {
        this.formItem.networkName = item.label
        this.formItem.networkId = item.value;
      }
    },
    // 修改IP
    modelOK(){
      this.$refs['formItem'].validate((valid) => {
        if(valid){
          this.disabled = true
          networkCheckip({
            now_ipv4:this.formItem.oldIP,
            new_ipv4:this.formItem.newIP
          }).then((check)=>{
            if(check.data.msg == 'ok'){
              vmGroupTableReplaceIP({
                id: this.formItem.vmid,
                mac_addr: this.formItem.oldID,
                // now_subnet_id: "",
                now_ipv4: this.formItem.oldIP,
                new_netid: this.formItem.networkId.split(":")[0],
                subnet_id: this.formItem.networkId.split(":")[1],
                new_ipv4: this.formItem.newIP,
                name: this.formItem.vmName,
                // ...(this.formItem.presetsIP?{new_ipv4:this.formItem.ipPash}:"")
              })
              .then((callback)=>{
                if(callback.data.msg=='修改成功') {
                  this.model=false
                  this.$emit("return-ok",'修改虚拟机IP操作完成');
                }else{
                  this.disabled = false
                  this.$Message.error({
                    background: true,
                    closable: true,
                    duration: 10,
                    content: callback.data.msg
                  });
                }
              }).catch((error) => {
                this.disabled = false
                this.$emit("return-error",'修改虚拟机IP操作失败');
              })
            }else {
              this.disabled = false
              this.$Message.error({
                background: true,
                closable: true,
                duration: 10,
                content: check.data.msg
              });
            }
          })
        }else{
          this.disabled = false
        }
      })
    },
    ipInSubnet(ip, subnet) {
      let [subnetIp, maskBits] = subnet.split('/');
      maskBits = parseInt(maskBits, 10);
      let ipBinary = ip.split('.').map(octet => parseInt(octet, 10).toString(2).padStart(8, '0')).join('');
      let subnetBinary = subnetIp.split('.').map(octet => parseInt(octet, 10).toString(2).padStart(8, '0')).join('');
      for(let i=0; i<maskBits; i++){
          if(ipBinary[i] != subnetBinary[i]){
              return false;
          }
      }
      return true;
    },
  }
}
</script>