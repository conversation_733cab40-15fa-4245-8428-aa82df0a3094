<template>
  <div class="nfs-wrap">
    <div class="table-button-area">
      <div>
        <Button class="plus_btn" @click="createdNFS"><span class="icon iconfont icon-plus-circle"></span>创建</Button>
      </div>
      <div>
        <!-- <Input v-model="tablePageForm.search_str" search enter-button placeholder="请输入名称" style="width: 300px" @on-search="tableSearchInput"/> -->
      </div>
    </div>
    <div style="position: relative">
      <Spin fix v-if="loading" size="large" style="color:#ef853a">
        <Icon type="ios-loading" size=50 class="demo-spin-icon-load"></Icon>
        <div style="font-size:16px;padding:20px">Loading...</div>
      </Spin>
      <Table :columns="journalColumn" :data="tableData" ref="table"></Table>
    </div>
    <div class="pages" v-if="tableData.length>0">
      <Pagination  :total="tableTotal" :page-size="tablePageForm.pagecount"  @page-change="getTableList" @page-size-change="onPageSizeChange"/>
    </div>
    <CreateNfs  ref="refsCreateNfs" :selectType="selectType" ></CreateNfs>
  </div>
</template>

<script>
  import Pagination from '@/components/public/Pagination.vue';
  import CreateNfs from '@/components/storeService/Nfs/CreateNfs'
  export default {
    components: {
     CreateNfs,
     Pagination,
   },
   data() {
     return{
       operation: {
        add: '添加',
        edit: '编辑'
       },
       selectType: '添加',
       tableData: [
         {
          "export_id": 1,
          "path": "/",
          "fsal": {
              "name": "RGW",
              "rgw_user_id": "rgw"
          },
          "cluster_id": "css-nfs",
          "daemons": [],
          "pseudo": "/worm",
          "tag": null,
          "access_type": "RW",
          "squash": "no_root_squash",
          "security_label": false,
          "protocols": [
              4
          ],
          "transports": [
              "TCP",
              "UDP"
          ],
          "clients": []
          },
          {
          "export_id": 2,
          "path": "/test",
          "fsal": {
              "name": "CEPH",
              "user_id": "admin",
              "fs_name": "css-fs",
              "sec_label_xattr": null
          },
          "cluster_id": "css-nfs",
          "daemons": [],
          "pseudo": "/test",
          "tag": null,
          "access_type": "RW",
          "squash": "no_root_squash",
          "security_label": false,
          "protocols": [
              4
          ],
          "transports": [
              "TCP",
              "UDP"
          ],
          "clients": []
        }
       ],
       tableTotal: 0,
       loading: false,
       journalColumn: [
        { title: "路径", key: "path", sortable: true },
        { title: "别名", key: "pseudo", sortable: true,align: "center"  },
        { title: "集群名", key: "cluster_id", sortable: true,align: "center" },
        { title: "实例", key: "daemonsList", sortable: true ,align: "center" },
        { title: "后端存储", key: "storageType", sortable: true ,align: "center" },
        { title: "访问类型", key: "access_type", sortable: true ,align: "center" },
        { title: "操作",key: "action",width:200,
          render: (h, params) => {
            return h("div", [
              h("Button", {
                props:{
                  icon:"ios-create",
                  class:"close_btn"
                },
                on: {
                  click: () => {
                    this.tableAction(params.row,"edit")
                  },
                },
              },"编辑"),
              h("Button", {
                style:{color:'red',marginLeft:'10px'},
                props:{
                  icon:"ios-trash",
                  class:"plus_btn"
                },
                on: {
                  click: () => {
                    this.tableAction(params.row,"deleted")
                  },
                },
              },"删除"),
            ])
          }
        },
      ],
       tablePageForm: {
        page: 1, // 当前页
        pagecount: this.$store.state.power.pagecount, // 每页条
        search_str: "", // 收索
        order_type: "asc", // 排序规则
        order_by: "", // 排序列
      },
      dialogVisible: false,
      
     }
    },
    mounted(){
      // this.getTableList()
    },
    methods:{
      createdNFS() {
        this.selectType = this.operation.add
        this.$refs.refsCreateNfs.dialogVisible = true
      },
      // 每页条数
      onPageSizeChange(item) {
        this.$store.state.power.pagecount = item
        this.tablePageForm.pagecount = this.$store.state.power.pagecount
        this.tablePageForm.page = 1
        this.getTableList();
      },
      getTableList() {
        this.loading = !this.loading
        this.$axios.get("/thecephapi/api/nfs-ganesha/export").then((res) => {
          this.tableData = res.data.map((item) => {
            let str = ''
             item.daemons.forEach(element => {
              str+= element + ','
            })
            item.storageType = item.fsal.name !== null ? this.BACK_END_STORAGE_TYPE.find(item => item.value == scope.row.fsal.name).label : '暂无'
            item.daemonsList = str
            return item
          })
          this.loading = !this.loading
          this.tableTotal = this.tableData && this.tableData.length
        })
      },
      // 表格操作
      tableAction(row,action){
        switch (action) {
        case "edit":
          this.selectType = this.operation.edit
          this.$refs.refsCreateNfs.formData = JSON.parse(JSON.stringify(row))
          this.$refs.refsCreateNfs.dialogVisible = true
          break;   
        case "deleted":
          this.$Modal.confirm({
            title: "删除",
            content:
              '<p>是删除路径为<span style="font-weight: 600;color:red;word-wrap: break-word;">' +
              row.path +
              "</span>的数据？</p>",
            onOk: () => {
              this.$axios.delete(`/thecephapi/api/nfs-ganesha/export/${row.cluster_id}/${row.export_id}`).then((callback) => {
                  this.$Message.success({
                    background: true,
                    closable: true,
                    duration: 5,
                    content: "删除完成",
                  });
                  this.getTableList()
              }).catch((error) => {
                this.$Message.error({
                    background: true,
                    closable: true,
                    duration: 5,
                    content: "删除失败",
                  });
              });
            },
          });
          break;
        }
      },
    },
  }
</script>

<style scoped lang='less'>
  .nfs-wrap {
    width: 100%;
    height: 100%;
  }
</style>