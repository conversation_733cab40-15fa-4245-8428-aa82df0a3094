<style lang="less">
@import "./systemFunction.less";
</style>
<template>
  <div class="sys_tab">
    <Tabs class="tabs_template" name="1" v-model="tabName" @on-click="tabsClick">
      <TabPane
        v-if="item.show"
        v-for="item in tabsData"
        :key="item.name"
        :name="item.name"
        tab="1"
        :label="renderTabLabel(item.name)">
        <TabUser v-if="item.name === '用户管理'" :tabName="tabName" />
        <TabEmpower v-if="item.name === '授权管理'" :tabName="tabName" />
        <TabChangePW v-if="item.name === '修改密码'" :tabName="tabName" />
        <TabSecure v-if="item.name === '安全配置'" :tabName="tabName" />
      </TabPane>
    </Tabs>
  </div>
</template>
<script>
import { powerCodeQuery } from '@/api/other'; // 权限可用性 查询

import TabUser from "./tabUser/TabUser.vue"
import TabEmpower from "./tabEmpower/TabEmpower.vue"
import TabChangePW from "./tabChangePW/TabChangePW.vue"
import TabSecure from "./tabSecure/TabSecure.vue"
export default {
  components: {
    TabUser,
    TabEmpower,
    TabChangePW,
    TabSecure,
  },
  data() {
    return {
      tabName: '',
      tabsData: [
        { name: '用户管理', code: 'yonghuguanli', show: false },
        { name: '授权管理', code: 'shouquanguanli', show: false },
        { name: '修改密码', code: 'xiugaimima', show: false },
        { name: '安全配置', code: 'anquanpeizhi', show: false },
      ],
    }
  },
  watch: {
    '$store.state.power.systemFunctionTab'(news){
      this.tabName = news
    }
  },
  mounted(){
    this.tabsQuery()
  },
  methods: {
    renderTabLabel(item){
      return (h) => {
        return h('div', [
          h('span', {
            class: 'select_tab_border',
            style: { background: this.tabName === item ? '#fb6129' : '' }
          }),
          h('span', item)
        ]);
      };
    },
    // 查询标签页权限
    tabsQuery(){
      powerCodeQuery({
        module_code:[
          'yonghuguanli',
          'xiugaimima',
          'shouquanguanli',
          'anquanpeizhi'
        ]
      }).then(callback=>{
        this.tabsData.forEach(item => {
          if (callback.data.data.hasOwnProperty(item.code)) {
            item.show = callback.data.data[item.code];
          }
        });
        if(this.$store.state.power.systemFunctionTab == '修改密码'){
          this.tabName = '修改密码'
        }else{
          if(callback.data.data.yonghuguanli) {
            this.tabName = '用户管理'
          }else if(callback.data.data.xiugaimima) {
            this.tabName = '修改密码'
          }else if(callback.data.data.anquanpeizhi) {
            this.tabName = '安全配置'
          }
          this.$store.state.power.systemFunctionTab = this.tabName
        }
      })
    },
    tabsClick(name){
      this.$store.state.power.systemFunctionTab = name
    },
  }
}
</script>
