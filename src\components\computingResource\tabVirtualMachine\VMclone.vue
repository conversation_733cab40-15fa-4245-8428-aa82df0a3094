<template>
  <div>
    <Modal v-model="model" width="600" :mask-closable="false">
      <template #header><p>克隆虚拟机</p></template>
      <Form ref="formItem" :model="formItem" :rules="rules_data" :label-width="130">
        <!-- <FormItem label="克隆虚拟机名称"> -->
        <FormItem label="虚拟机名称">
          <Input disabled v-model="formItem.vmName"></Input>
        </FormItem>
        <FormItem label="计算资源" prop="hostID">
          <Select v-model="formItem.hostID" :label-in-value='true'>
            <Option v-for="item in hostData" :value="item.availability_zone" :key="item.id" >{{ item.name }}</Option>
          </Select>
        </FormItem>
        <FormItem label="网络名称" prop="networkId">
          <Select v-model="formItem.networkId" :label-in-value="true"  @on-change="netChange">
            <Option v-for="item in netOption" :value="item.id" :key="item.id" >{{ item.name }}</Option>
          </Select>
        </FormItem>
        <FormItem label="预设IP" v-if="false">
          <Checkbox v-model="formItem.presetsIP"></Checkbox>
        </FormItem>
        <FormItem label="IP地址" prop="addrIP"  v-if="this.formItem.presetsIP">
          <Input v-model="formItem.addrIP" placeholder="请输入IP地址"></Input>
        </FormItem>
      </Form>
      <template #footer>
        <Button type="text" @click="model = false">取消</Button>
        <Button type="info" class="plus_btn" @click="modelOK" :disabled="disabled">确定</Button
        >
      </template>
    </Modal>
  </div>
</template>
<script>
import {vmClone} from '@/api/virtualMachine';  // 虚拟机克隆
import {physicsTableAllQuery} from '@/api/physics';
import {networkTableQuery} from '@/api/network';
export default {
  props: {
    cloneTime: String,
    vmRow: Object,
  },
  watch: {
    cloneTime(news){
      this.formItem.vmID = this.vmRow.id
      this.formItem.vmName =this.vmRow.name
      this.formItem.osType =this.vmRow.os_type

      this.vmData()
      this.model = true
      this.disabled = false;
    }
  },
  data(){
    const propAddr = (rule, value, callback) => {
      let list = /^(25[0-4]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-4]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-4]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-4]|2[0-4][0-9]|[01]?[1-9][0-9]?|1[0-9]{2}|2[0-4][0-9])$/
      let subnet = this.formItem.networkName.split(":")[1]
      if(list.test(value)){
        if (this.ipInSubnet(value, subnet)) {
          callback()
        }else {
          callback(new Error("IP地址与所选网段不匹配"));
        }
      }else {
        callback(new Error("该ip不可用"))
      }
    }
    return {
      model: false,
      disabled: false,
      hostData: [], // 计算资源数据
      netOption: [], // 网络数据
      formItem: {
        vmID: '',
        vmName: '', // 克隆虚拟机名称
        hostID: '', // 计算资源id
        networkId: '', // 网络id
        networkName: '', // 网络名称
        presetsIP: false, // 预设IP
        addrIP: '', // IP地址
        osType: '', // 系统类型
      },
      // 正则
      rules_data:{
        hostID:[{ required: true, message: '必选项' }],
        networkId:[{ required: true, message: '必选项' }],
        addrIP:[
          { required: true, message: "必填项" },
          { validator: propAddr, trigger: "blur" },
        ],
      },
    }
  },
  methods: {
    // 获取下拉数据
    vmData(){
      // 获取计算资源下拉数据
      physicsTableAllQuery().then((res) => {
        this.hostData = res.data;
        this.formItem.hostID = res.data[0].availability_zone;
      });
      // 获取网络下拉框数据
      networkTableQuery().then((callback) => {
        let arr = new Array();
        callback.data.forEach(item=>{
          item.cidr.forEach((em,i) => {
            arr.push({
              id: item.id+":"+item.subnets[i],
              name: em,
            })
          })
        })
        this.netOption = arr;
        this.formItem.networkId = arr[0].id;
        this.formItem.networkName = arr[0].name
      });
    },
    // 选择网络
    netChange(item){
      this.formItem.networkName = item.label
    },
    // 克隆确认
    modelOK(){
      this.$refs.formItem.validate((valid) => {
        if(valid) {
          this.disabled = true;
          vmClone({
            vm_id:this.formItem.vmID,
            network_id:this.formItem.networkId.split(":")[0],
            subnet_id:this.formItem.networkId.split(":")[1],
            os_type:this.formItem.osType,
            availability_zone:this.formItem.hostID
          })
          .then((res) => {
            if(res.data.msg == "ok") {
              this.model=false
              this.$emit("return-ok",'克隆虚拟机操作完成');
            }else {
              this.disabled = false
              this.$emit("return-error",'克隆虚拟机操作失败');
            }
          }).catch((error) => {
            this.disabled = false
            this.$emit("return-error",'克隆虚拟机操作失败');
          })
        }
      })
    },
    // 判断网络属于子网
    ipInSubnet(ip, subnet) {
      let [subnetIp, maskBits] = subnet.split('/');
      maskBits = parseInt(maskBits, 10);
      let ipBinary = ip.split('.').map(octet => parseInt(octet, 10).toString(2).padStart(8, '0')).join('');
      let subnetBinary = subnetIp.split('.').map(octet => parseInt(octet, 10).toString(2).padStart(8, '0')).join('');
      for(let i=0; i<maskBits; i++){
          if(ipBinary[i] != subnetBinary[i]){
              return false;
          }
      }
      return true;
    },
  }
}
</script>