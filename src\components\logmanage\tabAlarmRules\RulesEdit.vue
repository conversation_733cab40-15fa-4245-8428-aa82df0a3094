<template>
  <div>
    <Modal v-model="model" width="630" :mask-closable="false">
      <template #header><p>编辑告警规则</p></template>
      <Form
        :model="formItem"
        ref="formItem"
        :rules="rulesForm"
        :label-width="120"
      >
        <FormItem label="当前规则">
          <Input
            v-model="tableRow.name"
            disabled
          ></Input>
        </FormItem>
        <div v-if="tableRow.expr_code=='0'">
          <FormItem label="告警级别">
            <RadioGroup v-model="formItem.radio">
              <Radio label="critical" border>严重告警</Radio>
              <Radio label="major" border>重要告警</Radio>
              <Radio label="warning" border>次要告警</Radio>
              <Radio label="info" border>提示告警</Radio>
            </RadioGroup>
          </FormItem>
        </div>
        <div v-if="tableRow.expr_code!=='0'">
          <FormItem prop="criticalValue">
            <template #label>
              <Checkbox v-model="formItem.criticalCheck"></Checkbox>
              严重告警
            </template>
            <Input v-model="formItem.criticalValue" :disabled='!formItem.criticalCheck' type="number">
              <template #append>
                <span>{{tableRow.unit}}</span>
              </template>
            </Input>
          </FormItem>
          <FormItem prop="majorValue">
            <template #label>
              <Checkbox v-model="formItem.majorCheck"></Checkbox>
              重要告警
            </template>
            <Input v-model="formItem.majorValue" :disabled='!formItem.majorCheck' type="number">
              <template #append>
                <span>{{tableRow.unit}}</span>
              </template>
            </Input>
          </FormItem>
          <FormItem prop="warningValue">
            <template #label>
              <Checkbox v-model="formItem.warningCheck"></Checkbox>
              次要告警
            </template>
            <Input v-model="formItem.warningValue" :disabled='!formItem.warningCheck' type="number">
              <template #append>
                <span>{{tableRow.unit}}</span>
              </template>
            </Input>
          </FormItem>
          <FormItem prop="infoValue">
            <template #label>
              <Checkbox v-model="formItem.infoCheck"></Checkbox>
              提示告警
            </template>
            <Input v-model="formItem.infoValue" :disabled='!formItem.infoCheck' type="number">
              <template #append>
                <span>{{tableRow.unit}}</span>
              </template>
            </Input>
          </FormItem>
        </div>
        <FormItem label="持续时间" prop="for">
          <Input v-model="formItem.for" type="number">
            <template #append>
              <span>分钟</span>
            </template>
          </Input>
        </FormItem>
      </Form>
      <div slot="footer">
        <Button type="text" @click="model = false">取消</Button>
        <Button type="primary" @click="modelOK" :disabled="disabled"
          >确认</Button
        >
      </div>
    </Modal>
  </div>
</template>
<script>
import {
  alarmRulesEdit, // 告警规则 修改
} from "@/api/log";

export default {
  props: {
    tableRow: Object,
    editTime: String,
  },
  watch: {
    editTime(news) {
      this.formItem.for =  this.tableRow.for_interval.split('m')[0]
      if(this.tableRow.expr_code=='0'){
        if(this.tableRow.critical_value=='-1') {
          this.formItem.radio = 'critical'
        }else if(this.tableRow.major_value=='-1') {
          this.formItem.radio = 'major'
        }else if(this.tableRow.warning_value=='-1') {
          this.formItem.radio = 'warning'
        }else if(this.tableRow.info_value=='-1') {
          this.formItem.radio = 'info'
        }
      }else if(this.tableRow.expr_code!=='0') {
        this.formItem.criticalValue = this.tableRow.critical_value.toString()
        this.formItem.majorValue = this.tableRow.major_value.toString()
        this.formItem.warningValue = this.tableRow.warning_value.toString()
        this.formItem.infoValue = this.tableRow.info_value.toString()
        this.tableRow.critical_value=='0'?this.formItem.criticalCheck = false:this.formItem.criticalCheck = true
        this.tableRow.major_value=='0'?this.formItem.majorCheck = false:this.formItem.majorCheck = true
        this.tableRow.warning_value=='0'?this.formItem.warningCheck = false:this.formItem.warningCheck = true
        this.tableRow.info_value=='0'?this.formItem.infoCheck = false:this.formItem.infoCheck = true
      }
      this.model = true;
      this.disabled = false;
    },
  },
  data() {
    const propFor =(rule,value,callback)=>{
      let list = /^[0-9]\d*$/
      if(list.test(value)){
        callback()
      }else{
        callback(new Error("请输入0以上的整数值"));
      }
    }
    const propCritical =(rule,value,callback)=>{
      let list = /^(?:[1-9]|[1-9][0-9]|100)$/
      if(this.formItem.criticalCheck) {
        if(list.test(value)){
          callback()
        }else{
          callback(new Error("请输1-100之内的数字"));
        }
      }else {
        callback()
      }
    }
    const propMajor =(rule,value,callback)=>{
      let list = /^(?:[1-9]|[1-9][0-9]|100)$/
      if(this.formItem.majorCheck) {
        if(list.test(value)) {
          if(this.formItem.criticalCheck) {
            if(this.formItem.criticalValue*1>=value*1) {
              callback()
            }else {
              callback(new Error("重要告警应小于严重告警"))
            }
          }else {
            callback()
          }
        }else {
          callback(new Error("请输1-100之内的数字"));
        }
      }else {
        callback()
      }
    }
    const propWarning =(rule,value,callback)=>{
      let list = /^(?:[1-9]|[1-9][0-9]|100)$/
      if(this.formItem.warningCheck) {
        if(list.test(value)){
          if(this.formItem.criticalCheck&&!this.formItem.majorCheck) {
            if(this.formItem.criticalValue*1>=value*1) {
              callback()
            }else {
              callback(new Error("次要告警应小于严重告警"))
            }
          }else if(this.formItem.majorCheck){
            if(this.formItem.majorValue*1>=value*1) {
              callback()
            }else {
              callback(new Error("次要告警应小于重要告警"))
            }
          }else {
            callback()
          }
        }else {
          callback(new Error("请输1-100之内的数字"));
        }
      }else {
        callback()
      }
      
    }
    const propInfo =(rule,value,callback)=>{
      let list = /^(?:[1-9]|[1-9][0-9]|100)$/
      if(this.formItem.infoCheck){
        if(list.test(value)){
          if(this.formItem.criticalCheck&&!this.formItem.majorCheck&&!this.formItem.warningCheck) {
            if(this.formItem.criticalValue*1>=value*1) {
              callback()
            }else {
              callback(new Error("提示告警应小于严重告警"))
            }
          }else if(this.formItem.majorCheck&&!this.formItem.warningCheck) {
            if(this.formItem.majorValue*1>=value*1) {
              callback()
            }else {
              callback(new Error("提示告警应小于重要告警"))
            }
          }else if(this.formItem.warningCheck) {
            if(this.formItem.warningValue*1>=value*1) {
              callback()
            }else {
              callback(new Error("提示告警应小于次要告警"))
            }
          }else {
            callback()
          }
        }else {
          callback(new Error("请输1-100之内的数字"));
        }
      }else {
        callback()
      }
    }
    return {
      model: false,
      disabled: false,
      formItem: {
        radio: 'critical',
        criticalCheck: false,
        majorCheck: false,
        warningCheck: false,
        infoCheck: false,
        criticalValue: '1',
        majorValue: '1',
        warningValue: '1',
        infoValue: '1',

        for: '1',
      },
      // 正则验证
      rulesForm: {
        for:[
          { required: true, message: '必填项', trigger: 'change' },
          { validator: propFor, trigger:'change' }
        ],
        criticalValue:[
          { required: true, message: '必填项', trigger: 'change' },
          { validator: propCritical, trigger:'change' }
        ],
        majorValue:[
          { required: true, message: '必填项', trigger: 'change' },
          { validator: propMajor, trigger:'change' }
        ],
        warningValue:[
          { required: true, message: '必填项', trigger: 'change' },
          { validator: propWarning, trigger:'change' }
        ],
        infoValue:[
          { required: true, message: '必填项', trigger: 'change' },
          { validator: propInfo, trigger:'change' }
        ]
      },
    };
  },
  methods: {
    // 编辑网络确认事件
    modelOK() {
      this.$refs.formItem.validate((valid) => {
        if (valid) {
          let data = {
            critical_value: '0',
            major_value: '0',
            warning_value: '0',
            info_value: '0',
            for_interval: this.formItem.for+'m',
            id: this.tableRow.id,
            name: this.tableRow.name
          }
          if(this.tableRow.expr_code=='0') {
            if(this.formItem.radio == 'critical'){
              data.critical_value = '-1'
            }else if(this.formItem.radio == 'major'){
              data.major_value = '-1'
            }else if(this.formItem.radio == 'warning'){
              data.warning_value = '-1'
            }else if(this.formItem.radio == 'info'){
              data.info_value = '-1'
            }
          }else {
            if(this.formItem.criticalCheck) {
              data.critical_value = this.formItem.criticalValue
            }
            if(this.formItem.majorCheck){
              data.major_value = this.formItem.majorValue
            }
            if(this.formItem.warningCheck){
              data.warning_value = this.formItem.warningValue
            }
            if(this.formItem.infoCheck){
              data.info_value = this.formItem.infoValue
            }
          }
          this.disabled = true;
          alarmRulesEdit(data)
            .then((callback) => {
              if (callback.data.msg == "ok") {
                this.$emit("return-ok", "编辑告警规则操作完成");
                this.model = false;
              } else {
                this.$emit("return-error", callback.data.msg);
                this.disabled = false;
              }
            })
            .catch((error) => {
              this.$emit("return-error", "编辑告警规则操作失败");
              this.disabled = false;
            });
        }
      });
    },
  },
};
</script>
