<template>
  <div style="overflow: auto; height: 100%; padding: 0 0 150px 0">
    <div class="table-button-area">
      <div>
        <Button class="plus_btn" @click="newClick"
          ><span class="icon iconfont icon-plus-circle"></span> 创建硬盘域</Button
        >
        <Button class="plus_btn" @click="hdfTableData"
          ><span class="icon iconfont icon-plus-circle"></span>刷新</Button
        >
      </div>
      <div>
        <!-- <Input v-model="tablePageForm.search_str" search enter-button placeholder="请输入名称" style="width: 300px" @on-search="tableSearchInput"/> -->
      </div>
    </div>
    <div style="position: relative">
      <Spin fix v-if="spinShow" size="large" style="color: #ef853a">
        <Icon type="ios-loading" size="50" class="demo-spin-icon-load"></Icon>
        <div style="font-size:16px; padding: 20px">Loading...</div>
      </Spin>
      <Table :columns="hdfColumns" :data="hdfData"></Table>
      <div class="pages" v-if="this.hdfData.length > 0">
        <Pagination
          :total="tableTotal"
          :page-size="tablePageForm.pagecount"
          @page-change="onPageChange"
          @page-size-change="onPageSizeChange"
        />
      </div>
    </div>
    <!-- 创建硬盘域弹框 -->
    <Modal
      v-model="hdfModal"
      :loading="hdfload"
      width="600"
      :title="titleModel"
      @on-ok="newQok(titleModel)"
      @on-cancel="cancel"
      :mask-closable="false"
    >
      <div>
        <Form :model="formItem" :rules="ruleForm" :label-width="120">
          <FormItem label="硬盘域名称" prop="rootMapName">
            <Input v-model="formItem.rootMapName"></Input>
          </FormItem>
          <FormItem label="硬盘域类型" prop="osdtype">
            <Select v-model="formItem.osdtype" :disabled="osddisabled">
              <Option
                v-for="item in typeData"
                :value="item.key"
                :key="item.key"
                >{{ item.value }}</Option
              >
            </Select>
          </FormItem>
        </Form>
      </div>
    </Modal>
    <!-- 管理磁盘单元 -->
    <ManagingDiskUnits :manage="manage" @custom-event="managingDiskChild"></ManagingDiskUnits>
  </div>
</template>
<script>
import Pagination from "@/components/public/Pagination.vue";
import ManagingDiskUnits from "./ManagingDiskUnits.vue";
export default {
  components: {
    Pagination,
    ManagingDiskUnits,
  },
  props: {
    tabName: String,
  },
  watch: {
    tabName(value) {
      if(value=='云硬盘'){
        this.hdfTableData()
      }
    },
  },
  mounted() {
    if(this.$store.state.power.storageResourceTab == '云硬盘') {
      this.hdfTableData()
    }
  },
  data() {
    const propPoolname = (rule, value, callback) => {
      let list = /^[\u4e00-\u9fa5_a-zA-Z0-9@_.-]{2,32}$/;
      if (list.test(value)) {
        callback();
      } else {
        callback(new Error("请输入2-32个中文或字符（特殊字符可用@_.-）"));
      }
    };
    return {
      spinShow: false,
      // 分页
      tablePageForm: {
        page: 1, // 当前页
        pagecount: this.$store.state.power.pagecount, // 每页条
        search_str: "", // 收索
        order_type: "asc", // 排序规则
        order_by: "", // 排序列
      },
      ruleForm: {
        rootMapName: [
          { required: true, message: "必填项", trigger: "blur" },
          { validator: propPoolname, trigger: "change" },
          { validator: propPoolname, trigger: "blur" },
        ],
        osdtype: [{ required: true }],
      },
      hdfColumns: [
        { title: "硬盘域名称", key: "poolName" },
        { title: "存储节点名称", key: "saveNodeName", align: "center" },
        { title: "存储单元id", key: "osdid", align: "center" },
        { title: "存储单元路径", key: "path", align: "center" },
        { title: "存储单元类型", key: "type", align: "center" },
        { title: "总容量", key: "size", align: "center" },
        {
          title: "操作",
          key: "operation",
          width: 120,
          render: (h, params) => {
            return h("Dropdown", { props: { trigger: "click" } }, [
              h("Button", "配置 ▼"),
              h("DropdownMenu", { slot: "list" }, [
                h(
                  "DropdownItem",
                  {
                    nativeOn: {
                      click: () => {
                        this.titleModel = "编辑硬盘域";
                        this.osddisabled = true;
                        this.hdfModal = true;
                        this.formItem.rootMapName = params.row.poolName;
                        this.editForm.srcName = params.row.poolName;
                        this.editForm.full_type_poolname =
                          params.row.full_type_poolname;
                        this.editForm.yvType = params.row.yv_osdtype;
                      },
                    },
                  },
                  "编辑"
                ),
                h(
                  "DropdownItem",
                  {
                    nativeOn: {
                      click: () => {
                        this.manage=[params.row,this.guanli++]
                      },
                    },
                  },
                  "管理"
                ),
                h(
                  "DropdownItem",
                  {
                    style: { color: "red" },
                    nativeOn: {
                      click: () => {
                        this.$Modal.confirm({
                          title: "删除硬盘域",
                          content:
                            '<p>是否删除名称为<span style="font-weight: 600;color:red;word-wrap: break-word;">' +
                            params.row.poolName +
                            "</span>硬盘域？</p>",
                          onOk: () => {
                            this.$axios.delete("/thecss/v2/deleteYingPanYv/",{data:{osdidL:params.row.osdid,rootMapName:params.row.poolName,yv_osdtype:params.row.yv_osdtype}}).then((callback) => {
                              this.$Message.success({
                                background: true,
                                closable: true,
                                duration: 5,
                                content: "删除硬盘域操作完成",
                              });
                              this.hdfTableData()
                            }).catch((error) => {
                              this.$Message.error({
                                  background: true,
                                  closable: true,
                                  duration: 5,
                                  content: "删除硬盘域操作失败",
                                });
                            });
                          },
                          onCancel: () => {},
                        });
                      },
                    },
                  },
                  "删除"
                ),
              ]),
            ]);
          },
        },
      ],
      hdfData: [],
      hdfAllData: [],
      // 创建/编辑硬盘域
      hdfModal: false,
      formItem: {
        rootMapName: "",
        osdtype: "hdd",
      },
      editForm: {
        srcName: "",
        targetName: "",
        full_type_poolname: "",
        yvType: "",
      },
      typeData: [
        { value: "HDD", key: "hdd" },
        { value: "SSD", key: "ssd" },
        { value: "NVME", key: "nvme" },
      ],
      osddisabled: false,
      titleModel: "创建硬盘域",
      
      // 管理磁盘单元
      manage:[],
      guanli:1,



      hdfload: false,
    };
  },
  updated() {
    this.tablePageForm.pagecount = this.$store.state.power.pagecount;
  },
  methods: {
    hdfTableData() {
      this.spinShow = true;
      this.$axios.get("/thecss/v2/getYingPanYv").then((callback) => {
        this.tableTotal = callback.data.length;
        this.hdfAllData = callback.data;
        this.hdfData = this.hdfAllData.slice(
          this.tablePageForm.page - 1,
          this.tablePageForm.page * this.$store.state.power.pagecount
        );
        this.spinShow = false;
      });
    },
    // 当前分页
    onPageChange(item) {
      this.tablePageForm.page = item;
      this.hdfTableData();
    },
    // 每页条数
    onPageSizeChange(item) {
      this.$store.state.power.pagecount = item;
      this.tablePageForm.pagecount = this.$store.state.power.pagecount;
      this.tablePageForm.page = 1;
      this.hdfTableData();
    },
    newClick() {
      this.titleModel = "创建硬盘域";
      this.osddisabled = false;
      this.hdfModal = true;
      this.formItem.rootMapName = "";
      this.formItem.osdtype = "hdd";
    },

    // 创建硬盘域弹框确认
    newQok(title) {
      let name = /^[\u4e00-\u9fa5_a-zA-Z0-9@_.-]{2,32}$/;
      if (name.test(this.formItem.rootMapName)) {
        this.$Message.info({
          background: true,
          closable: true,
          duration: 5,
          content: title + "操作已开始",
        });
        if (title == "创建硬盘域") {
          this.$axios
            .post("/thecss/v2/createYingPanYv", this.formItem)
            .then((callback) => {
              this.$Message.success({
                background: true,
                closable: true,
                duration: 5,
                content: title + "操作完成",
              });
              this.hdfTableData();
            });
        } else {
          this.editForm.targetName = this.formItem.rootMapName;
          this.$axios
            .put("/thecss/v2/updateYingPanYvName", this.editForm)
            .then((callback) => {
              this.$Message.success({
                background: true,
                closable: true,
                duration: 5,
                content: title + "操作完成",
              });
              this.hdfTableData();
            });
        }
      } else {
        this.$Message.warning({
          background: true,
          closable: true,
          duration: 5,
          content: title + "的硬盘域名称不符合规则",
        });
      }
    },
    managingDiskChild(data){
      this.hdfTableData()
    },
    cancel() {},
  },
};
</script>