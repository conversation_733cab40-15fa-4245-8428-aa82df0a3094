<template>
  <div>
    <Modal v-model="model" width="600" :mask-closable="false">
      <template #header
        ><p>
          <span style="color: green">{{ row.name }}</span
          >执行命令
        </p></template
      >
      <Form :model="formItem" ref="formItem" :label-width="80">
        <FormItem label="命令">
          <div class="output_input">
            <Input
              v-model="formItem.command"
              placeholder="请输入要执行的命令"
              @on-enter="modalOK"
            ></Input>
            <Button type="primary" style="margin-left: 10px" @click="modalOK"
              >发送</Button
            >
          </div>
        </FormItem>

        <h3>输出</h3>
        <div class="output_area">
          <p v-for="(item,index) in output" :key="index">{{item}}</p>
        </div>
      </Form>
      <div slot="footer">
        <Button type="text" @click="model = false">取消</Button>
        <!-- <Button type="primary" @click="modalOK">确认</Button> -->
      </div>
    </Modal>
  </div>
</template>
<script>
import { containerInstruction } from "@/api/container"; // 容器表 重启容器
export default {
  props: {
    row: Object,
    commandTime: String,
  },
  watch: {
    commandTime(news) {
      this.model = true;
      this.formItem.id = this.row.uuid;
      this.formItem.name = this.row.name;
      this.formItem.command = "";
    },
  },
  data() {
    return {
      model: false,
      formItem: {
        id: "",
        name: "",
        command: "",
      },
      output: [],
    };
  },

  methods: {
    modalOK() {
      if (this.formItem.command !== "") {
        containerInstruction(this.formItem)
          .then((callback) => {
            console.log(callback)
            if(callback.data.msg == 'ok') {
              this.output = callback.data.output.split("\n").filter(item => item !== '')
            }else {
              this.$emit("return-ok",{
                msg: callback.data.msg,
                type: 'error'
              });
            }
          })
      }
    },
  },
};
</script>
<style scoped>
h3 {
  text-align: center;
  padding: 5px 0;
}
.output_input {
  display: flex;
}
.output_area {
  border: 1px solid #7c7b7b;
  width: 100%;
  height: 300px;
  overflow: auto;
  padding: 0 10px;
  background: #dbdada;
  border-radius: 5px;
}
</style>