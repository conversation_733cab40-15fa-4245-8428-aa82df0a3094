<template>
  <Modal v-model="model" width="600" :mask-closable="false">
    <template #header><p>新建集群组</p></template>
    <Form :model="formItem" ref="formItem" :rules="rulesForm" :label-width="120">
      <FormItem label="集群组名称" prop="name">
        <Input v-model="formItem.name" placeholder="请输入集群组名称" ></Input>
      </FormItem>
    </Form>
    <div>
      <Spin fix v-if="spinShow" size="large" style="color:#ef853a">
        <Icon type="ios-loading" size=50 class="demo-spin-icon-load"></Icon>
        <div style="font-size:16px;padding:20px">Loading...</div>
      </Spin>
      <Table
        :columns="tableColumn"
        :data="tableData"
        no-data-text="暂无空闲主机"
        @on-selection-change="tableChange"
      >
        <template v-slot:state="{ row }">
          {{ row.state =="up" ? '良好' : "错误" }}
        </template>
        <template v-slot:status="{ row }">
          {{ row.status =="enabled" ? '已启动' : "未启动" }}
        </template>
      </Table>
    </div>
    <div slot="footer">
      <Button type="text" @click="model = false">取消</Button>
      <Button type="primary" @click="modelOK" :disabled="disabled">确认</Button>
    </div>
  </Modal>
</template>
<script>
import {
  hostUnusedQuery, // 主机未使用 查询
  clusterGroupNew, // 集群组 新建
  clusterGroupMovein // 集群组 移入
} from '@/api/physics';

export default {
  props: {
    newTime: String,
  },
  watch: {
    newTime(news){
      // this.formItem.title = '新建集群组'
      // this.tableSelect=new Array()
      this.$refs.formItem.resetFields()
      this.hostQuery()
      this.model = true
      this.disabled = false
    }
  },
  data() {
    // 名称
    const propName = (rule, value, callback) => {
      let list = /^[\u4e00-\u9fa5_a-zA-Z0-9@_.-]{2,32}$/;
      if(list.test(value)) {
        callback()
      }else {
        callback(new Error('2-32 个中文、英文、数字、特殊字符(@_.-)'))
      }
    };
    return {
      model: false,
      disabled: false,
      formItem:{
        title: '',
        name: '',
        describe: '',
      },
      spinShow: false,
      tableColumn: [
        { type: 'selection',width: 30,align: 'center' },
        { title: '主机',key: 'hypervisor_hostname' },
        { title: '健康状态',key: 'state',align: "center", slot: "state" },
        { title: '运行状态',key: 'status',align: "center", slot: "status" }
      ],
      tableData:[],
      tableSelect:[],
      // 正则验证
      rulesForm: {
        name: [
          { required: true, message: "必填项", trigger: "change" },
          { validator: propName, trigger: "change" },
        ],
      },
    };
  },
  methods: {
    // 查询剩余主机
    hostQuery(){
      this.spinShow = true
      hostUnusedQuery()
      .then(callback =>{
        this.spinShow =false;
        this.tableData = callback.data
      })
      .catch((error) => {
        this.spinShow =false;
      });
    },
    // 表格选中
    tableChange(item) {
      this.tableSelect = item
    },
    modelOK() {
      this.$refs.formItem.validate((valid) => {
        if(valid){
          if(this.tableSelect.length>0){
            this.disabled = true
            clusterGroupNew({name:this.formItem.name})
            .then(callback=>{
              if (callback.data.msg !=="409") {
                for(var i=0;i<this.tableSelect.length;i++) {
                  if(i==this.tableSelect.length-1) {
                    clusterGroupMovein({id:callback.data.id,name:this.formItem.name,hostname:this.tableSelect[i].hypervisor_hostname})
                    .then(res=>{
                      this.model = false
                      this.$emit("custom-ok",'新建集群组完成');
                    })
                  }else{
                    clusterGroupMovein({id:callback.data.id,name:this.formItem.name,hostname:this.tableSelect[i].hypervisor_hostname})
                  }
                }
              }else {
                this.$emit("custom-error",'禁止创建同名集群组');
                this.disabled = false
              }
            })
          }else {
            this.$emit("custom-error",'未选择主机，无法新建集群组');
          }
        }
      })
    },
  },
};
</script>