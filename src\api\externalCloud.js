// 计算资源/多云纳管（外部云）
import axios from "axios";
import * as basic_proxy from "@/api/config";

// 外部云组 查询
export async function externalCloudGroupQuery() {
  return await axios.get(basic_proxy.cloudy + "/v1/cloudy/tree");
}

// 外部云组 添加
export async function externalCloudGroupAdd(params) {
  return await axios.post(basic_proxy.cloudy + "/v1/cloudy/add", params);
}

// 外部云组 删除
export async function externalCloudGroupDelete(params) {
  return await axios.delete(basic_proxy.cloudy + "/v1/cloudy/delete", params);
}

// 外部云表格 查询
export async function externalCloudTableQuery(params) {
  return await axios.post(basic_proxy.cloudy + "/v1/vmware/vm/all", params);
}

// 外部云表格 开机
export async function externalCloudTableOpen(params) {
  return await axios.post(basic_proxy.cloudy + "/v1/vmware/vm/start", params);
}

// 外部云表格 关机
export async function externalCloudTableClose(params) {
  return await axios.post(basic_proxy.cloudy + "/v1/vmware/vm/stop", params);
}

// 外部云表格 迁移
export async function externalCloudTableMigrate(params) {
  return await axios.post(basic_proxy.cloudy + "/v1/vmware/vm/migrate", params);
}

// 迁移任务查询
export async function migrateTaskQuery(params) {
  return await axios.post(basic_proxy.cloudy + "/v1/vmware/vm/tasks", params);
}