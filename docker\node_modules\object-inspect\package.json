{"_from": "object-inspect@^1.9.0", "_id": "object-inspect@1.12.2", "_inBundle": false, "_integrity": "sha512-z+cPxW0QGUp0mcqcsgQyLVRDoXFQbXOwBaqyF7VIgI4TWNQsDHrBpUQslRmIfAoYWdYzs6UlKJtB2XJpTaNSpQ==", "_location": "/object-inspect", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "object-inspect@^1.9.0", "name": "object-inspect", "escapedName": "object-inspect", "rawSpec": "^1.9.0", "saveSpec": null, "fetchSpec": "^1.9.0"}, "_requiredBy": ["/side-channel"], "_resolved": "https://registry.npmmirror.com/object-inspect/-/object-inspect-1.12.2.tgz", "_shasum": "c0641f26394532f28ab8d796ab954e43c009a8ea", "_spec": "object-inspect@^1.9.0", "_where": "/root/docker/node_modules/side-channel", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "browser": {"./util.inspect.js": false}, "bugs": {"url": "https://github.com/inspect-js/object-inspect/issues"}, "bundleDependencies": false, "deprecated": false, "description": "string representations of objects in node and the browser", "devDependencies": {"@ljharb/eslint-config": "^21.0.0", "aud": "^2.0.0", "auto-changelog": "^2.4.0", "core-js": "^2.6.12", "error-cause": "^1.0.4", "es-value-fixtures": "^1.4.1", "eslint": "=8.8.0", "for-each": "^0.3.3", "functions-have-names": "^1.2.3", "has-tostringtag": "^1.0.0", "make-arrow-function": "^1.2.0", "mock-property": "^1.0.0", "npmignore": "^0.3.0", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "string.prototype.repeat": "^1.0.0", "tape": "^5.5.3"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "homepage": "https://github.com/inspect-js/object-inspect", "keywords": ["inspect", "util.inspect", "object", "stringify", "pretty"], "license": "MIT", "main": "index.js", "name": "object-inspect", "publishConfig": {"ignore": [".github/workflows", "./test-core-js.js"]}, "repository": {"type": "git", "url": "git://github.com/inspect-js/object-inspect.git"}, "scripts": {"lint": "eslint .", "posttest": "npx aud --production", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run tests-only && npm run test:corejs", "test:corejs": "nyc tape test-core-js.js 'test/*.js'", "tests-only": "nyc tape 'test/*.js'", "version": "auto-changelog && git add CHANGELOG.md"}, "sideEffects": false, "support": true, "testling": {"files": ["test/*.js", "test/browser/*.js"], "browsers": ["ie/6..latest", "chrome/latest", "firefox/latest", "safari/latest", "opera/latest", "iphone/latest", "ipad/latest", "android/latest"]}, "version": "1.12.2"}