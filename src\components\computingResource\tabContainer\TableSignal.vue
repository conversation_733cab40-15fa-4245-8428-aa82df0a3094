<template>
  <div>
    <Modal v-model="model" width="600" :mask-closable="false">
      <template #header
        ><p>
          <span style="color: green">{{ row.name }}</span
          >发送终止信号
        </p></template
      >
      <Form :model="formItem" ref="formItem" :label-width="120">
        <FormItem label="终止信号">
          <Input
            v-model="formItem.signal"
            placeholder="请输入要发送的终止信号"
          ></Input>
        </FormItem>
      </Form>
      <p style="color:#ccc">
        提示：发送给容器的信号:整数或类似SIGINT的字符串。如果没有设置，则设置SIGKILL作为默认值，容器将退出。支持的信号因平台而异。此外，您可以省略“SIG前缀”。
      </p>
      <div slot="footer">
        <Button type="text" @click="model = false">取消</Button>
        <Button type="primary" @click="modalOK" :disabled="disabled"
          >确认</Button
        >
      </div>
    </Modal>
  </div>
</template>
<script>
import { containerKill } from "@/api/container"; // 容器表 重启容器
export default {
  props: {
    row: Object,
    signalTime: String,
  },
  watch: {
    signalTime(news) {
      this.model = true;
      this.formItem.id = this.row.uuid;
      this.formItem.name = this.row.name;
      this.formItem.signal = "";
    },
  },
  data() {
    return {
      model: false,
      disabled: false,
      formItem: {
        id: "",
        name: "",
        signal: "",
      },
    };
  },
  methods: {
    modalOK() {
      this.$refs.formItem.validate((valid) => {
        if (valid) {
          this.disabled = true;
          containerKill(this.formItem)
            .then((callback) => {
              this.model = false;
              this.$emit("return-ok", {
                msg: "发送终止信号操作已完成",
                type: "ok",
              });
            })
            .catch((error) => {
              this.disabled = false;
              this.$emit("return-ok", {
                msg: "发送终止信号操作失败",
                type: "error",
              });
            });
        }
      });
    },
  },
};
</script>