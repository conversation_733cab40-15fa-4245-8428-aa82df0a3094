<template>
  <!--  启动、停止、暂停、恢复 -->
  <div>
    <Modal v-model="model" width="600" :mask-closable="false">
      <template #header><p>{{type}}容器</p></template>
      <div style="padding: 5px">
        <span>是否{{type}}下列容器？</span>
        <p :style="{color: type == '启动'||type == '恢复'?'green':'red','word-wrap':'break-word'}">
          {{ tableNames.toString() }}
        </p>
      </div>
      <div slot="footer">
        <Button type="text" @click="model = false">取消</Button>
        <Button type="primary" @click="modelOK" :disabled="disabled">确认</Button>
      </div>
    </Modal>
  </div>
</template>
<script>
import {containerOperate} from '@/api/container'; // 容器表 操作

export default {
  props: {
    tableArr: Array,
    manyTime: String,
  },
  watch: {
    manyTime(news) {
      this.type = news.split("/")[0]
      this.tableNames = this.tableArr.map((em) => {
        return em.name;
      });
      this.tableIDS = this.tableArr.map((em) => {
        return em.uuid;
      });
      this.model = true;
      this.disabled = false;
    },
  },
  data() {
    return {
      model: false,
      disabled: false,
      type: '',
      tableNames: [],
      tableIDS: [],
    };
  },
  methods: {
    modelOK() {
      let list = {
        id: this.tableIDS[0],
        name: this.tableNames[0],
        action: '',
      }
      if(this.type == '启动') {
        list.action = 'start'
      }else if(this.type == '停止') {
        list.action = 'stop'
      }else if(this.type == '暂停') {
        list.action = 'pause'
      }else if(this.type == '恢复') {
        list.action = 'unpause'
      }
      this.disabled = true;
      containerOperate(list)
        .then((callback) => {
          this.model = false;
          if (callback.data.msg == "ok") {
            setTimeout(() => {
              this.$emit("return-ok", {
                msg: this.type+"容器操作完成",
                type: "ok",
              });
            }, 500);
          } else {
            this.$emit("return-ok", {
              msg: this.type+"容器操作失败",
              type: "error",
            });
          }
        })
        .catch((error) => {
          this.disabled = false
          this.$emit("return-ok", {
            msg: this.type+"容器操作失败",
            type: "error",
          });
        });
    },
  },
};
</script>