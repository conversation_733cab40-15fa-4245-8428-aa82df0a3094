
<template>
  <div class="container_area">
    <!-- 局部加载装置 -->
    <Spin fix v-if="spinShow" size="large" style="color: #ef853a">
      <Icon type="ios-loading" size="50" class="demo-spin-icon-load"></Icon>
      <div style="font-size: 16px; padding: 20px">Loading...</div>
    </Spin>
    <!-- 侧边虚拟机分组 -->
    <div class="table-button-area">
      <div>
        <Button class="plus_btn" @click="newClick"><span class="icon iconfont icon-plus-circle"></span> 拉取容器镜像</Button>
        <Button class="plus_btn" @click="uploadClick"><Icon type="md-cloud-upload" /> 上传容器镜像</Button>
        <Button class="close_btn" @click="deletClick(tableSelec)"
          ><span class="icon iconfont icon-close-circle"></span>
          删除容器镜像</Button
        >
        <Button class="close_btn" @click="tableQuery">刷新</Button>
      </div>
      <div>
        <Input
          v-model="tablePageForm.search_str"
          search
          enter-button
          placeholder="请输入名称"
          style="width: 300px"
          @on-search="tableRQsearchInput"
        />
      </div>
    </div>
    <Table
      :columns="columns"
      :data="data"
      @on-sort-change="sortChange"
      @on-selection-change="slectChange"
    >
      <!-- 名称 -->
      <template v-slot:name="{ row }">
        <!-- <Tooltip :content="row.repo+':'+row.tag" style="width: 100%"> -->
          <span class="text_overflow">{{ row.repo }}:{{row.tag}}</span>
        <!-- </Tooltip> -->
      </template>
      <!-- 大小 -->
      <template v-slot:size="{ row }">
        <span>{{ byteUnitConversion(row.size) }}</span>
      </template>
      <!-- 操作 -->
      <template v-slot:operation="{ row }">
        <Button class="close_btn" @click="deletClick([row])">删除</Button>
      </template>
    </Table>
    <!-- 虚拟机表格 分页  -->
    <div class="pages" v-if="this.data.length > 0">
      <!-- <Page
        :total="tableTotal"
        show-total
        show-sizer
        :page-size="tablePageForm.pagecount"
        placement="top"
        :page-size-opts="[10, 20, 30]"
        @on-change="onPageChange"
        @on-page-size-change="onPageSizeChange"
      /> -->
      <Pagination
        :total="tableTotal"
        :page-size="tablePageForm.pagecount"
        @page-change="onPageChange"
        @page-size-change="onPageSizeChange"
      />
    </div>
    <TableNew :newTime="newTime" @return-ok="returnOK"></TableNew>
    <TableUpload :uploadTime="uploadTime" @return-ok="returnOK"></TableUpload>
    <TableDeleted
      :deletionTime="deletionTime"
      :tableArr="tableArr"
      @return-ok="returnOK"
    ></TableDeleted>
  </div>
</template>
<script>
import {
  containerImageQuery, // 容器镜像表 查询
} from '@/api/container'; 
import Pagination from "@/components/public/Pagination.vue";
import TableNew from "./TableNew.vue";
import TableUpload from "./TableUpload.vue";
import TableDeleted from "./TableDeleted.vue";

export default {
  components: {
    Pagination,
    TableNew,
    TableUpload,
    TableDeleted,
  },

  props: {
    tabSelected: String,
  },
  data() {
    return {
      spinShow: false,
      columns: [],
      data: [],
      tableSelec: [],
      selectName: [],
      selectID: [],
      tableTotal: 0,
      tablePageForm: {
        page: 1, // 当前页
        pagecount: this.$store.state.power.pagecount, // 每页条
        search_str: "", // 收索
        order_type: "asc", // 排序规则
        order_by: "", // 排序列
      },
      newTime: "", // 新建容器
      uploadTime: "", // 上传容器
      deletionTime: "", // 删除容器
      tableArr: [],
    };
  },
  watch: {
    tabSelected(value) {
      if (value == "容器镜像") {
        this.tableColumn(); // 表列
        this.tableQuery(); // 表数据
      } else {
        this.tableColumn(); // 表列
      }
    },
  },
  methods: {
    // 获取容器表格数据
    tableQuery() {
      this.spinShow = true;
      if(!true){
        setTimeout(() => {
          this.spinShow = false;
          this.tableSelec = new Array();
          this.data = [{id:'aaa',repo:'aaaa',tag:'5000/ubuntu:22.04'},{id:'bb',name:'bbbb'}];
          this.tableTotal = this.data.length;
        }, 1000);
      }else {
        containerImageQuery(this.tablePageForm)
        .then((callback) => {
          this.spinShow = false;
          this.tableSelec = new Array();
          this.tableTotal = callback.data.total;
          this.data = callback.data.data;
        })
        .catch((error) => {
          this.spinShow = false;
        });
      }
    },
    // 表格选中数据
    slectChange(item) {
      this.tableSelec = item;
      let Name = new Array();
      let ID = new Array();
      item.forEach((it) => {
        Name.push(it.name);
        ID.push(it.uuid);
      });
      this.selectName = Name;
      this.selectID = ID;
    },
    // 当前分页
    onPageChange(item) {
      this.tablePageForm.page = item;
      this.tableQuery();
    },
    // 每页条数
    onPageSizeChange(item) {
      this.$store.state.power.pagecount = item;
      this.tablePageForm.pagecount = this.$store.state.power.pagecount;
      this.tablePageForm.page = 1;
      this.tableTotal = 0;
      this.tablePageForm.search_str = "";
      this.tableQuery();
    },
    // 列表 列排序
    sortChange(column, key, order) {
      if (column.key !== "normal") {
        this.tablePageForm.order_by = column.key;
        this.tablePageForm.order_type = column.order;
        this.tablePageForm.page = 1;
        this.tableTotal = 0;
        this.tablePageForm.search_str = "";
        this.tableQuery();
      }
    },
    // 列表 搜索
    tableRQsearchInput() {
      this.tablePageForm.page = 1;
      this.tableTotal = 0;
      this.tableQuery();
    },
    // 新建容器
    newClick() {
      this.newTime = "" + new Date();
    },
    // 上传容器镜像
    uploadClick() {
      this.uploadTime = "" + new Date();
    },
    // 删除容器弹框
    deletClick(data) {
      if (data == 0) {
        this.packageWarning("未选择表格数据");
      } else {
        this.tableArr = data;
        this.deletionTime = "" + new Date();
      }
    },
    // 表格列获取
    tableColumn() {
      this.columns = [
        { type: "selection", width: 30, align: "center" },
        {
          type: "expand",
          width: 50,
          render: (h, params) => {
            return h("div", { style: { width: "100%" } }, [
              h(
                "span",
                { style: { width: "45%", display: "inline-block" } },
                "ID：" + params.row.uuid
              ),
              h(
                "span",
                { style: { width: "45%", display: "inline-block" } },
                "image_id：" + params.row.image_id
              ),
              // h(
              //   "span",
              //   { style: { width: "45%", display: "inline-block" } },
              //   "image_id：" + params.row.image_id
              // ),
              // h("span",'名称：'+params.row.name)
            ]);
          },
        },
        { title: "容器镜像名称", key: "name", sortable: "custom", slot: "name" },
        { title: "大小", key: "size", sortable: "custom", align: "center", slot: "size" },
        // { title: "大小", key: "size", sortable: "custom",tooltip:true, align: "center" },
        { title: "主机", key: "host", align: "center" },
        { title: "操作", key: "operation", width: 80, slot: "operation" },
      ];
    },
    // 弹窗返回
    returnOK(item) {
      if(item.type == 'ok') {
        this.packageOK(item.msg)
      }else{
        this.packageError(item.msg)
      }
    },
    // 封装告警提示
    packageWarning(data) {
      this.$Message.warning({
        background: true,
        closable: true,
        duration: 5,
        content: data,
      });
    },
    packageOK(data) {
      this.tableQuery();
      this.$Message.success({
        background: true,
        closable: true,
        duration: 5,
        content: data,
      });
    },
    packageError(data) {
      this.$Message.error({
        background: true,
        closable: true,
        duration: 5,
        content: data,
      });
    },
    // 字节+单位转换
    byteUnitConversion(size) {
      if (size == undefined) {
        return "-";
      }
      const units = ["B", "KB", "MB", "GB", "TB", "PB"];
      let unitIndex = 0;
      while (size >= 1024 && unitIndex < units.length - 1) {
        size /= 1024;
        unitIndex++;
      }
      return Math.floor(size * 100) / 100 + " " + units[unitIndex];
    },
  },
  beforeDestroy() {
  },
};
</script>
<style lang="less">
.container_area {
  height: 100%;
  border-radius: 10px;
  background-color: #fff;
  padding: 15px 5px;
  position: relative;
}
</style>
