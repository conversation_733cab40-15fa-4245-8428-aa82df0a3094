<template>
  <div>
    <Modal
      v-model="model"
      width="600"
      :mask-closable="false"
    >
      <template #header>
       <p><span style="color:green">{{vmRow.name}}</span>虚拟机删除网卡</p>
      </template>
      <div style="text-align:center">
        <Form :label-width="120">
          <FormItem label="选择IP">
            <Select v-model="formItem.networkcard">
              <Option
                v-for="item in ipData"
                :value="item.addr"
                :key="item.addr"
                >{{ item.addr }}</Option
              >
            </Select>
          </FormItem>
        </Form>
      </div>
      <template #footer>
        <Button type="text" @click="model=false">取消</Button>
        <Button type="info" class="plus_btn" @click="modelOK" :disabled="disabled">确定</Button>
      </template>
    </Modal>
  </div>
</template>
<script>
import {vmGroupTableDeleteAdapter} from '@/api/virtualMachine';  // 删除网卡
export default {
  props: {
    vmRow: Object,
    deleteipTime: String,
  },
  watch: {
    deleteipTime(news){
      this.ipData = this.vmRow.addresses
      this.formItem.networkcard = this.vmRow.addresses[0].addr
      this.formItem.vmid = this.vmRow.id
      this.formItem.vmName = this.vmRow.name
      this.model = true
      this.disabled = false
    }
  },
  data(){
    return {
      disabled:false,
      model:false,
      ipData:[],
      formItem:{
        vmid:"",
        vmName:"",
        networkcard:"",
      },
    }
  },
  methods: {
    // 删除网卡
    modelOK() {
      this.disabled = true
      this.$Modal.confirm({
        title: this.formItem.vmName+"虚拟机删除网卡",
        content:
          '<p>是否删除<span style="font-weight: 600;color:red;" >' +
          this.formItem.networkcard +
          "</span>网卡?</p>",
        onOk: () => {
          this.model=false
          vmGroupTableDeleteAdapter({
            data: {
              id: this.formItem.vmid,
              ipv4: this.formItem.networkcard,
              name: this.formItem.vmName,
            }
          })
          .then((callback)=>{
            if(callback.data.msg=="删除成功"){
              this.$emit("return-ok",'删除网卡操作完成');
            }else {
              this.disabled = false
              this.$emit("return-error",callback.data.msg);
            }
          })
          .catch((error) => {
            this.disabled = false
            this.$emit("return-error",'删除网卡操作失败');
          })
        },
        onCancel: () => {},
      });
    },
    cancel(){},
  }
}
</script>