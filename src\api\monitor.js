// 监控
import axios from "axios";
import * as basic_proxy from "@/api/config";

// 物理机/虚拟机（windows/linux）监控 查询 (旧接口已不再使用)
export async function monitorDataQuery() {
  return await axios.get(basic_proxy.theapi + "/v1/monitor");
}

// 存储集群/存储池监控查询 (旧接口已不再使用)
export async function storageDataQuery() {
  return await axios.get(basic_proxy.theapi + "/v1/ceph");
}

// 物理机 基本数据 查询
export async function physicalMachineBasicQuery() {
  return await axios.get(basic_proxy.theapi + "/v1/all/clusters");
}
// 物理机 CPU数据 查询
export async function physicalMachineCPUquery(params) {
  return await axios.post(basic_proxy.theapi + "/v1/monitor/host/cpu", params);
}
// 物理机 内存数据 查询
export async function physicalMachineMemoryQuery(params) {
  return await axios.post(basic_proxy.theapi + "/v1/monitor/host/mem", params);
}
// 物理机 磁盘数据 查询
export async function physicalMachineDiskQuery(params) {
  return await axios.post(
    basic_proxy.theapi + "/v1/monitor/host/disk",
    params
  );
}
// 物理机 网络数据 查询
export async function physicalMachineNetworkQuery(params) {
  return await axios.post(
    basic_proxy.theapi + "/v1/monitor/host/network",
    params
  );
}


// 虚拟机 基本数据 查询
export async function vmBasicQuery() {
  return await axios.get(basic_proxy.theapi + "/v1/monitor/vm/home");
}
// 虚拟机 CPU数据 查询
export async function vmCPUquery(params) {
  return await axios.post(basic_proxy.theapi + "/v1/monitor/vm/cpu", params);
}
// 虚拟机 内存数据 查询
export async function vmMemoryQuery(params) {
  return await axios.post(basic_proxy.theapi + "/v1/monitor/vm/mem", params);
}
// 虚拟机 磁盘数据 查询
export async function vmDiskQuery(params) {
  return await axios.post(basic_proxy.theapi + "/v1/monitor/vm/disk", params);
}
// 虚拟机 网络数据 查询
export async function vmNetworkQuery(params) {
  return await axios.post(
    basic_proxy.theapi + "/v1/monitor/vm/network",
    params
  );
}


// 存储 基本数据 查询
export async function storageMonitorBasicQuery() {
  return await axios.get(basic_proxy.theapi + "/v1/monitor/storage/base");
}
// 存储 节点信息 查询
export async function storageNodeInformationQuery() {
  return await axios.get(basic_proxy.theapi + "/v1/ceph/osd");
}

// 存储 集群 存储使用率数据 查询
export async function storageMonitorStorageQuery(params) {
  return await axios.post(
    basic_proxy.theapi + "/v1/monitor/storage/capacity",
    params
  );
}
// 存储 集群  内存使用率数据 查询
export async function storageMonitorMemoryQuery(params) {
  return await axios.post(
    basic_proxy.theapi + "/v1/monitor/storage/mem",
    params
  );
}
// 存储 集群 IOPS数据 查询
export async function storageMonitorIOPSquery(params) {
  return await axios.post(
    basic_proxy.theapi + "/v1/monitor/storage/iops",
    params
  );
}
// 存储 集群 吞吐量 查询
export async function storageMonitorThroughputQuery(params) {
  return await axios.post(
    basic_proxy.theapi + "/v1/monitor/storage/throughput",
    params
  );
}

// 存储 节点 iops读 查询
export async function storagIOPSreadQuery(params) {
  return await axios.post(
    basic_proxy.theapi + "/v1/monitor/storage/nodeior",
    params
  );
}
// 存储 节点 iops写 查询
export async function storagIOPSwriteQuery(params) {
  return await axios.post(
    basic_proxy.theapi + "/v1/monitor/storage/nodeiow",
    params
  );
}
// 存储 节点 网络上行 查询
export async function storagNetworkupQuery(params) {
  return await axios.post(
    basic_proxy.theapi + "/v1/monitor/storage/networkup",
    params
  );
}
// 存储 节点 网络下行 查询
export async function storagNetworkdownQuery(params) {
  return await axios.post(
    basic_proxy.theapi + "/v1/monitor/storage/networkdown",
    params
  );
}

