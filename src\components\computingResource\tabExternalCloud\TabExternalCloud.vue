<style lang="less">
@import "./tabExternalCloud.less";
</style>
<template>
  <div class="multi_cloud_management_area">
    <!-- 侧边外部云管理 -->
    <div class="multi_cloud_management_group">
      <div class="multi_cloud_management_title">
        <p >外部云管理</p>
      </div>
      <div class="nanotube_navigation">
        
        <Tooltip content="添加外部云" placement="top-start" v-show="powerAcitons.waibuyuntianjia">
          <Button
            type="text"
            @click="newlyWBYclick"
            style="padding: 0px"
            icon="md-add-circle"
          ></Button>
        </Tooltip>
        <Tooltip content="删除外部云" placement="top" v-show="powerAcitons.waibuyunshanchu">
          <Button
            type="text"
            @click="deletWBYclick"
            :disabled="disabledeWBY"
            style="padding: 0px"
            icon="ios-trash"
          ></Button>
        </Tooltip>
      </div>
      <div class="vm_tree_area" v-show="powerAcitons.waibuyunliebiao">
        <Tree
          :data="cloudData"
          :render="renderContent"
          class="demo-tree-render"
          @on-select-change="treeWBYclick"
        ></Tree>
      </div>
    </div>
    <!-- 新建外部云 弹框 -->
    <Modal v-model="newGroupModal" width="600" :mask-closable="false">
      <p slot="header" >
        <span>添加外部云</span>
      </p>
      <Form :model="formGroup" ref="formGroup" :rules="rulesForm" :label-width="120">
        <FormItem label="外部云 IP" prop="ip">
          <Input
            v-model="formGroup.ip"
            placeholder="请输入外部云 IP"
          ></Input>
        </FormItem>
        <FormItem label="外部云账号" prop="username">
          <Input
            v-model="formGroup.username"
            placeholder="请输入外部云账号"
          ></Input>
        </FormItem>
        <FormItem label="外部云密码" prop="password">
          <Input
            v-model="formGroup.password"
            type="password"
            password
            placeholder="请输入外部云密码"
          ></Input>
        </FormItem>
      </Form>
      <div slot="footer">
        <Button type="text" @click="newGroupModal = false">取消</Button>
        <Button type="primary" @click="newGroupOK" :disabled="newGroupDisabled">确认</Button>
      </div>
    </Modal>
    
    <!-- 虚拟机 表数据 -->
    <div class="multi_cloud_table_area">
      <Spin fix v-if="spinShow" size="large" style="color:#ef853a">
        <Icon type="ios-loading" size=50 class="demo-spin-icon-load"></Icon>
        <div style="font-size:16px;padding:20px">Loading...</div>
      </Spin>
      <div class="multi_cloud_table_title">
        <p>虚拟机列表</p>
        <div style="display: flex" v-show="powerAcitons.xunijitiaojianchaxun">
          <!-- <Button type="text" @click="statusSEE=true"><span class="icon iconfont icon-renwu"></span> 迁移任务</Button> -->
          <Input v-model="tablePageForm.search_str" search enter-button placeholder="请输入名称或VM_ID" style="width: 300px" @on-search="tableVMsearchInput" />
        </div>
      </div>
      <!-- 虚拟机表格  -->
      <div class="table_currency_area"  v-show="powerAcitons.waibuyunliebiao">
        <Table
          :columns="vmcolumn"
          :data="vmdata"
          @on-sort-change="sortColumn"
          @on-selection-change="tableSlectXNJdata"
        ></Table>
        <!-- 虚拟机表格 分页  -->
        <div class="pages" v-if="this.vmdata.length>0">
          <!-- <Page
            :total="tableTotal"
            show-total
            show-sizer
            :page-size="tablePageForm.pagecount"
            placement="top"
            :page-size-opts="[10, 20, 30]"
            @on-change="onPageChange"
            @on-page-size-change="onPageSizeChange"
          /> -->
          <Pagination  :total="tableTotal" :page-size="tablePageForm.pagecount"  @page-change="onPageChange" @page-size-change="onPageSizeChange"/>
        </div>
      </div>
    </div>

    <!-- 迁移虚拟机 弹框 -->
    <Modal
      v-model="transferModal" width="600" :mask-closable="false">
      <p slot="header" >
        <span>外部云虚拟机迁移</span>
      </p>
      <Form :model="cloudVMform" ref="cloudVMform" :label-width="120" :rules="ruleTransfer">
        <FormItem label="虚拟机名称" prop="vm_name">
          <Input v-model="cloudVMform.vm_name"></Input>
        </FormItem>
        <div v-if="false">
          <FormItem label="操作系统">
            <RadioGroup v-model="cloudVMform.vm_os" class="cloudOS">
                <Radio label="windows">
                  <Icon type="logo-windows"></Icon>
                  <span>Windows</span>
                </Radio>
                <Radio label="linux">
                  <Icon type="logo-tux"></Icon>
                  <span>Linux</span>
                </Radio>
                <Radio label="apple">
                  <Icon type="logo-apple"></Icon>
                  <span>Apple</span>
                </Radio>
            </RadioGroup>
          </FormItem>
          <FormItem label="版本">
            <Select v-model="cloudVMform.vm_os_version">
              <Option
                v-for="(item,i) in cloudVersion"
                :value="item.name"
                :key="item.i"
                >{{ item.name }}</Option
              >
            </Select>
          </FormItem>
        </div>
        <FormItem label="集群组" prop="availability_zone">
          <Select v-model="cloudVMform.availability_zone">
            <Option
              v-for="item in cloudAvailable"
              :value="item.name"
              :key="item.key"
              >{{ item.name }}</Option
            >
          </Select>
        </FormItem>
        <FormItem label="网络" prop="network_uuid">
          <Select v-model="cloudVMform.network_uuid">
            <Option
              v-for="item in cloudNETworks"
              :value="item.key"
              :key="item.key"
              >{{ item.name }}</Option
            >
          </Select>
        </FormItem>
        <FormItem label="CPU">
          <div class="slider_area">
            <div style="width:350px">
              <Slider
                v-model="cloudVMform.vm_cpu"
                :min="1"
                :max="maxORmin.cpumax"
                :tip-format="formCpu"
              ></Slider>
            </div>
            <div style="width:80px">
              <InputNumber :min="1" :max="maxORmin.cpumax" v-model="cloudVMform.vm_cpu" :formatter="value => `${value}核`" :parser="value => value.replace('核', '')"></InputNumber>
            </div>
          </div>
        </FormItem>
        <FormItem label="内存容量">
          <div class="slider_area">
            <div style="width:350px">
              <Slider
                v-model="cloudVMform.vm_ram"
                :min="1"
                :max="maxORmin.rammax"
                :tip-format="formMemory"
              ></Slider>
            </div>
            <div style="width:80px">
              <InputNumber :min="1" :max="maxORmin.rammax" v-model="cloudVMform.vm_ram" :formatter="value => `${value}GB`" :parser="value => value.replace('GB', '')"></InputNumber>
            </div>
          </div>
        </FormItem>
        <div v-for="(item, i) in cloudDisks" :key="i">
          <FormItem :label="item.key">
            <div class="slider_area">
              <div style="width:350px">
                <Slider
                  v-model="cloudVMform.vm_disks[i]"
                  :min="1"
                  :max="maxORmin.diskmax"
                  :tip-format="formDisk"
                ></Slider>
              </div>
              <div style="width:80px">
                <InputNumber :min="1" :max="maxORmin.diskmax" v-model="cloudVMform.vm_disks[i]" :formatter="value => `${value}GB`" :parser="value => value.replace('GB', '')"></InputNumber>
              </div>
            </div>
          </FormItem>
        </div>
      </Form>
      <div slot="footer">
        <Button type="text" @click="transferModal = false">取消</Button>
        <Button type="primary" @click="transferOK" :disabled="transferDisabled">确认</Button>
      </div>
    </Modal>
    <!-- 迁移详情抽屉 -->
    <Drawer title="迁移任务" width="50%" :closable="false" v-model="statusSEE">
      <TransferTask :taskBoolean="statusSEE"></TransferTask>
    </Drawer>
  </div>
</template>
<script>
import { powerCodeQuery } from '@/api/other'; // 权限可用性 查询

import {physicsTableAllQuery} from '@/api/physics';
import {networkTableQuery} from '@/api/network';
import TransferTask from "./transferTask/TransferTask.vue";
import Pagination from '@/components/public/Pagination.vue';
import {
  externalCloudGroupQuery, // 外部云组 查询
  externalCloudGroupAdd, // 外部云组 添加
  externalCloudGroupDelete, // 外部云组 删除
  externalCloudTableQuery, // 外部云表格 查询
  externalCloudTableOpen, // 外部云表格 开机
  externalCloudTableClose, // 外部云表格 关机
  externalCloudTableMigrate, // 外部云表格 迁移
  migrateTaskQuery, // 迁移任务查询
} from '@/api/externalCloud';
export default {
  components:{
    TransferTask,
    Pagination
  },
  props: {
    tabSelected: String,
  },
  data() {
    const propXJname = (rule, value, callback) => {
      // let list = /^([0-9]|[1-9][0-9]|1[0-9]{1,2}|2[0-4][0-9]|25[0-5])\.([0-9]|[1-9][0-9]|1[0-9]{1,2}|2[0-4][0-9]|25[0-5])\.([0-9]|[1-9][0-9]|1[0-9]{1,2}|2[0-4][0-9]|25[0-5])\.([0-9]|[1-9][0-9]|1[0-9]{1,2}|2[0-4][0-9]|25[0-5])$/;
      // let list1 = /^((25[0-5]|2[0-4]\d|[01]?\d\d?)($|(?!\.$)\.)){4}$/;
      // if (list1.test(value)) {
      //   callback();
      // } else {
      //   callback(new Error("请输入正确的IP地址"));
      // }
    };
    const propVMname = (rule, value, callback) => {
      let list = /^[\u4e00-\u9fa5_a-zA-Z0-9@_.]{2,32}$/;
      if(list.test(value)){
        callback()
      }else{
        callback(new Error('请输入2-32个中文或字符（特殊字符可用@_.）'))
      }
    }
    return {
    // 虚拟机分组
      // 树形图分组数据
      cloudData: [],
      // 监听树形图分组数据选中项
      treeWBYdatas: [],
      // 新建分组弹框
      newGroupModal: false,
      newGroupDisabled:false,
      formGroup: {
        ip: "",
        username: "",
        password: "",
      },
      // 正则判断用户输入
      rulesForm: {
        ip: [
          { required: true, message: "必填项", trigger: "change" },
          // { validator: propXJname, trigger: "change" },
        ],
        username: [
          { required: true, message: "必填项", trigger: "change" },
          { max: 64, message: "长度不可超过64个英文或数字", trigger: "change" },
        ],
        password: [
          { required: true, message: "必填项", trigger: "change" },
          { max: 64, message: "长度不可超过64个英文或数字", trigger: "change" },
        ],
      },
      // 编辑分组弹框
      editWBYmodal: false,
      // 刷新外部云
      loadRefresh: false,
      // 删除外部云
      disabledeWBY:true,
    // 虚拟机列表
      spinShow:false,
      vmcolumn: [],
      vmdata: [],
      // 虚拟机表格数据需求
      tablePageForm: {
        god:0,
        id:'',
        level:'',
        page: 1, // 当前页
        pagecount: this.$store.state.power.pagecount, // 每页条
        search_str: "", // 收索
        order_type: "asc", // 排序规则
        order_by: "", // 排序列
      },
      // 虚拟机分页总条数
      tableTotal: 0,
      // 计时器
      timerss: null,

      // 虚拟机迁移
      transferModal:false,
      transferDisabled:false,
      cloudVMform:{
        cloudy_id:'',
        availability_zone:'',
        network_uuid:'',
        vmware_id:'',
        vmware_name:'',
        vm_name:'',
        vm_os:'windows',
        vm_os_version:'',
        vm_cpu:1,
        vm_ram:1,
        vm_disks:[],
      },
      // 外部云虚拟机迁移 正则
      ruleTransfer: {
        vm_name: [
          { required: true, message: "必填项", trigger: "change" },
          { validator: propVMname, trigger: "change" },
        ],
        availability_zone: [{ required: true, message: "必选项", trigger: "change" }],
        network_uuid: [{ required: true, message: "必选项", trigger: "change" }],
        
      },
      maxORmin:{
        cpumin:1,
        cpumax:64,
        rammin:1,
        rammax:256,
        diskmax:2048,
      },
      // 版本 
      cloudVersion: [
        { name: "Microsoft Windows Server 2019(64位)", id: 2 },
        { name: "Microsoft Windows Server 2016(64位)", id: 3 },
        { name: "Microsoft Windows Server 2012 R2(64位)", id: 4 },
        { name: "Microsoft Windows Server 2012(64位)", id: 1 },
        { name: "Microsoft Windows Server 2008 R2(64位)", id: 5 },
        { name: "Microsoft Windows Server 2008(64位)", id: 6 },
        { name: "Microsoft Windows Server 2008(32位)", id: 7 },
        { name: "Microsoft Windows Server 2003 R2(64位)", id: 8 },
        { name: "Microsoft Windows Server 2003(64位)", id: 9 },
        { name: "Microsoft Windows Server 2003(32位)", id: 10 },
        { name: "Microsoft Windows 10(64位)", id: 11 },
        { name: "Microsoft Windows 10(32位)", id: 12 },
        { name: "Microsoft Windows 8.1(64位)", id: 13 },
        { name: "Microsoft Windows 8.1(32位)", id: 14 },
        { name: "Microsoft Windows 8(64位)", id: 15 },
        { name: "Microsoft Windows 8(32位)", id: 16 },
        { name: "Microsoft Windows 7(64位)", id: 17 },
        { name: "Microsoft Windows 7(32位)", id: 18 },
        { name: "Microsoft Windows XP(64位)", id: 19 },
        { name: "Microsoft Windows XP(32位)", id: 20 },
        { name: "Red Hat Enterprise Linux 8(64位)", id: 21 },
        { name: "Red Hat Enterprise Linux 8(32位)", id: 22 },
        { name: "Red Hat Enterprise Linux 7(64位)", id: 23 },
        { name: "Red Hat Enterprise Linux 7(32位)", id: 24 },
        { name: "Red Hat Enterprise Linux 6(64位)", id: 25 },
        { name: "Red Hat Enterprise Linux 6(32位)", id: 26 },
        { name: "Red Hat Enterprise Linux 5(64位)", id: 27 },
        { name: "Red Hat Enterprise Linux 5(32位)", id: 28 },
        { name: "CentOS 8(64位)", id: 29 },
        { name: "CentOS 8(32位)", id: 30 },
        { name: "CentOS 7(64位)", id: 31 },
        { name: "CentOS 7(32位)", id: 32 },
        { name: "CentOS 6(64位)", id: 33 },
        { name: "CentOS 6(32位)", id: 34 },
        { name: "CentOS 5(64位)", id: 35 },
        { name: "CentOS 5(32位)", id: 36 },
        { name: "Ubuntu Linux(64位)", id: 37 },
        { name: "Ubuntu Linux(32位)", id: 38 },
        { name: "Oracle Linux7(64位)", id: 39 },
        { name: "Oracle Linux7(32位)", id: 40 },
        { name: "Oracle Linux6(64位)", id: 41 },
        { name: "Oracle Linux6(32位)", id: 42 },
        { name: "Oracle Linux5(64位)", id: 43 },
        { name: "Oracle Linux5(32位)", id: 44 },
        { name: "Oracle Linux4(64位)", id: 45 },
        { name: "Oracle Linux4(32位)", id: 46 },
        { name: "银河麒麟服务器版V10(64位)", id: 47 },
        { name: "银河麒麟桌面版V10(64位)", id: 48 },
        { name: "UOS 20 SP1 桌面版(64位)", id: 49 },
        { name: "UOS SP2 桌面版(64位)", id: 50 },
        { name: "Other Linux(64位)", id: 51 },
        { name: "Other Linux(64位)", id: 52 },
      ],
      // 可用域 
      cloudAvailable:[],
      // 网络  
      cloudNETworks:[],
      // 硬盘容量
      cloudDisks:[],
      // 迁移详情抽屉
      statusSEE:false,

      powerAcitons: {}, // 操作权限数据
    };
  },
  watch: {
    tabSelected(value) {
      if(value=='多云纳管'){
        this.actionQuery()
      }
    },
    'tablePageForm.id':{
      handler (news,old) {
        if(news==this.tablePageForm.god) {
          this.disabledeWBY = false
        }else {
          this.disabledeWBY = true
        }
      }
    }
  },
  mounted() {
    if(this.$store.state.power.computingResourceTab == '多云纳管') {
      this.actionQuery()
    }
  },
  updated() {
    this.tablePageForm.pagecount=this.$store.state.power.pagecount
  },
  methods: {
  // 外部云管理
    // 外部云树形数据获取
    treeGET() {
      externalCloudGroupQuery()
      .then((callback) => {
        if(callback.data==0) {
          console.log("无数据")
        }else {
          this.cloudData = callback.data;
          this.tablePageForm.god =callback.data[0].god
          this.tablePageForm.id =callback.data[0].id
          this.tablePageForm.level =callback.data[0].level
          this.tablePageForm.page =1
          this.tablePageForm.pagecount =this.$store.state.power.pagecount
          this.tablePageForm.search_str =""
          this.tablePageForm.order_type ="asc"
          this.tablePageForm.order_by =""
          this.tableXNJpost()
        }
      })
    },
    // 树形图添加图标
    renderContent(h, { root, node, data }) {
      switch (data.level) {
        case "cloudy":
          return h("span",{
              style: {
                display: "inline-block",
                width: "100%",
              },
            },
            [
              h("span", [
                h("Icon", {
                  props: {
                    type: "md-cloud",
                  },
                  style: {
                    marginRight: "8px",
                    fontSize: "18px",
                    color:"#f1b223"
                  },
                }),
                h("span",{
                  style: {
                    width:'100%'
                  },
                },data.title),
              ]),
            ]
          );
          break;
        case "datacenter":
          return h("span",{
              style: {
                display: "inline-block",
                width: "100%",
              },
            },
            [
              h("span", [
                h("Icon", {
                  props: {
                    type: "md-cube",
                  },
                  style: {
                    marginRight: "8px",
                    fontSize: "18px",
                    color:"#f1b223"
                  },
                }),
                h("span", data.title),
              ]),
            ]
          );
          break;
        case "cluster":
          return h("span",{
              style: {
                display: "inline-block",
                width: "100%",
              },
            },
            [
              h("span", [
                h("Icon", {
                  props: {
                    type: "ios-apps",
                  },
                  style: {
                    marginRight: "8px",
                    fontSize: "18px",
                    color:"#f1b223"
                  },
                }),
                h("span", data.title),
              ]),
            ]
          );
          break;
        case "host":
          return h("span",{
              style: {
                display: "inline-block",
                width: "100%",
              },
            },
            [
              h("span", [
                h("Icon", {
                  props: {
                    type: "ios-list-box",
                  },
                  style: {
                    marginRight: "8px",
                    fontSize: "18px",
                    color:"#f1b223"
                  },
                }),
                h("span", data.title),
              ]),
            ]
          );
          break;
      }
    },
    // 外部云 点击树形节点 事件
    treeWBYclick(arr) {
      if(arr.length > 0) {
        this.treeWBYdatas = arr;
        this.tablePageForm.god =arr[0].god
        this.tablePageForm.id =arr[0].id
        this.tablePageForm.level =arr[0].level
        this.tablePageForm.page =1
        this.tablePageForm.pagecount =this.$store.state.power.pagecount
        this.tablePageForm.search_str =""
        this.tablePageForm.order_type ="asc"
        this.tablePageForm.order_by =""
        this.tableXNJpost()
      }
    },
    // 添加外部云 按钮
    newlyWBYclick() {
      this.newGroupModal = true;
      this.newGroupDisabled = false;
      this.$refs['formGroup'].resetFields()
    },
    // 添加外部云 弹框确认事件
    newGroupOK() {
      this.newGroupDisabled = true;
      this.$refs['formGroup'].validate((valid) => {
        if(valid){
          externalCloudGroupAdd(this.formGroup)
          .then((callback) => {
            if(callback.data.msg == "ok") {
              this.newGroupModal = false;
              this.treeGET()
            }else {
              this.newGroupDisabled = false;
              this.$Message.error({
                background: true,
                closable: true,
                duration: 5,
                content: callback.data.msg,
              });
            }
          }).catch((error) => {
            this.newGroupDisabled = false
            this.$Message.error({
              background: true,
              closable: true,
              duration: 5,
              content: '外部云连接失败',
            });
          })
        }else {
          this.newGroupDisabled = false;
        }
      })
    },
    // 删除外部云 按钮
    deletWBYclick() {
      this.$Modal.confirm({
        title: "删除外部云",
        content:
          '<p>是否删除<span style="font-weight: 600;color:red;word-wrap: break-word;">' +
          this.treeWBYdatas[0].title +
          "</span>外部云?</p>",
        onOk: () => {
          externalCloudGroupDelete({data: { ids: [this.treeWBYdatas[0].id] }})
          .then((em) => {
            this.treeGET();
            this.vmdata = new Array();
            this.cloudData = new Array();
          })
        },
        onCancel: () => {
           
        },
      });
    },

  // 虚拟机列表
    // 虚拟机列表 表头
    tableCloumn() {
      this.vmcolumn = [
        // { type: "selection", width: 30, align: "center"},
        { title: "VM_ID", key: "vm", tooltip:true,minWidth: 160 },
        // { title: "名称", key: "name", tooltip:true, align: "center",sortable: 'custom' },
        { title: "名称", key: "name", tooltip:true, align: "center" },
        { title: "VCPU", key: "cpu_count", align: "center"},
        { title: "内存", key: "memory_size_mib", align: "center",
          render: (h, params) => {
            return h("span",(params.row.memory_size_mib/1024).toFixed(1)+" GB")
          }
        },
        { title: "开机状态", key: "power_state", align: "center",
          render: (h, params) => {
            if(params.row.power_state == 'POWERED_OFF') {
              return  h("span",{
                style:{
                  color:"#ccc"
                }
              },"已关机",)
            }else {
              return  h("span",{
                style:{
                  color:"green"
                }
              },"已开机",)
            }
          }
        },
        {
          title: "操作",
          key: "operation",
          width: 120,
          render: (h, params) => {
            let but = new Array();
            // but.push(h("Button",{
            //   style: {
            //     background:params.row.power_state == 'POWERED_OFF'?"#fff":"#f7f7f7",
            //     cursor:params.row.power_state == 'POWERED_OFF'?"pointer":"not-allowed",
            //     color:params.row.power_state == 'POWERED_OFF'?"#515a6e":"#c5c8ce"
            //   },
            //   props: {
            //     // disabled:params.row.power_state !== 'POWERED_ON'
            //   },
            //   nativeOn: {
            //     click: () => {
            //       if(params.row.power_state !== 'POWERED_OFF'){
            //         this.$Message.warning({
            //           background: true,
            //           closable: true,
            //           duration: 5,
            //           content: "请先关闭虚拟机，再进行此操作",
            //         });
            //       }else {
            //         this.transferModal = true
            //         this.transferDisabled = false
            //         this.cloudVMform.cloudy_id = this.tablePageForm.god
            //         this.cloudVMform.availability_zone = ''
            //         this.cloudVMform.network_uuid = ''
            //         this.cloudVMform.vmware_id = params.row.vm
            //         this.cloudVMform.vmware_name = params.row.name
            //         this.cloudVMform.vm_name = params.row.name
            //         this.cloudVMform.vm_os = 'windows'
            //         this.cloudVMform.vm_os_version = ''
            //         this.cloudVMform.vm_cpu = params.row.cpu_count
            //         this.maxORmin.cpumin = params.row.cpu_count
            //         this.cloudDisks = new Array()
            //         this.cloudVMform.vm_disks = new Array()
            //         if(params.row.disks!==undefined) {
            //           params.row.disks.forEach((em,index)=>{
            //             this.cloudDisks.push({
            //               label:em.label,
            //               key:'硬盘容量'+(index+1)+"：",
            //               capacity:(em.capacity/1073741824).toFixed(0)-0
            //             })
            //             this.cloudVMform.vm_disks.push((em.capacity/1073741824).toFixed(0)-0)
            //           })
            //         }else {
            //           this.transferModal = false
            //           this.$Message.error({
            //             background: true,
            //             closable: true,
            //             duration: 10,
            //             content:  "未获取到硬盘参数无法迁移该虚拟机",
            //           });
            //         }
            //         this.cloudVMform.vm_ram = (params.row.memory_size_mib/1024).toFixed(0)-0
            //         this.maxORmin.rammin = (params.row.memory_size_mib/1024).toFixed(0)-0
            //         // 获取可用域 数据
            //         physicsTableAllQuery.then((callback) => {
            //           let arr = new Array()
            //           callback.data.forEach(em => {
            //             arr.push({
            //               name:em.name,
            //               key:em.id,
            //             })
            //           })
            //           this.cloudAvailable = arr
            //         });
            //         // 获取网络下拉框数据
            //         networkTableQuery().then((callback) => {
            //           let arr = new Array();
            //           callback.data.forEach(em=>{
            //             em.cidr.forEach(ci=>{
            //                  arr.push({
            //                    id: em.id,
            //                    name: ci,
            //                  })
            //                })
            //           });
            //           this.cloudNETworks = arr;
            //         });
            //       }
            //     },
            //   },
            // },"迁移"))
            switch (params.row.power_state) {
              case "POWERED_OFF":
                but.unshift(
                  h("Button",{
                    style:{
                      color:"green",
                      marginRight: '5PX',
                    },
                    nativeOn: {
                      click: () => {
                        this.actionPackage(params, "start");
                      },
                    },
                  },"开机")
                );
                break;
              case "POWERED_ON":
                but.unshift(
                  h("Button",{
                    style:{
                      color:"red",
                      marginRight: '5PX',
                    },
                    nativeOn: {
                      click: () => {
                        this.actionPackage(params, "stop");
                      },
                    },
                  },"关机")
                );
                break;
            }
            return h("div", but);
          },
        },
      ];
    },
    // 虚拟机列表 数据获取
    tableXNJpost() {
      this.spinShow=true
      externalCloudTableQuery(this.tablePageForm)
      .then((callback) => {
        this.spinShow=false
        this.tableTotal = callback.data.total
        this.vmdata = callback.data.data
      }).catch((error) => {
        this.spinShow=false
      })
    },
    // 当前分页
    onPageChange(item) {
      this.tablePageForm.page = item;
      this.tableXNJpost();
    },
    // 每页条数
    onPageSizeChange(item) {
      this.$store.state.power.pagecount = item
      this.tablePageForm.pagecount = this.$store.state.power.pagecount
      this.tablePageForm.page = 1
      this.tableTotal = 0
      this.tablePageForm.search_str = ""
      this.tableXNJpost();
    },
    // 虚拟机列表 列排序
    sortColumn(column, key, order) {
      if (column.key !== "normal") {
        this.tablePageForm.order_by = column.key;
        this.tablePageForm.order_type = column.order;
        this.tablePageForm.page = 1
        this.tableTotal = 0
        this.tablePageForm.search_str = ""
        this.tableXNJpost();
      }
    },
    // 搜索虚拟机列表
    tableVMsearchInput(){
      this.tablePageForm.page = 1
      this.tableTotal = 0
      this.tableXNJpost();
    },
    // 虚拟机列表 勾选 数据
    tableSlectXNJdata(item) {
      this.removeTableData = item;
      let arrName = new Array();
      let arrID = new Array();
      item.forEach((em) => {
        arrID.push(em.vm);
        arrName.push(em.name);
      });
      this.removeTableID = arrID;
      this.removeTableName = arrName;
    },
    cancel() {},
    // 虚拟机表格 操作栏 开机 关机 重启等状态封装
    actionPackage(par, action) {
      switch (action) {
        // 开机
        case "start":
          this.$Modal.confirm({
            title: par.row.name+"开机操作",
            content:
              '<p>是否对<span style="font-weight: 600;font-siaze:30px;">' +
              par.row.name +
              '</span>虚拟机进行<span style="font-weight: 600;color:green;">开机</span>操作?</p>',
            onOk: () => {
              externalCloudTableOpen({
                ids:[par.row.vm],
                god:this.tablePageForm.god,
              })
              .then((callbackKJ) => {
                if(callbackKJ.data.msg == "ok"){
                  this.$Message.success({
                    background: true,
              closable: true,
                    duration: 10,
                    content:  par.row.name + "虚拟机已启动",
                  });
                  setTimeout(() => {
                    this.tableXNJpost()
                  }, 1000);
                }else {
                  this.$Message.error({
                    background: true,
                    closable: true,
                    duration: 10,
                    content:  par.row.name + "虚拟机启动失败",
                  });
                }
              })
            },
            onCancel: () => {
               
            },
          });
          break;
        // 关机
        case "stop":
          this.$Modal.confirm({
            title: par.row.name+"关机操作",
            content:
              '<p>是否对<span style="font-weight: 600;font-siaze:30px;">' +
              par.row.name +
              '</span>虚拟机进行<span style="font-weight: 600;color:red;">关机</span>操作?</p>',
            onOk: () => {
              externalCloudTableClose({
                ids:[par.row.vm],
                god:this.tablePageForm.god,
              })
              .then((callbackGJ) => {
                if(callbackGJ.data.msg == "ok"){
                  this.$Message.success({
                    background: true,
              closable: true,
                    duration: 10,
                    content:  par.row.name + "虚拟机已关机",
                  });
                  setTimeout(() => {
                    this.tableXNJpost()
                  }, 1000);
                }else {
                  this.$Message.error({
                    background: true,
              closable: true,
                    duration: 10,
                    content:  par.row.name + "虚拟机关闭失败",
                  });
                }
              })
            },
            onCancel: () => {
               
            },
          });
          break;
        // 重启
        case "reboot":
          this.$Modal.confirm({
            title: par.row.name+"重启操作",
            content:
              '<p>是否对<span style="font-weight: 600;font-siaze:30px;">' +
              par.row.name +
              '</span>虚拟机进行<span style="font-weight: 600;color:#ff9b9b;">重启</span>操作?</p>',
            onOk: () => {
              console.log('重启')
            },
            onCancel: () => {
               
            },
          });
          break;
      }
    },
    // 迁移虚拟机 确认
    transferOK(){
      this.transferDisabled = true
      let cpus=false
      let rams=false
      let desks=false
      if(this.maxORmin.cpumin<=this.cloudVMform.vm_cpu){
        cpus=true
      }
      if(this.maxORmin.rammin<=this.cloudVMform.vm_ram){
        rams=true
      }
      for(var i=0;i<this.cloudDisks.length;i++){
        if(this.cloudDisks[i].capacity<=this.cloudVMform.vm_disks[i]) {
          desks=true
        }
      }
      if(cpus&&rams&&desks){
        let listname = /^[\u4e00-\u9fa5_a-zA-Z0-9@_.]{2,32}$/;
        if(listname.test(this.cloudVMform.vm_name)&&this.cloudVMform.availability_zone!==""&&this.cloudVMform.network_uuid!==""){
          externalCloudTableMigrate(this.cloudVMform)
          .then((callback) => {
            this.transferModal = false;
            if(callback.data.msg== "ok") {
              this.$Message.success({
                background: true,
                closable: true,
                duration: 5,
                content: '迁移任务已发起',
              });
            }else{
              this.$Message.error({
                background: true,
                closable: true,
                duration: 5,
                content: '迁移任务发起失败',
              });
            }
          })
        }else {
          this.transferDisabled = false
          this.$Message.error({
            background: true,
            closable: true,
            duration: 5,
            content: '虚拟机名称不符合规范、可用域、网络未选择',
          });
        }
      }else{
        this.transferDisabled = false
        this.$Message.error({
          background: true,
          closable: true,
          duration: 5,
          content: 'CPU、内存容量、硬盘容量不能小于默认值',
        });
      }
    },

    // cpu选择
    formCpu(val) {
      return val + "核";
    },
    // 内存选择
    formMemory(val) {
      return val + " GB";
    },
    // 硬盘选择
    formDisk(val) {
      return val + " GB";
    },
    
    // 单位转换MB到EB
    unitConversion(size){
        let list = size
        let kB = 1048576
        let MB = 1073741824
      if(size<1024) {
        list = size+'MB'
      }else if(size>=1024 &&size<kB) {
        list = (size/1024).toFixed(1)+' GB'
      }else if(size>=kB &&size<MB) {
        list = (size/kB).toFixed(1)+'TB'
      }else {
        list = (size/MB).toFixed(1)+'EB'
      }
      return list
    },
    // 操作权限获取
    actionQuery(){
      powerCodeQuery({
        module_code:[
          'waibuyuntianjia',
          'xunijitiaojianchaxun',
          'waibuyunshanchu',
          'waibuyunliebiao',
        ]
      }).then(callback=>{
        this.powerAcitons = callback.data.data
        if(this.powerAcitons.waibuyunliebiao) {
          this.treeGET()
          this.tableCloumn()
        }
      })
    },
  },
};
</script>
