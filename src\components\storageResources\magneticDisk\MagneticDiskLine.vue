<template>
  <div class="magnetic-disk-line-wrap">
    <div :id="id" style="width: 100%;height: 100%;"></div>
  </div>
</template>

<script>
  import * as echarts from 'echarts';
  export default {
    props: {
      setLineId: {
        type: Number,
      },
      seriesName: {
        type: String,
        default: '自定义'
      },
      seriesData: {
        type: Array,
        default: () => {
          return []
        }
      }
    },
   data() {
     return{
       id: ''
     }
    },
    mounted(){
      this.id = 'setLineId' + this.setLineId
      setTimeout(() => {
        this.lineCharts()
      }, 500);
    },
    methods:{
      lineCharts() {
        const id = 'setLineId' + this.setLineId
        var chartDom = document.getElementById(id);
        var myChart = echarts.init(chartDom);
        const colors = ['#66e361', '#ea5858'];
        const _this = this
        var option;
        option = {
          color: colors,
          tooltip: {
            trigger: 'item',
            position: [0, 0],
            backgroundColor:'rgba(50,50,50,0.2)',
            borderColor:'rgba(50,50,50,0.1)',
            formatter: '{a0}: {c0}',
            textStyle: {
              color: '#ff6902'
            }
          },
          // legend: {
          //   data: ['读', '写']
          // },
          grid: {
            left: '2%',
            // right: '4%',
            bottom: '90%',
            data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
            containLabel: true
          },
          xAxis: {
            type: 'category',
            show:false
          },
          yAxis: {
            type: 'value',
            show:false,
          },
          series: [
            {
              name: _this.seriesName,
              type: 'line',
              stack: 'Total',
              data: this.seriesData,
              lineStyle: {
              }
            },
          ]
        };
        option && myChart.setOption(option);
      },
    },
  }
</script>

<style scoped lang='less'>
  .magnetic-disk-line-wrap {
    width: 100%;
    height: 100%;
    #magneticDiskLine {
      
    }
  }
</style>