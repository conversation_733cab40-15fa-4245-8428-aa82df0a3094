{"_from": "get-intrinsic@^1.0.2", "_id": "get-intrinsic@1.1.3", "_inBundle": false, "_integrity": "sha512-QJVz1Tj7MS099PevUG5jvnt9tSkXN8K14dxQlikJuPt4uD9hHAHjLyLBiLR5zELelBdD9QNRAXZzsJx0WaDL9A==", "_location": "/get-intrinsic", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "get-intrinsic@^1.0.2", "name": "get-intrinsic", "escapedName": "get-intrinsic", "rawSpec": "^1.0.2", "saveSpec": null, "fetchSpec": "^1.0.2"}, "_requiredBy": ["/call-bind", "/side-channel"], "_resolved": "https://registry.npmmirror.com/get-intrinsic/-/get-intrinsic-1.1.3.tgz", "_shasum": "063c84329ad93e83893c7f4f243ef63ffa351385", "_spec": "get-intrinsic@^1.0.2", "_where": "/root/docker/node_modules/side-channel", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "bugs": {"url": "https://github.com/ljharb/get-intrinsic/issues"}, "bundleDependencies": false, "dependencies": {"function-bind": "^1.1.1", "has": "^1.0.3", "has-symbols": "^1.0.3"}, "deprecated": false, "description": "Get and robustly cache all JS language-level intrinsics at first require time", "devDependencies": {"@ljharb/eslint-config": "^21.0.0", "aud": "^2.0.0", "auto-changelog": "^2.4.0", "call-bind": "^1.0.2", "es-abstract": "^1.20.2", "es-value-fixtures": "^1.4.2", "eslint": "=8.8.0", "evalmd": "^0.0.19", "for-each": "^0.3.3", "make-async-function": "^1.0.0", "make-async-generator-function": "^1.0.0", "make-generator-function": "^2.0.0", "mock-property": "^1.0.0", "npmignore": "^0.3.0", "nyc": "^10.3.2", "object-inspect": "^1.12.2", "safe-publish-latest": "^2.0.0", "tape": "^5.6.0"}, "exports": {".": [{"default": "./index.js"}, "./index.js"], "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "homepage": "https://github.com/ljharb/get-intrinsic#readme", "keywords": ["javascript", "ecmascript", "es", "js", "intrinsic", "getintrinsic", "es-abstract"], "license": "MIT", "main": "index.js", "name": "get-intrinsic", "publishConfig": {"ignore": [".github/workflows"]}, "repository": {"type": "git", "url": "git+https://github.com/ljharb/get-intrinsic.git"}, "scripts": {"lint": "eslint --ext=.js,.mjs .", "posttest": "aud --production", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prelint": "evalmd README.md", "prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run tests-only", "tests-only": "nyc tape 'test/**/*.js'", "version": "auto-changelog && git add CHANGELOG.md"}, "testling": {"files": "test/GetIntrinsic.js"}, "version": "1.1.3"}