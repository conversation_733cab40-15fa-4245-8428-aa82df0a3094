<template>
  <div>
    <Modal
      v-model="model"
      width="600"
      :mask-closable="false"
    >
      <template #header><p>编辑虚拟机组</p></template>
      <Form :model="formItem" ref="formItem" :rules="ruleValidate" :label-width="150">
        <FormItem label="当前虚拟机组名称">
          <Input
            v-model="formItem.oldname"
            disabled
          ></Input>
        </FormItem>
        <FormItem label="虚拟机组新名称" prop="name">
          <Input
            v-model="formItem.name"
            placeholder="请输入虚拟机组新名称"
          ></Input>
        </FormItem>
      </Form>
      <div slot="footer">
        <Button type="text" @click="model = false">取消</Button>
        <Button type="primary" @click="modalOK" :disabled="disabled">确认</Button>
      </div>
    </Modal>
  </div>
</template>
<script>
import {vmGroupModify} from '@/api/virtualMachine';  // 编辑虚拟机组
export default {
  props: {
    groupSelect:Object,
    editGroupTime:String,
  },
  watch: {
    editGroupTime(news){
      this.formItem.oldname = this.groupSelect.name
      this.formItem.id = this.groupSelect.id
      this.model = true
      this.disabled = false
      this.$refs.formItem.resetFields()
    }
  },
  data(){
    const propName = (rule, value, callback) => {
      let list = /^[\u4e00-\u9fa5_a-zA-Z0-9@_.-]{2,32}$/;
      if (list.test(value)) {
        callback();
      } else {
        callback(new Error("2-32 个中文、英文、数字、特殊字符(@_.-"));
      }
    };
    return {
      model:false,
      disabled: false,
      formItem:{
        oldname:"",
        name:"",
        id:""
      },
      ruleValidate:{
        name:[
          { required: true, message: '必填项', trigger: 'change' },
          { validator: propName, trigger: "change" },
        ]
      }
    }
  },
  methods: {
    modalOK(){
      this.$refs.formItem.validate((valid) => {
        if(valid){
          this.disabled = true
          vmGroupModify({
            id: this.formItem.id,
            new_name: this.formItem.name,
          })
          .then((callback) => {
            this.model=false
            this.$emit("group-ok",'编辑虚拟机组操作完成',this.formItem.name);
          }).catch((error) => {
            this.disabled = false
            this.$emit("group-error",'编辑虚拟机组操作失败');
          })
        }else{
          this.disabled = false
        }
      })
    },
  }
}
</script>