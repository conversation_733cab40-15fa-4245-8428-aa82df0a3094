<style lang="less">
@import "./virtualMachineTab.less";

/* 自定义虚拟机分组样式 */
.vm_tree_area {
  .ivu-tree {
    width: 100%;

    .ivu-tree-children {
      width: 100%;
    }

    .ivu-tree-children-item {
      width: 100%;
      margin-bottom: 0; /* 减少项目间距 */
    }

    .ivu-tree-title {
      width: 100%;
      display: block;
      transition: background-color 0.3s;
      box-sizing: border-box;
      padding: 0;

      &:hover {
        background-color: #f5f5f5;
      }
    }

    /* 选中项样式 */
    .ivu-tree-title-selected {
      background-color: #f0f0f0 !important;
      width: 100%;
    }

    /* 修复箭头换行问题 */
    .ivu-tree-arrow {
      float: left;
      margin-right: 4px;
    }

    /* 调整节点间距 */
    .ivu-tree-arrow-drop-down,
    .ivu-tree-arrow-right {
      vertical-align: middle;
    }
  }
}
</style>

<style>
@keyframes move {
  0% {
    background-position: -28px 0;
  }
  100% {
    background-position: 0 0;
  }
}

.table_schedule{
  width: 100%;
  height: 15px;
  border-radius: 5px;
  border: 1px solid red;
  background-image: repeating-linear-gradient(-45deg,#f67f45,#f67f45 11px,#eee 10px,#eee 20px);
  background-size: 2000px 28px;
  animation: move 1s linear infinite;
}
.dropdstyle>.ivu-select-dropdown {
  height:205px;
  overflow: auto !important;
}

/* 拖拽相关样式 */
.vm_table_content .ivu-table-body tr {
  cursor: grab;
  user-select: none; /* 防止文本选择 */
  -webkit-user-drag: element; /* 启用元素拖拽 */
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

.vm_table_content .ivu-table-body tr:active {
  cursor: grabbing;
}

/* 拖拽中的行样式 */
.vm_table_content .ivu-table-body tr.dragging {
  opacity: 0.7;
  background-color: #f0f7ff !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  position: relative;
  z-index: 100;
}

.vm_group_area {
  position: relative;
}

/* 减小树节点间距 */
.vm_tree_area .ivu-tree ul {
  padding-left: 16px; /* 减少缩进 */
}

/* 调整节点内容布局 */
.vm_tree_area .ivu-tree-title-selected:before {
  display: none; /* 移除可能导致布局问题的伪元素 */
}

/* 确保树形节点的内容区域更宽 */
.vm_tree_area .ivu-tree-children-item {
  width: 100%;
  line-height: 1.5; /* 减少行高 */
  padding: 2px 0; /* 减少内边距 */
}

.vm_tree_area .ivu-tree-title[data-group-id]:hover {
  background-color: #f5f5f5;
}

/* 修改树节点选中项样式，使背景填充整行 */
.vm_tree_area .ivu-tree-title-selected,
.vm_tree_area .ivu-tree-title-selected:hover {
  background-color: #f0f0f0 !important;
  width: 100%;
}

/* 拖拽时的行高亮 */
.vm_table_content .ivu-table-row[draggable=true]:hover {
  background-color: #f8f8f8;
}

/* 目标区域激活状态 */
.vm_tree_area .drag-over {
  background-color: #e6f7ff !important;
  border: 1px dashed #1890ff;
}

/* 拖拽到分组区域时的高亮效果 */
.dragover-highlight {
  background-color: rgba(24, 144, 255, 0.1);
  border: 2px dashed #1890ff;
}

/* 默认分组样式 */
.vm_tree_area .ivu-tree-title[data-is-default="true"]:hover {
  background-color: #fff7e6;
}

/* 拖拽到默认分组时的警告效果 */
.default-group-dragover {
  background-color: rgba(255, 77, 79, 0.1) !important;
  border: 1px dashed #ff4d4f !important;
  position: relative;
}

.default-group-dragover::after {
  content: "不能移动到默认分组";
  position: absolute;
  top: 100%;
  left: 0;
  background: #ff4d4f;
  color: white;
  padding: 2px 5px;
  border-radius: 2px;
  font-size: 12px;
  z-index: 10;
}

/* 修复箭头和文本对齐问题 */
.vm_tree_area .ivu-tree-arrow {
  float: left;
  vertical-align: middle;
  margin-top: 2px;
}

.vm_tree_area .ivu-tree-title {
  display: inline-flex !important;
  align-items: center;
  min-height: 24px;
  padding: 2px 0;
}

/* 解决树箭头和节点间距问题 */
.vm_tree_area .ivu-tree-arrow {
  position: relative;
  top: 0;
  margin-right: 2px;
  line-height: 1;
  display: inline-block;
  vertical-align: middle;
}

.vm_tree_area .ivu-tree-children {
  padding-left: 18px !important;
}

.vm_tree_area .ivu-tree-children-item {
  padding: 1px 0 !important;
  margin-bottom: 0 !important;
}

.vm_tree_area .ivu-tree-title {
  display: inline-flex !important;
  align-items: center;
  padding: 2px 0;
  line-height: 1.5;
}

/* 减少层级间距 */
.vm_tree_area .ivu-tree .ivu-tree-children {
  margin-left: -8px;
}

/* 确保整行显示 */
.vm_tree_area .ivu-tree-title-selected {
  width: calc(100% - 24px) !important;
}

/* 解决树箭头和节点间距问题 */
.vm_tree_area .ivu-tree-arrow {
  position: relative;
  top: 0;
  margin-right: 1px; /* 减少箭头右边距 */
  line-height: 1;
  display: inline-block;
  vertical-align: middle;
}

.vm_tree_area .ivu-tree-children {
  padding-left: 12px !important; /* 减少层级缩进 */
}

.vm_tree_area .ivu-tree-children-item {
  padding: 0 !important; /* 移除内边距 */
  margin-bottom: 0 !important; /* 移除下边距 */
  line-height: 1.2 !important; /* 减少行高 */
}

.vm_tree_area .ivu-tree-title {
  display: inline-flex !important;
  align-items: center;
  padding: 1px 0 !important; /* 减少上下内边距 */
  line-height: 1.2; /* 减少行高 */
  height: 20px; /* 固定行高 */
  min-height: unset; /* 重置最小高度 */
}

/* 减少层级间距 */
.vm_tree_area .ivu-tree .ivu-tree-children {
  margin-left: -5px;
  margin-top: 1px; /* 增加最小的上边距 */
}

/* 确保整行显示 */
.vm_tree_area .ivu-tree-title-selected {
  width: calc(100% - 16px) !important; /* 减少背景宽度以适配减小后的边距 */
  padding: 0 !important; /* 移除内边距 */
}

/* 减小图标大小 */
.vm_tree_area .ivu-tree-title .ivu-icon {
  font-size: 14px; /* 减小图标大小 */
}

/* 修改根节点样式 */
.vm_tree_area > .ivu-tree {
  margin-top: 0;
  padding-top: 0;
}
</style>
<template>
  <div class="vm_manage_area">
     <!-- 局部加载装置 -->
      <Spin fix v-if="spinShowVM" size="large" style="color:#ef853a">
        <Icon type="ios-loading" size=50 class="demo-spin-icon-load"></Icon>
        <div style="font-size:16px;padding:20px">Loading...</div>
      </Spin>
    <!-- 侧边虚拟机分组 -->
    <div class="vm_group_area"
         @dragover.prevent="handleDragOver($event)"
         @dragleave.prevent="handleDragLeave($event)"
         @drop="handleDropOnGroup($event)">
      <p>虚拟机分组</p>
      <div class="virtual_unit">
        <Tooltip content="新建虚拟机组" placement="top-start" v-show="powerAcitons.xunijifenzutianjia">
          <Button
            type="text"
            @click="newlyXJZmodal"
            style="padding: 0px"
            icon="md-add-circle"
            :disabled="newVMdisabled"
          ></Button>
        </Tooltip>
        <Tooltip content="编辑虚拟机组" placement="top" v-show="powerAcitons.xunijifenzubianji">
          <Button
            type="text"
            @click="editXJZclick"
            style="padding: 0px"
            icon="ios-create"
            :disabled="editVMdisabled"
          ></Button>
        </Tooltip>
        <Tooltip content="刷新虚拟机组" placement="top" v-show="powerAcitons.xunijifenzushuaxin">
          <Button
            type="text"
            @click="refreshXJZ"
            style="padding: 0px"
            :loading="loadRefresh"
          >
            <span v-if="!loadRefresh"><Icon type="md-refresh" /></span>
            <span v-else></span>
          </Button>
        </Tooltip>
        <Tooltip content="删除虚拟机组" placement="top" v-show="powerAcitons.xunijifenzushanchu">
          <Button
            type="text"
            @click="deletXJZ"
            style="padding: 0px"
            icon="ios-trash"
            :disabled="deletVMdisabled"
          ></Button>
        </Tooltip>
      </div>
      <div class="vm_tree_area" v-show="powerAcitons.xunijiliebiao">
        <Tree
          :data="vmData"
          :render="renderContent"
          class="demo-tree-render"
          @on-select-change="treeVMclick"
        ></Tree>
      </div>
    </div>
    <!-- 虚拟机 表数据 -->
    <div class="vm_table_area">
      <div class="vm_title_area">
        <p>虚拟机列表</p>
      </div>
      <span
        style="
          display: block;
          border-top: 1px solid #ccc;
          margin:0 0 8px -10px;
        "
      ></span>
      <div class="table-button-area">
        <div>
          <Button
            class="plus_btn"
            @click="newBuildClick"
            :disabled="!deletVMdisabled || deletVMstatus "
            v-show="powerAcitons.xunijixinjian"
            ><span class="icon iconfont icon-plus-circle"></span> 新建虚拟机</Button
          >
          <Button
            class="close_btn"
            @click="deletClick(tableSelec)"
            :disabled="deletVMstatus"
            v-show="powerAcitons.xunijishanchu"
            ><span class="icon iconfont icon-close-circle"></span> 删除虚拟机</Button
          >
          <Button
            class="move_in"
            @click="moveInModalClick"
            :disabled="deletVMdisabled"
            v-show="powerAcitons.xunijiyichufenzu"
            ><span class="icon iconfont icon-a-17Edaoru2"></span> 移入分组</Button
          >
          <Button
            class="remove"
            @click="removeClick(tableSelec)"
            :disabled="deletVMdisabled"
            v-show="powerAcitons.xunijiyirufenzu"
            ><span class="icon iconfont icon-a-17Hdaochu"></span> 移出分组</Button
          >
          <Button class="plus_btn" v-show="powerAcitons.xunijikaiji" @click="operateOnOff('start')"><Icon style="font-size:16px" type="md-power" /> 开机</Button>
          <Button class="close_btn" v-show="powerAcitons.xunijiguanji" @click="operateOnOff('stop')"><Icon style="font-size:16px" type="md-power" /> 关机</Button>
          <Button class="other_btn" v-show="powerAcitons.xunijixiugaipeizhi" @click="groupConfigClick(tableSelec)"><Icon style="font-size:16px" type="md-settings" /> 修改配置</Button>
          <Button class="other_btn" v-show="powerAcitons.xunijigaokeyong&&!this.automaticStatus" @click="haConfigClick(tableSelec)"><Icon style="font-size:16px" type="ios-switch" /> 高可用设置</Button>
        </div>
        <div v-show="powerAcitons.xunijichaxun">
          <Input v-model="tablePageForm.search_str" search enter-button placeholder="请输入名称" style="width: 300px" @on-search="tableVMsearchInput" />
        </div>
      </div>
      <!-- 虚拟机表格 -->
      <div class="vm_table_content" v-show="powerAcitons.xunijiliebiao">
        <Table
          :columns="vmcolumn"
          :data="vmdata"
          @on-sort-change="sortColumn"
          height=520
          @on-selection-change="tableChange"
          @on-row-click="rowClick"
          :row-attrs="rowAttrs"
          ref="vmTable"
        >
          <!-- 名称 -->
          <template v-slot:name="{row}">
            <Tooltip :content="row.name" style="width:100%">
              <a class="text_overflow" @click="nameClick(row)">{{ row.name }}</a>
            </Tooltip>
          </template>
          <!-- IP -->
          <template v-slot:ip="{row}">
            <Tooltip :content="convertIP(row.addresses)" style="width:100%">
              <span class="text_overflow">{{ convertIP(row.addresses) }}</span>
            </Tooltip>
          </template>
          <!-- 内存 -->
          <template v-slot:ram="{row}">
            <span>{{ (row.ram/1024).toFixed(1) }} GB</span>
          </template>
          <!-- 硬盘 -->
          <template v-slot:disk="{row}">
            <span>{{ row.disk}} GB</span>
          </template>
          <!-- 任务 -->
          <template v-slot:task_state="{row}">
            <div v-if="row.task_state" class="table_schedule">{{ convertTask(row.task_state) }}</div>
            <span v-else>无</span>
          </template>
          <!-- 状态 -->
          <template v-slot:status="{row}">
            <span :style='{color:convertStatus("colour",row.status)}'>{{ convertStatus("fields",row) }}</span>
          </template>
          <!-- 高可用 -->
          <template v-slot:ha_status="{row}">
            <i-switch size="large" v-if="!automaticStatus" v-model="row.ha_status" :before-change="()=>handleBeforeChange(row)">
              <span slot="open">启用</span>
              <span slot="close">禁用</span>
            </i-switch>
            <i-switch size="large" v-else disabled :value='true'>
              <span slot="open">启用</span>
              <span slot="close">禁用</span>
            </i-switch>
          </template>
          <!-- 操作 -->
          <template v-slot:operation="{row}">
            <Button v-show="powerAcitons.xunijikongzhitai" :disabled='row.status !== "ACTIVE"' ghost type="info" @click='actionPackage(row, "getVNCConsole")'>控制台</Button>
            <Dropdown class="dropdstyle"  @on-click="dropdownClick($event,row)" placement="bottom-end">
              <Button><Icon type="ios-arrow-down"></Icon></Button>
              <template #list>
                <DropdownMenu>
                  <DropdownItem name='tcgp'  v-if="row.description=='iso'" >弹出光盘</DropdownItem>
                  <DropdownItem name='kj' v-show="powerAcitons.xunijikaiji&&row.status == 'SHUTOFF'">开机</DropdownItem>
                  <DropdownItem name='gj' v-show="powerAcitons.xunijiguanji&&row.status !== 'SHUTOFF'" :disabled="dropdownDisabled(row.status)">关机</DropdownItem>
                  <DropdownItem name='cq' v-show="powerAcitons.xunijichongqi">重启</DropdownItem>
                  <DropdownItem name='bjxj' v-show="powerAcitons.xunijibianji" :disabled="dropdownDisabled(row.status)">编辑虚拟机</DropdownItem>
                  <DropdownItem name='qhxt' v-show="powerAcitons.xunijiqiehuanxitongleixing">切换系统类型</DropdownItem>
                  <DropdownItem name='xgpz' v-show="powerAcitons.xunijixiugaipeizhi" :disabled="dropdownDisabled(row.status)">修改配置</DropdownItem>
                  <!-- <DropdownItem name='pzusb'>配置USB</DropdownItem> -->
                  <DropdownItem name='xgip' v-show="powerAcitons.xunijixiugaiip&&row.addresses.length!==0" :disabled="dropdownDisabled(row.status)">修改IP</DropdownItem>
                  <DropdownItem name='tjwk' v-show="powerAcitons.xunijitianjiawangka" :disabled="dropdownDisabled(row.status)">添加网卡</DropdownItem>
                  <DropdownItem name='scwk' v-show="powerAcitons.xunijishanchuwangka&&row.addresses.length!==0" :disabled="dropdownDisabled(row.status)">删除网卡</DropdownItem>
                  <DropdownItem name='bdmac' v-show="powerAcitons.xunijibangdingMACdizhi&&row.addresses.length!==0">绑定MAC地址</DropdownItem>
                  <DropdownItem name='bjwk' v-show="powerAcitons.xunijibianjianquanzu" :disabled="dropdownDisabled(row.status)">编辑安全组</DropdownItem>
                  <DropdownItem name='qy' v-show="powerAcitons.xunijiqianyi" :disabled="dropdownDisabled(row.status)">迁移</DropdownItem>
                  <!-- <DropdownItem name='kz'>快照</DropdownItem> -->
                  <DropdownItem name='gzyyp' v-show="powerAcitons.xunijiguazaiyunyingpan" :disabled="dropdownDisabled(row.status)">挂载云硬盘</DropdownItem>
                  <DropdownItem name='flyyp' v-show="powerAcitons.xunijifenliyunyingpan&&row.status == 'SHUTOFF'">分离云硬盘</DropdownItem>
                  <DropdownItem name='gq' v-show="powerAcitons.xunijiguaqi&&row.status == 'ACTIVE'">虚拟机挂起</DropdownItem>
                  <DropdownItem name='jg' v-show="powerAcitons.xunijijiegua&&row.status == 'SUSPENDED'">虚拟机解挂</DropdownItem>
                  <DropdownItem name='fqxj' v-show="powerAcitons.xunijiquxiaofeiqi&&row.status == 'SHELVED_OFFLOADED'">取消废弃虚拟机</DropdownItem>
                  <DropdownItem name='qxfq' v-show="powerAcitons.xunijifeiqi&&row.status !== 'SHELVED_OFFLOADED'">废弃虚拟机</DropdownItem>
                  <DropdownItem name='kl' v-show="powerAcitons.xunijikelong">克隆</DropdownItem>
                  <DropdownItem name='dtzykz' v-show="powerAcitons.xunijidongtaiziyuankuozhan">动态资源扩展</DropdownItem>
                  <DropdownItem name='czmm' v-show="powerAcitons.xunijichongzhimima&&row.status == 'ACTIVE'" >重置密码</DropdownItem>
                  <DropdownItem name='sc' v-show="powerAcitons.xunijishanchu" style="color:red" divided>删除</DropdownItem>
                </DropdownMenu>
              </template>
            </Dropdown>
          </template>
        </Table>
      </div>
      <!-- 虚拟机表格 分页  -->
      <div class="pages" v-show="this.vmdata.length!==0">
        <Pagination  :total="tableTotal" :page-size="tablePageForm.pagecount"  @page-change="onPageChange" @page-size-change="onPageSizeChange"/>
      </div>
      <!-- 任务监听 -->
      <div class="vm_task_aera">
        <div class="task_title">
          <h4>任务</h4>
          <span></span>
        </div>
        <Table :show-header="false" height=80 :columns="[{title: '名称',key: 'name',align: 'center'},{title: '状态',key: 'status',align: 'center'}]" :data="taskData"></Table>
      </div>
    </div>
    <!-- 新建虚拟机组 弹框 -->
    <GroupNew
      :newGroupTime="newGroupTime"
      :groupSelect="groupSelect"
      @group-error="groupError"
      @group-ok="groupOK"
    ></GroupNew>
    <!-- 编辑虚拟机组-->
    <GroupEdit
      :editGroupTime="editGroupTime"
      :groupSelect="groupSelect"
      @group-error="groupError"
      @group-ok="groupOK"
    ></GroupEdit>
    <!-- 虚拟机移入分组 -->
    <VMmovein
      :moveinTime="moveinTime"
      :groupSelect="groupSelect"
      @custom-error="customError"
      @custom-ok="customOK"
    ></VMmovein>
    <!-- 虚拟机移出分组 -->
    <VMremve
      :groupSelect="groupSelect"
      :tableArr="tableArr"
      :remveTime="remveTime"
      @return-ok="returnOK"
      @return-error="returnError"
    ></VMremve>
    <!-- 群修改配置 -->
    <VMgroupConfig
      :tableArr="tableArr"
      :configGroupTime="configGroupTime"
      @return-ok="returnOK"
      @return-error="returnError"
    ></VMgroupConfig>
    <!-- 群高可用配置 -->
    <VMhaConfig
      :tableArr="tableArr"
      :haGroupTime="haGroupTime"
      @return-ok="returnOK"
      @return-error="returnError"
    ></VMhaConfig>
    <!-- 虚拟机 操作 -->
    <!-- 新建虚拟机 -->
    <VMnew
      :newvmTime="newvmTime"
      @return-ok="returnOK"
      @return-error="returnError"
    ></VMnew>
    <!-- 虚拟机详情 -->
    <VMdetails
      :vmRow="vmRow"
      :detailsTime="detailsTime"
      @return-ok="returnOK"
      @return-error="returnError"
    ></VMdetails>
    <!-- 编辑虚拟机名称 -->
    <VMeditname
      :vmRow="vmRow"
      :editvmTime="editvmTime"
      @return-ok="returnOK"
      @return-error="returnError"
    ></VMeditname>
    <!-- 修改配置 -->
    <VMconfig
      :vmRow="vmRow"
      :configTime="configTime"
      @return-ok="returnOK"
      @return-error="returnError"
    ></VMconfig>
    <!-- 配置USB -->
    <VMusb
      :vmRow="vmRow"
      :usbTime="usbTime"
      @return-ok="returnOK"
      @return-error="returnError"
    ></VMusb>
    <!-- 修改IP  -->
    <VMmodifyip
      :vmRow="vmRow"
      :modifyipTime="modifyipTime"
      @return-ok="returnOK"
      @return-error="returnError"
    ></VMmodifyip>
    <!-- 添加网卡  -->
    <VMaddip
      :vmRow="vmRow"
      :addipTime="addipTime"
      @return-ok="returnOK"
      @return-error="returnError"
    ></VMaddip>
    <!-- 删除网卡  -->
    <VMdeletenetip
      :vmRow="vmRow"
      :deleteipTime="deleteipTime"
      @return-ok="returnOK"
      @return-error="returnError"
    ></VMdeletenetip>
    <!-- 绑定MAC地址 -->
    <VMmac
      :vmRow="vmRow"
      :macTime="macTime"
      @return-ok="returnOK"
      @return-error="returnError"
    ></VMmac>
    <!-- 编辑安全组  -->
    <VMsecurityGroup
      :vmRow="vmRow"
      :securityGroupTime="securityGroupTime"
      @return-error="returnError"
    ></VMsecurityGroup>
    <!-- 冷热迁移 -->
    <VMcoldHeatMigrate
      :vmRow="vmRow"
      :coldHeatTime="coldHeatTime"
      @return-ok="returnOK"
      @return-error="returnError"
    ></VMcoldHeatMigrate>
    <!-- 挂载云硬盘 -->
    <VMmount
      :vmRow="vmRow"
      :mountTime="mountTime"
      @return-error="returnError"
    ></VMmount>
    <!-- 分离云硬盘 -->
    <VMunhook
      :vmRow="vmRow"
      :unhookTime="unhookTime"
    ></VMunhook>
    <!-- 废弃虚拟机 -->
    <VMshelved
      :vmRow="vmRow"
      :shelvedTime="shelvedTime"
      @return-ok="returnOK"
      @return-error="returnError"
    ></VMshelved>
    <!-- 克隆 -->
    <VMclone
      :vmRow="vmRow"
      :cloneTime="cloneTime"
      @return-ok="returnOK"
      @return-error="returnError"
    ></VMclone>
    <!-- 动态资源扩展 -->
    <VMdynamicResource
      :vmRow="vmRow"
      :dynamicResourceTime="dynamicResourceTime"
      @return-ok="returnOK"
      @return-error="returnError"
    ></VMdynamicResource>
    <!-- 重置密码 -->
    <VMpassword
      :vmRow="vmRow"
      :pwdTime="pwdTime"
      @return-ok="returnOK"
      @return-error="returnError"
    ></VMpassword>
    <!-- 删除虚拟机 -->
    <VMdeletion
      :tableArr="tableArr"
      :deletionTime="deletionTime"
      @return-ok="returnOK"
      @return-error="returnError"
    ></VMdeletion>
  </div>
</template>
<script>
import { powerCodeQuery } from '@/api/other'; // 权限可用性 查询

import {
  vmGroupQuery, // 虚拟机组 查询
  vmGroupDelete, // 虚拟机组 删除
  vmGroupTableQuery, // 虚拟机组表格 查询

  vmGroupTableAction, // 虚拟机组表格 操作(开机 关机 重启 控制台等接口调用)
  vmGroupTableBatchOnOffMachine, // 虚拟机组表格 批量开关机
  vmGroupTableSystemType, // 虚拟机组表格 切换系统类型
  vmEjectDiscX86, // 虚拟机组表格 X86 弹出光盘
  vmEjectDiscARM, // 虚拟机组表格 ARM 弹出光盘

  vmGroupTableVMtask, // 虚拟机组表格 虚拟机任务查询
  vmHAaction, // 虚拟机表格 高可用操作
  vmGroupTableMoveinGroup, // 虚拟机组表格 移入分组
} from '@/api/virtualMachine';
import {automaticDowntimeMigrationQuery} from '@/api/system';

import Pagination from '@/components/public/Pagination.vue';
import GroupNew from './GroupNew.vue'; // 新建虚拟机组
import GroupEdit from './GroupEdit.vue'; // 编辑虚拟机组
import VMmovein from './VMmovein.vue'; // 虚拟机移入分组
import VMremve from './VMremve.vue'; // 虚拟机移出分组
import VMgroupConfig from './VMgroupConfig.vue'; // 虚拟机群修改配置
import VMhaConfig from './VMhaConfig.vue'; // 虚拟机群群高可用配置
import VMnew from './VMnew.vue'; // 新建虚拟机
import VMdetails from './VMdetails.vue'; // 虚拟机详情
import VMeditname from './VMeditname.vue'; // 编辑虚拟机名称
import VMconfig from './VMconfig.vue'; // 修改配置
import VMusb from './VMusb.vue'; // 配置USB
import VMmodifyip from './VMmodifyip.vue'; // 修改IP
import VMaddip from './VMaddip.vue'; // 添加网卡
import VMdeletenetip from './VMdeletenetip.vue'; // 删除网卡
import VMmac from './VMmac.vue'; // 绑定MAC地址
import VMsecurityGroup from './VMsecurityGroup.vue'; // 编辑安全组
import VMcoldHeatMigrate from './VMcoldHeatMigrate.vue'; // 冷热迁移
import VMmount from './VMmount.vue'; // 挂载云硬盘
import VMunhook from './VMunhook.vue'; // 分离云硬盘
import VMshelved from './VMshelved.vue'; // 解载云硬盘
import VMclone from './VMclone.vue'; // 克隆
import VMdynamicResource from './VMdynamicResource.vue'; // 动态资源扩展
import VMpassword from './VMpassword.vue'; // 重置密码
import VMdeletion from './VMdeletion.vue'; // 删除
export default {
  components: {
    Pagination,
    GroupNew,
    GroupEdit,
    VMmovein,
    VMremve,
    VMgroupConfig,
    VMhaConfig,
    VMnew,
    VMdetails,
    VMeditname,
    VMconfig,
    VMusb,
    VMmodifyip,
    VMaddip,
    VMdeletenetip,
    VMmac,
    VMsecurityGroup,
    VMcoldHeatMigrate,
    VMmount,
    VMunhook,
    VMshelved,
    VMclone,
    VMdynamicResource,
    VMpassword,
    VMdeletion,
  },
  props: {
    tabSelected: String,
  },
  data() {
    const propXJname = (rule, value, callback) => {
      let list = /^[\u4e00-\u9fa5_a-zA-Z0-9@_.-]{2,32}$/;
      if (list.test(value)) {
        callback();
      } else {
        callback(new Error('请输入2-32个中文或字符（特殊字符可用@_.-）'));
      }
    };

    return {
      // 选中分组
      groupSelect: {
        id: null,
        name: '',
        rootID: null
      },
      newGroupTime: '', // 组 新建
      editGroupTime: '', // 组 编辑
      moveinTime: '', // 虚拟机移入分组
      remveTime: '', // 虚拟机移出分组
      configGroupTime: '', // 虚拟机配置 群操作
      haGroupTime: '', // 高可用设置 群操作
      newvmTime: '', // 新建虚拟机
      detailsTime: '', // 虚拟机详情
      vmRow:{}, // 表格行数据
      editvmTime: '', // 编辑虚拟机名称
      configTime: '', // 修改配置
      usbTime: '', // 配置USB
      modifyipTime:"",  // 修改IP
      addipTime: '', // 添加网卡
      deleteipTime: '', // 删除网卡
      macTime: '', // 绑定MAC地址
      securityGroupTime: '', // 编辑安全组
      coldHeatTime: '', // 冷热迁移
      mountTime: '', // 挂载云硬盘
      unhookTime: '', // 分离云硬盘
      shelvedTime: '', // 废弃虚拟机
      dynamicResourceTime: '', // 动态资源扩展
      pwdTime: '', // 重置密码
      cloneTime: '', // 克隆
      tableArr: [],
      deletionTime: '', // 删除

      // 任务监听
      taskTimer:null, // 计时刷新任务（5m)
      taskWS:null, // WebSocket（5m)
      taskData:[], // 任务表格数据
      taskNumber:1, // 任务计数
    // 虚拟机分组
      // 树形图分组数据
      vmData: [],
      // 监听树形图分组数据选中项
      treevmDatas: [],
      // 新建分组可用？
      newVMdisabled: false,
      formXJZobj: {
        formXJZnode: '',
        editXJZactiveName: '',
      },

      // 正则判断用户输入
      ruleXJZ: {
        name: [
          { required: true, message: '必填项', trigger: 'blur' },
          { validator: propXJname, trigger: 'change' },
        ],

        driveimageId:[{ required: true, message: '必选项', trigger: 'blur' }],
      },
      // 编辑分组可用？
      editVMdisabled: false,
      // 刷新虚拟机组
      loadRefresh: false,
      // 删除分组可用？
      deletVMdisabled: true,
      deletVMstatus:false,

    // 虚拟机列表
      spinShowVM:false,
      vmcolumn: [],
      vmdata: [],
      // 虚拟机表格数据需求
      tablePageForm: {
        id: 1,
        page: 1, // 当前页
        pagecount: this.$store.state.power.pagecount, // 每页条
        search_str: '', // 收索
        order_type: 'desc', // 排序规则
        order_by: '', // 排序列
      },
      tableTotal: 0, // 虚拟机分页总条数

      // 新建虚拟机 弹框 硬件配置项 及信息概览
      newVMForm: {
        // 基本信息-虚拟机名
        name: '',
        networkName: '',
        presetsIP: false,
      },

      // 删除虚拟机-虚拟机移出分组 （通用表格 勾选 数据/名字/ID）
      tableSelec: [],
      tableIDs: [],
      tableNames: [],

      automaticStatus: false, // 宕机自动迁移状态
      // 计时器
      XJnewCallback: null,
      XJtableTask: null,
      taskList: false,

      powerAcitons: {}, // 操作权限数据

      // 存储当前拖拽的虚拟机数据
      draggedVMData: null,

      // 为每一行设置drag属性
      rowAttrs: function(row, index) {
        if (!row || !row.id) {
          console.error('Row data is missing or incomplete:', row);
          return {};
        }
        return {
          draggable: true,
          'data-row-id': row.id,
          'data-row-name': row.name,
          style: 'user-select: none; cursor: grab;',
          ondragstart: (event) => {
            this.handleDragStart(event, row);
          }
        };
      },
    };
  },
  watch: {
    tabSelected(value) {
      if(value=='虚拟机'){
        // this.listeningState() // WebSocket连接
        this.taskQuery() // 任务查询
        this.automaticShutdown() // 自动宕机迁移 查询
        this.actionQuery()
      }else {
        clearInterval(this.XJtableTask)
        clearInterval(this.taskTimer)
      }
    },
    taskNumber(value){
      if(value == 1) {
        this.XJZtableXNJSpinget();
      }
    },
    $route:{
      handler(news , old) {
        if(news.fullPath.split("?")[1] == 'debug') {
          this.vmcolumn.splice(3,0,{ title: "id", key: "id" })
        }else{
        }
      }
    },
    // 监听虚拟机组选中项
    treevmDatas: {
      handler(news, old) {
        if (news.length > 0 && news[0].pid == -1) {
          this.newVMdisabled = false;
          this.editVMdisabled = false;
          this.deletVMdisabled = true;
        } else if (news.length > 0 && news[0].jibie == 4) {
          this.newVMdisabled = true;
          this.editVMdisabled = false;
          this.deletVMdisabled = false;
        } else if (news.length == 0) {
          this.newVMdisabled = true;
          this.editVMdisabled = true;
          this.deletVMdisabled = true;
        } else {
          this.newVMdisabled = false;
          this.editVMdisabled = false;
          this.deletVMdisabled = false;
        }
      },
    },
    // 表格数据刷新判定
    taskList(news){
      if(news==true) {
        this.XJtableTask = setInterval(() => {
          vmGroupTableQuery(this.tablePageForm)
          .then((callback) => {
            let tasknuber = 0
            this.tableTotal = callback.data.total;
            this.vmdata = callback.data.data;
            callback.data.data.forEach((item) => {
              if(item.task_state !==null) {
                tasknuber++
              }
            })
            if(tasknuber==0){
              clearInterval(this.XJtableTask)
              this.taskList= false
            }
          })
        },10000)
      }
    },

  },
  mounted() {
    if(this.$store.state.power.computingResourceTab == '虚拟机') {
      this.taskQuery() // 任务查询
      this.automaticShutdown() // 自动宕机迁移 查询
      this.actionQuery()

      // 添加全局事件处理，防止拖拽时选中文本
      document.addEventListener('dragstart', this.handleGlobalDragStart);
      document.addEventListener('dragend', this.handleGlobalDragEnd);

      // 延迟添加表格行拖拽功能，确保数据已加载
      setTimeout(() => {
        this.initDragFunctionality();
      }, 1000);

      // 监听表格数据变化，重新添加拖拽功能
      this.$watch('vmdata', (newData, oldData) => {
        console.log('vmdata变化:', { newLength: newData.length, oldLength: oldData ? oldData.length : 0 });
        if (newData && newData.length > 0) {
          this.$nextTick(() => {
            setTimeout(() => {
              this.addDragToTableRows();
            }, 500);
          });
        }
      }, { immediate: true });
    }
  },

  beforeDestroy() {
    clearInterval(this.XJnewCallback)
    clearInterval(this.XJtableTask)
    clearInterval(this.taskTimer)

    // 移除全局事件监听器
    document.removeEventListener('dragstart', this.handleGlobalDragStart);
    document.removeEventListener('dragend', this.handleGlobalDragEnd);
  },
  updated() {
    this.tablePageForm.pagecount=this.$store.state.power.pagecount
  },
  methods: {
  // WebSocket连接
  listeningState(){
    if (!window.WebSocket) {
      return console.log('您的浏览器不支持WebSocket');
    }
    let host = window.location.host;
    let url = 'ws://'+host+'/ws';
    this.taskWS = new WebSocket(url);
    // 连接成功的事件
    this.taskWS.onopen = (callback) => {
      console.log('WebSocket连接服务端成功了',callback)
    };
    // 2.当连接成功之后, 服务器关闭的情况
    this.taskWS.onclose = (callback) => {
      console.log('连接成功, 服务器关闭',callback)

    };
    // 得到服务端发送过来的数据
    this.taskWS.onmessage = (callback) => {
      console.log("服务端发来的数据",callback)
    }
  },
  // 任务查询
  taskQuery(){
    this.taskTimer = setInterval(() => {
      vmGroupTableVMtask().then((callback) => {
        let arr = new Array()
        callback.data.data.forEach(em => {
          arr.push({
            name:em.name,
            status:"进行中",
          })
        });
        this.taskData = arr
        callback.data.data.length>0?this.taskNumber++:this.taskNumber=1
      })
    }, 5000);
  },
  // 虚拟机分组
    // 树形图封装
    treeFunciton(arr) {
      // 虚拟机移入分组组弹框表格数据加载
      this.groupSelect.rootID = arr[0].id
      if(this.groupSelect.name == arr[0].name) {
        this.treevmDatas = [arr[0]]
      }
      if(this.groupSelect.name == "" || this.groupSelect.name == arr[0].name){
        this.tablePageForm.id = arr[0].id
        this.groupSelect.id = arr[0].id
        this.groupSelect.name = arr[0].name
        arr[0].selected = true
        this.XJZtableXNJSpinget();
      }
      let f_id = 0;
      let tmp_object = new Object();
      arr[0]["expand"] = "true";
      tmp_object = arr[0];
      let root = arr[0];
      let jibie = 1;
      let list = -1;
      for (var i = 1; i < arr.length; i++) {
        if(this.groupSelect.id == arr[i].id) {
          arr[i].selected = true
        }
        if (arr[i].pid == f_id) {
          if (!tmp_object.children) {
            tmp_object.children = new Array();
            if (list !== arr[i].pid) {
              let tmp_list = -1;
              let tmp_arr = -1;
              for (var k = 1; k < i; k++) {
                if (arr[k].id == list) {
                  tmp_list = arr[k].pid;
                }
                if (arr[k].id == arr[i].pid) {
                  tmp_arr = arr[k].pid;
                }
              }
              if (tmp_arr !== tmp_list) {
                jibie++;
              }
            }
            //arr[i].jibie = jibie
          }
          arr[i].jibie = jibie;
          arr[i]["expand"] = true;
          tmp_object.children.push(arr[i]);
        } else {
          for (var j = 1; j < arr.length; j++) {
            if (arr[j].id == arr[i].pid) {
              tmp_object = arr[j];
            }
          }
          if (!tmp_object.children) {
            tmp_object.children = new Array();
            if (list !== arr[i].pid) {
              let tmp_list = -1;
              let tmp_arr = -1;
              for (var k = 1; k < i; k++) {
                if (arr[k].id == list) {
                  tmp_list = arr[k].pid;
                }
                if (arr[k].id == arr[i].pid) {
                  tmp_arr = arr[k].pid;
                }
              }
              if (tmp_arr !== tmp_list) {
                jibie++;
              }
            }
            //arr[i].jibie = jibie
          }
          arr[i].jibie = jibie;
          arr[i]["expand"] = true;
          tmp_object.children.push(arr[i]);
          f_id = arr[i].pid;
        }
        list = arr[i].pid;
      }
      this.vmData = [root];
    },
    // 树形图添加图标
    renderContent(h, { root, node, data }) {
      if(data.jibie==undefined) {
        return h("span",{
            style: {
              display: "inline-flex",
              width: "100%",
              padding: "0",
              boxSizing: "border-box",
              alignItems: "center",
              height: "20px"
            },
            attrs: {
              "data-group-id": data.id,
              "data-group-name": data.name,
              "data-is-default": "true"
            },
            on: {
              dragover: (event) => {
                event.preventDefault();
                event.stopPropagation();
                // 对默认节点添加警告样式
                event.currentTarget.classList.add('default-group-dragover');
              },
              dragleave: (event) => {
                event.preventDefault();
                event.stopPropagation();
                event.currentTarget.classList.remove('default-group-dragover');
              },
              drop: (event) => {
                event.preventDefault();
                event.stopPropagation();
                event.currentTarget.classList.remove('default-group-dragover');
                // 显示警告提示
                this.$Message.warning({
                  background: true,
                  closable: true,
                  duration: 5,
                  content: '不能将虚拟机移动到默认分组中！',
                });
              }
            }
          },
          [
            h("Icon", {
              props: {
                type: "md-folder",
              },
              style: {
                marginRight: "8px",
                color:"#f1b223",
                flexShrink: 0
              },
            }),
            h("span",{
              style:{
                color:"#333",
                flex: "1",
                whiteSpace: "nowrap",
                overflow: "hidden",
                textOverflow: "ellipsis"
              }
            }, data.name)
          ]
        );
      }else {
        return h("span",{
            style: {
              display: "inline-flex",
              width: "100%",
              padding: "2px 0",
              boxSizing: "border-box",
              alignItems: "center"
            },
            attrs: {
              "data-group-id": data.id,
              "data-group-name": data.name
            },
            on: {
              dragover: (event) => {
                event.preventDefault();
                event.stopPropagation();
                event.currentTarget.style.backgroundColor = '#f0f0f0';
                event.currentTarget.style.border = '1px dashed #1890ff';
              },
              dragleave: (event) => {
                event.preventDefault();
                event.stopPropagation();
                event.currentTarget.style.backgroundColor = '';
                event.currentTarget.style.border = '';
              },
              drop: (event) => {
                event.preventDefault();
                event.stopPropagation();
                event.currentTarget.style.backgroundColor = '';
                event.currentTarget.style.border = '';
                this.handleDropOnTreeNode(event, data);
              }
            }
          },
          [
            h("Icon", {
              props: {
                type: "ios-folder-open",
              },
              style: {
                marginRight: "8px",
                color:"#f1b223",
                flexShrink: 0
              },
            }),
            h("span",{
              style:{
                color:"#333",
                flex: "1",
                whiteSpace: "nowrap",
                overflow: "hidden",
                textOverflow: "ellipsis"
              }
            }, data.name)
          ]
        );
      }
    },
    // 虚拟机组 点击树形节点 事件
    treeVMclick(arr) {
      this.treevmDatas = arr;
      if(arr.length !== 0) {
        this.deletVMstatus = false
        this.groupSelect.id = arr[0].id
        this.groupSelect.name = arr[0].name
        this.tablePageForm.id = arr[0].id
        this.tablePageForm.page = 1
        this.tablePageForm.search_str = ''
        this.tablePageForm.order_type = 'desc'
        this.tablePageForm.order_by = ''
        this.XJZtableXNJSpinget();
      }else {
        this.deletVMstatus = true
        this.deletVMdisabled= true
      }
    },
    // 新建虚拟机点击按钮
    newlyXJZmodal() {
      this.newGroupTime = "" + new Date()
    },
    // 编辑虚拟机组点击事件
    editXJZclick() {
      this.editGroupTime = ""+new Date()
    },
    // 刷新虚拟机组数据
    refreshXJZ() {
      this.loadRefresh = true;
      this.vmData = new Array();
      vmGroupQuery()
        .then((em) => {
          setTimeout(() => {
            this.loadRefresh = false;
            this.treeFunciton(em.data);
          }, 1000);
        })
    },
    // 删除虚拟机组
    deletXJZ() {
      this.$Modal.confirm({
        title: "删除虚拟机组",
        content:
          '<p>是否删除<span style="font-weight: 600;color:red;" >' +
          this.groupSelect.name +
          "</span>虚拟机组?</p>",
        onOk: () => {
          vmGroupDelete({data: { id: this.groupSelect.id,name: this.groupSelect.name }})
            .then((em) => {
              this.$Message.success({
                background: true,
                closable: true,
                duration: 5,
                content: " 删除虚拟机组完成",
              });
              this.refreshXJZ();
              this.vmdata = new Array()
              this.groupSelect.name = ""
              this.deletVMstatus = false
              this.deletVMdisabled= true
            })
        },
        onCancel: () => {

        },
      });
    },

  // 虚拟机列表
    // 虚拟机列表 表头
    cloumnManage() {
      this.vmcolumn = [
        { type: 'selection', width: 30, align: 'center'},
        // { type: 'expand', width: 50,
        //   render: (h, params) => {
        //     return h('div',{style:{width:'100%'}},[
        //       h('span',{style:{width:'45%',display: 'inline-block'}},'ID：'+params.row.id),
        //     ]);
        //   },
        // },
        { title: "名称", key: 'name', sortable: 'custom', minWidth: 100, slot: 'name' },
        // { title: 'IP', key: 'ip',minWidth: 120,sortable: 'custom',slot: 'ip' },
        { title: 'IP', key: 'ip',minWidth: 120, slot: 'ip' },
        { title: '主机名',align: 'center', minWidth: 100,key: 'hostname' },
        { title: 'VCPU',align: 'center', key: 'vcpus',minWidth: 50 },
        { title: '内存',align: 'center', key: 'ram',minWidth: 95, slot: 'ram' },
        { title: '硬盘',align: 'center', key: 'disk',minWidth: 95, slot: 'disk' },
        { title: '任务',align: 'center',key: 'task_state', slot: 'task_state' },
        { title: '状态',align: 'center', key: 'status',minWidth: 80, slot: 'status' },
        ...(this.powerAcitons.xunijigaokeyong?[{ title: '高可用',align: 'center', key: 'ha_status',minWidth: 120, slot: 'ha_status' }]:[]),
        ...(this.powerAcitons.xunijicaozuo?[{ title: "操作", key: "operation",width:120,slot: "operation" }]:[]),
      ];
    },
    // 自动宕机迁移 查询
    automaticShutdown(){
      automaticDowntimeMigrationQuery().then(callback =>{
        if(callback.data.enabled_auto){
          this.automaticStatus=callback.data.enabled_auto=='on'?true:false
        }
      })
    },
    // 状态切换
    handleBeforeChange(row){
      return new Promise((resolve) => {
        this.$Modal.confirm({
          title: '高可用启用',
          content: `<p>是否修改虚拟机 <span style="font-weight: 800;">${row.name}</span> 高可用状态为 <span style="color:${row.ha_status?'red':'green'};">${row.ha_status?'禁用':'启用'}</span></p>`,
          onOk: () => {
            vmHAaction({
              ids: [row.id],
              names: [row.name],
              ha_status: row.ha_status==undefined
            })
            .then((callback) => {
              if(callback.data.msg == 'ok') {
                this.returnOK('高可用状态操作完成')
                resolve();
              }else {
                this.returnError(callback.data.msg)
              }
            }).catch(error=>{
              this.returnError('高可用状态操作失败')
            })

          }
        });
      });
    },
    // 虚拟机列表 数据获取lodng...
    XJZtableXNJSpinget() {
      this.spinShowVM = true
      this.tableSelec = new Array()
      vmGroupTableQuery(this.tablePageForm)
        .then((callback) => {
          this.spinShowVM = false
          this.tableTotal = callback.data.total;
          this.vmdata = callback.data.data;

          // 数据加载完成后，重新初始化拖拽功能
          this.$nextTick(() => {
            setTimeout(() => {
              console.log('数据加载完成，重新初始化拖拽功能');
              this.addDragToTableRows();
            }, 500);
          });

          // for(var i=0;i<100;i++){
          //   this.vmdata.push(
          //     {name:"aaaa"+i}
          //   )
          // }
        })
        .catch((error) => {
          this.spinShowVM = false
        });

    },
    // 虚拟机列表 数据获取
    XJZtableXNJget() {
      this.tableSelec = new Array()
      vmGroupTableQuery(this.tablePageForm)
        .then((callback) => {
          this.tableTotal = callback.data.total;
          this.vmdata = callback.data.data;

          // 数据加载完成后，重新初始化拖拽功能
          this.$nextTick(() => {
            setTimeout(() => {
              console.log('数据更新完成，重新初始化拖拽功能');
              this.addDragToTableRows();
            }, 500);
          });
        })
    },
    // 虚拟机列表 状态数据查询
    XJStatusTableGET(data,action){
      let sizeTitle = "进行中"
      this.$Message.info({
        background: true,
        closable: true,
        duration: 5,
        content: this.vmRow.name + "虚拟机" + action + sizeTitle,
      });
      setTimeout(() => {this.XJZtableXNJget()},1000)

    },
    // 当前分页
    onPageChange(item) {
      this.tablePageForm.page = item;
      this.XJZtableXNJSpinget();
    },
    // 每页条数
    onPageSizeChange(item) {
      this.$store.state.power.pagecount = item
      this.tablePageForm.pagecount = this.$store.state.power.pagecount
      this.tablePageForm.page = 1
      this.tableTotal = 0
      this.tablePageForm.search_str = ""
      this.XJZtableXNJSpinget();
    },
    // 虚拟机列表 列排序
    sortColumn(column, key, order) {
      if (column.key !== "normal") {
        this.tablePageForm.order_by = column.key;
        this.tablePageForm.order_type = column.order;
        this.tablePageForm.page = 1
        this.tableTotal = 0
        this.tablePageForm.search_str = ""
        this.XJZtableXNJSpinget();
      }
    },
    // 搜索虚拟机列表
    tableVMsearchInput(){
      this.tablePageForm.page = 1
      this.tableTotal = 0
      this.XJZtableXNJSpinget();
    },
    // 虚拟机列表 勾选 数据
    tableChange(item) {
      this.tableSelec = item;
      let ids = new Array();
      let names = new Array();
      item.forEach((em) => {
        ids.push(em.id);
        names.push(em.name);
      });
      this.tableIDs = ids;
      this.tableNames = names;
    },
    // 新建虚拟机点击事件
    newBuildClick() {
      this.newvmTime=this.tableTotal+"/"+new Date()
    },
    // 虚拟机移入分组 点击事件
    moveInModalClick(){
      this.moveinTime = "" + new Date()
    },
    // 虚拟机移出分组
    removeClick(data) {
      if (data == 0) {
        this.packageWarning('未选择表格数据')
      } else {
        this.remveTime = "" + new Date()
        this.tableArr = data
        // this.$Modal.confirm({
        //   title: "移出虚拟机",
        //   content:
        //     '<p>是否移出<span style="font-weight: 600;color:green;">' +
        //     this.groupSelect.name +
        //     '</span>虚拟机组下的<span style="font-weight: 600;color:red;;word-break: break-all">' +
        //     this.tableNames.toString() +
        //     "</span>虚拟机？</p>",
        //   onOk: () => {
        //     vmGroupTableRemoveVM({
        //       data: {
        //         group_id: this.groupSelect.id,
        //         group_name: this.groupSelect.name,
        //         vmids: this.tableIDs,
        //         vm_names: this.tableNames
        //       },
        //     })
        //     .then((res) => {
        //       if(res.data.msg == "ok") {
        //         this.XJZtableXNJget()
        //       }else {
				// 				this.$Message.error({
				// 					background: true,
        //           closable: true,
				// 					duration: 5,
				// 					content: "移出虚拟机失败",
				// 				});
				// 			}
        //     });
        //   },
        //   onCancel: () => {},
        // });
      }
    },
    // 虚拟机配置 群操作
    groupConfigClick(data){
      if (data == 0) {
        this.packageWarning('未选择表格数据')
      } else {
        this.configGroupTime = "" + new Date()
        this.tableArr = data
      }
    },
    // 虚拟机高可用设置 群操作
    haConfigClick(data){
      if (data == 0) {
        this.packageWarning('未选择表格数据')
      } else {
        this.haGroupTime = "" + new Date()
        this.tableArr = data
      }
    },
    // 删除虚拟机
    deletClick(data) {
      if(data == 0) {
        this.packageWarning('未选择表格数据')
      }else {
        this.tableArr = data
        this.deletionTime = ''+new Date()
      }
    },


    // 虚拟机表格 操作栏 开机 关机 重启等状态封装
    actionPackage(row, action) {
      this.vmRow = row;
      let lis = {
        id:row.id,
        action:action,
        data:"",
        name:row.name
      }
      switch (action) {
        // 开机
        case "start":
          this.$Modal.confirm({
            title: row.name+"开机操作",
            content:
              '<p>是否对<span style="font-weight: 600;font-siaze:30px;">' +
              row.name +
              '</span>虚拟机进行<span style="font-weight: 600;color:green;">开机</span>操作?</p>',
            onOk: () => {
              this.vmTableActionAxios(lis, "开机");
            },
            onCancel: () => {},
          });
          break;
        // 关机
        case "stop":
          this.$Modal.confirm({
            title: row.name+"关机操作",
            content:
              '<p>是否对<span style="font-weight: 600;font-siaze:30px;">' +
              row.name +
              '</span>虚拟机进行<span style="font-weight: 600;color:red;">关机</span>操作?</p>',
            onOk: () => {
              this.vmTableActionAxios(lis, "关机");
            },
            onCancel: () => {},
          });
          break;
        // 重启
        case "reboot":
          this.$Modal.confirm({
            title: row.name+"重启操作",
            content:
              '<p>是否对<span style="font-weight: 600;font-siaze:30px;">' +
              row.name +
              '</span>虚拟机进行<span style="font-weight: 600;color:red;">重启</span>操作?</p>',
            onOk: () => {
              this.vmTableActionAxios(lis, "重启");
            },
            onCancel: () => {},
          });
          break;
        // 确认调整大小/迁移
        case "confirm":
          this.vmTableActionAxios(lis, "确认调整大小/迁移");
          break;
        // 取消调整大小/迁移
        // case "revert":
        //   this.vmTableActionAxios(lis, "取消调整大小/迁移");
        //   break;
        // 控制台
        case "getVNCConsole":
          vmGroupTableAction({id: row.id,action: action,data:'',name:row.name})
          .then((callback) => {
            if(callback.data.msg == "ok") {
              window.open(callback.data.console)
            }else{
              this.$Message.error({
                background: true,
                closable: true,
                duration: 5,
                content: "打开控制台失败",
              });
            }
          })
          break;
        // 挂起
        case "suspend":
          this.$Modal.confirm({
            title: "虚拟机挂起",
            content:
              '<p>是否对虚拟机<span style="font-weight: 800">' +
              row.name +
              "</span>进行挂起操作?</p>",
            onOk: () => {
              this.vmTableActionAxios(lis, "虚拟机挂起");
            },
            onCancel: () => {},
          });
          break;
          // 解挂
        case "resume":
          this.$Modal.confirm({
            title: "虚拟机解挂",
            content:
              '<p>是否对虚拟机<span style="font-weight: 800">' +
              row.name +
              "</span>进行解挂操作?</p>",
            onOk: () => {
              this.vmTableActionAxios(lis, "虚拟机解挂");
            },
            onCancel: () => {},
          });
          break;
      }
    },
    // 虚拟机表格 操作栏 开机 关机 重启等接口调用
    vmTableActionAxios(data, action) {
      vmGroupTableAction(data)
      .then((callback) => {
        if(callback.data.msg == "ok") {
          setTimeout(() => {
            this.XJStatusTableGET(data,action)
          }, 1000);
        }else{
          this.$Message.error({
            background: true,
            closable: true,
            duration: 5,
            content: action+"失败",
          });
        }
      });
    },

    // 虚拟组返回
    groupError(data) {
      this.$Message.error({
        background: true,
        closable: true,
        duration: 5,
        content: data,
      });
    },
    groupOK(data,name) {
      this.groupSelect.name = name;
      this.$Message.success({
        background: true,
        closable: true,
        duration: 5,
        content: data,
      });
      setTimeout(() => {
        this.refreshXJZ()
      }, 1000);
    },
    // 子组件返回 全局loding
    customError(data) {
      this.$Message.error({
        background: true,
        closable: true,
        duration: 5,
        content: data,
      });
    },
    customOK(data) {
      this.XJZtableXNJSpinget()
      this.$Message.success({
        background: true,
        closable: true,
        duration: 5,
        content: data,
      });
    },
    // 子组件返回 更新表数据无loding
    returnError(data){
      this.$Message.error({
        background: true,
        closable: true,
        duration: 5,
        content: data,
      });
    },
    returnOK(data) {
      setTimeout(() => {
        this.XJZtableXNJget()
      }, 1000);
      this.$Message.success({
        background: true,
        closable: true,
        duration: 5,
        content: data,
      });
    },
    // 封装告警提示
    packageWarning(data) {
      this.$Message.warning({
        background: true,
        closable: true,
        duration: 5,
        content: data,
      });
    },

    // 开关机v2
    operateOnOff(state){
      if (this.tableSelec==0) {
        this.packageWarning('未选择表格数据')
      } else {
        let data = {
          ids:[],
          action:state,
          data:"",
          names:[]
        }
        let ids = new Array()
        let names = new Array()
        this.tableSelec.forEach(em=>{
          if(state=="start"){
            if(em.status=="SHUTOFF") {
              ids.push(em.id)
              names.push(em.name)
            }
          }else {
            if(em.status=="ACTIVE") {
              ids.push(em.id)
              names.push(em.name)
            }
          }
        })
        data.ids = ids
        data.names = names
        this.$Modal.confirm({
          title:state=="start"?"开机":"关机",
          content:
            '<p>是否对所选虚拟机进行'+
            '<span style="font-weight: 600;color:'+(state=="start"?"green":"red")+'">'+(state=="start"?"开机":"关机")+'</span>操作?</p>',
          onOk: () => {
            this.$Message.info({
              background: true,
              closable: true,
              duration: 5,
              content: state=="start"?"开机操作已运行":"关机操作已运行",
            });
            if(data.ids.length>0){
              vmGroupTableBatchOnOffMachine(data)
              .then((callback) => {
                if(callback.data.msg == "ok") {
                  setTimeout(() => {
                    this.XJZtableXNJget()
                  }, 5000);
                }else{
                  this.$Message.error({
                    background: true,
                    closable: true,
                    duration: 5,
                    content: state=="start"?"开机失败":"关机失败",
                  });
                }
              });
            }
          },
          onCancel: () => {},
        });
      }
    },

    // 判断操作是否禁用
    dropdownDisabled(item){
      if(item=='SUSPENDED') {
        return true
      }else {
        return false
      }
    },
    // 操作下拉点击
    dropdownClick(event,row){
      this.vmRow = row
      switch (event) {
        case "tcgp":
          if(row.status =='SHUTOFF' && window.gurl.architecture == 'X86') {
            vmEjectDiscX86({ id: row.id })
            .then((callbackSC) => {
              if(callbackSC.data.msg == "ok") {
                this.returnOK(row.name+'虚拟机已经弹出光盘')
              }else {
                this.returnError("虚拟机弹出光盘失败")
              }
            });
          }else if(row.status =='SHUTOFF' && window.gurl.architecture == 'ARM'){
            vmEjectDiscARM({ id: row.id })
            .then((callbackSC) => {
              if(callbackSC.data.msg == "ok") {
                this.returnOK(row.name+'虚拟机已经弹出光盘')
              }else {
                this.returnError("虚拟机弹出光盘失败")
              }
            });
          }else{
            this.packageWarning('请先关闭当前虚拟机,再执行此操作')
          }
          break;
        case "kj":
          this.actionPackage(row, 'start')
          break;
        case "gj":
          this.actionPackage(row, 'stop')
          break;
        case "cq":
          this.actionPackage(row, 'reboot')
          break;
        case "bjxj":
          this.editvmTime = ""+new Date()
          break;
        case "qhxt":
          this.VMswitching(row)
          break;
        case "xgpz":
          this.configTime = ""+ new Date()
          break;
        case "pzusb":
          this.usbTime = ""+new Date()
          break;
        case "xgip":
          if(row.addresses.length>0) {
            this.modifyipTime = ""+new Date()
          }
          break;
        case "tjwk":
          this.addipTime = ""+new Date()
          break;
        case "scwk":
          this.deleteipTime = ""+new Date()
          break;
        case "bdmac":
          this.macTime = ""+new Date()
          break;
        case "bjwk":
          this.securityGroupTime = ""+new Date()
          break;
        case "qy":
          this.coldHeatTime = ""+new Date()
          break;
        case "kz":
          // this.snapshotTime = ""+new Date()
          break;
        case "gzyyp":
          this.mountTime = ''+new Date()
          break;
        case "flyyp":
          this.unhookTime = ''+new Date()
          break;
        // case "gqjg":
        //   if(row.status == "ACTIVE") {
        //     this.actionPackage(row, "suspend")
        //   }else if(row.status == "SUSPENDED") {
        //     this.actionPackage(row, "resume")
        //   }
        //   break;
        case "gq":
          this.actionPackage(row, "suspend")
          break;
        case "jg":
          this.actionPackage(row, "resume")
          break;
        case "fqxj":
          this.shelvedTime = ''+new Date()
          break;
        case "qxfq":
          this.shelvedTime = ''+new Date()
          break;
        case "kl":
          this.cloneTime = ''+new Date()
          break;
        case "dtzykz":
          this.dynamicResourceTime = ''+new Date()
          break;
        case "czmm":
          this.pwdTime = ''+new Date()
          break;
        case "sc":
          this.deletClick([row])
          break;
      }
    },
    // 虚机组详情
    nameClick(row){
      this.vmRow = row
      this.detailsTime = ""+new Date();
    },
    // 表格 切换系统类型
    VMswitching(row) {
      let type = row.os_type=="linux"?"windows":"linux"
      this.$Modal.confirm({
        title: row.name+'切换系统类型',
        content: '<p>是否将当前系统类型 <span style="font-style: italic;font-weight:100">'+row.os_type+'</span> 切换为 <span style="color:green;font-weight:800"> '+type+'</span> ？</p>',
        onOk: () => {
          vmGroupTableSystemType({
            vm_id: row.id,
            os_type: type,
            vm_name: row.name,
          }).then(callback=>{
            if(callback.data.msg == "ok") {
              this.customOK(row.name+"切换系统类型操作完成")
            }else {
              this.customError(row.name+"切换系统类型操作失败")
            }
          })
        },
        onCancel: () => {}
      });
    },


    // IP转换
    convertIP(item){
      let ips = item.map(em=>{return em.addr})
      return ips.toString()
    },
    // 任务转换
    convertTask(item){
      if(item) {
        this.taskList = true
      }
    },
    // 状态转换
    convertStatus(list,row) {
      let text = '未监测到'
      let color = '#000'
      switch (row.status) {
        case "ACTIVE":
          text = '开机'
          color = 'green'
          break;
        case "BUILD":
          text = '构建'
          break;
        case "DELETED":
          text = '删除'
          break;
        case "ERROR":
          text = '错误'
          break;
        case "HARD_REBOOT":
          text = '重启'
          break;
        case "REBOOT":
          text = '重启'
          break;
        case "MIGRATING":
          text = '迁移'
          break;
        case "PASSWORD":
          text = '密码'
          break;
        case "PAUSED":
          text = '已暂停'
          break;
        case "REBUILD":
          text = '重建'
          break;
        case "RESCUE":
          text = '救援'
          break;
        case "RESIZE":
          text = '准备调整大小/迁移'
          color =  '#a2e3a1'
          break;
        case "REVERT_RESIZE":
          text = '已取消调整大小/迁移'
          color =  '#e58fab'
          break;
        case "VERIFY_RESIZE":
          text = '正在调整大小/迁移'
          color =  '#ed9e5b'
          setTimeout(() => {
            this.actionPackage(row, "confirm")
          }, 1000);
          break;
        case "SHELVED":
          text = '搁置'
          break;
        case "SHELVED_OFFLOADED":
          text = '搁置卸载'
          break;
        case "SHUTOFF":
          text = '关机'
          color = '#ccc'
          break;
        case "SOFT_DELETED":
          text = '软删除'
          break;
        case "SUSPENDED":
          text = '挂起'
          break;
        case "UNKNOWN":
          text = '未知'
          break;
      }
      if(list == 'fields') {
        return text
      }else {
        return color
      }
    },

    // 操作权限获取
    actionQuery(){
      powerCodeQuery({
        module_code: [
          'xunijiliebiao',
          'xunijichaxun',
          'xunijifenzutianjia',
          'xunijifenzubianji',
          'xunijifenzushanchu',
          'xunijifenzushuaxin',
          'xunijixinjian',
          'xunijiyirufenzu',
          'xunijiyichufenzu',
          'xunijiyirufenzu',

          'xunijicaozuo',
          'xunijishanchu',
          'xunijikongzhitai',
          'xunijigaokeyong',
          'xunijikaiji',
          'xunijiguanji',
          'xunijichongqi',
          'xunijibianji',
          'xunijiqiehuanxitongleixing',
          'xunijixiugaipeizhi',
          'xunijixiugaiip',
          'xunijitianjiawangka',
          'xunijishanchuwangka',
          'xunijibangdingMACdizhi',
          'xunijibianjianquanzu',
          'xunijiqianyi',
          'xunijiguazaiyunyingpan',
          'xunijifenliyunyingpan',
          'xunijiguaqi',
          'xunijijiegua',
          'xunijifeiqi',
          'xunijiquxiaofeiqi',
          'xunijikelong',
          'xunijidongtaiziyuankuozhan',
          'xunijichongzhimima',
        ]
      }).then(callback=>{
        this.powerAcitons = callback.data.data
        this.cloumnManage()
        if(this.powerAcitons.xunijiliebiao){
          this.refreshXJZ(); // 虚拟机组默认加载
        }
      })
    },
    // 拖拽事件处理
    handleDragStart(event, row) {
      // 阻止默认行为，防止文本选择
      event.stopPropagation();

      try {
        // 保存被拖拽的虚拟机数据到全局变量
        this.draggedVMData = row;

        // 保存到window对象上，确保在任何地方都能访问
        window.lastDraggedVM = row;

        // 同时也设置到dataTransfer中
        const rowData = JSON.stringify(row);
        event.dataTransfer.setData('text/plain', rowData);
        event.dataTransfer.setData('application/json', rowData);

        // 设置拖拽效果
        event.dataTransfer.effectAllowed = 'move';

        // 添加拖拽时的视觉反馈
        const target = event.currentTarget;
        if (target) {
          target.classList.add('dragging');

          // 拖拽结束后移除样式
          const dragEndHandler = () => {
            target.classList.remove('dragging');
            window.removeEventListener('dragend', dragEndHandler);
          };
          window.addEventListener('dragend', dragEndHandler);
        }
      } catch (error) {
        console.error('设置拖拽数据时出错:', error);
      }
    },

    // 处理拖拽到虚拟机组区域的事件
    handleDropOnGroup(event) {
      event.preventDefault();

      // 移除高亮样式
      event.currentTarget.classList.remove('dragover-highlight');

      // 如果没有选择任何组，提示用户
      if (!this.groupSelect.id) {
        this.$Message.warning({
          background: true,
          closable: true,
          duration: 5,
          content: '请先选择一个虚拟机组！',
        });
        return;
      }

      // 检查是否是默认分组
      if (this.treevmDatas.length > 0 && (this.treevmDatas[0].jibie === undefined || this.treevmDatas[0].pid === -1)) {
        this.$Message.warning({
          background: true,
          closable: true,
          duration: 5,
          content: '不能将虚拟机移动到默认分组中！',
        });
        return;
      }

      try {
        // 首先尝试使用全局变量中的数据
        let vmData = this.draggedVMData || window.lastDraggedVM;

        if (!vmData) {
          // 如果全局变量中没有数据，尝试从dataTransfer获取
          let dataStr = event.dataTransfer.getData('text/plain');
          // 如果没有获取到数据，尝试其他格式
          if (!dataStr) {
            dataStr = event.dataTransfer.getData('application/json');
          }

          if (dataStr) {
            // 解析JSON数据
            try {
              vmData = JSON.parse(dataStr);
            } catch (e) {
              console.error('解析JSON数据失败:', e);
            }
          }
        }

        // 如果仍然没有获取到数据，尝试从拖拽源元素获取
        if (!vmData) {
          const dragSource = document.querySelector('.dragging');
          if (dragSource) {
            const dragInfoStr = dragSource.getAttribute('data-drag-info');
            if (dragInfoStr) {
              try {
                vmData = JSON.parse(dragInfoStr);
              } catch (e) {
                console.error('解析拖拽源元素数据失败:', e);
              }
            }
          }
        }

        // 如果所有方法都失败了，报错并返回
        if (!vmData) {
          console.error('没有获取到拖拽数据');
          this.$Message.error({
            background: true,
            content: '拖拽操作失败：无法获取虚拟机数据'
          });
          return;
        }
        // 确认拖放操作
        this.$Modal.confirm({
          title: "移入虚拟机",
          content:
            '<p>是否将虚拟机<span style="font-weight: 600;color:red;">' +
            vmData.name +
            '</span>移入<span style="font-weight: 600;color:green;">' +
            this.groupSelect.name +
            '</span>分组?</p>',
          onOk: () => {
            // 使用移入分组的API
            vmGroupTableMoveinGroup({
              group_id: this.groupSelect.id,
              group_name: this.groupSelect.name,
              vmids: [vmData.id],
              vm_names: [vmData.name]
            })
            .then((callback) => {
              if(callback.data.msg == "ok") {
                this.$Message.success({
                  background: true,
                  closable: true,
                  duration: 5,
                  content: '虚拟机移入分组操作完成',
                });

                // 存储当前分组ID以便刷新后恢复
                const currentGroupId = this.groupSelect.id;

                // 先刷新分组树
                this.refreshXJZ();

                // 然后使用定时器，确保在树刷新完成后重新选中当前分组
                setTimeout(() => {
                  // 找到并选中原来的分组
                  if (this.vmData && this.vmData.length > 0) {
                    this.vmData.forEach(node => {
                      this.findAndSelectNode(node, currentGroupId);
                    });
                  }

                  // 刷新表格数据
                  this.XJZtableXNJget();
                }, 1000);
              } else {
                this.$Message.error({
                  background: true,
                  closable: true,
                  duration: 5,
                  content: '虚拟机移入分组操作失败',
                });
              }
            })
            .catch((error) => {
              this.$Message.error({
                background: true,
                closable: true,
                duration: 5,
                content: '虚拟机移入分组操作失败',
              });
            });
          }
        });
      } catch (error) {
        console.error('拖拽数据解析失败:', error);
      }
    },

    // 处理拖拽悬停在虚拟机组区域上的事件
    handleDragOver(event) {
      // 添加高亮样式
      event.currentTarget.classList.add('dragover-highlight');
    },

    // 处理拖拽离开虚拟机组区域的事件
    handleDragLeave(event) {
      // 移除高亮样式
      event.currentTarget.classList.remove('dragover-highlight');
    },

    // 处理拖拽到树节点的事件
    handleDropOnTreeNode(event, node) {
      try {
        // 检查是否是默认分组
        if (node.jibie === undefined || node.pid === -1) {
          this.$Message.warning({
            background: true,
            closable: true,
            duration: 5,
            content: '不能将虚拟机移动到默认分组中！',
          });
          return;
        }

        // 首先尝试使用全局变量中的数据
        let vmData = this.draggedVMData || window.lastDraggedVM;

        if (!vmData) {
          // 如果全局变量中没有数据，尝试从dataTransfer获取
          let dataStr = event.dataTransfer.getData('text/plain');
          // 如果没有获取到数据，尝试其他格式
          if (!dataStr) {
            dataStr = event.dataTransfer.getData('application/json');
          }

          if (dataStr) {
            // 解析JSON数据
            try {
              vmData = JSON.parse(dataStr);
            } catch (e) {
              console.error('解析JSON数据失败:', e);
            }
          }
        }

        // 如果仍然没有获取到数据，尝试从拖拽源元素获取
        if (!vmData) {
          const dragSource = document.querySelector('.dragging');
          if (dragSource) {
            const dragInfoStr = dragSource.getAttribute('data-drag-info');
            if (dragInfoStr) {
              try {
                vmData = JSON.parse(dragInfoStr);
              } catch (e) {
                console.error('解析拖拽源元素数据失败:', e);
              }
            }
          }
        }

        // 如果所有方法都失败了，报错并返回
        if (!vmData) {
          console.error('没有获取到拖拽数据');
          this.$Message.error({
            background: true,
            content: '拖拽操作失败：无法获取虚拟机数据'
          });
          return;
        }
        // 确认拖放操作
        this.$Modal.confirm({
          title: "移入虚拟机",
          content:
            '<p>是否将虚拟机<span style="font-weight: 600;color:red;">' +
            vmData.name +
            '</span>移入<span style="font-weight: 600;color:green;">' +
            node.name +
            '</span>分组?</p>',
          onOk: () => {
            // 使用移入分组的API
            vmGroupTableMoveinGroup({
              group_id: node.id,
              group_name: node.name,
              vmids: [vmData.id],
              vm_names: [vmData.name]
            })
            .then((callback) => {
              if(callback.data.msg == "ok") {
                this.$Message.success({
                  background: true,
                  closable: true,
                  duration: 5,
                  content: '虚拟机移入分组操作完成',
                });
                // 刷新表格数据
                this.XJZtableXNJget();

                // 如果当前选中的不是目标分组，则刷新左侧树状分组数据
                if (this.groupSelect.id !== node.id) {
                  // 记住当前选中的分组
                  const currentGroupId = this.groupSelect.id;
                  const currentGroupName = this.groupSelect.name;

                  // 刷新左侧树
                  this.refreshXJZ();

                  // 定时恢复当前选中的分组状态
                  setTimeout(() => {
                    if (this.treevmDatas.length === 0 || this.treevmDatas[0].id !== currentGroupId) {
                      // 恢复选中状态
                      this.vmData.forEach(item => {
                        this.findAndSelectNode(item, currentGroupId);
                      });
                    }
                  }, 1200); // 等待树刷新完成
                }
              } else {
                this.$Message.error({
                  background: true,
                  closable: true,
                  duration: 5,
                  content: '虚拟机移入分组操作失败: ' + callback.data.msg,
                });
              }
            })
            .catch((error) => {
              console.error('API错误:', error);
              this.$Message.error({
                background: true,
                closable: true,
                duration: 5,
                content: '虚拟机移入分组操作失败',
              });
            });
          }
        });
      } catch (error) {
        console.error('拖拽数据解析失败:', error);
        this.$Message.error({
          background: true,
          closable: true,
          duration: 5,
          content: '拖拽数据处理失败: ' + error.message,
        });
      }
    },

    // 查找并选中指定ID的节点
    findAndSelectNode(node, targetId) {
      if (node.id === targetId) {
        // 找到目标节点，手动触发选择事件
        this.treeVMclick([node]);
        return true;
      }

      if (node.children && node.children.length > 0) {
        for (let child of node.children) {
          if (this.findAndSelectNode(child, targetId)) {
            return true;
          }
        }
      }

      return false;
    },

    // 点击表格行事件
    rowClick(row, index) {
      // 可以在这里处理行点击事件，如果需要的话
    },

    // 初始化拖拽功能
    initDragFunctionality() {
      console.log('初始化拖拽功能');

      // 检查数据是否已加载
      if (!this.vmdata || this.vmdata.length === 0) {
        console.log('数据未加载，延迟初始化拖拽功能');
        setTimeout(() => {
          this.initDragFunctionality();
        }, 1000);
        return;
      }

      // 检查表格是否已渲染
      const tableRows = document.querySelectorAll('.vm_table_content .ivu-table-body tr');
      if (!tableRows.length) {
        console.log('表格未渲染，延迟初始化拖拽功能');
        setTimeout(() => {
          this.initDragFunctionality();
        }, 500);
        return;
      }

      console.log('数据和表格都已准备好，开始添加拖拽功能');
      this.addDragToTableRows();
    },

    // 手动为表格行添加拖拽功能
    addDragToTableRows() {
      console.log('开始添加表格行拖拽功能');

      // 检查vmdata是否存在且有数据
      if (!this.vmdata || this.vmdata.length === 0) {
        console.error('vmdata为空或未定义，无法添加拖拽功能');
        return;
      }

      // 获取表格行元素
      const tableRows = document.querySelectorAll('.vm_table_content .ivu-table-body tr');

      if (!tableRows.length) {
        console.log('未找到表格行，可能表格还未渲染完成');
        return;
      }

      console.log('找到', tableRows.length, '个表格行');
      console.log('vmdata长度:', this.vmdata.length);
      console.log('vmdata前3项:', this.vmdata.slice(0, 3));

      // 确保表格行数量与数据数量匹配
      if (tableRows.length !== this.vmdata.length) {
        console.warn('表格行数量与数据数量不匹配', {
          tableRows: tableRows.length,
          vmdata: this.vmdata.length
        });
      }

      // 为每一行添加拖拽功能
      tableRows.forEach((row, index) => {
        // 获取行数据
        const rowData = this.vmdata[index];
        console.log(`处理第${index}行，数据:`, rowData);

        if (!rowData) {
          console.error('第', index, '行没有数据，跳过');
          return;
        }

        if (!rowData.id || !rowData.name) {
          console.error('第', index, '行数据不完整，缺少id或name:', rowData);
          return;
        }

        // 设置拖拽属性
        row.setAttribute('draggable', 'true');
        row.setAttribute('data-row-id', rowData.id);
        row.setAttribute('data-row-name', rowData.name);
        row.style.userSelect = 'none';
        row.style.cursor = 'grab';

        // 移除旧的事件监听器
        const oldMouseDownHandler = row._mouseDownHandler;
        const oldDragStartHandler = row._dragStartHandler;
        if (oldMouseDownHandler) {
          row.removeEventListener('mousedown', oldMouseDownHandler);
        }
        if (oldDragStartHandler) {
          row.removeEventListener('dragstart', oldDragStartHandler);
        }

        // 创建新的事件处理器并保存引用
        const mouseDownHandler = (event) => {
          console.log('鼠标按下，行索引:', index, '行数据:', rowData);
          this.handleRowMouseDown(event, rowData, index);
        };

        const dragStartHandler = (event) => {
          console.log('拖拽开始，行索引:', index);
          this.handleRowDragStart(event, index);
        };

        // 保存事件处理器引用到元素上
        row._mouseDownHandler = mouseDownHandler;
        row._dragStartHandler = dragStartHandler;
        row._rowData = rowData;
        row._rowIndex = index;

        // 添加事件监听器
        row.addEventListener('mousedown', mouseDownHandler);
        row.addEventListener('dragstart', dragStartHandler);

        console.log(`第${index}行拖拽功能已添加，数据:`, {
          id: rowData.id,
          name: rowData.name
        });
      });

      console.log('所有表格行的拖拽功能已添加完成');
    },

    // 行鼠标按下事件处理
    handleRowMouseDown(event, rowData, index) {
      console.log('鼠标按下事件处理 - 行索引:', index, '传入的行数据:', rowData);

      // 多重数据获取策略
      let finalRowData = rowData;

      // 策略1：使用传入的rowData
      if (!finalRowData && typeof index !== 'undefined') {
        console.log('策略1失败，尝试策略2：从vmdata中获取');
        finalRowData = this.vmdata[index];
      }

      // 策略2：从元素的存储数据中获取
      if (!finalRowData) {
        console.log('策略2失败，尝试策略3：从元素存储中获取');
        const row = event.currentTarget;
        finalRowData = row._rowData;
      }

      // 策略3：从元素属性中获取
      if (!finalRowData) {
        console.log('策略3失败，尝试策略4：从元素属性中获取');
        const row = event.currentTarget;
        const rowId = row.getAttribute('data-row-id');
        const rowName = row.getAttribute('data-row-name');
        if (rowId && rowName) {
          // 从vmdata中查找匹配的数据
          finalRowData = this.vmdata.find(item => item.id === rowId);
        }
      }

      // 策略4：从表格行位置推断
      if (!finalRowData && typeof index !== 'undefined') {
        console.log('策略4失败，尝试策略5：重新计算索引');
        const row = event.currentTarget;
        const tableRows = document.querySelectorAll('.vm_table_content .ivu-table-body tr');
        const actualIndex = Array.from(tableRows).indexOf(row);
        if (actualIndex >= 0 && actualIndex < this.vmdata.length) {
          finalRowData = this.vmdata[actualIndex];
        }
      }

      if (!finalRowData) {
        console.error('所有策略都失败，无法获取行数据');
        return;
      }

      console.log('成功获取行数据:', finalRowData);

      // 保存被拖拽的虚拟机数据到全局变量
      this.draggedVMData = finalRowData;
      window.lastDraggedVM = finalRowData;

      // 将数据保存到元素的自定义属性中
      const row = event.currentTarget;
      row.setAttribute('data-drag-info', JSON.stringify(finalRowData));

      console.log('拖拽数据已保存:', {
        name: finalRowData.name,
        id: finalRowData.id,
        draggedVMData: this.draggedVMData,
        windowData: window.lastDraggedVM
      });
    },

    // 行拖拽开始事件处理
    handleRowDragStart(event, index) {
      console.log('拖拽开始事件处理 - 行索引:', index);

      // 从元素的自定义属性中获取数据
      const target = event.currentTarget;
      const dragInfoStr = target.getAttribute('data-drag-info');

      // 尝试获取行数据
      let rowData = this.draggedVMData || window.lastDraggedVM;

      // 如果全局变量中没有数据，尝试从元素属性中获取
      if (!rowData && dragInfoStr) {
        try {
          rowData = JSON.parse(dragInfoStr);
          console.log('从元素属性中获取到数据:', rowData);
        } catch (e) {
          console.error('解析元素属性数据失败:', e);
        }
      }

      // 如果仍然没有数据，尝试从vmdata中获取
      if (!rowData && typeof index !== 'undefined') {
        rowData = this.vmdata[index];
        console.log('从vmdata中获取到数据:', rowData);
      }

      // 如果仍然没有数据，尝试从元素的自定义属性中获取
      if (!rowData) {
        const storedRowData = target._rowData;
        if (storedRowData) {
          rowData = storedRowData;
          console.log('从元素存储的数据中获取:', rowData);
        }
      }

      if (!rowData) {
        console.error('拖拽开始时无法获取行数据');
        return;
      }

      console.log('拖拽开始，使用数据:', rowData);

      // 同时也设置到dataTransfer中
      try {
        const rowDataStr = JSON.stringify(rowData);
        event.dataTransfer.setData('text/plain', rowDataStr);
        event.dataTransfer.setData('application/json', rowDataStr);

        // 设置拖拽效果
        event.dataTransfer.effectAllowed = 'move';

        // 添加拖拽时的视觉反馈
        if (target) {
          target.classList.add('dragging');
        }

        console.log('拖拽数据已设置到dataTransfer');
      } catch (error) {
        console.error('设置拖拽数据时出错:', error);
      }
    },

    // 全局拖拽开始事件处理
    handleGlobalDragStart(event) {
      if (event.target.closest('.ivu-table-body tr')) {
        // 阻止文本选择
        document.body.style.userSelect = 'none';
      }
    },

    // 全局拖拽结束事件处理
    handleGlobalDragEnd() {
      // 恢复文本选择
      document.body.style.userSelect = '';

      // 移除拖拽样式
      const draggingRows = document.querySelectorAll('.dragging');
      draggingRows.forEach(row => {
        row.classList.remove('dragging');
        // 清除自定义属性
        row.removeAttribute('data-drag-info');
      });

      // 清除所有表格行的data-drag-info属性
      const tableRows = document.querySelectorAll('.vm_table_content .ivu-table-body tr');
      tableRows.forEach(row => {
        row.removeAttribute('data-drag-info');
      });

      // 清除拖拽数据
      setTimeout(() => {
        this.draggedVMData = null;
        window.lastDraggedVM = null;
      }, 100);
    },
  },
  beforeDestroy() {
    clearInterval(this.XJnewCallback)
    clearInterval(this.XJtableTask)
    clearInterval(this.taskTimer)
  },
};
</script>
<style>
.chongse .ivu-form-item {
  padding: 10px 0;
}
.cipan .ivu-tooltip ,.chongse .ivu-tooltip{
  position: relative;
}
.cipan .ivu-tooltip-popper, .chongse .ivu-tooltip-popper {
  top: -48px!important;
}
</style>

rewritten_file>