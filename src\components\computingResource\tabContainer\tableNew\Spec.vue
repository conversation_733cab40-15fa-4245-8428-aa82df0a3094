<template>
  <div class="general_page_area">
    <Form :model="formItem" ref="formItem" :rules="ruleValidate" :label-width="120">
			<FormItem label="主机名称">
        <Input v-model="formItem.hostname" placeholder="请输入主机名称"></Input>
    	</FormItem>
      <FormItem label="运行时">
        <Input v-model="formItem.runtime" placeholder="请输入创建容器的运行时"></Input>
    	</FormItem>
      <FormItem label="CPU" prop="cpu">
        <Input v-model="formItem.cpu" type='number' placeholder="请输入容器虚拟CPU数量"></Input>
    	</FormItem>
      <FormItem label="内存" prop="memory">
        <Input v-model="formItem.memory"  type='number' placeholder="请输入内存大小(MB)"></Input>
    	</FormItem>
      <FormItem label="磁盘">
        <Input v-model="formItem.disk" type='number' placeholder="请输入磁盘大小(GB)"></Input>
    	</FormItem>
      <!-- <FormItem label="可用域">
        <Select v-model="formItem.domain">
          <Option value="Select availability zone">Select availability zone</Option>
          <Option value="nova" >nova</Option>
        </Select>
    	</FormItem> -->
      <FormItem label="选择策略">
        <Select v-model="formItem.restartPolicy">
          <Option value="no" >容器退出后不自动重启</Option>
          <Option value="on-failure" >失败时重启</Option>
          <Option value="always" >始终重启</Option>
          <Option value="unless-stopped" >停止时重启</Option>
          <Option value="Remove-container" >删除容器</Option>
        </Select>
    	</FormItem>
      <FormItem label="最大重试次数">
        <Input v-model="formItem.retry" type='number' :disabled="formItem.restartPolicy!=='on-failure'" placeholder="请输入最大重试次数"></Input>
    	</FormItem>
      <FormItem label="启用自动修复">
        <Checkbox v-model="formItem.heal"></Checkbox>
    	</FormItem>
		</Form>
  </div>
</template>
<script>
export default {
  props: {
    times:String,
  },
  watch: {
    times(value) {
      this.$emit("returnOK",{
        page: 1,
        data: this.formItem,
      });
      this.$refs.formItem.validate((valid) => {
        if (valid) {
          this.$emit("returnOK",{
            page: 1,
            type: true,
            data: this.formItem,
          });
        }else {
          this.$emit("returnOK",{
            page: 1,
            type: false,
            data: this.formItem,
          });
        }
      })
    },
    'formItem.restartPolicy'(news) {
      if(news !== 'on-failure') {
        this.formItem.retry = ''
      }
    }
  },
  data(){
    const propCPU = (rule, value, callback) => {
      if (this.cpuJudge(value)) {
        callback()
      } else {
        callback(new Error("请输入大于等于0的数字"))
      }
    }
    const propMem = (rule, value, callback) => {
      if (this.memJudge(value)) {
        callback()
      } else {
        callback(new Error("请输入请输入128倍数的数字"))
      }
    }
    return {
      formItem: {
        hostname: '',
        runtime: '',
        cpu: '',
        memory: '',
        disk: '',
        domain: 'Select availability zone',
        restartPolicy: '',
        retry: "",
        heal: false,
      },
      ruleValidate:{
        name:[{ required: true, message: '必填项', trigger: 'change' }],
        runtime:[{ required: true, message: '必填项', trigger: 'change' }],
        cpu:[
          // { required: true, message: '必填项', trigger: 'change' },
          { validator: propCPU, trigger: "change" }
        ],
        memory:[{ validator: propMem, trigger: "change" }],
        disk:[{ validator: propCPU, trigger: "change" }],
        disk:[{ required: true, message: '必填项', trigger: 'change' }],
      }
    }
  },
  methods: {
    cpuJudge(value) {
      if (value === '') return true;
      const num = Number(value);
      return !isNaN(num) && num > 0;
    },
    memJudge(value) {
      if (value === '') return true;
      const num = Number(value);
      return !isNaN(num) && num > 0 && num % 128 === 0;
    },
  },
}
</script>
<style lang="less" scoped>
  .general_page_area {
    width: 100%;
    height: 100%;
    overflow: auto;
  }
</style>