<template>
  <div>
    <Modal
      v-model="model"
      width="600"
      :mask-closable="false"
    >
    <template #header><p><span style="color:green">{{vmRow.name}}</span>虚拟机修改配置</p></template>
    <div class="chongse">
      <Form :model="formItem" :label-width="100" class="resetTooltip">
        <FormItem label="VCPU核数">
          <div class="slider_area">
            <div style="width:370px">
              <Slider
                v-model="formItem.vcpus"
                :min="1"
                :max="64"
                :tip-format="formCpu"
              ></Slider>
            </div>
            <div style="width:80px">
              <InputNumber :min="1" :max="64" v-model="formItem.vcpus" :formatter="value => `${value}核`" :parser="value => value.replace('核', '')"></InputNumber>
            </div>
          </div>
        </FormItem>
        <FormItem label="内存容量">
          <div class="slider_area">
            <div style="width:370px">
              <Slider
                v-model="formItem.ram"
                :min="1"
                :max="256"
                :tip-format="formMemory"
              ></Slider>
            </div>
            <div style="width:80px">
              <InputNumber :min="1" :max="256" v-model="formItem.ram" :formatter="value => `${value}GB`" :parser="value => value.replace('GB', '')"></InputNumber>
            </div>
          </div>
        </FormItem>
        <FormItem label="硬盘容量">
          <div class="slider_area">
            <div style="width:370px">
              <Slider
                v-model="formItem.disk"
                :min="1"
                :max="2048"
                :tip-format="formDisk"
              ></Slider>
            </div>
            <div style="width:80px">
              <InputNumber :min="1" :max="2048" v-model="formItem.disk" :formatter="value => `${value}GB`" :parser="value => value.replace('GB', '')"></InputNumber>
            </div>
          </div>
        </FormItem>
      </Form>
    </div>
    <div slot="footer">
        <Button type="text" @click="model = false">取消</Button>
        <Button type="primary" @click="modelOK" :disabled="disabled">确认</Button>
      </div>
    </Modal>
  </div>
</template>
<script>
import {
  vmTemplateNewBuilt, // 虚拟机 模板新建
  vmGroupTableAction, // 虚拟机组表格 操作
  vmGroupTableSeparateClouddisc, // 虚拟机组表格 分离云硬盘
} from '@/api/virtualMachine';

export default {
  props: {
    vmRow: Object,
    configTime: String,
  },
  watch: {
    configTime(news){
      this.queryAxios(this.vmRow.id)
      this.disabled = false
      this.model = true
    }
  },
  data(){
    return {
      disabled:true,
      model:false,
      // 虚拟机 修改配置弹框 
      formItem: {
        name: "",
        ram: null,
        vcpus: null,
        disk: null,
        extdisk: 0,
        sys_version:undefined,
      },
      formMin: {
        ramMin: null,
        vcpusMin: null,
        diskMin: null,
      },
    }
  },
  methods: {
    queryAxios(vmid){
      vmGroupTableSeparateClouddisc({ id: vmid })
      .then((callback) => {
        this.formMin.vcpusMin = callback.data.vcpus;
        this.formMin.ramMin = callback.data.ram;
        this.formMin.diskMin = callback.data.disk;
        this.formItem.vcpus = callback.data.vcpus;
        this.formItem.ram = callback.data.ram;
        this.formItem.disk = callback.data.disk;
        this.formItem.sys_version = callback.data.extra_specs['hw:sys_version']
      })
    },
    // 虚拟机表操作（设置）确认事件
    modelOK() {
      this.formItem.name = this.formItem.vcpus + "-" + this.formItem.ram + "-" + this.formItem.disk; // 模板名称拼接
      if (this.formMin.diskMin<=this.formItem.disk) {
        this.disabled = true
        let parameter = {
          name:this.formItem.name,
          ram:this.formItem.ram,
          vcpus:this.formItem.vcpus,
          disk:this.formItem.disk,
          extdisk:this.formItem.extdisk,
          ...(this.formItem.sys_version!==undefined ? {metadata_sys_version:this.formItem.sys_version}:"")
        }
        // 判断 硬盘容量 是否有改变
        vmTemplateNewBuilt(parameter)
        .then((callback) => {
          vmGroupTableAction({
            id: this.vmRow.id,
            action: "resize",
            data: callback.data.id,
            name: this.vmRow.name
          })
          .then((returndata) => {
            if(returndata.data.msg == "ok"){
              this.$emit("return-ok",'重新配置虚拟机: '+ this.vmRow.name+' 操作完成');
              this.model = false
            }else {
              this.$emit("return-ok",'重新配置虚拟机: '+ this.vmRow.name+' 操作失败');
              this.disabled = false
            }
          }).catch((error) => {
            this.disabled=false
            this.$emit("custom-event",'重新配置虚拟机: '+ this.vmRow.name+' 操作失败');
          })
        });
      } else {
        this.$Message.warning({
          background: true,
          closable: true,
          duration: 5,
          content: "硬盘容量值不能小于当前分配值",
        });
      }
    },
    // cpu选择
    formCpu(val) {
      return val + "核";
    },
    // 内存选择
    formMemory(val) {
      return val + " GB";
    },
    // 硬盘选择
    formDisk(val) {
      return val + " GB";
    },
  }
}
</script>