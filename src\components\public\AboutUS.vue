<template>
  <div>
    <!-- 关于我们弹框 -->
    <Modal v-model="frame" width="800" title="关于我们" :footer-hide="true">
      <div class="about_modal_area">
        <div class="about_modal_img">
          <img src="@/assets/loginCRH.png" alt="THXH" v-if="THElogoTitle=='CRH'" >
          <img src="@/assets/loginYGL.png" alt="THXH" v-if="THElogoTitle!=='CRH'" >
        </div>
        <ul>
          <li>
            <span>版本信息</span> <span>{{THEedition}}</span>
          </li>
          <li>
            <span>版权所有</span> <span>{{THEcopyright}}</span>
          </li>
          <li>
            <span>公司网址</span> <span><a target="_blank" :href="THEherfPath">{{THEpath}}</a></span>
          </li>
          <li>
            <span>联系电话</span> <span>{{THEtelephone}}</span>
          </li>
          <li>
            <span>联系地址</span> <span>{{THEaddress}}</span>
          </li>
        </ul>
        <div class="about_modal_copyright">
          <p>{{THEbanquansuoyou}}</p>
        </div>
      </div>
    </Modal>
  </div>
</template>
<script>
export default {
  props: {
    datas:Number,
  },
  watch: {
    datas(news){
      this.frame = true
    }
  },
  data() {
    return {
      frame:false,
      THElogoTitle:"CRH",
      THEedition:"版本信息未获取",
      THEcopyright:"版权信息未获取",
      THEherfPath:"未获取到地址信息",
      THEpath:"公司网址未获取",
      THEtelephone:"电话信息未获取",
      THEaddress:"地址信息未获取",
      THEbanquansuoyou:"版权所有信息未获取",
    }
  },
  mounted(){
    // 获取关于我们弹框数据
    this.THElogoTitle = window.gurl.THElogoTitle
    // 版本信息
    this.THEedition = window.gurl.edition
    // 版权所有
    this.THEcopyright = window.gurl.copyright
    // 公司网址herf
    this.THEherfPath= 'http://'+window.gurl.httpPath
    // 公司网址
    this.THEpath =window.gurl.httpPath
    // 联系电话
    this.THEtelephone = window.gurl.telephone
    // 联系地址
    this.THEaddress = window.gurl.address
    // 版权所有
    this.THEbanquansuoyou = window.gurl.THEbanquansuoyou
  },
}
</script>
<style>
.about_modal_area ul li {
  display:flex;
  justify-content: space-between;
  height: 50px;
  line-height: 50px;
  border-bottom: 1px solid #e4e4e4;
  color: #333;
  font-size: 14px;
  padding: 0 18px;
}
.about_modal_img {
  display: flex;
  justify-content: center;
  padding:36px 0 50px 0;
}
.about_modal_img img{
  height:35px;
}
.about_modal_copyright {
  display: flex;
  justify-content: center;
  padding:120px 0 36px 0;
}
.about_modal_area ul li a {
  color: #529ae8;
}
.about_modal_area ul li a:hover {
  color: #2361a3;
}
</style>