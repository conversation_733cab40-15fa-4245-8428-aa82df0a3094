// 系统
import axios from "axios";
import * as basic_proxy from "@/api/config";

// 系统功能 --- 用户管理
// 用户查询
export async function userTableQuery() {
  return await axios.get(basic_proxy.user + "/v1/user/all");
}
// 用户新建
export async function userTableNewBuilt(params) {
  return await axios.post(basic_proxy.user + "/v1/user/add", params);
}
// 用户修改
export async function userTableModify(params) {
  return await axios.put(basic_proxy.user + "/v1/user/update", params);
}
// 用户状态
export async function userTableState(params) {
  return await axios.put(basic_proxy.user + "/v1/user/status", params);
}
// 用户重置密码
export async function userTableResetPassword(params) {
  return await axios.put(basic_proxy.user + "/v1/user/resetpassword", params);
}
// 用户删除
export async function userTableDelete(params) {
  return await axios.delete(basic_proxy.user + "/v1/user/delete", params);
}


// 系统功能 --- 修改密码
export async function changePassword(params) {
  return await axios.put(basic_proxy.user + "/v1/user/changepassword", params);
}
// 系统功能 --- 安全配置

// 自动退出时间查询
export async function automaticExitTimeQuery() {
  return await axios.get(basic_proxy.theapi + "/v1/session/out/time");
}
// 自动退出时间修改
export async function automaticExitTimeModify(params) {
  return await axios.put(
    basic_proxy.theapi + "/v1/session/out/time/update",
    params
  );
}
// 防病毒配置
export async function antivirusConfiguration(params) {
  return await axios.post(basic_proxy.theapi + "/v1/暂无", params);
}
// 自动宕机迁移查询
export async function automaticDowntimeMigrationQuery() {
  return await axios.get(basic_proxy.theapi + "/v1/auto/evacuate");
}
// 自动宕机迁移修改
export async function automaticDowntimeMigrationModify(params) {
  return await axios.post(basic_proxy.theapi + "/v1/auto/evacuate", params);
}

// 授权信息查询
export async function authorizationQuery() {
  return await axios.get(basic_proxy.thelicense + "/v1/authentication/info");
}