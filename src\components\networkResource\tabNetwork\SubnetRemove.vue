<template>
  <Modal v-model="model" width="600" :mask-closable="false">
    <template #header><p>删除子网</p></template>
    <Form :model="formItem" :label-width="120">
      <FormItem label="选择子网">
        <Select v-model="formItem.cidrid" :label-in-value="true" @on-change="cidrChange">
          <Option
            v-for="item in cidrData"
            :value="item.value"
            :key="item.value"
            >{{ item.label }}</Option
          >
        </Select>
      </FormItem>
    </Form>
    <div slot="footer">
      <Button type="text" @click="model = false">取消</Button>
      <Button type="primary" @click="modelOK" :disabled="disabled">确认</Button>
    </div>
  </Modal>
</template>
<script>
import {networkTableSubnetRemove} from '@/api/network';

export default {
  props: {
    tableRow: Object,
    removeTime: String,
  },
  watch: {
    removeTime(news){
      // this.formItem.title = this.tableRow.name+' 网络删除子网'
      let arr = new Array()
      this.tableRow.cidr.forEach((em,index)=>{
        arr.push({
          label:em,
          value:index,
        })
      })
      this.cidrData = arr // 子网数据
      this.formItem.cidrname = this.cidrData[0].label
      this.formItem.cidrid = this.cidrData[0].value
      this.subnetData = this.tableRow.subnets // subnetID
      this.formItem.subnetID = this.subnetData[this.formItem.cidrid]
      this.model = true
      this.disabled = false
    }
  },
  data(){
    return {
      model: false,
      disabled: false,
      cidrData:[],
      subnetData:[],
      formItem:{
        title:'',
        cidrname: '',
        cidrid: '',
        subnetID: '',
      },
    }
  },
  methods: {
    // 选择子网
    cidrChange(item){
      this.formItem.cidrname = item.label
      this.formItem.cidrid = item.value
      this.formItem.subnetID = this.subnetData[item.value]
    },
    // 编辑网络确认事件
    modelOK() {
      this.disabled = true;
      networkTableSubnetRemove({
        subnet_id: this.formItem.subnetID,
        name: this.formItem.cidrname
      })
      .then(callback => {
        if(callback.data.msg == 'ok') {
          this.$emit("return-ok",'删除子网完成');
          this.model = false;
        }else {
          this.$emit("return-error",callback.data.msg);
          this.disabled = false;
        }
      })
      .catch((error) => {
        this.$emit("return-error",'删除子网失败');
        this.disabled = false;
      });
    },
    
  }
}
</script>
