<style lang="less">
@import "../monitoring.less";
@import "./storageMonitor.less";
</style>
<template>
  <div class="storage_monitor_area">
    <div class="cluster_management_area">
      <p class="storage_big_title">集群管理</p>
      <div class="cluster_management_template">
        <div class="cluster_management_alarm">
          <p class="storage_module_title">警报</p>
          <div class="alarm_module_area">
            <img src="../../../assets/monitorIMG/warning.png" alt="" />
            <div class="alarm_information_area">
              <span>警报数量</span>
              <span @click="alarmJump" class="storage_alarm_number">{{
                storageBasicInformation.alert
              }}</span>
            </div>
          </div>
        </div>
        <div class="cluster_management_storage_capacity">
          <p class="storage_module_title">存储容量</p>
          <div class="storage_capacity_area">
            <i-circle
              :size="160"
              :trail-width="10"
              :stroke-width="11"
              :percent="storageBasicInformation.storageUsageRate"
              stroke-color="#6d3df1"
            >
              <div class="demo-Circle-custom">
                <p>{{ storageBasicInformation.storageUsageRate }}%</p>
                <p>（使用量）</p>
              </div>
            </i-circle>
            <div class="storage_capacity_information">
              <div class="capacity_information_common">
                <span style="background: #6d3df1"></span>
                <span>已使用</span>
                <span>{{
                  formatFileSize(storageBasicInformation.storageUsed)
                }}</span>
              </div>
              <div class="capacity_information_common">
                <span style="background: #ccc"></span>
                <span>未使用</span>
                <span>{{
                  formatFileSize(storageBasicInformation.storageAvailable)
                }}</span>
              </div>
              <div class="capacity_information_common">
                <span></span>
                <span>共计</span>
                <span>{{
                  formatFileSize(storageBasicInformation.storageTotal)
                }}</span>
              </div>
            </div>
          </div>
        </div>
        <div class="cluster_management_storage_status">
          <p class="storage_module_title">存储健康状态</p>
          <ChartGaugeState
            :status="storageBasicInformation.healthStatus"
            style="width: 100%; height: 88%"
          ></ChartGaugeState>
        </div>
        <div class="cluster_management_storage_status">
          <p class="storage_module_title">IOPS</p>
          <div class="io_charts_area">
            <div class="io_charts_reading">
              <ChartGaugeRate
                :datas="IOreading"
                style="width: 100%; height: 100%"
              ></ChartGaugeRate>
            </div>
            <div class="io_charts_writing">
              <ChartGaugeRate
                :datas="IOwriting"
                style="width: 100%; height: 100%"
              ></ChartGaugeRate>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="node_xiangqing_area">
        <div class="node_radio_area">
          <RadioGroup v-model="nodeForm" type="button">
            <Radio v-for="(item,index) in nodeAllData" :key="index" :label="index">{{item.title}}</Radio>
          </RadioGroup>
        </div>
        <Table :columns="nodeColumn" :data="nodeArr" border class="node_radio_table">
          <template v-slot:osd_stats="{row}">
            <span>{{capacity(row.osd_stats.kb)}}</span>
          </template>
          <template v-slot:kb_used="{row}">
            <span>{{capacity(row.osd_stats.kb_used)}}</span>
          </template>
          <template v-slot:status="{row}">
            <span>{{ row.status=='up'?'正常':'异常' }}</span>
          </template>
        </Table>
        <!-- <p class="storage_big_title">OSD状态</p>
        <div class="osd_state_area">
          <div
            class="osd_state_template"
            v-for="(item, index) in osdData"
            :class="'osd_div_' + index"
          >
            <p>{{ item.title }}</p>
            <p>{{ item.value }}</p>
          </div>
        </div> -->
      </div>
    <!-- 性能统计分析 -->
    <div class="system_performance">
      <div class="storage_big_title">
        资源统计
        <!-- <div class="cluster_node_switch">
          <span
            :style="{
              background: this.clusterORnode == 'cluster' ? '#fff' : '',
              color: this.clusterORnode == 'cluster' ? '#fe9e1f' : '#333',
            }"
            @click="switchClick('cluster')"
            >集群</span
          >
          <span
            :style="{
              background: this.clusterORnode == 'node' ? '#fff' : '',
              color: this.clusterORnode == 'node' ? '#fe9e1f' : '#333',
            }"
            @click="switchClick('node')"
            >节点</span
          >
        </div> -->
      </div>
      <!-- 图表 集群 -->
      <div
        v-if="clusterORnode == 'cluster'"
        class="performance_statistical_analysis"
      >
        <div class="universal_chart_module" v-for="item in clusterData">
          <p class="storage_module_title">{{ item.title }}</p>
          <ChartLine
            :datas="item.datas"
            :times="timeZone"
            style="width: 100%; height: calc(100% - 45px)"
          ></ChartLine>
        </div>
      </div>
      <!-- 图表 节点 -->
      <div
        v-if="clusterORnode == 'node'"
        class="performance_statistical_analysis"
      >
        <div class="universal_chart_module" v-for="item in nodeData">
          <p class="storage_module_title">{{ item.title }}</p>
          <ChartLine
            :datas="item.datas"
            :times="timeZone"
            style="width: 100%; height: calc(100% - 45px)"
          ></ChartLine>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { powerCodeQuery } from '@/api/other'; // 权限可用性 查询

import {
  storageMonitorBasicQuery, // 基本数据
  storageNodeInformationQuery, // 节点信息
  storageMonitorStorageQuery, // 集群 存储使用率
  storageMonitorMemoryQuery, // 集群 内存使用率
  storageMonitorIOPSquery, // 集群 IOPS数据
  storageMonitorThroughputQuery, // 集群 吞吐量
  storagIOPSreadQuery, // 节点 iops读
  storagIOPSwriteQuery, // 节点 iops写
  storagNetworkupQuery, // 节点 网络上行
  storagNetworkdownQuery, // 节点 网络下行
  physicalMachineCPUquery, // 节点 CPU数据
  physicalMachineMemoryQuery, // 节点 内存数据
  
} from "@/api/monitor";
import ChartLine from "../chartTemplate/ChartLine.vue"
import ChartGaugeState from "../chartTemplate/ChartGaugeState.vue";
import ChartGaugeRate from "../chartTemplate/ChartGaugeRate.vue";
export default {
  components: {
    ChartLine,
    ChartGaugeState,
    ChartGaugeRate,
  },
  props: {
    tabName: String,
  },
  watch: {
    tabName(value) {
      if (value == "存储监控") {
        this.basicQuery();
        this.switchClick("cluster");
        this.tabsQuery()
      }
    },
    nodeForm(news) {
      this.nodeArr = this.nodeAllData[news].osd_data
    }
  },
  mounted() {
    if(this.$store.state.power.itMonitoringTab == '存储监控') {
      this.basicQuery();
      this.switchClick("cluster");
      this.tabsQuery()
    }
  },
  data() {
    return {
      // 存储基本信息
      storageBasicInformation: {
        alert: 0, // 告警
        storageTotal: 500, // 存储总量
        storageUsed: 200, // 存储已用
        storageAvailable: 300, // 存储可用
        healthStatus: "", // 健康状态
        storageUsageRate: 50, // 存储使用率
      },
      IOreading: {}, // IO读
      IOwriting: {}, // IO写
      
      // 节点信息数据
      nodeAllData:[],
      nodeForm: 0,
      nodeColumn:[
        // { title: "槽位编号", key: "number",width:120 },
        { title: "磁盘序列号", key: "devid" },
        { title: "接口类型", key: "device_class" },
        { title: "磁盘总容量", key: "osd_stats",slot: 'osd_stats' },
        { title: "OSD已使用容量", key: "kb_used",slot: 'kb_used',width:200 },
        { title: "磁盘状态", key: "status",slot: 'status',width:120 },
      ],
      nodeArr:[],
      // OSD状态
      osdData: [
        { title: "集群内的 OSD 数量", value: 0 },
        { title: "集群外的 OSD 数量", value: 0 },
        { title: "运行中的 OSD 数量", value: 0 },
        { title: "停止的 OSD 数量", value: 0 },
      ],

      // 性能统计分析
      clusterORnode: "cluster", // 节点集群选中项
      timeZone: 1, // 监控查询时间单位小时
      // 集群数据
      clusterData: [
        { title: "CPU平均使用率", datas: null },
        { title: "平均内存使用量", datas: null },
        { title: "IOPS", datas: null },
        { title: "吞吐量", datas: null },
      ],
      // 节点数据
      nodeData: [
        { title: "平均存储使用率", datas: null },
        { title: "内存平均使用量", datas: null },
        { title: "读IOPS", datas: null },
        { title: "写IOPS", datas: null },
        { title: "网络上行", datas: null },
        { title: "网络下行", datas: null },
      ],
      storageData: null,
      memoryData: null,
      isoData: null,
      bandwidthData: null,

      powerAcitons: {}, // 操作权限数据
    };
  },
  methods: {
    
    // 告警跳转
    alarmJump() {
      if(this.powerAcitons.cunchugaojing) {
        this.$store.state.power.logManagementTab = "存储告警";
        this.$router.push("/logmanage");
      }else {
        this.$Message.warning({
          background: true,
          closable: true,
          duration: 5,
          content: '未配置存储告警权限'
        });
      }
    },
    // 存储基本数据获取
    basicQuery() {
      storageMonitorBasicQuery().then((callback) => {
        this.storageBasicInformation.alert = callback.data.alertcounts; // 告警
        this.storageBasicInformation.storageTotal = callback.data.total; // 存储总量
        this.storageBasicInformation.storageUsed = callback.data.used; // 存储已用
        this.storageBasicInformation.storageAvailable = callback.data.residue; // 存储可用
        this.storageBasicInformation.healthStatus = callback.data.status; // 健康状态
        this.storageBasicInformation.storageUsageRate =
          Math.floor((callback.data.used / callback.data.total) * 1000) / 10; // 存储使用率
        // let arr = new Array();
        // arr.push(
        //   { title: "集群内的 OSD 数量", value: callback.data.osd_in },
        //   { title: "集群外的 OSD 数量", value: callback.data.osd_out },
        //   { title: "运行中的 OSD 数量", value: callback.data.osd_up },
        //   { title: "停止的 OSD 数量", value: callback.data.osd_down }
        // );
        // this.osdData = arr;
      });
      storageNodeInformationQuery().then((callback) => {
        this.nodeAllData = callback.data
        this.nodeForm = 0
        this.nodeArr = this.nodeAllData[0].osd_data
      })
    },
    
    // 切换集群图表
    switchClick(str) {
      this.clusterORnode = str;
      if (str == "cluster") {
        this.clusterQuery();
      } else {
        this.nodeQuery();
      }
    },
    // 获取集群图表数据
    clusterQuery() {
      let times = this.chartTimeEncapsulation(1)
      // 存储存储使用率数据获取
      physicalMachineCPUquery(times).then((callback) => {
        this.clusterData[0].datas = callback.data;
      });
      // 存储内存使用率数据获取
      storageMonitorMemoryQuery(times).then((callback) => {
        this.clusterData[1].datas = callback.data;
      });
      // 存储IOPS数据获取
      storageMonitorIOPSquery(times).then((callback) => {
        this.clusterData[2].datas = callback.data;
        this.isoData = callback.data;
        callback.data.data.forEach((em) => {
          if (em.title == "IOPS读") {
            this.IOreading = {
              title: em.title,
              value: em.list[em.list.length - 1]* -1,
            };
          } else if (em.title == "IOPS写") {
            this.IOwriting = {
              title: em.title,
              value: em.list[em.list.length - 1],
            };
          }
        });
      });
      // 存储吞吐量数据获取
      storageMonitorThroughputQuery(times).then((callback) => {
        this.clusterData[3].datas = callback.data;
      });
    },
    // 获取节点图表数据
    nodeQuery() {
      let times = this.chartTimeEncapsulation(1)
      // 存储 节点 CPU数据
      storageMonitorStorageQuery(times).then((callback) => {
        this.nodeData[0].datas = callback.data;
      });
      // 存储 节点 内存数据
      physicalMachineMemoryQuery(times).then((callback) => {
        this.nodeData[1].datas = callback.data;
      });
      // 存储 节点 iops读
      storagIOPSreadQuery(times).then((callback) => {
        this.nodeData[2].datas = callback.data;
      });
      // 存储 节点 iops写
      storagIOPSwriteQuery(times).then((callback) => {
        this.nodeData[3].datas = callback.data;
      });
      // 存储 节点 网络上行
      storagNetworkupQuery(times).then((callback) => {
        this.nodeData[4].datas = callback.data;
      });
      // 存储 节点 网络下行
      storagNetworkdownQuery(times).then((callback) => {
        this.nodeData[5].datas = callback.data;
      });
    },
    dkClic() {
      let list = {
        unit: "%",
        time: [
          "1:05",
          "1:10",
          "1:15",
          "1:20",
          "1:25",
          "1:30",
          "1:35",
          "1:40",
          "1:45",
          "1:50",
          "1:55",
          "2:00",
        ],
        data: [
          { title: "物理机1", list: [] },
          { title: "物理机2", list: [] },
        ],
      };
      for (var i = 0; i < 12; i++) {
        let a = Math.floor(Math.random() * 50) + 1;
        list.data[0].list.push(a);
      }
      for (var i = 0; i < 12; i++) {
        let a = Math.floor(Math.random() * 50) - 50;
        list.data[1].list.push(a);
      }
      this.bandwidthData = list;
    },

    // 字节+单位转换
    formatFileSize(size) {
      const units = ["B", "KB", "MB", "GB", "TB", "PB"];
      let unitIndex = 0;
      while (size >= 1024 && unitIndex < units.length - 1) {
        size /= 1024;
        unitIndex++;
      }
      return Math.floor(size * 100) / 100 + " " + units[unitIndex];
    },
    // 图表时间封装
    chartTimeEncapsulation(item){
      // 获取当前时间
      var currentDate = new Date();
      // 获取一小时前的时间
      var oneHourBefore = new Date(currentDate.getTime() - item*60 * 60 * 1000);
      // 获取年、月、日
      var year = currentDate.getFullYear();
      var month = ("0" + (currentDate.getMonth() + 1)).slice(-2); // 月份从0开始，所以要加1
      var day = ("0" + currentDate.getDate()).slice(-2);
      // 获取小时、分钟（当前时间）
      var hours = ("0" + currentDate.getHours()).slice(-2);
      var minutes = currentDate.getMinutes();
      // 调整分钟（当前时间）
      if (minutes % 10 < 5) {
        minutes = Math.floor(minutes / 10) * 10; // 当分钟尾数小于5时，取整为10的倍数
      } else {
        minutes = Math.ceil(minutes / 10) * 10 - 5; // 当分钟尾数大于6时，取整为10的倍数再减去5
      }
      // 格式化分钟（当前时间）
      minutes = ("0" + minutes).slice(-2);

      // 输出当前时间结果
      var formattedCurrentDateTime =
        year + "-" + month + "-" + day + " " + hours + ":" + minutes;

      // 获取一小时前的年、月、日、时、分
      var beforeYear = oneHourBefore.getFullYear();
      var beforeMonth = ("0" + (oneHourBefore.getMonth() + 1)).slice(-2);
      var beforeDay = ("0" + oneHourBefore.getDate()).slice(-2);
      var beforeHours = ("0" + oneHourBefore.getHours()).slice(-2);
      var beforeMinutes = oneHourBefore.getMinutes();

      // 调整分钟（一小时前的时间）
      if (beforeMinutes % 10 < 5) {
        beforeMinutes = Math.floor(beforeMinutes / 10) * 10; // 当分钟尾数小于5时，取整为10的倍数
      } else {
        beforeMinutes = Math.ceil(beforeMinutes / 10) * 10 - 5; // 当分钟尾数大于6时，取整为10的倍数再减去5
      }

      // 格式化分钟（一小时前的时间）
      beforeMinutes = ("0" + beforeMinutes).slice(-2);

      // 输出一小时前的时间结果
      var formattedBeforeDateTime =
        beforeYear +
        "-" +
        beforeMonth +
        "-" +
        beforeDay +
        " " +
        beforeHours +
        ":" +
        beforeMinutes;
      let params = {
        start:formattedBeforeDateTime,
        end:formattedCurrentDateTime,
      }
      return params
    },
    capacity(size){
      const units = ["KB", "MB", "GB", "TB", "PB"];
      let unitIndex = 0;
      while (size >= 1024 && unitIndex < units.length - 1) {
        size /= 1024;
        unitIndex++;
      }
      return Math.floor(size * 100) / 100 + " " + units[unitIndex];
    },
    // 查询标签页权限
    tabsQuery(){
      powerCodeQuery({
        module_code:[
          'cunchugaojing'
        ]
      }).then(callback=>{
        this.powerAcitons = callback.data.data
      })
    },
  },
};
</script>