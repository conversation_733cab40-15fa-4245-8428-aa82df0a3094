<style lang="less">
@import "./JSvm.less";
</style>
<template>
  <div class="zombie_vm">
    <div class="zombie_title"><p>僵尸虚机</p></div>
    <div class="zombie_query">
      <ul>
        <li>
          <Button class="plus_btn" @click="startUPVM" :disabled="tableData"
            >启动</Button
          >
          <Button class="close_btn" @click="deleteVM" :disabled="tableData"
            >删除</Button
          >
        </li>
        <li class="zombi_screening">
          关机时间：<Select
            v-model="tablePageForm.shutdownTime"
            style="width: 200px"
            @on-change="shutdownSelect"
          >
            <Option
              v-for="item in daysData"
              :value="item.days"
              :key="item.days"
              >{{ item.label }}</Option
            >
          </Select>
          <Button class="remove zombi_refresh" @click="refreshTable"
            ><span class="icon iconfont icon-a-15Jhuanyuan"></span> 刷新</Button
          >
          <Input
            v-model="tablePageForm.search_str"
            search
            enter-button
            placeholder="请输入名称"
            style="width: 300px"
            @on-search="tableVMsearchInput"
          />
        </li>
      </ul>
    </div>
    <div class="zombi_table">
      <Spin fix v-if="spinShowVM" size="large" style="color: #ef853a">
        <Icon type="ios-loading" size="50" class="demo-spin-icon-load"></Icon>
        <div style="font-size: 16px; padding: 20px">Loading...</div>
      </Spin>
      <Table
        :columns="JScolumn"
        :data="JSdata"
        @on-sort-change="sortColumn"
        @on-selection-change="slectchange"
      ></Table>
      <!-- 虚拟机表格 分页  -->
      <div class="pages" v-if="this.JSdata.length > 0">
        <!-- <Page
          :total="tableTotal"
          show-total
          show-sizer
          :page-size="tablePageForm.pagecount"
          placement="top"
          :page-size-opts="[10, 20, 30]"
          @on-change="onPageChange"
          @on-page-size-change="onPageSizeChange"
        /> -->
        <Pagination
          :total="tableTotal"
          :page-size="tablePageForm.pagecount"
          @page-change="onPageChange"
          @page-size-change="onPageSizeChange"
        />
      </div>
    </div>
  </div>
</template>
<script>
import {
  vmGroupTableDelete, // 虚拟机组表格 删除
  vmGroupTableAction, // 虚拟机组表格 操作
  zombieVMQuery, // 僵尸虚拟 查询
} from "@/api/virtualMachine";
import Pagination from "@/components/public/Pagination.vue";
export default {
  components: {
    Pagination,
  },
  data() {
    return {
      tableData: true,
      daysData: [
        { label: "大于15天", days: "15" },
        { label: "大于1个月", days: "30" },
        { label: "大于3个月", days: "90" },
        { label: "大于6个月", days: "180" },
        { label: "大于1年", days: "365" },
        { label: "大于2年", days: "730" },
        { label: "大于3年", days: "1095" },
      ],
      spinShowVM: false, // 加载模块
      JScolumn: [
        // 表列
        { type: "selection", width: 30, align: "center" },
        {
          type: "expand",
          width: 50,
          render: (h, params) => {
            return h("div", { style: { width: "100%" } }, [
              h(
                "span",
                { style: { width: "45%", display: "inline-block" } },
                "ID：" + params.row.id
              ),
              // h("span",'名称：'+params.row.name)
            ]);
          },
        },
        { title: "名称", key: "name", sortable: "custom", minWidth: 120 },
        {
          title: "IP",
          key: "ip",
          sortable: "custom",
          align: "center",
          minWidth: 120,
        },
        { title: "物理机", key: "hostname", align: "center", minWidth: 120 },
        {
          title: "关机时间",
          key: "days",
          align: "center",
          minWidth: 120,
          render: (h, params) => {
            return h("span", params.row.days + " 天");
          },
        },
        {
          title: "状态",
          key: "status",
          align: "center",
          minWidth: 100,
          render: (h, params) => {
            if (params.row.status == "SHUTOFF") {
              return h("span", "关机");
            } else {
              return h("span", "开机");
            }
          },
        },
        // { title: "内存", key: "ram", align: "center",
        //   render: (h, params) => {
        //     return h("span",(params.row.ram/1024).toFixed(1)+" GB")
        //   }
        // },
        // { title: "硬盘",align: "center", key: "disk",
        //   render: (h, params) => {
        //     return h("span",params.row.disk+" GB")
        //   }
        // },
      ],
      JSdata: [], // 表数据
      tableSelec: [], // 表选中数据
      tableNames: [], // 表选中所有表名数据
      tableIDs: [], // 表选中所有表ID数据
      // 分页
      tablePageForm: {
        shutdownTime: "15",
        page: 1, // 当前页
        pagecount: this.$store.state.power.pagecount, // 每页条
        search_str: "", // 收索
        order_type: "asc", // 排序规则
        order_by: "", // 排序列
      },
      tableTotal: 0, // 虚拟机分页总条数
    };
  },
  mounted() {
    this.refreshTable();
  },
  updated() {
    this.tablePageForm.pagecount = this.$store.state.power.pagecount;
  },
  methods: {
    // 表数据
    refreshTable() {
      this.spinShowVM = true;
      zombieVMQuery(this.tablePageForm)
        .then((callback) => {
          this.spinShowVM = false;
          this.tableTotal = callback.data.total;
          this.JSdata = callback.data.data;
        })
        .catch((error) => {
          this.spinShowVM = false;
        });
    },
    // 启动按钮
    startUPVM() {
      this.$Modal.confirm({
        title: "启动虚拟机",
        content:
          '<p>是否启动<span style="font-weight: 600;color:green;" >' +
          this.tableNames.toString() +
          "</span>虚拟机?</p>",
        onOk: () => {
          this.tableData = true;
          this.$Message.info({
            background: true,
            closable: true,
            duration: 5,
            content: "正在启动虚拟机",
          });
          this.tableIDs.forEach((item, index) => {
            if (index == this.tableIDs.length - 1) {
              vmGroupTableAction({ id: item, action: "start", data: "" ,name: this.tableNames[index]})
              .then((callback) => {
                  if (callback.data.msg == "ok") {
                    setTimeout(() => {
                      this.$Message.success({
                        background: true,
                        closable: true,
                        duration: 5,
                        content: "虚拟机启动已完成",
                      });

                      this.refreshTable();
                    }, 1000);
                  }
                }
              );
            } else {
              vmGroupTableAction({ id: item, action: "start", data: "",name: this.tableNames[index] });
            }
          });
        },
        onCancel: () => {},
      });
    },
    // 删除按钮
    deleteVM() {
      this.$Modal.confirm({
        title: "删除虚拟机",
        content:
          '<p>是否删除<span style="font-weight: 600;color:red;" >' +
          this.tableNames.toString() +
          "</span>虚拟机?</p>",
        onOk: () => {
          this.tableData = true;
          this.$Message.info({
            background: true,
            closable: true,
            duration: 5,
            content: "正在删除虚拟机",
          });
          vmGroupTableDelete({ data: { ids: this.tableIDs } }).then(
            (callbackSC) => {
              if (callbackSC.data.msg == "ok") {
                setTimeout(() => {
                  this.$Message.success({
                    background: true,
                    closable: true,
                    duration: 5,
                    content: "删除虚拟机操作已完成",
                  });
                  this.refreshTable();
                }, 1000);
              }
            }
          );
        },
        onCancel: () => {},
      });
    },
    // 选中表格数据
    slectchange(item) {
      this.tableSelec = item;
      if (item.length > 0) {
        this.tableData = false;
      } else {
        this.tableData = true;
      }
      let names = new Array();
      let ids = new Array();
      item.forEach((em) => {
        names.push(em.name);
        ids.push(em.id);
      });
      this.tableNames = names;
      this.tableIDs = ids;
    },
    // 选择天数
    shutdownSelect(data) {
      this.tablePageForm.shutdownTime = data;
      this.refreshTable();
    },
    // 当前分页
    onPageChange(item) {
      this.tablePageForm.page = item;
      this.refreshTable();
    },
    // 每页条数
    onPageSizeChange(item) {
      this.$store.state.power.pagecount = item;
      this.tablePageForm.pagecount = this.$store.state.power.pagecount;
      this.tablePageForm.page = 1;
      this.tableTotal = 0;
      this.tablePageForm.search_str = "";
      this.refreshTable();
    },
    // 列表 列排序
    sortColumn(column, key, order) {
      if (column.key !== "normal") {
        this.tablePageForm.order_by = column.key;
        this.tablePageForm.order_type = column.order;
        this.tablePageForm.page = 1;
        this.tableTotal = 0;
        this.tablePageForm.search_str = "";
        this.refreshTable();
      }
    },
    // 搜索列表
    tableVMsearchInput() {
      this.tablePageForm.page = 1;
      this.tableTotal = 0;
      this.refreshTable();
    },
  },
};
</script>
