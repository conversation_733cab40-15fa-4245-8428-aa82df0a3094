<template>
  <div>
    <Modal v-model="model" width="900" :mask-closable="false">
      <template #header
        ><p>虚拟机动态资源扩展</p></template
      >
      <div>
        <Table :columns="tableColumns" :data="tableData" height="500">
          <!-- cpu -->
          <template v-slot:cpu_enabled="{ row }">
            <span v-if="row.cpu_enabled=='true'">
              <Tooltip :content="convertCPU(row.cpu)">
                <span>{{ row.cpu }}% </span> <Icon type="ios-help-circle-outline" />
              </Tooltip>
            </span>
            <span v-else>-</span>
          </template>
          <!-- 内存 -->
          <template v-slot:mem_enabled="{ row }">
            <span v-if="row.mem_enabled=='true'">
              <Tooltip :content="convertMEM(row.mem)">
                <span>{{ row.mem }}% </span> <Icon type="ios-help-circle-outline" />
              </Tooltip>
            </span>
            <span v-else>-</span>
          </template>
          <!-- 敏感度 -->
          <template v-slot:interval="{ row }">
            <Tooltip :content="convertInterval(row.interval)">
              <span>{{ row.interval }}分钟</span> </span> <Icon type="ios-help-circle-outline" />
            </Tooltip>
          </template>
          <!-- 操作 -->
          <template v-slot:operation="{ row }">
            <Tooltip content="动态资源扩展">
              <Button
                @click="editClick(row)"
                type="text"
                icon="ios-create-outline"
              ></Button>
            </Tooltip>
            <Tooltip content="重置动态资源扩展">
              <Button
                @click="resetClick(row)"
                type="text"
                icon="ios-backspace-outline"
              ></Button>
            </Tooltip>
          </template>
        </Table>
      </div>
      <div slot="footer">
        <Button type="text" @click="model = false">取消</Button>
      </div>
    </Modal>
    <!-- 动态资源扩展 -->
    <VMdispatchEdit
      :vmRow="vmRow"
      :dynamicResourceTime="dynamicResourceTime"
      @return-ok="returnOK"
      @return-error="returnError"
    ></VMdispatchEdit>
    <!-- 重置动态资源扩展 -->
    <Modal v-model="resetModel" width="600" :mask-closable="false">
      <template #header><p>重置动态资源扩展</p></template>
      <div style="padding: 5px">
        <span>是否对下列虚拟机进行重置动态资源扩展操作？</span>
        <p style="color: red; word-wrap: break-word">{{ vmRow.vm_name }}</p>
      </div>
      <div slot="footer">
        <Button type="text" @click="resetModel = false">取消</Button>
        <Button type="primary" @click="modelOK">确认</Button>
      </div>
    </Modal>
  </div>
</template>
<script>
import VMdispatchEdit from "./VMdispatchEdit.vue"; // 动态资源扩展
import {
  ddispatchQuery,
  dynamicResourceExtendEdit,
} from "@/api/virtualMachine"; // 动态资源扩展
export default {
  props: {
    dispatchTime: String,
  },
  watch: {
    dispatchTime(news) {
      this.queryData();
      this.model = true;
    },
  },
  components: {
    VMdispatchEdit,
  },
  data() {
    return {
      model: false,
      tableColumns: [
        { title: "虚拟机", key: "vm_name", tooltip: true },
        // { title: "IP", key: "ip", align: "center", slot: "ip" },
        { title: "CPU利用率", key: "cpu_enabled", align: "center", slot: "cpu_enabled" },
        { title: "内存阈值", key: "mem_enabled", align: "center", slot: "mem_enabled" },
        { title: "敏感度", key: "interval", align: "center", slot: "interval" },
        { title: "操作", key: "operation", width: 90, slot: "operation" },
      ],
      tableData: [],
      vmRow: {},
      dynamicResourceTime: "",
      resetModel: false,
    };
  },
  methods: {
    // 查询数据
    queryData() {
      ddispatchQuery()
        .then((callback) => {
          this.tableData = callback.data.data;
        })
        .catch((error) => {
          this.tableData = new Array();
        });
    },
    listDatas() {
      this.tableData = [
        {
          id: "c51f7988-279e-456e-99ec-6eabaeabeec6",
          vm_name: "测试假数据",
          cpu_enabled: "false",
          cpu: 80,
          mem_enabled: "true",
          mem: 80,
          interval: 5,
        },
      ];
    },
    // CPU转换
    convertCPU(item) {
      return '当虚拟机CPU利用率 大于 '+item+'% 时，将为虚拟机添加CPU,每次增加一个插槽的核数，上限为虚拟机原配置两倍。'
    },
    // 内存转换
    convertMEM(item) {
      return '当虚拟机CPU利用率 大于 '+item+'% 时，将为虚拟机添加CPU,每次增加一个插槽的核数，上限为虚拟机原配置两倍。'
    },
    // 敏感度转换
    convertInterval (item) {
      return '在该模式下，系统检测到衡量因素达到阈值并持续 '+item+'分钟，将对虚拟机进行动态资源添加。'
    },
    // 动态资源扩展
    editClick(row) {
      this.vmRow = row;
      this.dynamicResourceTime = "" + new Date();
    },
    // 重置动态资源扩展
    resetClick(row) {
      this.vmRow = row;
      this.resetModel = true;
    },
    modelOK() {
      this.resetModel = false;
      dynamicResourceExtendEdit({
        vm_id: this.vmRow.vm_id,
        vm_name: this.vmRow.vm_name,
        enabled: "false",
      }).then((callback) => {
        if (callback.data.msg == "ok") {
          this.resetModel = false;
          this.returnOK("虚拟机重置动态资源扩展操作完成");
        } else {
          this.returnError(callback.dta.msg);
        }
      });
    },
    returnOK(data) {
      this.queryData()
      this.$Message.success({
        background: true,
        closable: true,
        duration: 5,
        content: data,
      });
    },
    returnError(data) {
      this.$Message.error({
        background: true,
        closable: true,
        duration: 5,
        content: data,
      });
    },
  },
};
</script>
<style scoped>
</style>