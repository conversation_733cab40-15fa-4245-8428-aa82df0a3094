<style lang="less">
  @import "./storageResources.less";
</style>
<template>
  <div class="storage_tab">
    <Tabs class="tabs_template" name="1" v-model="tabName" @on-click="tabsClick">
      <TabPane
        v-if="item.show"
        v-for="item in tabsData"
        :key="item.name"
        :name="item.name"
        tab="1"
        :label="renderTabLabel(item.name)">
        <TabCloudHardDrive v-if="item.name === '云硬盘'" :tabName="tabName" />
        <TabSnapshot v-if="item.name === '快照'" :tabName="tabName" />
        <TabType v-if="item.name === '类型管理'" :tabName="tabName" />
        <TabThrough v-if="item.name === '直通管理'" :tabName="tabName" />
        <TabOverview v-if="item.name === '存储概览'" :tabName="tabName" />
        <TabHardDriveDomain v-if="item.name === '硬盘域管理'" :tabName="tabName" />
        <PoolManagement v-if="item.name === '卷池管理'" :tabName="tabName" />
        <TabBlockDevice v-if="item.name === '块设备管理'" :tabName="tabName" />
        <MagneticDisk v-if="item.name === '存储单元'" :tabName="tabName" />
        <Master v-if="item.name === '节点'" :tabName="tabName" />
      </TabPane>
    </Tabs>
  </div>
</template>
<script>
import { powerCodeQuery } from '@/api/other'; // 权限可用性 查询

import TabOverview from "./tabOverview/TabOverview.vue" // 存储概览
import TabThrough from "./tabThrough/TabThrough.vue" // 直通管理
import TabType from "./tabType/TabType.vue" // 类型管理
import TabCloudHardDrive from "./tabCloudHardDrive/TabCloudHardDrive.vue" // 云硬盘
import TabSnapshot from "./tabSnapshot/TabSnapshot.vue" // 快照
import TabHardDriveDomain from "./tabHardDriveDomain/TabHardDriveDomain.vue" // 硬盘域管理

import PoolManagement from "./poolManagement/PoolManagement.vue" // 卷池管理
import PoolManagementCEPH from "./poolManagement/PoolManagementCEPH.vue" // 
import TabBlockDevice from "./tabBlockDevice/TabBlockDevice.vue" // 块设备管理
import MagneticDisk from "./magneticDisk/MagneticDisk.vue" // 存储单元
import Master from "./master/Master.vue" // 节点
export default {
  components: {
    TabOverview,
    TabThrough,
    TabType,
    TabCloudHardDrive,
    TabSnapshot,
    TabHardDriveDomain,
    PoolManagement,
    TabBlockDevice,
    MagneticDisk,
    Master,
    PoolManagementCEPH,
  },
  data() {
    return {
      tabName: '',
      tabsData: [
        { name: '云硬盘', code: 'yunyingpan', show: false },
        { name: '快照', code: 'kuaizhao', show: false },
        { name: '类型管理', code: 'leixingguanli', show: false },
        { name: '直通管理', code: 'zhitongguanli', show: false },
        // { name: '存储概览', code: 'cunchugailan', show: false },
        // { name: '硬盘域管理', code: 'yingpanyuguanli', show: false },
        // { name: '卷池管理', code: 'juanchiguanli', show: false },
        // { name: '块设备管理', code: 'kuaishebeiguanli', show: false },
        // { name: '存储单元', code: 'cunchudanyuan', show: false },
        // { name: '节点', code: 'jiedian', show: false },
      ],
    }
  },
  mounted(){
    this.tabsQuery()
  },
  methods: {
    renderTabLabel(item){
      return (h) => {
        return h('div', [
          h('span', {
            class: 'select_tab_border',
            style: { background: this.tabName === item ? '#fb6129' : '' }
          }),
          h('span', item)
        ]);
      };
    },
    // 查询标签页权限
    tabsQuery(){
      powerCodeQuery({
        module_code:[
          'yunyingpan',
          'kuaizhao',
          'leixingguanli',
          'zhitongguanli'
        ]
      }).then(callback=>{
        if(callback.data.data.yunyingpan) {
          this.tabName = '云硬盘'
        }else if(callback.data.data.kuaizhao) {
          this.tabName = '快照'
        }else if(callback.data.data.leixingguanli) {
          this.tabName = '类型管理'
        }else if(callback.data.data.leixingguanli) {
          this.tabName = '直通管理'
        }
        this.tabsData.forEach(item => {
          if (callback.data.data.hasOwnProperty(item.code)) {
            item.show = callback.data.data[item.code];
          }
        });
        this.$store.state.power.storageResourceTab = this.tabName
      })
    },
    tabsClick(name){
      this.$store.state.power.storageResourceTab = name
    },
  }
}
</script>
