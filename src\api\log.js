// 日志
import axios from "axios";
import * as basic_proxy from "@/api/config";

// 集群告警
// 集群告警信息 查询
export async function alarmDataQuery() {
  // return await axios.get(basic_proxy.theapi + "/v1/alerts/all");
  return await axios.get(basic_proxy.theapi + "/v2/alerts/all");
}
// 集群告警 忽略
export async function alarmIgnored(params) {
  return await axios.post(
    basic_proxy.theapi + "/v2/alerts/add/silences",
    params
  );
}

// 存储告警
// 存储告警信息 查询
export async function storageAlarmQuery() {
  return await axios.get(basic_proxy.theapi + "/v2/alerts/ceph");
}
// 存储告警 忽略
export async function storageAlarmIgnore(params) {
  return await axios.post(
    basic_proxy.theapi + "/v2/alerts/add/silencesceph",
    params
  );
}

// 告警规则
// 告警规则 查询
export async function alarmRulesQuery(params) {
  return await axios.post(basic_proxy.theapi + "/v1/alertsrules/all",params);
}
// 告警规则 添加
export async function alarmRulesAdd(params) {
  return await axios.post(basic_proxy.theapi + "/v1/alertsrules/add", params);
}
// 告警规则 修改
export async function alarmRulesEdit(params) {
  return await axios.put(basic_proxy.theapi + "/v1/alertsrules/edit", params);
}
// 告警规则 删除
export async function alarmRulesDelet(params) {
  return await axios.delete(
    basic_proxy.theapi + "/v1/alertsrules/delete",
    params
  );
}
// 告警规则 启用禁用
export async function alarmRulesAction(params) {
  return await axios.put(basic_proxy.theapi + "/v1/alertsrules/status", params);
}

// 日志
// 日志 查询条件
export async function queryCriteria() {
  return await axios.get(basic_proxy.thelog + "/v1/log/config");
}
export async function queryCriteriaV2() {
  return await axios.get(basic_proxy.thelog + "/v3/log/config");
}
// 日志数据 查询
export async function logDataQuery(params) {
  return await axios.post(basic_proxy.thelog + "/v1/log/all", params);
}
export async function logDataQueryV2(params) {
  return await axios.post(basic_proxy.thelog + "/v2/log/all", params);
}