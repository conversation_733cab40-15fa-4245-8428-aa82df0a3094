<template>
  <div>
    <Modal
      v-model="initialmodal"
      width="800"
      :mask-closable="false"
    >
      <p slot="header">
        <span>新建虚拟机</span>
      </p>
      <div class="vm_new_dialog">
        <Menu theme="light" :active-name="activeNumber" @on-select="modalMenuItem">
          <Menu-item :name="i" v-for="(item, i) in menniuItemData" :key="i">
            <span>{{ item }}</span>
          </Menu-item>
        </Menu>
        <div class="modalcontent">
          
        </div>
      </div>
      <div slot="footer">
        <Button v-if="activeNumber>0" type="primary" @click="activeNumber--">上一步</Button>
        <Button type="text" @click="initialmodal = false">取消</Button>
        <Button v-if="activeNumber<7" type="primary" @click="activeNumber++">下一步</Button>
        <Button v-if="activeNumber==7" type="primary" :loading="loading" @click="modalOK">
          <span v-if="!loading">确认</span>
          <span v-else>创建中</span>
        </Button>
      </div>
    </Modal>
  </div>
</template>
<script>
import {

} from '@/api/virtualMachine';
export default {
  props: {
    newTime: String,
  },
  watch: {
    newTime(news){
      this.initialmodal = true
      this.loading = false
      this.activeNumber = 0
    },
    // activeNumber:{
    //   handler(news, old) {
    //     let carousel = document.querySelector("#lunbo");
    //     carousel.style.transform = `translateX(-${news * 600}px)`;
    //   },
    //   // deep:true,
    //   // immediate:true,
    // },
  },
  data(){
    return {
      initialmodal: false,
      loading: false, // 禁止多次触发
      menniuItemData: ['基本信息','Spec','Volumes','网络','Ports','安全组','Miscellaneous','Labels'],
      activeNumber: 0,
    }
  },
  methods: {
    // 导航点击
    modalMenuItem(item) {
      this.activeNumber = item;
    },
    modalOK() {

    },
  }
}
</script>
<style scoped lang="less">
.vm_new_dialog {
  height: 500px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  .ivu-menu-light {
    width: 160px!important;
    min-width: 160px!important;
    max-width: 160px!important;
  }
}
.modalcontent {
  width: calc(100% - 180px);
  height: 100%;
  overflow: hidden;
}
</style>
