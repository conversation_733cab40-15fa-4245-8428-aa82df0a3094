<style lang="less">
@import "../systemFunction.less";
</style>
<template>
  <div class="user_management_area">
    <Spin fix v-if="spinShow" size="large" style="color:#ef853a">
      <Icon type="ios-loading" size=50 class="demo-spin-icon-load"></Icon>
      <div style="font-size:16px;padding:20px">Loading...</div>
    </Spin>
    <div class="user_management_btn">
      <span>
        <Button class="plus_btn" @click="newClick" v-show="powerAcitons.xinjianyonghu"><span class="icon iconfont icon-plus-circle"></span> 新建用户</Button>
        <Button class="close_btn" @click="deleteData(tableSelec)" v-show="powerAcitons.shanchuyonghu"><span class="icon iconfont icon-close-circle"></span> 删除用户</Button>
        <Button class="remove" @click="refreshUser" v-show="powerAcitons.shuaxinyonghu" ><span class="icon iconfont icon-a-15Jhuanyuan"></span> 刷新</Button>
      </span>
    </div>
    <div class="table_currency_area" v-show="powerAcitons.yonghuliebiao">
      <Table :columns="tableColumn" :data="tableData" @on-selection-change="tableChange">
        <!-- 密码有效期 -->
        <template v-slot:expiredday="{ row }">
          <span v-if="row.username=='sysadm'||row.username=='adtadm'||row.username=='secadm'">永久有效</span>
          <span v-else>{{ row.expiredday }}</span>
        </template>
        <!-- 角色 -->
        <template v-slot:role_name="{ row }">
          {{ translationRole(row.role_name) }}
        </template>
        <!-- 用户状态 -->
        <template v-slot:status="{ row }">
          <span v-if="row.username=='sysadm'||row.username=='adtadm'||row.username=='secadm'">-</span>
          <i-switch v-else size="large" v-model="row.status=='on'" :before-change="()=>handleBeforeChange(row)">
            <span slot="open">启用</span>
            <span slot="close">禁用</span>
          </i-switch>
        </template>
        <!-- 操作 --> 
        <template v-slot:operation="{ row }">
          <!-- <span v-if="row.username=='sysadm'||row.username=='adtadm'||row.username=='secadm'">-</span>
          <Dropdown v-else @on-click="dropdownClick($event, row)"> -->
          <Dropdown @on-click="dropdownClick($event, row)">
            <Button>配置 ▼</Button>
            <template #list>
              <DropdownMenu>
                <DropdownItem name="czmm" v-show="powerAcitons.chongzhimima">重置密码</DropdownItem>
                <DropdownItem name="xgyh" v-show="powerAcitons.bianjiyonghu&&judgeRole(row)">编辑用户</DropdownItem>
                <DropdownItem name="qxsz" v-show="powerAcitons.fenpeiquanxian">权限设置</DropdownItem>
                <DropdownItem name="sc" v-show="powerAcitons.shuaxinyonghu&&judgeRole(row)" style="color: red" divided>删除</DropdownItem>
              </DropdownMenu>
            </template>
          </Dropdown>
        </template>
      </Table>
      <!-- <div class="pages" v-if="this.tableData.length>0">
        <Pagination  :total="tableTotal" :page-size="tablePageForm.pagecount"  @page-change="onPageChange" @page-size-change="onPageSizeChange"/>
      </div> -->
    </div>
    <!-- 新建用户 -->
    <UserNew
      :newTime="newTime"
      @custom-error="customError"
      @custom-ok="customOK"
    ></UserNew>
    <!-- 编辑用户 -->
    <UserEdit
      :editTime="editTime"
      :tableRow="tableRow"
      @custom-error="customError"
      @custom-ok="customOK"
    ></UserEdit>
    <!-- 权限设置 -->
    <UserPower
      :powerTime="powerTime"
      :tableRow="tableRow"
    ></UserPower>
    <!-- 删除用户 -->
    <UserDelete
      :deleteTime="deleteTime"
      :tableDelete="tableDelete"
      @custom-error="customError"
      @custom-ok="customOK"
    ></UserDelete>
  </div>
</template>
<script>
import { powerCodeQuery } from '@/api/other'; // 权限可用性 查询

import {
  userTableQuery, // 用户查询
  userTableState, // 用户状态
  userTableResetPassword, // 用户重置密码
} from '@/api/system';
import UserNew from "./UserNew.vue"; // 新建用户
import UserEdit from "./UserEdit.vue"; // 编辑用户
import UserPower from "./UserPower.vue"; // 权限设置
import UserDelete from "./UserDelete.vue"; // 删除用户

export default {
  components: {
    UserNew,
    UserEdit,
    UserPower,
    UserDelete,
  },
  props: {
    tabName: String,
  },
  watch: {
    tabName(value) {
      if(value=='用户管理') {
        this.refreshUser()
        this.actionQuery(value)
      }
    },
  },
  mounted() {
    if(this.$store.state.power.systemFunctionTab == '用户管理') {
      this.refreshUser()
      this.actionQuery(this.tabName)
    }
  },
  data() {
    return {
      spinShow:false,
      tableColumn:[], // 表格 列
      tableData:[], // 表格 行
      tableSelec:[], // 表格 选择
      newTime: '', // 新建用户
      tableRow: {},
      editTime: '', // 修改用户
      powerTime: '', // 权限设置
      tableDelete: [],
      deleteTime: '', // 删除用户
      
      powerAcitons: {}, // 操作权限数据
    }
  },
  methods: {
    cloumnManage(){
      this.tableColumn=[
        {type: 'selection',width: 30,align: 'center'},
        { title: "登录账号", key: "username", sortable: true },
        { title: "用户名称", key: "name", sortable: true,align: "center" },
        { title: "密码有效期", key: "expiredday",align: "center", slot: "expiredday" },
        { title: "角色", key: "role_name",align: "center", slot: "role_name" },
        ...(this.powerAcitons.yonghuzhuangtai?[{ title: "用户状态", key: "status",align: "center", slot: "status" }]:[]),
        ...(this.operationShow()?[{ title: "操作", key: "operation",width:120,slot: "operation" }]:[]),
      ]
    },
    // 表格操作
    dropdownClick(event, row) {
      switch (event) {
        case "czmm":
          this.$Modal.confirm({
            title: '重置用户密码',
            content: '<p>是否对用户名为： <span style="font-weight: 600;color:red;">'+row.username+'</span> 的用户进行密码重置?重置后密码恢复为默认密码.</p>',
            onOk: () => {
              userTableResetPassword({username: row.username}).then(callback=>{
                if(callback.data.msg == "ok") {
                  this.$Message.success({
                    background: true,
                    closable: true,
                    duration: 5,
                    content: '用户'+row.username+'密码重置完成'
                  });
                }else {
                  this.customError('用户'+row.username+'密码重置失败')
                }
              })
            },
            onCancel: () => {}
          });
          break;
        case "xgyh":
          this.tableRow = row
          this.editTime = ""+ new Date();
          break;
        case "qxsz":
          this.tableRow = row
          this.powerTime = ""+ new Date();
          break;
        case "sc":
          this.deleteData([row]);
          break;
      }
    },
    // 新建用户 按钮
    newClick(){
      this.newTime = ""+ new Date();
    },
    // 删除用户 按钮
    deleteData(data){
      if(data == 0) {
        this.$Message.warning({
          background: true,
          closable: true,
          duration: 5,
          content: "未选择表格数据",
        });
      }else {
        this.deleteTime = ""+ new Date();
        this.tableDelete = data
      }
    },
    // 刷新 按钮
    refreshUser() {
      this.spinShow=true;
      userTableQuery().then(callback=>{
        this.tableSelec = new Array()
        let lists = callback.data.map(em=>{
          let newEm = {...em}
          if(newEm.username == 'sysadm' ||newEm.username == 'adtadm' ||newEm.username == 'secadm' ) {
            newEm['_disabled'] = true
          }else {
            newEm['_disabled'] = false
          }
          return newEm
        })
        this.spinShow=false
        if(!this.moduleShow('tripleManage')) {
          this.tableData = lists.filter(item =>item.role_name=='sysadm')
        }else {
          this.tableData = lists
        }
      }).catch((error) => {
        this.spinShow=false
      });
    },
    // 状态切换
    handleBeforeChange(row){
      return new Promise((resolve) => {
        this.$Modal.confirm({
          title: '切换确认',
          content: `<p>是否修改用户 <span style="font-weight: 800;">${row.username}</span> 状态为 <span style="color:${row.status=='on'?'red':'green'};">${row.status=='on'?'禁用':'启用'}</span></p>`,
          onOk: () => {
            this.modifyUserOk(row,row.status=='on'?'off':'on')
            resolve();
          }
        });
      });
    },
    // 判断role
    judgeRole(row) {
      if(row.username=='sysadm'||row.username=='adtadm'||row.username=='secadm'){
        return false
      }else{
        return true
      }
    },
    // 修改用户状态确认
    modifyUserOk(rowName,rowStatus) {
      userTableState({username:rowName.username,status:rowStatus}).then(callback=>{
        if(callback.data.msg == "ok"){
          this.refreshUser()
        }else {
          this.$Message.error({
            background: true,
              closable: true,
            duration: 5,
            content: "修改用户状态失败",
          });
        }
      })
    },
    // 用户表勾选数据
    tableChange(item) {
      this.tableSelec = item
    },
    // 转译角色
    translationRole(item){
      switch (item){
        case "sysadm":
          return "系统管理员"
        break;
        case "secadm":
          return "安全员"
        break;
        case "adtadm":
          return "审计员"
        break;
        case "operator":
          return "操作员"
        break;
      }
    },
    // 子组件返回错误
    customError(data) {
      this.$Message.error({
        background: true,
        closable: true,
        duration: 5,
        content: data,
      });
    },
    // 子组件返回成功
    customOK(data) {
      this.refreshUser();
      this.$Message.success({
        background: true,
        closable: true,
        duration: 5,
        content: data,
      });
    },
    // 操作列
    operationShow(){
      let list = false
      if(
        this.powerAcitons.chongzhimima ||
        this.powerAcitons.bianjiyonghu ||
        this.powerAcitons.fenpeiquanxian
      ){
        list = true
      }
      return list
    },
    // 操作权限获取
    actionQuery(item){
      powerCodeQuery({
        module_code:[
          'yonghuliebiao',
          'xinjianyonghu',
          'shanchuyonghu',
          'shuaxinyonghu',
          'chongzhimima',
          'bianjiyonghu',
          'fenpeiquanxian',
          'yonghuzhuangtai',
        ]
      }).then(callback=>{
        this.powerAcitons = callback.data.data
        this.cloumnManage()
        this.powerAcitons.yonghuliebiao?this.refreshUser():null
      })
    },
    // 模块显示隐藏判断
    moduleShow(item){
      return window.gurl.PageModule.indexOf(item) == -1?true:false
    },
  }
}
</script>
