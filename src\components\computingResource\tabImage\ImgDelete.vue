<template>
  <div>
    <Modal
      v-model="model"
      width="600"
      :mask-closable="false"
    >
      <template #header><p>删除镜像</p></template>
      <div style="padding: 5px">
        <span>是否删除下列镜像？</span>
        <p style="color:red;word-wrap: break-word">{{tableNames.toString()}}</p>
      </div>
      <div slot="footer">
        <Button type="text" @click="model = false">取消</Button>
        <Button type="primary" :loading="disabled" @click="modelOK">
          <span v-if="!disabled">确认</span>
          <span v-else>删除中</span>
        </Button>
      </div>
    </Modal>
  </div>
</template>
<script>
import {
  imageTableDelete, // 镜像表 删除
} from '@/api/image'; 
export default {
  props: {
    tableDelet: Array,
    deleteTime: String,
  },
  watch: {
    deleteTime(news){
      this.tableNames = this.tableDelet.map(em=>{ return em.name})
      this.tableIDS = this.tableDelet.map(em=>{ return em.id})
      this.model = true
      this.disabled = false
    }
  },
  data(){
    
    return {
      model:false,
      disabled: false,
      tableNames: [],
      tableIDS: [],
    }
  },
  methods: {
    modelOK() {
      this.disabled = true
      this.tableNames.forEach((item,index)=>{
        imageTableDelete({data:{
          id: this.tableIDS[index],
          name: item,
        }})
        .then((callback) => {
          if(callback.data.msg == 'ok') {
            this.model = false;
            this.$emit("return-ok",item+' 镜像删除操作已完成');
          }else if(callback.data.msg == '409') {
            this.disabled = false
            this.$emit("return-error",item+' 镜像正在被使用无法被删除');
          }else {
            this.disabled = false
            this.$emit("return-error",item+' 镜像删除操作已失败');
          }
        })
        .catch((error) => {
          this.disabled = false
          this.$emit("return-error",item+' 镜像删除操作已失败');
        })
      })
    }
  },
}
</script>