{"_from": "content-type@~1.0.4", "_id": "content-type@1.0.4", "_inBundle": false, "_integrity": "sha512-hIP3EEPs8tB9AT1L+NUqtwOAps4mk2Zob89MWXMHjHWg9milF/j4osnnQLXBCBFBk/tvIG/tUc9mOUJiPBhPXA==", "_location": "/content-type", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "content-type@~1.0.4", "name": "content-type", "escapedName": "content-type", "rawSpec": "~1.0.4", "saveSpec": null, "fetchSpec": "~1.0.4"}, "_requiredBy": ["/body-parser", "/express"], "_resolved": "https://registry.npmmirror.com/content-type/-/content-type-1.0.4.tgz", "_shasum": "e138cc75e040c727b1966fe5e5f8c9aee256fe3b", "_spec": "content-type@~1.0.4", "_where": "/root/docker/node_modules/express", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/jshttp/content-type/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Create and parse HTTP Content-Type header", "devDependencies": {"eslint": "3.19.0", "eslint-config-standard": "10.2.1", "eslint-plugin-import": "2.7.0", "eslint-plugin-node": "5.1.1", "eslint-plugin-promise": "3.5.0", "eslint-plugin-standard": "3.0.1", "istanbul": "0.4.5", "mocha": "~1.21.5"}, "engines": {"node": ">= 0.6"}, "files": ["LICENSE", "HISTORY.md", "README.md", "index.js"], "homepage": "https://github.com/jshttp/content-type#readme", "keywords": ["content-type", "http", "req", "res", "rfc7231"], "license": "MIT", "name": "content-type", "repository": {"type": "git", "url": "git+https://github.com/jshttp/content-type.git"}, "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --check-leaks --bail test/", "test-ci": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/"}, "version": "1.0.4"}