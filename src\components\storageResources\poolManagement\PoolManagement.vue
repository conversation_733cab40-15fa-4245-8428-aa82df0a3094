<template>
  <!-- 卷池管理页面 -->
  <div style="overflow: auto;height: 100%; padding: 0 0 150px 0">
    <div  class="cloud_hard_drive_area">
      <div>
        <Button class="plus_btn" @click="increaseClick"><span class="icon iconfont icon-plus-circle"></span> 添加卷池</Button>
        <Button class="plus_btn" @click="volumePoolData"><span class="icon iconfont icon-plus-circle"></span> shua</Button>
      </div>
      <div>
          <!-- <Input v-model="tablePageForm.search_str" search enter-button placeholder="请输入名称" style="width: 300px" @on-search="tableSearchInput" /> -->
        </div>
    </div>  
    <div style="position:relative;">
      <Spin fix v-if="spinShow" size="large" style="color:#ef853a">
        <Icon type="ios-loading" size=50 class="demo-spin-icon-load"></Icon>
        <div style="font-size:16px;padding:20px">Loading...</div>
      </Spin>
      <Table :columns="vpColumns" :data="vpData"></Table>
      <div class="pages" v-if="this.vpData.length>0">
        <Pagination  :total="tableTotal" :page-size="tablePageForm.pagecount"  @page-change="onPageChange" @page-size-change="onPageSizeChange"/>
      </div>
    </div>
    <!-- 添加卷池 -->
    <Modal v-model="increaseModal" title="添加卷池" @on-ok="addOK" @on-cancel="cancel" :mask-closable="false">
      <div>
        <Form :model="FormItem" :label-width="120" :rules="ruleForm">
          <FormItem label="卷池名称" prop="pool">
            <Input v-model="FormItem.pool" placeholder="请输入类型名称"></Input>
          </FormItem>
          <FormItem label="硬盘域">
            <Select v-model="FormItem.domain">
              <Option v-for="item in domainData" :value="item.key" :key="item.key" >{{ item.value }}</Option>
            </Select>
          </FormItem>
          <FormItem label="应用类型">
            <Select v-model="FormItem.application_metadata">
              <Option v-for="item in applicationData" :value="item.key" :key="item.key" >{{ item.value }}</Option>
            </Select>
          </FormItem>
          <FormItem label="安全类型" >
            <Select v-model="FormItem.pool_type">
              <Option v-for="item in securityData" :value="item.key" :key="item.key" >{{ item.value }}</Option>
            </Select>
          </FormItem>
          <FormItem label="安全级别" v-if="FormItem.pool_type=='replicated'">
            <Select v-model="FormItem.size">
              <Option v-for="item in sizeData" :value="item.key" :key="item.key" >{{ item.value }}</Option>
            </Select>
          </FormItem>
          <FormItem v-if="FormItem.pool_type!=='replicated'" label="纠删码级别">
            <Select v-model="FormItem.erasure_code_profile">
              <Option v-for="item in erasureCodeProfileListFilter" :value="item.name" :key="item.name" >{{ item.name }}</Option>
            </Select>
          </FormItem>
          <FormItem label="卷池大小">
            <InputNumber v-model="FormItem.quota_max_bytes" style="width:200px" :min=1 />（GB）
          </FormItem>
          <FormItem label="开启压缩">
            <Select v-model="FormItem.compression_mode">
              <Option v-for="item in compressData" :value="item.key" :key="item.key" >{{ item.value }}</Option>
            </Select>
          </FormItem>
          <FormItem v-if="FormItem.compression_mode!=='none'" label="压缩算法">
            <Select v-model="FormItem.algorithm">
              <Option v-for="item in algorithmData" :value="item.key" :key="item.key" >{{ item.value }}</Option>
            </Select>
          </FormItem>
          <FormItem v-if="FormItem.compression_mode!=='none'" label="压缩块">
            <Row>
                <Col span="12">
                  最小 <InputNumber v-model="FormItem.minsize" :max="512" :min="128" />（KB）
                </Col>
                <Col span="12">
                  最大 <InputNumber v-model="FormItem.maxsize" :max="512" :min="129" />（KB）
                </Col>
            </Row>
          </FormItem>
          <FormItem v-if="FormItem.compression_mode!=='none'" label="压缩级别" prop="level">
            <Select v-model="FormItem.level">
              <Option v-for="item in compresslevelData" :value="item.key" :key="item.key" >{{ item.value }}</Option>
            </Select>
          </FormItem>
        </Form>
      </div>
    </Modal>
     <!-- 编辑卷池 -->
    <Modal v-model="editModal" title="编辑卷池" @on-ok="editOK" @on-cancel="cancel" :mask-closable="false">
      <div>
        <Form :model="editForm" :label-width="120" :rules="ruleForm">
          <FormItem label="卷池名称" prop="pool">
            <Input v-model="editForm.pool" placeholder="请输入类型名称"></Input>
          </FormItem>
          <FormItem label="硬盘域">
            <Select v-model="editForm.domain">
              <Option v-for="item in domainData" :value="item.key" :key="item.key" >{{ item.value }}</Option>
            </Select>
          </FormItem>
          <FormItem label="应用类型">
            <Select v-model="editForm.application_metadata">
              <Option v-for="item in applicationData" :value="item.key" :key="item.key" >{{ item.value }}</Option>
            </Select>
          </FormItem>
          <FormItem label="卷池大小">
            <InputNumber v-model="editForm.quota_max_bytes" style="width:200px" :min=1 />（GB）
          </FormItem>
          <FormItem label="开启压缩">
            <Select v-model="editForm.compression_mode">
              <Option v-for="item in compressData" :value="item.key" :key="item.key" >{{ item.value }}</Option>
            </Select>
          </FormItem>
          <FormItem v-if="editForm.compression_mode!=='none'" label="压缩算法">
            <Select v-model="editForm.algorithm">
              <Option v-for="item in algorithmData" :value="item.key" :key="item.key" >{{ item.value }}</Option>
            </Select>
          </FormItem>
          <FormItem v-if="editForm.compression_mode!=='none'" label="压缩块">
            <Row>
                <Col span="12">
                  最小 <InputNumber v-model="editForm.minsize" :max="512" :min="128" />（KB）
                </Col>
                <Col span="12">
                  最大 <InputNumber v-model="editForm.maxsize" :max="512" :min="129" />（KB）
                </Col>
            </Row>
          </FormItem>
          <FormItem v-if="editForm.compression_mode!=='none'" label="压缩级别" prop="level">
            <Select v-model="editForm.level">
              <Option v-for="item in compresslevelData" :value="item.key" :key="item.key" >{{ item.value }}</Option>
            </Select>
          </FormItem>
        </Form>
      </div>
    </Modal>
  </div>
</template>

<script>
  import Pagination from '@/components/public/Pagination.vue';
  export default {
    components: {
      Pagination
    },
    props: {
      tabName: String,
    },
    watch: {
      tabName(value) {
        value=='juanchi'?this.volumePoolData():""
        if(value=='juanchi') {
          this.volumePoolData()
          this.domainGET()
          this.getErasureCodeProfile()
        }
      },
    },
    data() {
      const propPoolname = (rule, value, callback) => {
        let list = /^[\u4e00-\u9fa5_a-zA-Z0-9@_.-]{2,32}$/;
        if (list.test(value)) {
          callback();
        } else {
          callback(new Error("请输入2-32个中文或字符（特殊字符可用@_.-）"));
        }
      };
      return{
        spinShow:false,
        // 卷池表数据
        vpColumns:[
          // {type: 'selection',width: 30,align: 'center'},
				  { title: "卷池名称",key: "pool_name" },
				  { title: "硬盘域",key: "crush_rule",align: "center" },
				  { title: "应用类型",key: "application_metadata",align: "center",
            render: (h, params) => {
              switch (params.row.application_metadata[0]) {
                case "rbd":
                  return h("span","块存储")
                break;
                case "cephfs":
                  return h("span","文件系统")
                break;
                case "rgw":
                  return h("span","对象存储")
                break;
                case "nfs":
                  return h("span","nfs")
                break;
                case "grace":
                  return h("span","grace")
                break;
                 case "mgr_devicehealth":
                  return h("span","未定义")
                break;
                 case "iscsi":
                  return h("span","磁盘类型")
                break;
              }
            }
          },
				  { title: "安全类型",key: "type",align: "center",
            render: (h, params) => {
              let text = "副本类型"
              params.row.type=="replicated"? text="副本类型":text="纠删码型"
              return h("span",text)
            }
          },
				  { title: "安全级别/纠删码规则",key: "size",align: "center",
            render: (h, params) => {
              let text = params.row.erasure_code_profile
              params.row.erasure_code_profile? text=params.row.erasure_code_profile:text=params.row.size
              return h("span",text)
            }
          },
				  { title: "容量",key: "quota_max_bytes",align: "center" },
				  { title: "操作",key: "action",width:200,
            render: (h, params) => {
              return h("div", [
                h("Button", {
                  props:{
                    icon:"ios-create",
                    class:"close_btn"
                  },
                  on: {
                    click: () => {
                      this.tableAction(params.row,"edit")
                    },
                  },
                },"编辑"),
                h("Button", {
                  style:{color:'red',marginLeft:'10px'},
                  props:{
                    icon:"ios-trash",
                    class:"plus_btn"
                  },
                  on: {
                    click: () => {
                      this.tableAction(params.row,"deleted")
                    },
                  },
                },"删除"),
              ])
            }
          },
        ],
        vpAallData:[],
        vpData:[],
        // 分页 
        tablePageForm: {
          page: 1, // 当前页
          pagecount: this.$store.state.power.pagecount, // 每页条
          search_str: "", // 收索
          order_type: "asc", // 排序规则
          order_by: "", // 排序列
        },
        
        // 添加卷池
        increaseModal:false,
        FormItem:{
          pool:"",
          domain:"",
          application_metadata:"rbd",
          pool_type:"replicated",
          size:2,
          erasure_code_profile:"",
          quota_max_bytes:2,
          compression_mode:"none",
          algorithm:"lz4",
          minsize:128,
          maxsize:512,
          level:0.5,
        },
        // 正则 
        ruleForm:{
          pool:[
            { required: true, message: "必填项", trigger: "blur" },
            { validator: propPoolname, trigger: "change" },
          ],
        },
        domainData:[],
        applicationData:[
          {value:"块存储",key:"rbd"},
          {value:"文件系统",key:"cephfs"},
          {value:"对象网关",key:"rgw"},
        ],
        securityData:[
          {value:"副本类型",key:"replicated"},
          {value:"纠删码型",key:"erasure"},
        ],
        sizeData:[
          {value:2,key:2},
          {value:3,key:3},
          {value:4,key:4},
        ],
        erasureData:[],
        compressData:[
          {value:"关闭",key:"none"},
          {value:"被动",key:"passive"},
          {value:"主动",key:"aggressive"},
          {value:"强制",key:"force"},
        ],
        algorithmData:[
          {value:"snappy",key:"snappy"},
          {value:"zlib",key:"zlib"},
          {value:"zstd",key:"zstd"},
          {value:"lz4",key:"lz4"},
        ],
        compresslevelData:[
          {value:0.1,key:0.1},
          {value:0.2,key:0.2},
          {value:0.3,key:0.3},
          {value:0.4,key:0.4},
          {value:0.5,key:0.5},
          {value:0.6,key:0.6},
          {value:0.7,key:0.7},
          {value:0.8,key:0.8},
          {value:0.9,key:0.9},
          {value:1,key:1},
        ],

        // 编辑卷池
        editModal:false,
        editForm:{
          pool:"",
          domain:"",
          application_metadata:"",
          quota_max_bytes:0,
          compression_mode:"",
          algorithm:"",
          minsize:128,
          maxsize:512,
          level:0.4,
        },
      }
    },
    mounted(){
    },
    updated() {
      this.tablePageForm.pagecount=this.$store.state.power.pagecount
    },
    computed: {
      // 纠删码过滤条件
      erasureCodeProfileListFilter() { // 根据硬盘域名字，过滤纠删码规则
        let filterName = this.FormItem.domain
        this.$set(this.FormItem, 'erasure_code_profile', '')
        let arrList = this.erasureData.filter( item => { return item['crush-root'] === filterName } )
        return  arrList
      }
    },
    methods:{
      // 获取卷池管理表数据
      volumePoolData() {
        this.spinShow = true
        this.$axios.get("/thecephapi/api/pool").then(callback=>{
          this.tableTotal = callback.data.length
          this.vpAallData = callback.data
          this.vpData = this.vpAallData.slice(this.tablePageForm.page - 1,this.tablePageForm.page * this.$store.state.power.pagecount)
          this.spinShow = false
        }).catch((error) => {
          // this.spinShow = false
        });
      },
      // 纠删码获取
      getErasureCodeProfile(){
        this.$axios.get("/thecephapi/api/erasure_code_profile").then(callback=>{
          this.erasureData = callback.data
        })
      },
      // 硬盘域数据获取
      domainGET(){
        this.$axios.get("/thecss/v2/getOnlyYvNames").then(callback=>{
          let arr = new Array()
          callback.data.forEach(em=>{
            arr.push({
              key: em,
              value: em
            })
          })
          this.domainData = arr
        })
      },
      // 添加卷池按钮
      increaseClick(){
        this.increaseModal = true;
        this.FormItem.pool = ""
        this.FormItem.domain = this.domainData[0].value
        this.FormItem.application_metadata = "rbd"
        this.FormItem.pool_type = "replicated"
        this.FormItem.size = 3
        this.FormItem.erasure_code_profile = ""
        // this.FormItem.erasure_code_profile = this.erasureCodeProfileListFilter[0].value
        this.FormItem.quota_max_bytes = 2
        this.FormItemcompression_mode = "none"
        this.FormItem.algorithm = "lz4"
        this.FormItem.minsize = 128
        this.FormItem.maxsize = 512
        this.FormItem.level = 0.4
      },
      // 添加卷池弹框确认
      addOK(){
        let datas = new Object()
        datas.pool = this.FormItem.pool
        datas.application_metadata = [this.FormItem.application_metadata]
        datas.pool_type = this.FormItem.pool_type
        this.FormItem.pool_type=='replicated'?
        datas.size = this.FormItem.size:
        datas.erasure_code_profile = this.FormItem.erasure_code_profile
        datas.quota_max_bytes = this.FormItem.quota_max_bytes* 1024*1024*1024
        datas.compression_mode = this.FormItem.compression_mode
        datas.erasure_code_profile = ""
        datas.pg_autoscale_mode = "on"
        datas.pg_num = 8
        datas.quota_max_objects = 100000
        // datas.rule_name = "replicated_rule"
        
        // if(this.FormItem.compression_mode!=="none") {
          datas.compression_algorithm =  this.FormItem.algorithm
          datas.compression_min_blob_size =  this.FormItem.minsize*1024
          datas.compression_max_blob_size =  this.FormItem.maxsize*1024
          datas.compression_required_ratio =  this.FormItem.level
        // }
        let ruleName = /^[\u4e00-\u9fa5_a-zA-Z0-9@_.-]{2,32}$/;
        if (ruleName.test(this.FormItem.pool)) {
          this.$axios.post("/thecephapi/api/pool",datas).then(callback=>{
            // let pattern = /^.{3}$/
            // let one = /^2/
            // if(pattern.test(callback.status.toString())&&one.test(callback.status.toString())){
              if(callback.data.name=='pool/create') {
                this.$Message.success({
                  background: true,
                  closable: true,
                  duration: 5,
                  content: "添加卷池操作完成",
                });
                this.volumePoolData()
              }else {
                this.$Message.error({
                  background: true,
                  closable: true,
                  duration: 5,
                  content: "添加卷池操作失败",
                });
              }
            // }else {
            //   this.$Message.error({
            //     background: true,
            //     closable: true,
            //     duration: 5,
            //     content: "添加卷池失败",
            //   });
            // }
          })
        }else {
          this.$Message.error({
            background: true,
            closable: true,
            duration: 5,
            content: "输入的卷池名称不符合规定",
          });
        }
        this.increaseModal = false;
      },
      // 当前分页
      onPageChange(item) {
        this.tablePageForm.page = item
        this.volumePoolData();
      },
      // 每页条数
      onPageSizeChange(item) {
        this.$store.state.power.pagecount = item
        this.tablePageForm.pagecount = this.$store.state.power.pagecount
        this.tablePageForm.page = 1
        this.volumePoolData();
      },

      // 表格操作
      tableAction(row,action){
        switch (action) {
        case "edit":
          this.editModal = true

          break;   
        case "deleted":
          this.$Modal.confirm({
            title: "删除卷池",
            content:
              '<p>是删除名称为<span style="font-weight: 600;color:red;word-wrap: break-word;">' +
              row.pool_name +
              "</span>的卷池？</p>",
            onOk: () => {
              // let pattern = /^.{3}$/
              // let one = /^2/
              this.$axios.delete("/thecephapi/api/pool/"+row.pool_name).then((callback) => {
                // if(pattern.test(callback.status.toString())&&one.test(callback.status.toString())){
                  this.$Message.success({
                    background: true,
                    closable: true,
                    duration: 5,
                    content: "删除卷池操作完成",
                  });
                  this.volumePoolData()
                // }
              }).catch((error) => {
                this.$Message.error({
                    background: true,
                    closable: true,
                    duration: 5,
                    content: "删除卷池操作失败",
                  });
              });
            },
            onCancel: () => {
              
            },
          });
          break;
        }
      },

      editOK(){
      },
      cancel(){},
    },
  }


  
</script>