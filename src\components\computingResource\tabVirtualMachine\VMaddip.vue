<template>
  <div>
    <Modal
      v-model="model"
      width="600"
      :mask-closable="false"
    >
      <template #header><p><span style="color:green">{{vmRow.name}}</span>虚拟机添加网卡</p></template>
      <Form :model="formItem" ref="formItem" :rules="ruleValidate" :label-width="150">
        <FormItem label="网络名称">
          <Select
            v-model="formItem.networkId"
            :label-in-value="true"
            @on-change="slectChange"
          >
            <Option
              v-for="item in netOption"
              :value="item.id"
              :key="item.id"
              >{{ item.name }}</Option
            >
          </Select>
        </FormItem>
        <FormItem label="预设IP">
          <Checkbox v-model="formItem.presetsIP"></Checkbox>
        </FormItem>
        <FormItem label="IP地址" prop="ipPash"  v-if="this.formItem.presetsIP">
          <Input
            v-model="formItem.ipPash"
            placeholder="请输入IP地址"
          ></Input>
        </FormItem>
      </Form>
      <div slot="footer">
        <Button type="text" @click="model = false">取消</Button>
        <Button type="primary" @click="modelOK" :disabled="disabled">确认</Button>
      </div>
    </Modal>
  </div>
</template>
<script>
import {networkTableQuery,networkCheckip} from '@/api/network';
import {vmGroupTableAddAdapter} from '@/api/virtualMachine';
export default {
  props: {
    vmRow: Object,
    addipTime: String,
  },
  watch: {
    addipTime(news){
      this.formItem.vmid = this.vmRow.id
      this.formItem.vmName = this.vmRow.name
      this.networkData()
      this.model = true
      this.disabled = false
      this.formItem.presetsIP= false
      this.$refs['formItem'].resetFields()
    }
  },
  data(){
    const propipPash = (rule, value, callback) => {
      let list = /^(25[0-4]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-4]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-4]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-4]|2[0-4][0-9]|[01]?[1-9][0-9]?|1[0-9]{2}|2[0-4][0-9])$/
      let subnet = this.formItem.networkName.split(':')[1]
      if(list.test(value)) {
        if(this.ipInSubnet(value, subnet)) {
          callback()
        }else {
          callback(new Error("IP地址与所选网段不匹配"))
        }
      }else {
        callback(new Error("该ip不可用"))
      }
      // if(list.test(value)){
      //   callback()
      // }else {
      //   callback(new Error("该ip不可用"))
      // }
    };
    return {
      model: false,
      disabled: false,
      netOption: [],
      formItem: {
        networkId: '',
        networkName: '',
        presetsIP: false,
        ipPash: '',
        vmid: '',
        vmName: '',
      },
      ruleValidate: {
        ipPash: [
          { required: true, message: "填写IP"},
          { validator: propipPash, trigger: "blur" },
        ],
      }
    }
  },
  methods: {
    // 获取网络下拉数据
    networkData(){
      networkTableQuery().then((callback) => {
        let arr = new Array();
        callback.data.forEach(item=>{
          item.cidr.forEach((em,i) => {
            arr.push({
              id: item.id+":"+item.subnets[i],
              name: em,
            })
          })
        })
        this.netOption = arr;
        this.formItem.networkId = this.netOption[0].id
        this.formItem.networkName = this.netOption[0].name
      });
    },
    slectChange(item) {
      if(item!==undefined) {
        this.formItem.networkName = item.label
        this.formItem.networkId = item.value;
      }
    },
    modelOK(){
      this.$refs['formItem'].validate((valid) => {
        if(valid){
          this.disabled = true
          if(this.formItem.presetsIP) {
            networkCheckip({
              now_ipv4:'',
              new_ipv4:this.formItem.ipPash
            }).then((check)=>{
              if(check.data.msg == 'ok'){
                vmGroupTableAddAdapter({
                  id: this.formItem.vmid,
                  new_netid: this.formItem.networkId.split(":")[0],
                  subnet_id: this.formItem.networkId.split(":")[1],
                  new_ipv4: this.formItem.presetsIP?this.formItem.ipPash:"",
                  name: this.formItem.vmName,
                  // ...(this.formItem.presetsIP?{new_ipv4:this.formItem.ipPash}:"")
                })
                .then((callback)=>{
                  if(callback.data.msg=='添加成功') {
                    this.model=false
                    this.$emit("return-ok",'添加网卡操作完成');
                  }else{
                    this.disabled = false
                    this.$emit("return-error",callback.data.msg);
                  }
                }).catch((error) => {
                  this.disabled = false
                  this.$emit("return-error",'添加网卡操作失败');
                })
              }else {
                this.disabled = false
                this.$Message.error({
                  background: true,
                  closable: true,
                  duration: 10,
                  content: check.data.msg
                });
              }
            })
          }else {
            vmGroupTableAddAdapter({
                id: this.formItem.vmid,
                new_netid:this.formItem.networkId.split(":")[0],
                subnet_id:this.formItem.networkId.split(":")[1],
                new_ipv4:this.formItem.presetsIP?this.formItem.ipPash:"",
                // ...(this.formItem.presetsIP?{new_ipv4:this.formItem.ipPash}:"")
              })
            .then((callback)=>{
                if(callback.data.msg=='添加成功') {
                  this.model=false
                  this.$emit("return-ok",'添加网卡操作完成');
                }else{
                  this.disabled = false
                  this.$emit("return-error",callback.data.msg);
                }
              }).catch((error) => {
              this.disabled = false
              this.$emit("return-error",'添加网卡操作失败');
            })
          }
        }else{
          this.disabled = false
        }
      })
    },
    // 判断网络属于子网
    ipInSubnet(ip, subnet) {
      let [subnetIp, maskBits] = subnet.split('/');
      maskBits = parseInt(maskBits, 10);
      let ipBinary = ip.split('.').map(octet => parseInt(octet, 10).toString(2).padStart(8, '0')).join('');
      let subnetBinary = subnetIp.split('.').map(octet => parseInt(octet, 10).toString(2).padStart(8, '0')).join('');
      for(let i=0; i<maskBits; i++){
          if(ipBinary[i] != subnetBinary[i]){
              return false;
          }
      }
      return true;
    },
    cancel(){},
  }
}
</script>