<style>
@keyframes move {
  0% {
    background-position: -28px 0;
  }
  100% {
    background-position: 0 0;
  }
}
.table_schedule{
  width: 100%;
  height: 15px;
  border-radius: 5px;
  border: 1px solid red;
  background-image: repeating-linear-gradient(-45deg,#f67f45,#f67f45 11px,#eee 10px,#eee 20px);
  background-size: 2000px 28px;
  animation: move 1s linear infinite;
}
</style>
<style lang="less">
@import "../storageResources.less";
</style>
<template>
  <div class="cloud_hard_drive_area">
    <Spin fix v-if="spinShow" size="large" style="color:#ef853a">
      <Icon type="ios-loading" size=50 class="demo-spin-icon-load"></Icon>
      <div style="font-size:16px;padding:20px">Loading...</div>
    </Spin>
    <div class="table-button-area">
      <div>
        <Button class="plus_btn" v-show="powerAcitons.yunyingpanxinjian" @click="newClick"><span class="icon iconfont icon-plus-circle"></span> 新建云硬盘</Button>
        <Button class="close_btn" v-show="powerAcitons.yunyingpanshanchu" @click="deletClick(tableSelec)"><span class="icon iconfont icon-close-circle"></span> 删除云硬盘</Button>
      </div>
      <div v-show="powerAcitons.yunyingpansousuo">
        <Input v-model="tablePageForm.search_str" search enter-button placeholder="请输入名称" style="width: 300px" @on-search="tableSearchInput"/>
      </div>
    </div>
    <div class="table_currency_area" v-show="powerAcitons.yunyingpanliebiao">
      <Table :columns="cloudColumn" :data="cloudData" @on-selection-change="tableChange" @on-sort-change="sortColumn">
        <!-- 状态 -->
        <template v-slot:status="{row}">
          <!-- <div :class="renwu('class',row)">{{ renwu('text',row) }}</div> -->
          <span v-if="row.status == 'in-use'">使用中</span>
          <span v-else-if="row.status == 'available'">空闲中</span>
          <span v-else-if="row.status == 'error'">错误</span>
          <span v-else-if="row.status == 'error_deleting'">删除错误</span>
          <span v-else-if="row.status == 'reserved'">保留</span>
          <div  v-else class="table_schedule">{{ convertTask(row.status) }}</div>
        </template>
        <!-- 云硬盘容量 -->
        <template v-slot:size="{row}">
          <span>{{ row.size }} GB</span>
        </template>
        <!-- 卷类型 -->
        <template v-slot:volume_type="{row}">
          <span>{{ row.volume_type == "__DEFAULT__"?'默认类型':row.volume_type }}</span>
        </template>
        <!-- 可启动 -->
        <template v-slot:bootable="{ row }">
          <i-switch size="large" v-model="row.bootable=='true'" :before-change="()=>handleBeforeChange(row)">
            <span slot="open">启用</span>
            <span slot="close">禁用</span>
          </i-switch>
        </template>
        <!-- 操作 -->
        <template v-slot:operation="{ row }">
          <Dropdown @on-click="dropdownClick($event, row)">
            <Button>配置 ▼</Button>
            <template #list>
              <DropdownMenu>
                <DropdownItem name="bjyp" v-show="powerAcitons.yunyingpanbianji">编辑云硬盘</DropdownItem>
                <DropdownItem name="ypkz" v-show="powerAcitons.yunyingpankuozhan">云硬盘扩展</DropdownItem>
                <DropdownItem name="cjkz" v-show="powerAcitons.chuangjiankuaizhao">创建快照</DropdownItem>
                <DropdownItem name="cjxj" v-show="powerAcitons.yunyingpanchuangjianxuniji" :disabled="row.status !=='available'||row.bootable =='false'">创建虚拟机</DropdownItem>
                <!-- <DropdownItem name="cjbf">创建备份</DropdownItem> -->
                <!-- <DropdownItem name="xglx">修改云硬盘类型</DropdownItem> -->
                <!-- <DropdownItem name="gxysj">更新元数据</DropdownItem> -->
                <!-- <DropdownItem name="qyyp">迁移云硬盘</DropdownItem> -->
                <!-- <DropdownItem name="gxjx">更新镜像</DropdownItem> -->
                <!-- <DropdownItem name="gxlj" v-if="row.connection!==''">管理连接</DropdownItem> -->
                <DropdownItem name="scjx" v-show="powerAcitons.yunyingpanshengchengjingxiang" :disabled="row.status!=='available'">生成镜像</DropdownItem>
                <DropdownItem name="sc" v-show="powerAcitons.yunyingpanshanchu" style="color: red" divided
                  >删除</DropdownItem
                >
              </DropdownMenu>
            </template>
          </Dropdown>
        </template>
      </Table>
      <div class="pages" v-if="this.cloudData.length>0">
        <!-- <Page :total="tableTotal" show-total show-sizer :page-size="tablePageForm.pagecount" placement=top :page-size-opts='[10, 20, 30, ]' @on-change="onPageChange" @on-page-size-change="onPageSizeChange" /> -->
        <Pagination  :total="tableTotal" :page-size="tablePageForm.pagecount"  @page-change="onPageChange" @page-size-change="onPageSizeChange"/>
      </div>
    </div>
    <!-- 新建云硬盘 -->
    <HardDriveNew
      :newTime="newTime"
      @return-ok="returnOK"
      @return-error="returnError"
    ></HardDriveNew>
    <!-- 编辑云硬盘 -->
    <HardDriveEdit
      :tableRow="tableRow"
      :editTime="editTime"
      @return-ok="returnOK"
      @return-error="returnError"
    ></HardDriveEdit>
    <!-- 扩展云硬盘 -->
    <HardDriveExtend
      :tableRow="tableRow"
      :extendTime="extendTime"
      @return-ok="returnOK"
      @return-error="returnError"
    ></HardDriveExtend>
    <!-- 创建快照 -->
    <HardDriveCreateSnapshot
      :tableRow="tableRow"
      :snapshotTime="snapshotTime"
      @return-error="returnError"
    ></HardDriveCreateSnapshot>
    <!-- 创建虚拟机 -->
    <HardDriveCreateVM
      :tableRow="tableRow"
      :vmTime="vmTime"
      @return-ok="returnOK"
      @return-error="returnError"
    ></HardDriveCreateVM>
    <!-- 管理连接 -->
    <HardDriveConnections
      :tableRow="tableRow"
      :connectionsTime="connectionsTime"
      @return-ok="returnOK"
      @return-error="returnError"
    ></HardDriveConnections>
    <!-- 删除云硬盘 -->
    <HardDriveDelete
      :tableDelet="tableDelet"
      :deleteTime="deleteTime"
      @return-ok="returnOK"
      @return-error="returnError"
    ></HardDriveDelete>
  </div>
</template>
<script>
import { powerCodeQuery } from '@/api/other'; // 权限可用性 查询

import {
  cloudDiskQuery, // 云硬盘 查询
  cloudDiskFiring, // 云硬盘 启动
  cloudDiskExpansion, // 云硬盘 操作（扩容、镜像）
} from '@/api/storage';
import Pagination from '@/components/public/Pagination.vue';
import HardDriveNew from "./HardDriveNew.vue"; // 新建云硬盘
import HardDriveEdit from "./HardDriveEdit.vue"; // 编辑云硬盘
import HardDriveDelete from "./HardDriveDelete.vue"; // 删除云硬盘
import HardDriveExtend from "./HardDriveExtend.vue"; // 扩展云硬盘
import HardDriveCreateSnapshot from "./HardDriveCreateSnapshot.vue"; // 创建快照
import HardDriveCreateVM from "./HardDriveCreateVM.vue"; // 创建虚拟机
import HardDriveConnections from "./HardDriveConnections.vue"; // 管理连接

export default {
  components: {
    Pagination,
    HardDriveNew,
    HardDriveEdit,
    HardDriveExtend,
    HardDriveCreateSnapshot,
    HardDriveCreateVM,
    HardDriveConnections,
    HardDriveDelete,
  },
  props:{
    tabName:String
  },
  watch: {
    tabName(value) {
      if(value=='云硬盘'){
        this.actionQuery()
      }else {
        clearInterval(this.cloudDiskTask)
      }
    },
    taskList(news){
      if(news==true) {
        this.cloudDiskTask = setInterval(() => {
          let tasknuber = 0
          cloudDiskQuery(this.tablePageForm).then(callback=>{
            this.tableTotal = callback.data.total
            this.cloudData = callback.data.data.map(item=>{
              if(item.status=="in-use") {
                item["_disabled"] = item.status=="in-use"
              }
              if(item.status =='in-use' || item.status =='available' || item.status =='error' || item.status =='error_deleting' || item.status =='reserved') {
                // console.log('============')
              }else {
                tasknuber++
              }
              return item
            })
            if(tasknuber==0){
              clearInterval(this.cloudDiskTask)
              this.taskList = false
            }
          })
        },10000)
      }
    },
  },
  mounted() {
    if(this.$store.state.power.storageResourceTab == '云硬盘') {
      this.actionQuery()
    }
  },
  data() {
    return {
      spinShow:false,
      tableDelet: [],
      deleteTime: '', // 云硬盘 删除
      newTime: '', // 云硬盘 新建
      tableRow: {},
      editTime: '', // 云硬盘 编辑
      extendTime: '', // 云硬盘 扩展
      snapshotTime: '', // 云硬盘 创建快照
      vmTime: '', // 云硬盘 创建虚拟机
      connectionsTime: '', // 云硬盘 管理连接

      cloudColumn: [],
      cloudData: [],
      tableSelec: [],
      // 表格任务自动刷新 
      taskList: false,
      cloudDiskTask: null,
      // 分页
      tablePageForm: {
        page: 1, // 当前页
        pagecount: this.$store.state.power.pagecount, // 每页条
        search_str: "", // 收索
        order_type: "asc", // 排序规则
        order_by: "", // 排序列
      },
      // 云硬盘 总条数
      tableTotal:0,

      powerAcitons: {}, // 操作权限数据
    }
  },
  updated() {
    this.tablePageForm.pagecount=this.$store.state.power.pagecount
  },
  methods: {
    cloumnManage(){
      this.cloudColumn=[
        { type: "selection", width: 30, align: "center"},
        { type: "expand", width: 50,
          render: (h, params) => {
            return h("div",{style:{width:'100%'}},[
              h("span",{style:{width:'45%',display: 'inline-block'}},'ID：'+params.row.id),
              // h("span",'名称：'+params.row.name)
            ]);
          },
        },
        { title: "名称", key: "name", tooltip:true,sortable: 'custom' },
        { title: "备注", key: "description",tooltip:true, align: "center" },
        { title: "云硬盘容量", key: "size", align: "center",sortable: "custom", slot: "size" },
        { title: "状态", key: "status", align: "center", slot: "status" },
        { title: "卷类型", key: "volume_type", align: "center", slot: "volume_type" },
        { title: "连接到", key: "connection", align: "center"},
        // { title: "主机名", key: "host", align: "center" },
        { title: "可启动", key: "bootable", align: "center", slot: "bootable"
          // render: (h, params) => {
          //   return h('i-switch',{
          //       props:{
          //         type:'primary',
          //         size:'large',
          //         value:params.row.bootable=='false'?false:true,
          //         beforeChange:function(){
          //           return new Promise((resolve)=>{
          //             this.$Modal.confirm({
          //               title: '可启动编辑',
          //               content:params.row.bootable=='true'? '<p>是否修改云硬盘 <span style="font-weight: 600;color:green;">'+params.row.name+'</span> 可启动<span style="font-weight: 600;color:red;">禁用</span></p>':'<p>是否修改云硬盘 <span style="font-weight: 600;color:green;">'+params.row.name+'</span> 可启动<span style="font-weight: 600;color:green;">启用</span></p>',
          //               onOk: () => {
          //                 resolve()
          //               }
          //             });
          //           })
          //         }
          //       },
          //       on: {
          //         'on-change':(val)=> {
          //           this.editable_startup(params.row,val)
          //         }
          //       }
          //     },
          //     [
          //       h('span',{slot:'open',domProps:{innerHTML:'启用'}}),
          //       h('span',{slot:'close',domProps:{innerHTML:'禁用'}}),
          //     ]
          //   )
          // }
        },
        ...(this.operationShow()?[{ title: "操作", key: "operation",width:120,slot: "operation" }]:[]),
      ]
    },
    // 表格操作
    dropdownClick(event, row) {
      this.tableRow = row
      switch (event) {
        case "bjyp":
          this.editTime = '' + new Date()
          break;
        case "ypkz":
          this.extendTime = '' + new Date()
          break;
        case "cjkz":
          this.snapshotTime = '' + new Date()
          break;
        case "cjxj":
          this.vmTime = '' + new Date()
          break;
        case "cjbf":
          // 创建备份
          break;
        case "xglx":
          // 修改云硬盘类型
          break;
        case "gxysj":
          // 更新元数据
          break;
        case "qyyp":
          // 迁移云硬盘
          break;
        case "gxjx":
          // 更新镜像
          break;
        case "gxlj":
          this.connectionsTime = '' + new Date()
          break;
        case "scjx":
          // 生成镜像
          this.$Modal.confirm({
            title: '提示',
            content: `<p>是否生成云硬盘 ${row.name} 的镜像?</p>`,
            onOk: () => {
              cloudDiskExpansion({
                id: row.id,
                data: row.name+'-云硬盘',
                action: 'upload',
                name: row.name+'-云硬盘',
              })
              .then((callback) => {
                if(callback.data.msg == "ok"){
                  this.returnOK('生成云硬盘镜像操作完成')
                }else {
                  this.returnError(callback.data.msg)
                }
              }).catch((error) => {
                this.returnError('生成云硬盘镜像失败')
              })
            },
          });
          break;
        case "sc":
          this.deletClick([row]);
          break;
      }
    },
    // 任务转换
    convertTask(item){
      if(item) {
        this.taskList = true
      }
    },
    // 表格可启动项
    handleBeforeChange(row){
      return new Promise((resolve) => {
        this.$Modal.confirm({
          title: '可启动编辑',
          content: `<p>是否修改云硬盘 <span style="font-weight: 800;">${row.name}</span> 可启动项为 <span style="color:${row.bootable=='true'?'red':'green'};">${row.bootable=='true'?'禁用':'启用'}</span></p>`,
          onOk: () => {
            cloudDiskFiring({id:row.id,status:row.bootable=='true'?false:true})
            .then(callback=>{
              if(callback.data.msg=="ok") {
                resolve();
                this.returnOK("可启动操作完成")
              }else {
                this.returnError("可启动操作失败")
              }
            })
          }
        });
      });
    },
    // 新建云硬盘点击事件
    newClick() {
      this.newTime = '' + new Date()
    },
    // 删除云硬盘
    deletClick(data) {
      if(data == 0) {
        this.$Message.warning({
          background: true,
          closable: true,
          duration: 5,
          content: '未选择表格数据'
        });
      }else {
        this.tableDelet = data
        this.deleteTime = '' + new Date()
      }
    },

    // 字节+单位转换
    byteUnitConversion(type,size) {
      const units = ['B','KB','MB','GB', 'TB', 'PB'];
      let unitIndex = 0;
      while (size >= 1024 && unitIndex < units.length - 1) {
          size /= 1024;
          unitIndex++;
      }
      if(type=="byte") {
        return Math.floor(size * 100) / 100 
      }else if(type=="unit") {
        return units[unitIndex] 
      }else {
        return Math.floor(size * 100) / 100 + ' ' + units[unitIndex];
      }
    },
    // 云硬盘表数据
    cloudTab(init) {
      this.spinShow=true;
      init=="init"?this.tableTotal = 0:""
      init=="init"?this.tablePageForm.page = 1:""
      cloudDiskQuery(this.tablePageForm)
      .then(callback=>{
        this.spinShow=false;
        this.tableTotal = callback.data.total
        this.tableSelec = new Array()
        this.cloudData = callback.data.data.map(item=>{
          if(item.status=="in-use") {
            item["_disabled"] = item.status=="in-use"
          }
          return item
        })
      })
      .catch((error) => {
        this.spinShow=false;
      });
    },
    // 云硬盘表选中数据
    tableChange(item){
      this.tableSelec = item
    },
    // 当前分页
    onPageChange(item) {
      this.tablePageForm.page = item;
      this.cloudTab();
    },
    // 每页条数
    onPageSizeChange(item) {
      this.$store.state.power.pagecount = item
      this.tablePageForm.pagecount = this.$store.state.power.pagecount
      this.tablePageForm.page = 1
      this.tablePageForm.search_str = ""
      this.cloudTab("init");
    },
    // 云硬盘列表 列排序
    sortColumn(column, key, order) {
      if (column.key !== "normal") {
        this.tablePageForm.order_by = column.key;
        this.tablePageForm.order_type = column.order;
        this.tablePageForm.page = 1
        this.tablePageForm.search_str = ""
        this.cloudTab("init");
      }
    },
    // 搜索云硬盘列表
    tableSearchInput(){
      this.$store.state.power.cloudDriveName = this.tablePageForm.search_str
      this.tablePageForm.page = 1
      this.cloudTab("init");
    },
    // 表格任务条封装
    renwu(type,row) {
      let cls = ''
      let text = '使用中'
      switch (row.status) {
        case "in-use":
          text = '使用中'
        break;
        case "available":
          text = '空闲中'
        break;
        case "creating":
          text = '创建中'
        break;
        case "attaching":
          text = '连接中'
        break;
        case "detaching":
          text = '分离中'
        break;
        case "deleting":
          text = ''
          cls = 'table_schedule'
          this.taskList = true
        break;
        case "error":
          text = '错误'
        break;
        case "error_deleting":
          text = '删除错误'
        break;
        case "downloading":
          text = '下载镜像中'
        break;
        case "extending":
          text = '扩展中'
        break;
        case "reserved":
          text = '保留'
        break;
      }
      if(type == 'class') {
        return cls
      }else {
        return text
      }
    },
    
    // 子组件返回
    returnError(data){
      this.$Message.error({
        background: true,
        closable: true,
        duration: 5,
        content: data,
      });
    },
    returnOK(data,type) {
      setTimeout(() => {
        this.cloudTab(type)
      }, 1000);
      this.$Message.success({
        background: true,
        closable: true,
        duration: 5,
        content: data,
      });
    },
    // 操作列
    operationShow(){
      let list = false
      if(
        this.powerAcitons.yunyingpanbianji ||
        this.powerAcitons.yunyingpankuozhan ||
        this.powerAcitons.yunyingpanchuangjianxuniji ||
        this.powerAcitons.chuangjiankuaizhao ||
        this.powerAcitons.yunyingpanshanchu
      ){
        list = true
      }
      return list
    },
    // 操作权限获取
    actionQuery(){
      powerCodeQuery({
        module_code:[
          'yunyingpanliebiao',
          'yunyingpansousuo',
          'yunyingpanxinjian',
          'yunyingpanshanchu',
          'yunyingpanbianji',
          'yunyingpankuozhan',
          'chuangjiankuaizhao',
          'yunyingpanchuangjianxuniji',
        ]
      }).then(callback=>{
        this.powerAcitons = callback.data.data
        this.cloumnManage()
        if( this.powerAcitons.yunyingpanliebiao ){
          this.cloudTab("init")
          this.tablePageForm.search_str = this.$store.state.power.cloudDriveName
        }
      })
    },
  },
  beforeDestroy() {
    this.$store.state.power.cloudDriveName = ""
    clearInterval(this.cloudDiskTask)
  },
};
</script>
