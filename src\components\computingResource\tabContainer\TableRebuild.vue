<template>
  <div>
    <Modal v-model="model" width="600" :mask-closable="false">
      <template #header
        ><p>
          <span style="color: green">{{ row.name }}</span>重建容器
        </p></template
      >
      <Form
        :model="formItem"
        ref="formItem"
        :rules="ruleValidate"
        :label-width="120"
      >
        <FormItem label="镜像" prop="image">
          <Input
            v-model="formItem.image"
            placeholder="请输入镜像"
          ></Input>
        </FormItem>
        <!-- <FormItem label="Image Driver">
          <Select v-model="formItem.image_driver">
            <Option value="docker">Docker Hub</Option>
            <Option value="glance">Glance</Option>
          </Select>
        </FormItem> -->
      </Form>
      <div slot="footer">
        <Button type="text" @click="model = false">取消</Button>
        <Button type="primary" @click="modalOK" :disabled="disabled"
          >确认</Button
        >
      </div>
    </Modal>
  </div>
</template>
<script>
import {containerOperate} from '@/api/container'; // 容器表 操作
export default {
  props: {
    row: Object,
    rebuildTime: String,
  },
  watch: {
    rebuildTime(news) {
      this.model = true;
      this.formItem.id = this.row.id;
      this.formItem.name = this.row.name;
      this.formItem.image = this.row.image;
    },
  },
  data() {
    const propTime = (rule, value, callback) => {
      if (value > 0) {
        callback();
      } else {
        callback(new Error("请输入大于0的数字"));
      }
    };
    return {
      model: false,
      disabled: false,
      formItem: {
        id: "",
        name: "",
        image: "",
        action: 'rebuild',
        image_driver: "docker",
      },
      ruleValidate: {
        image: [{ required: true, message: "必填项", trigger: "change" }],
        image_driver: [{ required: true, message: "必填项", trigger: "change" }],
      },
    };
  },
  methods: {
    modalOK() {
      this.$refs.formItem.validate((valid) => {
        if (valid) {
          this.disabled = true;
          containerOperate(this.formItem)
            .then((callback) => {
              this.model = false;
              this.$emit("return-ok", {
                msg: "重建容器操作已完成",
                type: "ok",
              });
            })
            .catch((error) => {
              this.disabled = false;
              this.$emit("return-ok", {
                msg: "重建容器操作失败",
                type: "error",
              });
            });
        }
      });
    },
  },
};
</script>