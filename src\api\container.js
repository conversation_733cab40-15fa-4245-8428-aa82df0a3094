// 计算资源/容器-容器镜像
import axios from "axios";
import * as basic_proxy from "@/api/config";
// 容器 查询
export async function containerQuery(params) {
  return await axios.post(basic_proxy.theapi + "/v1/containers", params);
}
// 容器 新建
export async function containerNew(params) {
  return await axios.post(basic_proxy.theapi + "/v1/containers/create", params);
}
// 容器 编辑
export async function containerEdit(params) {
  return await axios.post(basic_proxy.theapi + "/v1/containers/update", params);
}
// 容器 启动安全组
export async function containerGroupStart(params) {
  return await axios.post(basic_proxy.theapi + "/v1/containers/update", params);
}
// 容器 停止安全组
export async function containerGroupStop(params) {
  return await axios.post(basic_proxy.theapi + "/v1/containers/update", params);
}
// 容器 操作
export async function containerOperate(params) {
  return await axios.post(basic_proxy.theapi + "/v1/containers/action", params);
}
// 容器 执行命令
export async function containerInstruction(params) {
  return await axios.post(
    basic_proxy.theapi + "/v1/containers/action/execute",
    params
  );
}
// 容器 死亡信号
export async function containerKill(params) {
  return await axios.post(
    basic_proxy.theapi + "/v1/containers/action/kill",
    params
  );
}
// 容器 删除
export async function containerDeleted(params) {
  return await axios.post(basic_proxy.theapi + "/v1/containers/delete", params);
}
// 容器 停止并删除
export async function containerStopDeleted(params) {
  return await axios.post(basic_proxy.theapi + "/v1/containers/delete", params);
}
// 容器 强制删除
export async function containerForceDeleted(params) {
  return await axios.post(basic_proxy.theapi + "/v1/containers/delete", params);
}


// 容器镜像 查询
export async function containerImageQuery(params) {
  return await axios.post(basic_proxy.theapi + "/v1/containers/images", params);
}
// 容器镜像 拉取
export async function containerImagePull(params) {
  return await axios.post(
    basic_proxy.theapi + "/v1/containers/images/pull",
    params
  );
}
// 获取主机
export async function hostQuery() {
  return await axios.get(basic_proxy.theapi + "/v1/containers/hosts");
}
// 容器镜像 删除
export async function containerImageDeleted(params) {
  return await axios.post(
    basic_proxy.theapi + "/v1/containers/images/delete",
    params
  );
}

// 上传容器镜像
export const containerImageUpload = (data) => {
  return axios({
    method: 'post',
    url: basic_proxy.theapi + "/v1/images",
    data: data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
};