<style lang="less">
@import "../networkResource.less";
</style>
<template>
  <div class="network_area">
    <div class="table-button-area">
      <div>
        <Button class="plus_btn" @click="newlyModal" v-show="powerAcitons.xinijianwangluo"><span class="icon iconfont icon-plus-circle"></span> 新建网络</Button>
        <Button class="close_btn" @click="deleteClick(tableSelec)" v-show="powerAcitons.shanchuwangluo"><span class="icon iconfont icon-close-circle"></span> 删除网络</Button>
        <Button class="plus_btn" @click="detectionClick" v-show="powerAcitons.wangluotance"></span><Icon type="md-ionic" /> 网络探测</Button>
        
      </div>
      <div>
        <!-- <Input v-model="tablePageForm.search_str" search enter-button placeholder="请输入名称" style="width: 300px" @on-search="tableVMsearchInput" /> -->
      </div>
    </div>
    <div class="table_currency_area" v-show="powerAcitons.wangluoliebiao">
      <Table
        :columns="netColumn"
        :data="netData"
        @on-selection-change="tableChange"
      >
        <template v-slot:cidr="{ row }">
          <div v-for="item in row.cidr">
            {{ item }}
          </div>
        </template>
        <template v-slot:vlanid="{ row }">
          {{ row.vlanid ? row.vlanid : "-" }}
        </template>
        <template v-slot:operation="{ row }">
          <Dropdown @on-click="dropdownClick($event, row)">
            <Button>配置 ▼</Button>
            <template #list>
              <DropdownMenu>
                <DropdownItem name="xgmc" v-show="powerAcitons.bianjiwangluo">编辑网络</DropdownItem>
                <DropdownItem name="tjzw" v-show="powerAcitons.tianjiaziwang">添加子网</DropdownItem>
                <DropdownItem name="bjzw" v-show="powerAcitons.bianjiziwang" :disabled="row.cidr.length==0">编辑子网</DropdownItem>
                <DropdownItem name="sczw" v-show="powerAcitons.shanchuziwang" :disabled="row.cidr.length==0">删除子网</DropdownItem>
                <DropdownItem name="sc" v-show="powerAcitons.shanchuwangluo" style="color: red" divided
                  >删除</DropdownItem
                >
              </DropdownMenu>
            </template>
          </Dropdown>
        </template>
      </Table>
      <!-- <div class="pages" v-if="this.netData.length > 0">
        <Pagination  :total="tableTotal" :page-size="tablePageForm.pagecount"  @page-change="onPageChange" @page-size-change="onPageSizeChange"/>
      </div> -->
    </div>
    <!-- 新建网络弹框 -->
    <NetworkNew
      :newTime="newTime"
      :vlanData="vlanData"
      :nameAll="nameAll"
      @return-ok="returnOK"
      @return-error="returnError"
    ></NetworkNew>
    <!-- 网络探测 -->
    <NetworkDetection
      :detectionTime='detectionTime'
      @return-error="returnError"
    ></NetworkDetection>
    <!-- 修改名称 -->
    <NetworkEdit
      :tableRow="tableRow"
      :nameTime="nameTime"
      :nameAll="nameAll"
      @return-ok="returnOK"
      @return-error="returnError"
    ></NetworkEdit>
    <!-- 增加子网 -->
    <SubnetIncrease
      :tableRow="tableRow"
      :nameAll="nameAll"
      :addsubnetTime="addsubnetTime"
      @return-ok="returnOK"
      @return-error="returnError"
    ></SubnetIncrease>
    <!-- 更改子网 -->
    <SubnetChange
      :tableRow="tableRow"
      :subnetTime="subnetTime"
      @return-ok="returnOK"
      @return-error="returnError"
    ></SubnetChange>
    <!-- 删除子网 -->
    <SubnetRemove
      :tableRow="tableRow"
      :removeTime="removeTime"
      @return-ok="returnOK"
      @return-error="returnError"
    ></SubnetRemove>
    <!-- 删除网络 -->
    <NetworkDelete
      :tableDelete="tableDelete"
      :deleteTime="deleteTime"
      @return-ok="returnOK"
      @return-error="returnError"
    ></NetworkDelete>
  </div>
</template>
<script>
import { powerCodeQuery } from '@/api/other'; // 权限可用性 查询

import { networkTableQuery} from "@/api/network";
import Pagination from "@/components/public/Pagination.vue";
import NetworkNew from "./NetworkNew.vue"; // 网络 新建
import NetworkDetection from "./NetworkDetection.vue"; // 网络 新建
import NetworkEdit from "./NetworkEdit.vue"; // 网络 修改名称
import NetworkDelete from "./NetworkDelete.vue"; // 网络 删除
import SubnetIncrease from "./SubnetIncrease.vue"; // 子网 增加
import SubnetChange from "./SubnetChange.vue"; // 子网 更改 
import SubnetRemove from "./SubnetRemove.vue"; // 子网 去除
export default {
  components: {
    Pagination,
    NetworkNew,
    NetworkDetection,
    NetworkEdit,
    SubnetIncrease,
    SubnetChange,
    SubnetRemove,
    NetworkDelete,
  },
  props: {
    tabSelected: String,
  },
  data() {
    return {
      tableTotal: 0,
      tablePageForm: {
        page: 1, // 当前页
        pagecount: this.$store.state.power.pagecount, // 每页条
        search_str: "", // 收索
        order_type: "desc", // 排序规则
        order_by: "", // 排序列
      },
      netColumn: [],
      netData: [],
      tableSelec: [],
      routesColumns: [
        { title: "主机路由", key: "destination", align: "center" },
        { title: "下一跳", key: "nexthop", align: "center" },
      ],
      newTime: '', // 新建网络
      detectionTime: '', // 网络探测
      vlanData: [], // vlan集合
      tableRow: {}, // 表格行数据
      nameAll: [], // 网络名称集合
      nameTime: '', // 修改名称
      addsubnetTime: '', // 添加子网
      subnetTime: '', // 编辑子网
      removeTime: '', // 删除子网
      tableDelete: [],
      deleteTime: '', // 删除网络

      powerAcitons: {}, // 操作权限数据

    };
  },
  watch: {
    tabSelected(value) {
      if(value == "网络") {
        this.actionQuery()
      }
    },
  },
  mounted() {
    if(this.$store.state.power.networkResourceTab == '网络') {
      this.actionQuery()
    }
  },
  methods: {
    cloumnManage(){
      this.netColumn = [
        { type: "selection", width: 30, align: "center", sortable: true },
        { type: "expand", width: 50,
          render: (h, params) => {
            let listRow = params.row;
            return h(
              "div",
              {
                style: {
                  width: "100%",
                  display: "flex",
                  justifyContent: "flex-start",
                  flexWrap: "wrap",
                },
              },
              [
                listRow.subnets
                  ? listRow.subnets.map((em, ind) => {
                      return h(
                        "div",
                        {
                          style: {
                            width: "31%",
                            padding: "10px",
                            border: "1px solid #ccc",
                            marginRight: "1%",
                          },
                        },
                        [
                          h("h5", [
                            h(
                              "span",
                              {
                                style: {
                                  width: "200px",
                                  paddingLeft: "100px",
                                  display: "inline-block",
                                },
                              },
                              "网关："
                            ),
                            h(
                              "span",
                              { style: { display: "inline-block" } },
                              listRow.gateway_ip[ind]
                            ),
                          ]),
                          h("h5", [
                            h(
                              "span",
                              {
                                style: {
                                  width: "200px",
                                  paddingLeft: "100px",
                                  display: "inline-block",
                                },
                              },
                              "起始IP："
                            ),
                            h(
                              "span",
                              { style: { display: "inline-block" } },
                              listRow.allocation_pools[ind].start
                            ),
                          ]),
                          h("h5", [
                            h(
                              "span",
                              {
                                style: {
                                  width: "200px",
                                  paddingLeft: "100px",
                                  display: "inline-block",
                                },
                              },
                              "结束IP："
                            ),
                            h(
                              "span",
                              { style: { display: "inline-block" } },
                              listRow.allocation_pools[ind].end
                            ),
                          ]),
                          h("h5", [
                            h(
                              "span",
                              {
                                style: {
                                  width: "200px",
                                  paddingLeft: "100px",
                                  display: "inline-block",
                                },
                              },
                              "DNS："
                            ),
                            h(
                              "span",
                              { style: { display: "inline-block" } },
                              listRow.dns_nameservers[ind].length !== 0
                                ? listRow.dns_nameservers[ind].toString()
                                : "未设置DNS"
                            ),
                          ]),
                          // h("div",[
                          //   h("h3",[
                          //     h("span",{style: { width: "50%",textAlign: 'center',background:' #ccc', display: "inline-block"}},'主机路由'),
                          //     h("span",{style: { width: "50%",textAlign: 'center',background:' #ccc', display: "inline-block"}},'下一跳'),
                          //   ]),
                          //   listRow.host_routes[ind].map((rote,index)=>{
                          //     if(rote.length!==0) {
                          //       return h("h5",[
                          //         h("span",{style: { width: "200px",textAlign: 'center', display: "inline-block"}},rote.destination),
                          //         h("span",{style: { width: "200px",textAlign: 'center',display: "inline-block"}},rote.nexthop),
                          //       ])
                          //     }else {
                          //       return h("h5",[
                          //         h("span",{style: { width: "200px",textAlign: 'center', display: "inline-block"}},'未设置'),
                          //         h("span",{style: { width: "200px",textAlign: 'center',display: "inline-block"}},'未设置'),
                          //       ])
                          //     }
                          //   })
                          // ]),
                          h("Table", {
                            props: {
                              columns: this.routesColumns,
                              data: listRow.host_routes[ind],
                              border: true,
                            },
                          }),
                        ]
                      );
                    })
                  : "",
              ]
            );
          },
        },
        { title: "名称", key: "name", tooltip: true },
        { title: "子网", key: "cidr", align: "center", slot: "cidr" },
        // { title: "共享", key: "shared", align: "center" },
        // { title: "外部", key: "shared",align: "center" },
        { title: "状态", key: "status", align: "center" },
        { title: "网络类型", key: "network_type", align: "center" },
        { title: "VLAN ID", key: "vlanid", align: "center", slot: "vlanid" },
        ...(this.operationShow()?[{ title: "操作", key: "operation",width:120,slot: "operation" }]:[]),
      ]
    },
    // 表格操作
    dropdownClick(event, row) {
      switch (event) {
        case "xgmc":
          this.tableRow = row;
          this.nameTime = "" + new Date();
          break;
        case "tjzw":
          this.tableRow = row;
          this.addsubnetTime = "" + new Date();
          break;
        case "bjzw":
          this.tableRow = row;
          this.subnetTime = "" + new Date();
          break;
        case "sczw":
          this.tableRow = row;
          this.removeTime = "" + new Date();
          break;
        case "sc":
          this.deleteClick([row]);
          break;
      }
    },
    // 获取网络表格数据
    networlTab() {
      networkTableQuery()
        .then((callback) => {
          let vlan = new Array();
          let name = new Array();
          callback.data.forEach((em) => {
            vlan.push(em.vlanid);
            name.push(em.name);
          });
          this.vlanData = vlan;
          this.nameAll = name;
          this.netData = callback.data;
          this.tableSelec = new Array();
        })
        .catch((error) => {});
    },
    // 网络表格选中数据
    tableChange(item) {
      this.tableSelec = item;
    },
    // 当前分页
    onPageChange(item) {
      this.tablePageForm.page = item;
      this.networlTab();
    },
    // 每页条数
    onPageSizeChange(item) {
      this.$store.state.power.pagecount = item;
      this.tablePageForm.pagecount = this.$store.state.power.pagecount;
      this.tablePageForm.page = 1;
      this.tableTotal = 0;
      this.tablePageForm.search_str = "";
      this.networlTab();
    },
    // 虚拟机列表 列排序
    sortColumn(column, key, order) {
      if (column.key !== "normal") {
        this.tablePageForm.order_by = column.key;
        this.tablePageForm.order_type = column.order;
        this.tablePageForm.page = 1;
        this.tableTotal = 0;
        this.tablePageForm.search_str = "";
        this.networlTab();
      }
    },
    // 搜索虚拟机列表
    tableVMsearchInput() {
      this.tablePageForm.page = 1;
      this.tableTotal = 0;
      this.networlTab();
    },
    // 新建网络按钮
    newlyModal() {
      this.newTime = "" + new Date();
    },
    // 网络探测
    detectionClick(){
      this.detectionTime = "" + new Date();
    },
    // 删除网络
    deleteClick(data) {
      if (data == 0) {
        this.$Message.warning({
          background: true,
          closable: true,
          duration: 5,
          content: "未选择表格数据",
        });
      }else {
        this.tableDelete = data
        this.deleteTime = "" + new Date();
      }
    },
    // 子组件返回错误
    returnError(data) {
      this.$Message.error({
        background: true,
        closable: true,
        duration: 5,
        content: data,
      });
    },
    // 子组件返回成功
    returnOK(data) {
      this.networlTab();
      this.$Message.success({
        background: true,
        closable: true,
        duration: 5,
        content: data,
      });
    },
    // 操作列
    operationShow(){
      let list = false
      if(
        this.powerAcitons.bianjiwangluo ||
        this.powerAcitons.tianjiaziwang ||
        this.powerAcitons.bianjiziwang ||
        this.powerAcitons.shanchuziwang ||
        this.powerAcitons.shanchuwangluo
      ){
        list = true
      }
      return list
    },
    // 操作权限获取
    actionQuery(item){
      powerCodeQuery({
        module_code:[
          'wangluoliebiao',
          'xinijianwangluo',
          'shanchuwangluo',
          'wangluotance',
          'bianjiwangluo',
          'tianjiaziwang',
          'bianjiziwang',
          'shanchuziwang',
        ]
      }).then(callback=>{
        this.powerAcitons = callback.data.data
        this.cloumnManage()
        this.powerAcitons.wangluoliebiao?this.networlTab():null
      })
    },
  },
};
</script>
