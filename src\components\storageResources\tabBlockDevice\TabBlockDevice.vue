<template>
  <div style="overflow: auto; height: 100%; padding: 0 0 150px 0">
    <div class="table-button-area">
      <div>
        <Button class="plus_btn" @click="increaseClick"
          ><span class="icon iconfont icon-plus-circle"></span> 添加块设备</Button
        >
        <Button class="plus_btn" @click="blokGET"
          ><span class="icon iconfont icon-plus-circle"></span>刷新</Button
        >
      </div>
      <div>
        <!-- <Input v-model="tablePageForm.search_str" search enter-button placeholder="请输入名称" style="width: 300px" @on-search="tableSearchInput" /> -->
      </div>
    </div>
    <div style="position: relative">
      <Spin fix v-if="spinShow" size="large" style="color: #ef853a">
        <Icon type="ios-loading" size="50" class="demo-spin-icon-load"></Icon>
        <div style="font-size:16px; padding: 20px">Loading...</div>
      </Spin>
      <Table :columns="blockColumns" :data="blockData"></Table>
      <div class="pages" v-if="this.blockData.length > 0">
        <Pagination
          :total="tableTotal"
          :page-size="tablePageForm.pagecount"
          @page-change="onPageChange"
          @page-size-change="onPageSizeChange"
        />
      </div>
    </div>
    <!-- 添加块设备 -->
    <BlockAdd :addModal="addModal"></BlockAdd>
  </div>
</template>
<script>
import Pagination from "@/components/public/Pagination.vue";
import BlockAdd from "./BlockAdd.vue";
export default {
  components: {
    Pagination,
    BlockAdd,
  },
  props: {
    tabName: String,
  },
  watch: {
    tabName(value) {
      value == "kuaishebie" ? this.blokGET() : "";
    },
  },
  updated() {
    this.tablePageForm.pagecount = this.$store.state.power.pagecount;
  },
  data() {
    const propPoolname = (rule, value, callback) => {
      let list = /^[\u4e00-\u9fa5_a-zA-Z0-9@_.-]{2,32}$/;
      if (list.test(value)) {
        callback();
      } else {
        callback(new Error("请输入2-32个中文或字符（特殊字符可用@_.-）"));
      }
    };
    return {
      spinShow: false,
      blockColumns:[
        { title: "块设备名称",key: "name" },
				{ title: "卷池名称",key: "pool_name",align: "center" },
				{ title: "容量",key: "size",align: "center",
          render: (h, params) => {
            let sizes = this.byteUnitConversion("",params.row.size)
            return h("span",sizes)
          }
        },
        { title: "操作",key: "action",width:200,
          render: (h, params) => {
            return h("div", [
              h("Button", {
                props:{
                  icon:"ios-create",
                  class:"close_btn"
                },
                on: {
                  click: () => {
                    this.addModal="编辑块设备/"+new Date()
                  },
                },
              },"编辑"),
              h("Button", {
                style:{color:'red',marginLeft:'10px'},
                props:{
                  icon:"ios-trash",
                  class:"plus_btn"
                },
                on: {
                  click: () => {
                    this.$Modal.confirm({
                      title: "删除块设备",
                      content:
                        '<p>是否删除块设备名称为<span style="font-weight: 600;color:red;word-wrap: break-word;">' +
                        params.row.name +
                        "</span>设备？</p>",
                      onOk: () => {
                        this.$axios.delete("/thecephapi/api/block/image/"+params.row.pool_name+"%2F"+params.row.name+"/move_trash").then((callback) => {
                          this.$Message.success({
                            background: true,
                            closable: true,
                            duration: 5,
                            content: "删除块设备操作完成",
                          });
                          this.blokGET()
                        }).catch((error) => {
                          this.$Message.error({
                              background: true,
                              closable: true,
                              duration: 5,
                              content: "删除块设备操作失败",
                            });
                        });
                      },
                      onCancel: () => {},
                    });
                  },
                },
              },"删除"),
            ])
          }
        },
      ],
      blockData:[{name:"aaaaa"}],
      blockAllData:[],
      // 分页
      tableTotal:0,
      tablePageForm: {
        page: 1, // 当前页
        pagecount: this.$store.state.power.pagecount, // 每页条
        search_str: "", // 收索
        order_type: "asc", // 排序规则
        order_by: "", // 排序列
      },
      addModal:"添加块设备",
    };
  },
  methods:{
    // 获取块设备表数据
    blokGET(){
      this.spinShow = true;
      this.$axios.get("/thecephapi/api/block/image").then((callback) => {
        this.spinShow = false;
        this.tableTotal = callback.data.length
        this.blockAllData = callback.data
        if(this.blockAllData.length>0){
          this.blockData = this.blockAllData.slice(
            this.tablePageForm.page - 1,
            this.tablePageForm.page * this.$store.state.power.pagecount
          );
        }
      })
    },
    
    // 添加块设备
    increaseClick(){
      this.addModal="添加块设备/"+new Date()
    },
    // 字节+单位转换
    byteUnitConversion(type,size) {
      const units = ['B','KB','MB','GB', 'TB', 'PB'];
      let unitIndex = 0;
      while (size >= 1024 && unitIndex < units.length - 1) {
          size /= 1024;
          unitIndex++;
      }
      if(type=="byte") {
        return Math.floor(size * 100) / 100 
      }else if(type=="unit") {
        return units[unitIndex] 
      }else {
        return Math.floor(size * 100) / 100 + ' ' + units[unitIndex];
      }
    },
  },
};
</script>