.security_group_area {
  position: relative;
  display: flex;
  height: calc(100% - 70px);
  border-radius: 10px;
  background-color: #FFF;
  padding: 15px 5px;
  overflow: auto;

  p {
    font-weight: 600;
    border-left: 4px solid #fb6129;
    margin: 0 0 5px 5px;
    padding-left: 10px;
    color: #333;
    font-size: 14px;
  }

  .security_group_group {
    display: flex;
    flex-direction: row;
    justify-content: space-evenly;
    align-items: center;
    height: 40px;
    border-top: 1px solid #ccc;

    .ivu-icon {
      font-size: 20px;
    }
  }

  .ivu-menu-light {
    width: 200px !important;
    min-width: 200px !important;
    max-width: 200px !important;

    .ivu-menu-vertical {
      .ivu-menu-item-active:not(.ivu-menu-submenu) {
        color: #fff !important;
        background: #fb6129 !important;
      }

      .ivu-menu-item:hover,
      .ivu-menu-submenu-title:hover {
        color: #333;
        background: #ffe9e1;
      }

      .ivu-menu-item-active:not(.ivu-menu-submenu):after {
        background: none !important;
      }
    }
  }

  .security_group_menu {
    width: 160px;
    display: inline-block;
  }

  // 管理规则
  .rule_table_area {
    width: calc(100% - 200px);
    height: 100%;
    padding: 0 10px;

    .ivu-progress-show-info .ivu-progress-outer {
      padding-right: 0px !important;
      margin-right: 0px !important;
    }

    .ivu-progress-text {
      position: absolute;
      line-height: 20px;
      left: 32%;
      top: 0;
      color: #333;
    }

    .ivu-table-cell {
      padding: 0 5px !important;
    }

    .ivu-progress-bg {
      height: 20px !important;
    }

    .rule_table_line {
      display: block;
      border-top: 1px solid #ccc;
      padding-bottom: 6px;
      margin-left: -10px;
    }
  }
}