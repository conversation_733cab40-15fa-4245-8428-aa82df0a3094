<style scoped>
@import "./resouceAllocation.css";
</style>
<style scoped>

</style>
<template>
  <div class="vm_allocation_background">
    <Spin fix v-if="spinShowVM" size="large" style="color:#ef853a">
      <Icon type="ios-loading" size=50 class="demo-spin-icon-load"></Icon>
      <div style="font-size:16px;padding:20px">Loading...</div>
    </Spin>
    <div class="vm_allocation_area">
      <span class="vm_allocation_title">虚拟机分配</span>
      <div class="vm_allocation_btn">
        <span>
          <Button type="success" @click="allocationClick(tableSelec)">分配虚拟机</Button>
          <Button type="warning" @click="allocationRemove">取消分配</Button>
          <!-- <Button type="warning" @click="applyVM">申请虚拟机</Button> -->
        </span>
        <Input v-model="tablePageForm.search_str" search enter-button placeholder="请输入名称" style="width: 300px" @on-search="searchInput" />
      </div>
      <div class="vm_fenpei_table">
        <Table
          :columns="VMallocationColumn"
          :data="VMallocationData"
          @on-sort-change="sortColumn"
          @on-selection-change="tableChange"
        ></Table>
        <!-- 虚拟机表格 分页  -->
        <div class="pages" v-if="this.VMallocationData.length>0">
          <!-- <Page
            :total="tableTotal"
            show-total
            show-sizer
            :page-size="tablePageForm.pagecount"
            placement="top"
            :page-size-opts="[10, 20, 30]"
            @on-change="onPageChange"
            @on-page-size-change="onPageSizeChange"
          /> -->
          <Pagination  :total="tableTotal" :page-size="tablePageForm.pagecount"  @page-change="onPageChange" @page-size-change="onPageSizeChange"/>
        </div>
      </div>
      <!-- 分配虚拟机 -->
      <Modal v-model="distributionModel" width="800" :mask-closable="false">
        <template #header><p>虚拟机分配</p></template>
        <Form :label-width="120">
          <h4>是否把虚拟机<span style="color:green">{{tableNames.toString()}}</span>分配给<span style="color:green"> {{formItem.userName}} </span>用户？</h4>
          <FormItem label="用户选择" class="JXxinxi">
            <Select v-model="formItem.userID" :label-in-value="true" style="width: 300px" @on-change="onUserSlecte">
              <Option v-for="item in userData" :value="item.value" :key="item.value" >{{ item.label }}</Option>
            </Select>
          </FormItem>
        </Form>
        <div slot="footer">
          <Button type="text" @click="distributionModel = false">取消</Button>
          <Button type="primary" @click="distributionOK" :disabled="disabled">确认</Button>
        </div>
      </Modal>
      <!-- 申请虚拟机 弹框 -->
      <Modal
        v-model="newBuildModal"
        width="800"
        :styles="{ top: '20px' }"
        :mask-closable="false"
      >
        <p slot="header">
          <span>申请虚拟机</span>
        </p>
        <div style="height: 480px; overflow: hidden" class="vm_allocation_menu">
          <Menu theme="light" :active-name="modalName" @on-select="modalMenuItem">
            <Menu-item :name="i" v-for="(item, i) in menniuItemData" :key="i">
              <span :class="item.icon"></span>
              <span>{{ item.name }}</span>
            </Menu-item>
          </Menu>
          <div class="modalcontent">
            <ul id="lunbo">
              <li>
                <Form :model="newVMForm" :rules="ruleXJZ" :label-width="120">
                  <FormItem label="虚拟机名称" prop="name">
                    <Input
                      v-model="newVMForm.name"
                      placeholder="请输入虚拟机名称"
                      style="width: 300px"
                    ></Input>
                  </FormItem>
                  <FormItem label="创建方式">
                    <RadioGroup v-model="newVMForm.radio">
                      <Radio label="从镜像创建">镜像创建</Radio>
                      <Radio label="从ISO创建" style="margin-right: 30px"
                        >ISO创建</Radio
                      >
                    </RadioGroup>
                  </FormItem>
                  <FormItem label="镜像选择" prop="imageTypeId" v-if="this.newVMForm.radio=='从镜像创建'">
                    <Select
                      v-model="newVMForm.imageTypeId"
                      :label-in-value="true"
                      style="width: 300px"
                      @on-change="netIMGchange"
                    >
                      <Option
                        v-for="item in imageOption"
                        :value="item.id"
                        :key="item.id"
                        >{{ item.name }}</Option
                      >
                    </Select>
                  </FormItem>
                  <FormItem label="操作系统类型" prop="operatingSystemName" v-if="this.newVMForm.radio!=='从镜像创建'">
                    <Select
                      v-model="newVMForm.operatingSystemName"
                      :label-in-value="true"
                      style="width: 300px"
                      @on-change="iosIMGchange"
                    >
                      <Option
                        v-for="item in sysType"
                        :value="item.id"
                        :key="item.id"
                        >{{ item.name }}</Option
                      >
                    </Select>
                  </FormItem>
                  <FormItem label="网络名称" prop="networkId">
                    <Select
                      v-model="newVMForm.networkId"
                      :label-in-value="true"
                      style="width: 300px"
                      @on-change="netSlectChange"
                    >
                     <Option
                      v-for="item in netOption"
                      :value="item.id"
                      :key="item.id"
                      >{{ item.name }}</Option
                      >
                    </Select>
                  </FormItem>
                <FormItem label="预设IP">
                  <Checkbox v-model="newVMForm.presetsIP"></Checkbox>
                </FormItem>
                <FormItem label="IP地址" prop="ipPash"  v-if="this.newVMForm.presetsIP">
                  <Input
                    v-model="newVMForm.ipPash"
                    placeholder="请输入IP地址"
                    style="width: 300px"
                  ></Input>
                </FormItem>
                </Form>
              </li>
              <li>
                <Table
                  :columns="computingResourceColumn"
                  :data="computingResourceData"
                  height="460"
                  highlight-row
                  @on-current-change="computingResourceSelec"
                ></Table>
              </li>
              <li>
                <div class="cipan" v-if="this.newVMForm.radio=='从镜像创建'">
                  <Form :model="newVMForm" :label-width="100">
                    <FormItem label="虚拟机数量" style="padding-top: 20px">
                      <div class="slider_area">
                        <div style="width:390px">
                          <Slider
                            v-model="newVMForm.vmNumber"
                            :min="1"
                            :max="200"
                            :tip-format="formvm"
                          ></Slider>
                        </div>
                        <div style="width:80px">
                          <InputNumber :min="1" :max="200" v-model="newVMForm.vmNumber" :formatter="value => `${value}个`" :parser="value => value.replace('个', '')"></InputNumber>
                        </div>
                      </div>
                    </FormItem>
                    <FormItem label="VCPU核数">
                      <div class="slider_area">
                        <div style="width:390px">
                          <Slider
                            v-model="newVMForm.cpu"
                            :min="1"
                            :max="64"
                            :tip-format="formCpu"
                          ></Slider>
                        </div>
                        <div style="width:80px">
                          <InputNumber :min="1" :max="64" v-model="newVMForm.cpu" :formatter="value => `${value}核`" :parser="value => value.replace('核', '')"></InputNumber>
                        </div>
                      </div>
                    </FormItem>
                    <FormItem label="内存容量">
                      <div class="slider_area">
                        <div style="width:390px">
                          <Slider
                            v-model="newVMForm.memory"
                            :min="1"
                            :max="256"
                            :tip-format="formMemory"
                          ></Slider>
                        </div>
                        <div style="width:80px">
                          <InputNumber :min="1" :max="256" v-model="newVMForm.memory" :formatter="value => `${value}GB`" :parser="value => value.replace('GB', '')"></InputNumber>
                        </div>
                      </div>
                    </FormItem>
                    <FormItem label="硬盘容量">
                      <div class="slider_area">
                        <div style="width:390px">
                          <Slider
                            v-model="newVMForm.disk"
                            :min="1"
                            :max="2048"
                            :tip-format="formDisk"
                          ></Slider>
                        </div>
                        <div style="width:80px">
                          <InputNumber :min="1" :max="2048" v-model="newVMForm.disk" :formatter="value => `${value}GB`" :parser="value => value.replace('GB', '')"></InputNumber>
                        </div>
                      </div>
                    </FormItem>
                  </Form>
                </div>
                <div class="cipan" v-if="this.newVMForm.radio!=='从镜像创建'">
                  <Form :model="newVMForm" :label-width="120">
                    <FormItem label="VCPU核数" style="padding-top: 20px">
                      <div class="slider_area">
                        <div style="width:370px">
                          <Slider
                            v-model="newVMForm.ISOcpu"
                            :min="1"
                            :max="64"
                            :tip-format="formCpu"
                          ></Slider>
                        </div>
                        <div style="width:80px">
                          <InputNumber :min="1" :max="64" v-model="newVMForm.ISOcpu" :formatter="value => `${value}核`" :parser="value => value.replace('核', '')"></InputNumber>
                        </div>
                      </div> 
                    </FormItem>
                    <FormItem label="内存容量">
                      <div class="slider_area">
                        <div style="width:370px">
                          <Slider
                            v-model="newVMForm.ISOmemory"
                            :min="1"
                            :max="256"
                            :tip-format="formMemory"
                          ></Slider>
                        </div>
                        <div style="width:80px">
                          <InputNumber :min="1" :max="256" v-model="newVMForm.ISOmemory" :formatter="value => `${value}GB`" :parser="value => value.replace('GB', '')"></InputNumber>
                        </div>
                      </div>
                    </FormItem>
                    <FormItem label="硬盘容量">
                      <div class="slider_area">
                        <div style="width:370px">
                          <Slider
                            v-model="newVMForm.ISOdisk"
                            :min="1"
                            :max="2048"
                            :tip-format="formDisk"
                          ></Slider>
                        </div>
                        <div style="width:80px">
                          <InputNumber :min="1" :max="2048" v-model="newVMForm.ISOdisk" :formatter="value => `${value}GB`" :parser="value => value.replace('GB', '')"></InputNumber>
                        </div>
                      </div>
                    </FormItem>
                    <!-- <FormItem label="加载ISO镜像" v-if="this.newVMForm.radio!=='从镜像创建'"> -->
                    <FormItem label="加载ISO镜像" prop="imageTypeId">
                      <Select
                        v-model="newVMForm.imageTypeId"
                        placement="top"
                        :label-in-value="true"
                        @on-change="netIMGchange"
                      >
                        <Option
                          v-for="item in imageOptionISO"
                          :value="item.id"
                          :key="item.id"
                          >{{ item.name }}</Option
                        >
                      </Select>
                    </FormItem>
                    <FormItem label="启用驱动">
                      <Checkbox v-model="newVMForm.driveDisabled" @on-change="driveCheck"></Checkbox>
                    </FormItem>
                    <div v-if="newVMForm.driveDisabled" >
                      <FormItem label="驱动选择" prop="driveimageId">
                        <Select
                          v-model="newVMForm.driveimageId"
                          placement="top"
                          :label-in-value="true"
                          @on-change="driveSelect"
                        >
                          <Option
                            v-for="item in imageOptionISO"
                            :value="item.id"
                            :key="item.id"
                            >{{ item.name }}</Option
                          >
                        </Select>
                      </FormItem>
                    </div>
                    <!-- <FormItem label="其他">
                      <Input
                        v-model="newVMForm.description"
                        style="width: 300px"
                      ></Input>
                    </FormItem> -->
                  </Form>
                </div>
              </li>
              <li>
                <div class="xinxigailan" v-if="this.newVMForm.radio=='从镜像创建'">
                  <div style="display: flex">
                    <ul>
                      <li>
                        <span class="imitateTitle">虚拟机名称</span>
                        <div class="imitateInput">{{ newVMForm.name }}</div>
                      </li>
                      <li>
                        <span class="imitateTitle">镜像类型</span>
                        <div class="imitateInput">{{ newVMForm.imageTypeName }}</div>
                      </li>
                      <li>
                        <span class="imitateTitle">创建数量</span>
                        <div class="imitateInput">{{ newVMForm.vmNumber }} 台</div>
                      </li>
                      <li>
                        <span class="imitateTitle">硬盘容量</span>
                        <div class="imitateInput">{{ newVMForm.disk }} GB</div>
                      </li>
                      <li>
                        <span class="imitateTitle">网络</span>
                        <div class="imitateInput">{{ newVMForm.networkName }}</div>
                      </li>
                    </ul>
                    <ul>
                      <li>
                        <span class="imitateTitle">创建方式</span>
                        <div class="imitateInput">{{ newVMForm.radio }}</div>
                      </li>
                      <li>
                        <span class="imitateTitle">集群名称</span>
                        <div class="imitateInput">{{ newVMForm.computingResourceName }}</div>
                      </li>
                      <li>
                        <span class="imitateTitle">处理器数量</span>
                        <div class="imitateInput">{{ newVMForm.cpu }} 核</div>
                      </li>
                      <li>
                        <span class="imitateTitle">内存容量</span>
                        <div class="imitateInput">{{ newVMForm.memory }} GB</div>
                      </li>
                    </ul>
                  </div>
                </div>
                <div class="xinxigailan" v-if="this.newVMForm.radio!=='从镜像创建'">
                  <div style="display: flex">
                    <ul>
                      <li>
                        <span class="imitateTitle">虚拟机名称</span>
                        <div class="imitateInput">{{ newVMForm.name }}</div>
                      </li>
                      <li>
                        <span class="imitateTitle">加载的ISO镜像</span>
                        <div class="imitateInput">{{ newVMForm.imageTypeName }}</div>
                      </li>
                      <li>
                        <span class="imitateTitle">硬盘容量</span>
                        <div class="imitateInput">{{ newVMForm.ISOdisk }} GB</div>
                      </li>
                      <li>
                        <span class="imitateTitle">网络</span>
                        <div class="imitateInput">{{ newVMForm.networkName }}</div>
                      </li>
                      <li v-if="newVMForm.driveDisabled">
                        <span class="imitateTitle">驱动</span>
                        <div class="imitateInput">{{ newVMForm.driveName }}</div>
                      </li>
                    </ul>
                    <ul>
                      <li>
                        <span class="imitateTitle">创建方式</span>
                        <div class="imitateInput">{{ newVMForm.radio }}</div>
                      </li>
                      <li>
                        <span class="imitateTitle">集群名称</span>
                        <div class="imitateInput">{{ newVMForm.computingResourceName }}</div>
                      </li>
                      <li>
                        <span class="imitateTitle">处理器数量</span>
                        <div class="imitateInput">{{ newVMForm.ISOcpu }} 核</div>
                      </li>
                      <li>
                        <span class="imitateTitle">内存容量</span>
                        <div class="imitateInput">{{ newVMForm.ISOmemory }} GB</div>
                      </li>
                      
                      <!-- <li>
                        <span>其他</span>
                        <div class="imitateInput">
                          {{ newVMForm.description }}
                        </div>
                      </li> -->
                    </ul>
                  </div>
                </div>
              </li>
            </ul>
          </div>
        </div>
        <div slot="footer">
          <Button type="text" @click="newBuildModal = false">取消</Button>
          <Button
            type="info"
            class="shangyibu plus_btn"
            @click="shangyibu"
            style="display: none"
            >上一步</Button
          >
          <Button class="xiayibu plus_btn" @click="xiayibu">下一步</Button>
          <Button
            type="info"
            class="quereng plus_btn"
            :disabled="XJloadXNJ"
            @click="newBuildOK"
            style="display: none"
            >确认</Button
          >
        </div>
      </Modal>
    </div>
  </div>
</template>
<script>
import {networkTableQuery} from '@/api/network'; // 获取网络
import {userTableQuery} from '@/api/system'; // 用户查询
import {imageTableQuery} from '@/api/image'; // 镜像 查询
import {physicsTableDetail} from '@/api/physics'; // 物理机表细节
import {
  allocationVM, // 分配虚拟机
  unassignVM, // 取消分配虚拟机
} from '@/api/other';
import Pagination from '@/components/public/Pagination.vue';
export default {
  components: {
    Pagination
  },
  data() {
    const propXJname = (rule, value, callback) => {
      let list = /^[\u4e00-\u9fa5_a-zA-Z0-9@_.-]{2,32}$/;
      if (list.test(value)) {
        callback();
      } else {
        callback(new Error("请输入2-32个中文或字符（特殊字符可用@_.-）"));
      }
    };
    const propipPash = (rule, value, callback) => {
      let listNet = this.newVMForm.networkName.split(".")[2]
      let listIP = value.split(".")[2]
      if (listNet == listIP) {
        callback();
      } else {
        callback(new Error("IP地址与所选网段不匹配，创建时将根据所选网络分配IP"));
      }
    };
    return {
      spinShowVM: true,
			// 表格列 行
      VMallocationColumn: [
        { type: "selection", width: 60 },
        { type: "expand", width: 50,
          render: (h, params) => {
            return h("div",{style:{width:'100%'}},[
              h("span",{style:{width:'45%',display: 'inline-block'}},'ID：'+params.row.id),
              // h("span",'名称：'+params.row.name)
            ]);
          },
        },
        { title: "虚拟机", key: "name", sortable: 'custom' },
        { title: "IP", key: "ip", sortable: 'custom',align: "center" },
        { title: "已分配用户", key: "grant_username", sortable: 'custom',align: "center" },
        { title: "操作", key: "operation",width:200,
          render: (h, params) => {
            return h("div", [
              h("Button",{
								style: {
                  marginRight: '5px'
                },
                props: {
                  type: "primary",
                },
                on: {click: () => {
									this.allocationClick([params.row])
                },},
              },"分配虚拟机"),
              h("Button",{
                props: {
                  type: "info",
                  disabled: params.row.grant_username == "" ? true : false,
                },
                on: {click: () => {
                  this.$Modal.confirm({
										title: "取消分配虚拟机",
										content:
											'<h4>是否将<span style="font-weight: 600;color:red;word-wrap: break-word;">' +
											params.row.name +
											"</span>虚拟机，取消用户的分配权限？</h4>",
										onOk: () => {
											this.$axios.post("/theapi/v1/grant/instances/delete/user",{vmids:[params.row.id]})
											.then((callback) => {
												if(callback.data.msg == "ok"){
													this.resourceAllocationGET()
												}else {
													this.$Message.error({
														background: true,
                            closable: true,
														duration: 5,
														content: "取消分配虚拟机失败",
													});
												}
											})
										},
										onCancel: () => {
											 
										},
									});
                },},
              },"取消分配"),
            ]);
          },
        },
      ],
			VMallocationData: [],
			// 表数据 选择数据 ID 名字
			tableSelec:[],
			tableNames:[],
			tableIDs:[],
			// 分页
      tablePageForm: {
        page: 1, // 当前页
        pagecount: this.$store.state.power.pagecount, // 每页条
        search_str: "", // 收索
        order_type: "asc", // 排序规则
        order_by: "", // 排序列
      },
			// 表格数据总数 
			tableTotal:0,
			// 分配虚拟机
			distributionModel:false,
      disabled:false,
			// 用户下拉数据 
			userData:[],
			// 用户选中数据 id 
			formItem:{
				userName:'',
				userID: 0,
			},
      // 申请虚拟机
      newBuildModal:false,
      // 正则判断用户输入
      ruleXJZ: {
        name: [
          { required: true, message: "必填项", trigger: "blur" },
          { validator: propXJname, trigger: "change" },
        ],
        imageTypeId:[{ required: true, message: "必选项", trigger: "blur" }],
        operatingSystemName:[{ required: true, message: "必选项", trigger: "blur" }],
        ipPash:[
          { required: true, message: "必选项", trigger: "blur" },
          { validator: propipPash, trigger: "change" },
          
        ],
        driveimageId:[{ required: true, message: "必选项", trigger: "blur" }],
        networkId:[{ required: true, message: "必填项", trigger: "blur" }],
      },
      // 申请虚拟机导航 
      menniuItemData: [
        { name: "基本信息", icon: "icon iconfont icon-erji-yingyonggailan" },
        { name: "计算资源", icon: "icon iconfont icon-cpu" },
        { name: "虚拟机配置", icon: "icon iconfont icon-yunzhuji" },
        { name: "信息概览", icon: "icon iconfont icon-zonghegailan" },
      ],
      // 申请虚拟机 弹框 硬件配置项 及信息概览
      newVMForm: {
        // 基本信息-虚拟机名
        name: "",
        // 基本信息-创建方式 
        radio: "从镜像创建",
        // 基本信息-选择镜像名称 
        imageTypeName: "",
        // 基本信息-选择镜像id
        imageTypeId: "",
        // 是否启用驱动
        driveDisabled:false,
        // 驱动选择
        driveimageId:"", 
        driveName:"", 
        presetsIP:false, // 预设IP
        ipPash:"", // IP地址
        // 基本信息-选择操作系统类型名称
        operatingSystemName:"",
        // 计算资源-id
        vailabilityZone: "",
        // 计算资源-name
        computingResourceName: "",
        // 虚拟机配置-虚拟机数量
        vmNumber: 1,
        // 虚拟机配置-处理器
        cpu: 2,
        // 虚拟机配置-内存
        memory: 4,
        // 虚拟机配置-硬盘
        disk: 100,
        // 网络id 
        networkId: "",
        // 网络名称 
        networkName: "",
        // 硬件配置-处理器
        ISOcpu: 2,
        // 硬件配置-内存
        ISOmemory: 4,
        // 硬件配置-硬盘
        ISOdisk: 100,
        // 其他 
        description: "",

        hostip: "",
        storageid: "",
        storagename: "",
        filename: "无",
      },
      // 操作系统类型
      sysType: [
        { name: "Microsoft Windows Server 2019(64位)", id: 2 },
        { name: "Microsoft Windows Server 2016(64位)", id: 3 },
        { name: "Microsoft Windows Server 2012 R2(64位)", id: 4 },
        { name: "Microsoft Windows Server 2012(64位)", id: 1 },
        { name: "Microsoft Windows Server 2008 R2(64位)", id: 5 },
        { name: "Microsoft Windows Server 2008(64位)", id: 6 },
        { name: "Microsoft Windows Server 2008(32位)", id: 7 },
        { name: "Microsoft Windows Server 2003 R2(64位)", id: 8 },
        { name: "Microsoft Windows Server 2003(64位)", id: 9 },
        { name: "Microsoft Windows Server 2003(32位)", id: 10 },
        { name: "Microsoft Windows 10(64位)", id: 11 },
        { name: "Microsoft Windows 10(32位)", id: 12 },
        { name: "Microsoft Windows 8.1(64位)", id: 13 },
        { name: "Microsoft Windows 8.1(32位)", id: 14 },
        { name: "Microsoft Windows 8(64位)", id: 15 },
        { name: "Microsoft Windows 8(32位)", id: 16 },
        { name: "Microsoft Windows 7(64位)", id: 17 },
        { name: "Microsoft Windows 7(32位)", id: 18 },
        { name: "Microsoft Windows XP(64位)", id: 19 },
        { name: "Microsoft Windows XP(32位)", id: 20 },
        { name: "Red Hat Enterprise Linux 8(64位)", id: 21 },
        { name: "Red Hat Enterprise Linux 8(32位)", id: 22 },
        { name: "Red Hat Enterprise Linux 7(64位)", id: 23 },
        { name: "Red Hat Enterprise Linux 7(32位)", id: 24 },
        { name: "Red Hat Enterprise Linux 6(64位)", id: 25 },
        { name: "Red Hat Enterprise Linux 6(32位)", id: 26 },
        { name: "Red Hat Enterprise Linux 5(64位)", id: 27 },
        { name: "Red Hat Enterprise Linux 5(32位)", id: 28 },
        { name: "CentOS 8(64位)", id: 29 },
        { name: "CentOS 8(32位)", id: 30 },
        { name: "CentOS 7(64位)", id: 31 },
        { name: "CentOS 7(32位)", id: 32 },
        { name: "CentOS 6(64位)", id: 33 },
        { name: "CentOS 6(32位)", id: 34 },
        { name: "CentOS 5(64位)", id: 35 },
        { name: "CentOS 5(32位)", id: 36 },
        { name: "Ubuntu Linux(64位)", id: 37 },
        { name: "Ubuntu Linux(32位)", id: 38 },
        { name: "Oracle Linux7(64位)", id: 39 },
        { name: "Oracle Linux7(32位)", id: 40 },
        { name: "Oracle Linux6(64位)", id: 41 },
        { name: "Oracle Linux6(32位)", id: 42 },
        { name: "Oracle Linux5(64位)", id: 43 },
        { name: "Oracle Linux5(32位)", id: 44 },
        { name: "Oracle Linux4(64位)", id: 45 },
        { name: "Oracle Linux4(32位)", id: 46 },
        { name: "银河麒麟服务器版V10(64位)", id: 47 },
        { name: "银河麒麟桌面版V10(64位)", id: 48 },
        { name: "UOS 20 SP1 桌面版(64位)", id: 49 },
        { name: "UOS SP2 桌面版(64位)", id: 50 },
        { name: "Other Linux(64位)", id: 51 },
        { name: "Other Linux(64位)", id: 52 },
      ],
      // 申请虚拟机ISO创建
      netOption: [],
      modalName: 0,
      buzhoushu: 0,
      modalTabs: "",
      computingResourceColumn: [
        { title: "集群", key: "name" },
        { title: "CPU分配比", key: "cpu",align: "center",
          render: (h, params) => {
            return h("Progress",{
              props: {
                strokeColor:params.row.cpu>70?["#f45d3f","#f45d3f"]:["#2ebe76","#2ebe76"],
                percent: parseFloat(
                  params.row.cpu > 100 ? 99 : params.row.cpu
                ),
              },
            },params.row.cpu == null ? 0 : params.row.cpu+"%");
          },
        },
        { title: "内存分配比", key: "ram" ,align: "center",
          render: (h, params) => {
            return h("Progress",{
              props: {
                strokeColor:params.row.ram>70?["#f45d3f","#f45d3f"]:["#2ebe76","#2ebe76"],
                percent: parseFloat(
                  params.row.ram > 100 ? 99 : params.row.ram
                ),
              },
            },params.row.ram == null ? 0 : params.row.ram+"%");
          },
        },
        { title: "虚拟机数量", key: "vms",align: "center",
          render: (h, params) => {
            return h("span",params.row.vms)
          },
        },
      ],
      computingResourceData: [],
      imageOption: [],
      imageOptionISO: [],
      XJloadXNJ:false,
    }
  },
	watch: {
    // 监听虚拟机组选中项
    treevmDatas: {
      handler(news, old) {
        if (news.length > 0 && news[0].pid == -1) {
          this.newVMdisabled = false;
          this.editVMdisabled = false;
          this.deletVMdisabled = true;
        } else if (news.length > 0 && news[0].jibie == 4) {
          this.newVMdisabled = true;
          this.editVMdisabled = false;
          this.deletVMdisabled = false;
        } else if (news.length == 0) {
          this.newVMdisabled = true;
          this.editVMdisabled = true;
          this.deletVMdisabled = true;
        } else {
          this.newVMdisabled = false;
          this.editVMdisabled = false;
          this.deletVMdisabled = false;
        }
      },
    },
    // modalTabs(news, old) {
    //   if (news == 3) {
    //     this.modalTabs = 1;
    //   }
    // },
    buzhoushu(news, old) {
      let lunbo = document.getElementById("lunbo");
      let shangyibu = document.getElementsByClassName("shangyibu")[0];
      let xiayibu = document.getElementsByClassName("xiayibu")[0];
      let quereng = document.getElementsByClassName("quereng")[0];
      if (news == 1) {
        this.modalName = 1;
        shangyibu.style.display = "inline-block";
        quereng.style.display = "none";
        xiayibu.style.display = "inline-block";
        lunbo.style.transform = "translate3d(0px, -475px, 0px)";
        lunbo.style.transition = "transform .3s ease-in-out";
      } else if (news == 2) {
        this.modalName = 2;
        shangyibu.style.display = "inline-block";
        quereng.style.display = "none";
        xiayibu.style.display = "inline-block";
        lunbo.style.transform = "translate3d(0px, -950px, 0px)";
        lunbo.style.transition = "transform .3s ease-in-out";
      } else if (news == 3) {
        this.modalName = 3;
        shangyibu.style.display = "inline-block";
        quereng.style.display = "inline-block";
        xiayibu.style.display = "none";
        lunbo.style.transform = "translate3d(0px, -1425px, 0px)";
        lunbo.style.transition = "transform .3s ease-in-out";
      } else if (news == 0) {
        this.modalName = 0;
        shangyibu.style.display = "none";
        quereng.style.display = "none";
        xiayibu.style.display = "inline-block";
        lunbo.style.transform = "translate3d(0px, 0px, 0px)";
        lunbo.style.transition = "transform .3s ease-in-out";
      }
    },
    radio(news, old) {
      if (news == "从镜像创建") {
        this.menniuItemData = [
          { name: "基本信息", icon: "icon iconfont icon-erji-yingyonggailan" },
          { name: "计算资源", icon: "icon iconfont icon-cpu" },
          { name: "虚拟机配置", icon: "icon iconfont icon-yunzhuji" },
          { name: "信息概览", icon: "icon iconfont icon-zonghegailan" },
        ];
       
      } else if (news == "从ISO创建") {
        this.menniuItemData = [
          { name: "基本信息", icon: "icon iconfont icon-erji-yingyonggailan" },
          { name: "计算资源", icon: "icon iconfont icon-cpu" },
          { name: "硬件配置", icon: "icon iconfont icon-erji-xitongxinxi" },
          { name: "信息概览", icon: "icon iconfont icon-zonghegailan" },
        ];
        // for (var a = 0; a < JXxinxis.length; a++) {
        //   JXxinxis[a].style.display = "none";
        // }
        // for (var b = 0; b < ISOxinxis.length; b++) {
        //   ISOxinxis[b].style.display = "block";
        // }
      }
    },
  },
	mounted() {
    // 表数据
		this.resourceAllocationGET()
  },
  updated() {
    this.tablePageForm.pagecount=this.$store.state.power.pagecount
  },
	methods: {
		// 接口获取表数据
		resourceAllocationGET(){
			this.spinShowVM=true;
			this.$axios.post("/theapi/v1/grant/instances",this.tablePageForm).then((callback) => {
        this.tableSelec = new Array()
				this.tableTotal = callback.data.total;
        let arr = new Array();
        callback.data.data.forEach((item) => {
          arr.push({
            flavorid: item.flavorid,
						grant_username: item.grant_username,
            hostname: item.hostname,
            id: item.id,
            ip: item.ip,
            name: item.name,
            status: item.status,
            volumeid: item.volumeid,
          });
        });
        this.VMallocationData = arr;
				this.spinShowVM=false
      })
      .catch((error) => {
				this.spinShowVM=false
      });
		},
		// 分配虚拟机 按钮
		allocationClick(data){
      if(data==0) {
        this.$Message.warning({
					background: true,
          closable: true,
					duration: 5,
					content: "请先选择虚拟机",
				});
      }else {
        let names = new Array();
        let ids = new Array();
        data.forEach(em => {
          names.push(em.name);
          ids.push(em.id);
        });
        this.tableNames = names
        this.tableIDs = ids
        userTableQuery()
        .then((callback) => {
          let arr = new Array();
          callback.data.forEach(em=>{
            if(em.role_name=='operator') {
              arr.push({
                value:em.id,
                label:em.username,
              })
            }
          })
          this.userData = arr
          if(arr==0) {}else {
            this.formItem.userName = arr[0].label
            this.formItem.userID = arr[0].value
          }
        })
        this.distributionModel = true;
        this.disabled = false
      }
		},
		// 分配虚拟机弹框 用户选择
		onUserSlecte(item){
			this.formItem.userName = item.label
			this.formItem.userID = item.value
		},
		// 分配虚拟机弹框确认
		distributionOK(){
      if(this.userData == 0) {
        this.$Message.warning({
					background: true,
          closable: true,
					duration: 5,
					content: "无可分配用户",
				});
      }else {
        this.disabled = true
        allocationVM({
          vmids:this.tableIDs,
          vmnames:this.tableNames,
          userid:this.formItem.userID,
          username:this.formItem.userName,
        })
        .then((callback) => {
          this.distributionModel = false
          if(callback.data.msg == "ok"){
            this.resourceAllocationGET()
          }else {
            this.disabled = false
            this.$Message.error({
              background: true,
              closable: true,
              duration: 5,
              content: "分配虚拟机失败",
            });
          }
        })
      }
		},
		// 取消分配虚拟机按钮
		allocationRemove(){
			if (this.tableSelec==0) {
        this.$Message.warning({
          background: true,
          closable: true,
          duration: 5,
          content: "请先选择虚拟机",
        });
      } else {
        this.$Modal.confirm({
          title: "取消分配虚拟机",
          content:
            '<h4>是否将<span style="font-weight: 600;color:red;word-wrap: break-word;">' +
            this.tableNames.toString() +
            "</span>虚拟机，取消用户的分配权限？</h4>",
          onOk: () => {
            unassignVM({vmids:this.tableIDs,vmnames:this.tableNames})
						.then((callback) => {
							if(callback.data.msg == "ok"){
								this.resourceAllocationGET()
							}else {
								this.$Message.error({
									background: true,
                  closable: true,
									duration: 5,
									content: "取消分配虚拟机失败",
								});
							}
						})
          },
          onCancel: () => {},
        });
      }
		},
    
    applyVM(){
      this.buzhoushu = 0;
      this.newVMForm.name = "";
      this.newVMForm.radio="从镜像创建";
      this.newVMForm.imageTypeName = "";
      this.newVMForm.imageTypeId = "";
      this.newVMForm.presetsIP = false;
      this.newVMForm.ipPash = "";
      this.newVMForm.driveDisabled=false;
      this.newVMForm.driveimageId="";
      this.newVMForm.driveName = "";
      this.newVMForm.operatingSystemName = "";
      this.newVMForm.vailabilityZone = "";
      this.newVMForm.computingResourceName = "";
      this.newVMForm.vmNumber = 1;
      this.newVMForm.cpu = 2;
      this.newVMForm.memory = 4;
      this.newVMForm.disk = 100;
      this.newVMForm.networkId = "";
      this.newVMForm.networkName = "";
      this.newVMForm.ISOcpu = 2;
      this.newVMForm.ISOmemory = 4;
      this.newVMForm.ISOdisk = 100;
      this.newVMForm.description = "";
      this.newVMForm.hostip = "";
      this.newVMForm.storageid = "";
      this.newVMForm.storagename = "";
      this.newVMForm.filename = "";
      // 获取计算资源表数据
      physicsTableDetail()
      .then((JSitem) => {
        this.computingResourceData = JSitem.data;
      });
      // 获取网络下拉框数据
      networkTableQuery().then((callback) => {
        let arr = new Array();
        callback.data.forEach(em=>{
          arr.push({
            id: em.id,
            name: em.cidr.toString(),
          })
        })
        this.netOption = arr;
      });
      // 获取镜像下拉框数据
      imageTableQuery()
      .then((JXitem) => {
        let iso = new Array();
        let jx = new Array();
        for (var i = 0; i < JXitem.data.length; i++) {
          if (JXitem.data[i].disk_format == "iso") {
            iso.push({ id: JXitem.data[i].id, name: JXitem.data[i].name });
          } else {
            jx.push({ id: JXitem.data[i].id, name: JXitem.data[i].name });
          }
        }
        this.imageOptionISO = iso;
        this.imageOption = jx;
      });
      this.newBuildModal = true;
    },
    // 申请虚拟机弹框确认 
    newBuildOK() {
      this.XJloadXNJ = true;
      setTimeout(() => {
        this.XJloadXNJ = false;
      },500)
      let ruleName = /^[\u4e00-\u9fa5_a-zA-Z0-9@_.-]{2,32}$/;
      if (ruleName.test(this.newVMForm.name)) {
        setTimeout(() => {
          this.newBuildModal = false;
          if (this.newVMForm.radio == "从镜像创建") {
            let cloudHost = new Object();
            cloudHost.name =
              this.newVMForm.cpu +
              "-" +
              this.newVMForm.memory +
              "-" +
              this.newVMForm.disk;
            cloudHost.ram = this.newVMForm.memory;
            cloudHost.vcpus = this.newVMForm.cpu;
            cloudHost.disk = this.newVMForm.disk;
            cloudHost.extdisk = 0;
            this.$axios.post("/theapi/v1/flavors/create", cloudHost)
            .then((callbackJX) => {
              let newVMlist = new Object();
              newVMlist.vm_name = this.newVMForm.name;
              newVMlist.vm_availabilityzone = this.newVMForm.vailabilityZone;
              newVMlist.vm_imageid = this.newVMForm.imageTypeId;
              if(this.newVMForm.presetsIP) {
                newVMlist.ipv4 = this.newVMForm.ipPash;
              }else {
                newVMlist.ipv4 = ""
              }
              newVMlist.vm_networkid = this.newVMForm.networkId;
              newVMlist.vm_count = this.newVMForm.vmNumber;
              newVMlist.vm_flavorid = callbackJX.data.id;
              this.$axios.post("/theapi/v1/apply/vms", newVMlist)
              .then((callbackCJ) => {
                if(callbackCJ.data.msg !== "error") {
                  this.$Message.info({
                    background: true,
                    closable: true,
                    duration: 10,
                    content: "申请指令已发送",
                  });
                }else {
                  this.$Message.error({
                    background: true,
                    closable: true,
                    duration: 10,
                    content: "申请指令发送失败"
                  });
                }
              });
            });
          }else {
            let cloudHost = new Object();
            cloudHost.name =
              this.newVMForm.ISOcpu +
              "-" +
              this.newVMForm.ISOmemory +
              "-" +
              this.newVMForm.ISOdisk;
            cloudHost.ram = this.newVMForm.ISOmemory;
            cloudHost.vcpus = this.newVMForm.ISOcpu;
            cloudHost.disk = this.newVMForm.ISOdisk;
            cloudHost.extdisk = 0;
            let driveID=""
            if(this.newVMForm.driveDisabled){
              driveID = this.newVMForm.driveimageId
            }
            this.$axios
              .post("/theapi/v1/flavors/create", cloudHost)
              .then((mobanItem) => {
                let newVMlist = new Object();
                newVMlist.vm_name = this.newVMForm.name;
                newVMlist.vm_availabilityzone = this.newVMForm.vailabilityZone;
                newVMlist.vm_imageid = this.newVMForm.imageTypeId;
                if(this.newVMForm.presetsIP) {
                  newVMlist.ipv4 = this.newVMForm.ipPash;
                }else {
                  newVMlist.ipv4 = ""
                }
                newVMlist.vm_networkid = this.newVMForm.networkId;
                newVMlist.vm_flavorid = callbackJX.data.id;
                newVMlist.VM_driveimageId = driveID;
                newVMlist.flavorRef = mobanItem.data.id;
                this.$axios.post("/theapi/v1/apply/vms", newVMlist)
                  .then((callbackISO) => {
                    if(callbackISO.data.msg !== "error") {
                      let oldTotal = this.totalAll
                      this.$Message.info({
                        background: true,
                        closable: true,
                        duration: 10,
                        content: "申请指令已发送",
                      });
                    }else {
                      this.$Message.error({
                        background: true,
                        closable: true,
                        duration: 10,
                        content: "申请指令发送失败"
                      });
                    }
                  });
              });
          }
        }, 500);
      } else {
        this.$Message.warning({
          background: true,
          closable: true,
          duration: 5,
          content: "输入的虚拟机名称不符合规定",
        });
      }
    },
     // 申请虚拟机弹框-计算资源表选中集群数据
    computingResourceSelec(currentRow, oldCurrentRow) {
      this.newVMForm.vailabilityZone = currentRow.availability_zone;
      this.newVMForm.computingResourceName = currentRow.name;
    },
    // 虚拟机选择
    formvm(val) {
      return val + "个";
    },
    // cpu选择
    formCpu(val) {
      return val + "核";
    },
    // 内存选择
    formMemory(val) {
      return val + " GB";
    },
    // 硬盘选择
    formDisk(val) {
      return val + " GB";
    },
    // 镜像选择
    netIMGchange(item) {
      this.newVMForm.imageTypeName = item.label;
      this.newVMForm.imageTypeId = item.value;
    },
    // 驱动选择
    driveCheck(item){
      this.newVMForm.driveDisabled=item
    },
    driveSelect(item){
      this.newVMForm.driveName = item.label;
      this.newVMForm.driveimageId = item.value;
    },
    // 操作系统类型
    iosIMGchange(item) {
      this.newVMForm.operatingSystemName = item.value;
    },
    // 网络选择
    netSlectChange(item) {
      this.newVMForm.networkName = item.label;
      this.newVMForm.networkId = item.value;
    },
    xiayibu() {
      this.buzhoushu++;
    },
    shangyibu() {
      this.buzhoushu--;
    },
    modalMenuItem(item) {
      this.buzhoushu = item;
    },
		// 表格选中数据集合
		tableChange(item){
			this.tableSelec = item
		},
		// 当前分页
		onPageChange(item) {
			this.tablePageForm.page = item
			this.resourceAllocationGET()
		},
		// 每页条数
		onPageSizeChange(item){
      this.$store.state.power.pagecount = item
      this.tablePageForm.pagecount = this.$store.state.power.pagecount
			this.tablePageForm.page = 1
      this.tableTotal = 0
      this.tablePageForm.search_str = ""
			this.resourceAllocationGET()
		},
		// 表格排序按钮事件
		sortColumn(column, key, order) {
      if (column.key !== "normal") {
        this.tablePageForm.order_by = column.key;
        this.tablePageForm.order_type = column.order;
        this.tablePageForm.page = 1
        this.tableTotal = 0
        this.tablePageForm.search_str = ""
        this.resourceAllocationGET()
      }
    },
    
    // 搜索输入框
		searchInput(){
      this.tablePageForm.page = 1
      this.tableTotal = 0
			this.resourceAllocationGET()
		},
	}
};
</script>