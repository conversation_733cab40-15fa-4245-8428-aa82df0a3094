<style lang="less">
@import "./oneClick.less";
</style>
<template>
  <div class="operations_assistant">
    <!-- theme: 主题，可选值为 light、dark、primary，其中 primary 只适用于 mode="horizontal" -->
    <Menu :active-name="active" @on-select="menuClick">
      <Menu-group title="运维助手">
        <Menu-item name="jiankang" v-show="powerNavi.jiankangjiancha">
          <span class="icon iconfont icon-shiyongzhuangtai-"></span>
          健康检查
        </Menu-item>
        <!-- <Menu-item name="qingli">
          <span class="icon iconfont icon-qingli"></span>
          存储清理
        </Menu-item> -->
        <Menu-item name="jiangsi" v-show="powerNavi.jiangshixuji">
          <span class="icon iconfont icon-yiguoqi-yiyuqi-01"></span>
          僵尸虚机
        </Menu-item>
      </Menu-group>
    </Menu>
    <div class="asistant_content">
      <JKeinspect v-if="this.active=='jiankang'"></JKeinspect>
      <CCclear  v-if="this.active=='qingli'"></CCclear>
      <JSvm  v-if="this.active=='jiangsi'"></JSvm>
    </div>
  </div>
</template>
<script>
import { powerCodeQuery } from '@/api/other'; // 权限可用性 查询

import JKeinspect from "./JKeinspect/JKeinspect.vue";
import CCclear from "./CCclear/CCclear.vue";
import JSvm from "./JSvm/JSvm.vue";
export default {
  components: {
    JKeinspect,
    CCclear,
    JSvm,
  },
  data() {
    return {
      active: "jiankang",
      powerNavi: {}
    };
  },
  mounted() {
    this.menusQuery();
  },
  methods: {
    menusQuery(){
      powerCodeQuery({
        module_code:[
          'jiankangjiancha',
          'jiangshixuji'
        ]
      }).then(callback=>{
        this.powerNavi = callback.data.data
        if(callback.data.data.jiankangjiancha) {
          this.active = 'jiankang'
        }else if(callback.data.data.jiangshixuji) {
          this.active = 'jiangsi'
        }
      })
    },
    menuClick(name){
      this.active = name
    }
  }
};
</script>
