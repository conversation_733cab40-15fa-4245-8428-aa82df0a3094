<style>
  .zhongduan  {
    width:1170px;
    background-color: #000;
    padding: 15px;
  }
</style>
<template>
  <div>
    <Modal v-model="models" width="1200" :mask-closable="false" :before-close="handleBeforeClose">
      <template #header><p>终端</p></template>
      <div class="zhongduan" :style="{ height: computedHeight+20 + 'px' }">
        <div class="console" id="terminal"></div>
      </div>
      <template #footer>
        <Button type="text" @click="guanbi">关闭</Button>
      </template>
    </Modal>
  </div>
</template>
<script>
import Terminal from "./Xterm";
export default {
  props: {
    terminaltime:String,
  },
  watch: {
    terminaltime(news){
      let ipaddress = news.split("/")[0]
      this.models = true
      setTimeout(() => {
        this.websocketOpen(ipaddress)
      }, 500);
    }
  },
  data() {
    return {
      models:false,
      term: null,
      terminalSocket: null,
      sizewidth:100,
      sizeheight:36,
    };
  },
  computed: {
    computedHeight() {
      return this.sizeheight * 17.5;
    },
  },
  methods: {
    // 连接终端
    websocketOpen(ipaddress) {
      let jsonStr = `{"ipaddress":"${ipaddress}"}`;
      let datMsg = window.btoa(jsonStr);
      let cols = 54;
      let rows = 1080;
      let url = (location.protocol === "http:" ? "ws" : "wss") +"://"+location.host+"/thewebsocket/ws?msg="+datMsg+"&rows="+rows +"&cols="+cols;
      // let url = (location.protocol === "http:" ? "ws" : "ws") +"://"+"*************"+"/thewebsocket/ws?msg="+datMsg+"&rows="+rows +"&cols="+cols;
      let terminalContainer = document.getElementById("terminal");
      this.term = new Terminal();
      this.term.resize(this.sizewidth,this.sizeheight)
      this.term.open(terminalContainer);
      this.terminalSocket = new WebSocket(url);

      this.term.attach(this.terminalSocket);
      this.term._initialized = true;
    },
    // 关闭弹框
    handleBeforeClose(){
      if(this.terminalSocket) {
        this.terminalSocket.close();
        this.term.destroy();
      }
      this.models = false
    },
    guanbi(){
      if(this.terminalSocket) {
        this.terminalSocket.close();
        this.term.destroy();
      }
      this.models = false
    },
  },
  beforeDestroy() {
    // if(this.terminalSocket) {
    //   this.terminalSocket.close();
    //   this.term.destroy();
    // }
  },
  mounted() {},
  
};
</script>