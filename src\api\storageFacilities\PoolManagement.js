// import request from '@/utils/request'

// 查询Pool列表
// export function getPoolManagementList(query) {
//   return request({
//     url: '/api/api/pool',
//     method: 'get',
//     params: query
//   })
// }
// // 查询新建卷池信息
// export function getCreatedPoolInfo(query) {
//   return request({
//     url: '/api/ui-api/pool/info',
//     method: 'get',
//     params: query
//   })
// }




// 应用类型
export const APP_LICATION_METADATA = [
  {
    name: '块存储',
    key: 'rbd',
    id: '',
    value: 'rbd',
    label: '块存储'
  },
  {
    name: '文件系统',
    key: 'cephfs',
    id: '',
    value: 'cephfs',
    label: '文件系统'
  },
  {
    name: '对象存储',
    key: 'rgw',
    id: '',
    value: 'rgw',
    label: '对象存储'
  },
  {
    name: 'nfs',
    key: 'nfs',
    id: '',
    value: 'nfs',
    label: 'nfs'
  },
  {
    name: 'grace',
    key: 'grace',
    id: '',
    value: 'grace',
    label: 'grace'
  },
  {
    name: '未定义',
    key: 'mgr_devicehealth',
    id: '',
    value: 'mgr_devicehealth',
    label: '未定义'
  },
  {
    name: '磁盘类型',
    key: 'iscsi',
    id: '',
    value: 'iscsi',
    label: '磁盘类型'
  }
]

// 安全类型
export const POOL_TYPE =[
  {
    name: '副本类型',
    key: 'replicated',
    id: '',
    value: 'replicated',
    label: '副本类型'
  },
  {
    name: '纠删码型',
    key: 'erasurecode',
    id: '',
    value: 'erasurecode',
    label: '纠删码型'
  }
]
// 安全级别
export const SECURITY_LEVEL = [
  {
    name: 2,
    value: 2,
    label: 2
  },
  {
    name: 3,
    value: 3,
    label: 3
  },
  {
    name: 4,
    value: 4,
    label: 4
  },
]

// 压缩算法列表
export const  COMPRESSION_ALGORITHMS = [
  {
    name: 'snappy',
    key: '',
    value: 'snappy',
    label: 'snappy'
  },
  {
    name: 'zstd',
    key: '',
    value: 'zlib',
    label: 'zlib'
  },
  {
    name: 'zstd',
    key: '',
    value: 'zstd',
    label: 'zstd'
  },
  {
    name: 'lz4',
    key: '',
    value: 'lz4',
    label: 'lz4'
  },
]

// 压缩算法模式
export const COMPRESSION_MODES = [
  {
    name: 'none',
    key: '',
    value: 'none',
    label: 'none'
  },
  {
    name: 'passive',
    key: '',
    value: 'passive',
    label: 'passive'
  },
  {
    name: 'aggressive',
    key: '',
    value: 'aggressive',
    label: 'aggressive'
  },
  {
    name: 'force',
    key: '',
    value: 'force',
    label: 'force'
  },
]

// 压缩级别列表
export const COMPRESSION_LEVEL =[
  {
    value: '0.1',
    label: '0.1',
  },
  {
    value: '0.2',
    label: '0.2',
  },
  {
    value: '0.3',
    label: '0.3',
  },
  {
    value: '0.4',
    label: '0.4',
  },
  {
    value: '0.5',
    label: '0.5',
  },
  {
    value: '0.6',
    label: '0.6',
  },
  {
    value: '0.7',
    label: '0.7',
  },
  {
    value: '0.8',
    label: '0.8',
  },
  {
    value: '0.9',
    label: '0.9',
  },
  {
    value: '1.0',
    label: '1.0',
  },
]
