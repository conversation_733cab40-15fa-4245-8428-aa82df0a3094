<style lang="less">
@import "../monitoring.less";
@import "./hostMonitor.less";
</style>
<template>
  <div class="server_monitor_area">
    <div class="server_basic_information">
      <!-- 物理机监控信息 -->
      <div class="server_information_area">
        <div v-for="item in serverData" class="server_information">
          <div class="machine_title_area">
            <h4>{{item.title}}（{{item.ip}}）</h4>
            <span :class="item.status=='operation'?'machine_run':'machine_close'">{{item.status=='operation'?'机器运行':'机器关闭'}}</span>
          </div>
          <div class="server_img">
            <img v-if="item.status=='operation'" src="../../../assets/monitorIMG/run.png">
            <img v-if="item.status=='fault'" src="../../../assets/monitorIMG/fault.png">
            <img v-if="item.status=='shutdown'" src="../../../assets/monitorIMG/shutdown.png">
            <img v-if="item.status=='alarm'" src="../../../assets/monitorIMG/alarm.png">
          </div>
          <div class="server_schedule_area">
            <div class="server_rate">
              <span>CPU总数</span><span>{{item.status=="operation"?item.cpuTatal+" 核":"-"}}</span>
            </div>
            <div class="server_rate">
              <span>物理内存总数</span><span>{{item.status=="operation"?formatFileSize(item.memoryTatal):"-"}}</span>
            </div>
            <div class="server_rate">
              <span>CPU使用率</span><span>{{item.status=="operation"?item.cpuUsageRate+" %":"-"}}</span>
            </div>
            <Progress style="margin-bottom:10px" :stroke-color="['#98c4fa', '#0176f6']" :percent="item.cpuUsageRate>99?99:item.cpuUsageRate" :stroke-width="10" :hide-info="true" />
            <div class="server_rate">
              <span>物理内存使用率</span><span>{{item.status=="operation"?item.memoryUsageRate+" %":"-"}}</span>
            </div>
            <Progress :stroke-color="['#98c4fa', '#0176f6']" :percent="item.memoryUsageRate>99?99:item.memoryUsageRate" :stroke-width="10" :hide-info="true" />
          </div>
        </div>
      </div>
    </div>
    <!-- 物理机图表 -->
    <div class="server_chart_area">
      <div class="radio_chart_select">
        <div class="radio_area">
          <div class="radio_piece" v-for="every in radioData" @click="radioClick(every.lable)">
            <span class="radio_title" :style="{color:trendItem==every.lable?'#121529':'#717379'}">{{every.title}}</span>
            <span class="radio_selected" :style="{background:trendItem==every.lable?'#fe6902':''}"></span>
          </div>
        </div>
        <Select v-model="trendTime" style="width:150px" @on-change="timeONchange">
          <Option v-for="item in timeData" :value="item.value" :key="item.value">{{ item.label }}</Option>
        </Select>
      </div>
      <ChartLine :datas="datas" :times="trendTime" style="width:100%;height:calc(100% - 55px)"></ChartLine>
    </div>
  </div>
</template>
<script>
import {physicalMachineBasicQuery,physicalMachineCPUquery,physicalMachineMemoryQuery,physicalMachineDiskQuery,physicalMachineNetworkQuery} from '@/api/monitor';
import ChartLine from "../chartTemplate/ChartLine.vue"
export default {
  components: {
    ChartLine,
  },
  props: {
    tabName: String,
  },
  watch: {
    tabName(value) {
      if(value=='物理机监控') {
        this.trendItem = "cpu"
        this.trendTime = 1
        this.getBesicData()
        this.radioClick("cpu")
      }
    },
  },
  mounted() {
    if(this.$store.state.power.itMonitoringTab == '物理机监控') {
      this.trendItem = "cpu"
      this.trendTime = 1
      this.getBesicData()
      this.radioClick("cpu")
    }
  },
  data() {
    return {
      // 物理服务器数据
      serverData:[
        {
          title:"contorller1",
          ip:"***********",
          status:"operation", // 运行
          cpuTatal:0, // 分配比
          memoryTatal:"0 MB", // 分配比
          cpuUsageRate:0,  // 使用率
          memoryUsageRate:0, // 使用率
        },
      ],
      // 物理机图表
      trendItem:"cpu",
      trendTime:1,
      startTime:"",
      endTime:"",
      radioData:[
        {title:"CPU趋势",lable:"cpu"},
        {title:"内存趋势",lable:"memory"},
        {title:"磁盘趋势",lable:"disk"},
        {title:"网络趋势",lable:"network"},
      ],
      // timeData:[{label:"最近1小时",value:1},{label:"最近24小时",value:24},{label:"最近1个月",value:720}],
      timeData:[{label:"最近1小时",value:1},{label:"最近24小时",value:24}],
      datas:null,
    }
  },
  methods: {
    // 切换趋势
    radioClick(item){
      this.trendItem = item
      this.timeONchange(this.trendTime);
    },
    // 选择时间
    timeONchange(item){
      // let currentTimestamp = Date.now() / 1000  // 获取当前时间戳（毫秒）
      // let twentyFourHoursAgoTimestamp = currentTimestamp - (item * 60 * 60); // 计算item 小时前的时间戳
      // this.startTime = twentyFourHoursAgoTimestamp
      // this.endTime = currentTimestamp

      this.chartTimeEncapsulation(item)
      
      if(this.trendItem=="cpu"){
        this.getCPUdata()
      }else if(this.trendItem=="memory"){
        this.getMemoryData()
      }else if(this.trendItem=="disk"){
        this.getDistData()
      }else if(this.trendItem=="network"){
        this.getNetworkData()
      }
    },
    // 获取 基本 数据
    getBesicData(){
      physicalMachineBasicQuery().then(callback=>{
        let arr = new Array()
        callback.data.forEach(em => {
          arr.push({
            title:em.hypervisor_hostname,
            ip:em.host_ip,
            status:em.status=="enabled"&&em.state =="up"?"operation":"shutdown",
            cpuTatal:em.vcpus,
            memoryTatal:em.memory_mb,
            cpuUsageRate:em.status=="enabled"&&em.cpu_percent?parseFloat(em.cpu_percent.toFixed(1)):0,
            memoryUsageRate:em.status=="enabled"?parseFloat((em.memory_mb_used/em.memory_mb*100).toFixed(1)):0,
          })
        });
        this.serverData=arr
      })
    },
    // 获取 图表 数据
    getCPUdata(){
      physicalMachineCPUquery({start:this.startTime,end:this.endTime}).then(callback=>{
        this.datas = callback.data
      })
    },
    getMemoryData(){
      physicalMachineMemoryQuery({start:this.startTime,end:this.endTime}).then(callback=>{
        this.datas = callback.data
      })
    },
    getDistData(){
      physicalMachineDiskQuery({start:this.startTime,end:this.endTime}).then(callback=>{
        this.datas = callback.data
      })
    },
    getNetworkData(){
      physicalMachineNetworkQuery({start:this.startTime,end:this.endTime}).then(callback=>{
        this.datas = callback.data
      })
    },
    getNetworkData111(){
      let list = {
        unit:"mb",
        time:["1:05","1:10","1:15","1:20","1:25","1:30","1:35","1:40","1:45","1:50","1:55","2:00",],
        data:[
          {title:"物理机1上行",list:[]},
          {title:"物理机1下行",list:[]},
          {title:"物理机2上行",list:[]},
          {title:"物理机2下行",list:[]},
        ],
      }
      for(var i=0;i<12;i++){
        let a=Math.floor(Math.random() * 50) + 1
        list.data[0].list.push(a)
      }
      for(var i=0;i<12;i++){
        let a=Math.floor(Math.random() * 50)*-1
        list.data[1].list.push(a)
      }
      for(var i=0;i<12;i++){
        let a=Math.floor(Math.random() * 50) + 1
        list.data[2].list.push(a)
      }
      for(var i=0;i<12;i++){
        let a=Math.floor(Math.random() * 50)*-1
        list.data[3].list.push(a)
      }
      this.datas = list
    },
    // 颜色判断
    colorJudgment(status){
      switch (status) {
        case "operation":
          return "#01cfc7"
        break;
        case "shutdown":
          return "#b1b6c4"
        break;
        case "alarm":
          return "#fe9e1f"
        break;
        case "malfunction":
          return "#ff6966"
        break;
      }
    },
    // 字节+单位转换
    formatFileSize(size) {
      const units = ['MB', 'GB', 'TB', 'PB'];
      let unitIndex = 0;
      while (size >= 1024 && unitIndex < units.length - 1) {
          size /= 1024;
          unitIndex++;
      }
      return Math.floor(size * 100) / 100 + ' ' + units[unitIndex];
    },
    // 图表时间封装
    chartTimeEncapsulation(item){
      // 获取当前时间
      var currentDate = new Date();
      // 获取一小时前的时间
      var oneHourBefore = new Date(currentDate.getTime() - item*60 * 60 * 1000);
      // 获取年、月、日
      var year = currentDate.getFullYear();
      var month = ("0" + (currentDate.getMonth() + 1)).slice(-2); // 月份从0开始，所以要加1
      var day = ("0" + currentDate.getDate()).slice(-2);
      // 获取小时、分钟（当前时间）
      var hours = ("0" + currentDate.getHours()).slice(-2);
      var minutes = currentDate.getMinutes();
      // 调整分钟（当前时间）
      if (minutes % 10 < 5) {
        minutes = Math.floor(minutes / 10) * 10; // 当分钟尾数小于5时，取整为10的倍数
      } else {
        minutes = Math.ceil(minutes / 10) * 10 - 5; // 当分钟尾数大于6时，取整为10的倍数再减去5
      }
      // 格式化分钟（当前时间）
      minutes = ("0" + minutes).slice(-2);

      // 输出当前时间结果
      var formattedCurrentDateTime =
        year + "-" + month + "-" + day + " " + hours + ":" + minutes;

      // 获取一小时前的年、月、日、时、分
      var beforeYear = oneHourBefore.getFullYear();
      var beforeMonth = ("0" + (oneHourBefore.getMonth() + 1)).slice(-2);
      var beforeDay = ("0" + oneHourBefore.getDate()).slice(-2);
      var beforeHours = ("0" + oneHourBefore.getHours()).slice(-2);
      var beforeMinutes = oneHourBefore.getMinutes();

      // 调整分钟（一小时前的时间）
      if (beforeMinutes % 10 < 5) {
        beforeMinutes = Math.floor(beforeMinutes / 10) * 10; // 当分钟尾数小于5时，取整为10的倍数
      } else {
        beforeMinutes = Math.ceil(beforeMinutes / 10) * 10 - 5; // 当分钟尾数大于6时，取整为10的倍数再减去5
      }

      // 格式化分钟（一小时前的时间）
      beforeMinutes = ("0" + beforeMinutes).slice(-2);

      // 输出一小时前的时间结果
      var formattedBeforeDateTime =
        beforeYear +
        "-" +
        beforeMonth +
        "-" +
        beforeDay +
        " " +
        beforeHours +
        ":" +
        beforeMinutes;

      this.startTime = formattedBeforeDateTime // 开始时间
      this.endTime = formattedCurrentDateTime // 结束时间
    },
  }
}
</script>