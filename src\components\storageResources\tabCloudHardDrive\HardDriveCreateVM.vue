<template>
  <div>
    <Modal v-model="model" width="600" :mask-closable="false">
      <template #header><p>创建虚拟机</p></template>
      <Form
        :model="formItem"
        ref="formItem"
        :rules="rulesForm"
        :label-width="120"
      >
        <FormItem label="所选云硬盘">
          <Input v-model="tableRow.name" disabled></Input>
        </FormItem>
        <FormItem label="虚拟机名称" prop="name">
          <Input v-model="formItem.name" placeholder="请输入虚拟机名称"></Input>
        </FormItem>
        <FormItem label="计算资源" prop="host">
          <Select v-model="formItem.host" :label-in-value='true'>
            <Option v-for="item in hostData" :value="item.availability_zone" :key="item.id" >{{ item.name }}</Option>
          </Select>
        </FormItem>
        <FormItem label="网络名称" prop="netID">
          <Select :disabled="formItem.presetsIP" v-model="formItem.netID" :label-in-value="true" @on-change="netChange">
            <Option v-for="item in netData" :value="item.id" :key="item.id" >{{ item.name }}</Option>
          </Select>
        </FormItem>
        <FormItem label="预设IP">
          <div style="display: flex;">
            <Checkbox v-model="formItem.presetsIP"></Checkbox>
            <FormItem v-if="formItem.presetsIP"  prop="ipPash">
              <Input v-model="formItem.ipPash" placeholder="请输入IP地址" style="width: 422px"></Input>
            </FormItem>
          </div>
        </FormItem>
        <FormItem label="系统版本" v-if="architecture">
          <Select v-model="formItem.version" :label-in-value="true" >
            <Option v-for="item in versionData" :value="item.value" :key="item.value">{{ item.value }}</Option>
          </Select>
        </FormItem>
        <FormItem label="VCPU核数">
          <div class="slider_area">
            <div style="width:350px">
              <Slider
                v-model="formItem.cpu"
                :min="1"
                :max="64"
                :tip-format="formCPU"
              ></Slider>
            </div>
            <div style="width:80px">
              <InputNumber :min="1" :max="64" v-model="formItem.cpu" :formatter="value => `${value}核`" :parser="value => value.replace('核', '')"></InputNumber>
            </div>
          </div>
        </FormItem>
        <FormItem label="内存容量">
          <div class="slider_area">
            <div style="width:350px">
              <Slider
                v-model="formItem.mem"
                :min="1"
                :max="256"
                :tip-format="formMEM"
              ></Slider>
            </div>
            <div style="width:80px">
              <InputNumber :min="1" :max="256" v-model="formItem.mem" :formatter="value => `${value}GB`" :parser="value => value.replace('GB', '')"></InputNumber>
            </div>
          </div>
        </FormItem>
      </Form>
      <div slot="footer">
        <Button type="text" @click="model = false">取消</Button>
        <Button type="primary" :loading="disabled"  @click="modelOK">
          <span v-if="!disabled">确认</span>
          <span v-else>创建中</span>
        </Button>
      </div>
    </Modal>
  </div>
</template>
<script>
import { cloudDiskVMnewBuilt } from '@/api/storage'; // 云硬盘 新建虚拟机
import { physicsTableAllQuery } from '@/api/physics'; // 查询集群
import { networkTableQuery,networkCheckip } from '@/api/network'; // 查询网络
import { vmTemplateNewBuilt } from '@/api/virtualMachine'; // 虚拟机模板



export default {
  props: {
    vmTime: String,
    tableRow: Object,
  },
  watch: {
    vmTime(news){
      this.$refs.formItem.resetFields();
      if(window.gurl.architecture == "ARM") {
        this.formItem.version = "linux";
        this.architecture = false
      }else {
        this.formItem.version = "windows 其它版本";
        this.architecture = true
      }
      this.dropdownData() // 初始化下拉选择
      this.formItem.presetsIP = false; // 初始化预设IP
      this.formItem.cpu = 2;
      this.formItem.mem = 4;
      this.formItem.disk = parseInt(this.tableRow.size)
      
      this.model = true
      this.disabled = false
    }
  },
  data(){
    const propName =(rule,value,callback)=>{
      let list = /^[\u4e00-\u9fa5_a-zA-Z0-9@_.-]{2,32}$/;
      if(list.test(value)){
        callback()
      }else{
        callback(new Error("2-32 个中文、英文、数字、特殊字符(@_.-)"));
      }
    }
    const propipPash = (rule, value, callback) => {
      let list = /^(25[0-4]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-4]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-4]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-4]|2[0-4][0-9]|[01]?[1-9][0-9]?|1[0-9]{2}|2[0-4][0-9])$/
      let subnet = this.formItem.netName.split(':')[1]
      if(list.test(value)) {
        if(this.ipInSubnet(value, subnet)) {
          callback()
        }else {
          callback(new Error("IP地址与所选网段不匹配"))
        }
      }else {
        callback(new Error("该ip不可用"))
      }
    };
    return {
      architecture: false, // 架构是否启用
      model: false,
      disabled: false,
      formItem: {
        name: '',
        host: '',
        netID: '',
        netName: '',
        presetsIP: false,
        ipPash: '',
        version: 'windows 其它版本',
        cpu: 2,
        mem: 4,
        disk: 0,
      },
      
      hostData: [], // 计算资源
      netData: [], // 网络
      versionData: [
        {value:"windows 其它版本"},
        {value:"windows 2003"},
        {value:"windows 2008"},
        {value:"windows 2012"},
        {value:"windows 2016"},
        {value:"linux"},
      ],  
      // 正则验证
      rulesForm: {
        name:[
          { required: true, message: '必填项', trigger: 'change' },
          { validator: propName, trigger:'change' }
        ],
        host:[{ required: true, message: "必选项", trigger: "change" }],
        netID:[{ required: true, message: "必选项", trigger: "change" }],
        ipPash:[
          { required: true, message: "必填项", trigger: "blur" },
          { validator: propipPash, trigger: "change" },
        ],

      },
    }
  },
  methods: {
    dropdownData () {
      // 获取计算资源下拉数据
      physicsTableAllQuery()
      .then((callback) => {
        this.hostData = callback.data;
        this.formItem.host = callback.data[0].availability_zone
      });
      // 获取网络下拉框数据
      networkTableQuery()
      .then((callback) => {
        let arr = new Array();
        callback.data.forEach(item=>{
          item.cidr.forEach((em,i) => {
            arr.push({
              id: item.id+":"+item.subnets[i],
              name: em,
            })
          })
        })
        this.netData = arr;
        this.formItem.netID = arr[0].id
        this.formItem.netName = arr[0].name
      });
    },
    // 网络选择
    netChange(item) {
      this.formItem.netID = item.value;
      this.formItem.netName = item.label;
    },
    // cpu选择
    formCPU(val) {
      return val + "核";
    },
    // 内存选择
    formMEM(val) {
      return val + " GB";
    },
    // 确认事件
    modelOK() {
      if(this.formItem.presetsIP) {
        networkCheckip({
          now_ipv4:'',
          new_ipv4:this.formItem.ipPash
        }).then((check)=>{
          if(check.data.msg == 'ok'){
            this.sendData()
          }else {
            this.$emit("return-error", check.data.msg)
          }
        })
      }else {
        this.sendData()
      }
    },
    // 发送数据封装
    sendData() {
        this.$refs.formItem.validate((valid) => {
          if (valid) {
            this.disabled = true;
            vmTemplateNewBuilt({
              name: this.formItem.cpu+'-'+this.formItem.mem+'-'+this.formItem.disk,
              vcpus: this.formItem.cpu,
              ram: this.formItem.mem,
              disk: this.formItem.disk,
              extdisk: 0,
              ...(this.formItem.version=="windows 其它版本"||this.formItem.version=="linux"?"":{metadata_sys_versionthis:this.formItem.version})
            })
            .then(callback=>{
              cloudDiskVMnewBuilt({
                flavorRef: callback.data.id, // 模板id
                name: this.formItem.name, // 名称
                availability_zone: this.formItem.host, // 计算资源
                networkRef: this.formItem.netID.split(":")[0], // 网络ID
                subnet_id: this.formItem.netID.split(":")[1], // 子网ID
                ipv4: this.formItem.presetsIP?this.formItem.ipPash:"", // 使用预设IP
                volumeRef: this.tableRow.id, // 云硬盘表ID
                os_type: this.formItem.version=="linux"?"linux":"windows" // 系统类型默认填充
              }).then(res=>{
                if(res.data.msg == "ok"){
                  this.$emit("return-ok",'云硬盘创建虚拟机操作完成');
                  this.model = false;
                }else {
                  this.$emit("return-error","云硬盘创建虚拟机操作失败");
                  this.disabled = false;
                }
              })
              .catch((error) => {
                this.$emit("return-error",'云硬盘创建虚拟机操作失败');
                this.disabled = false;
              });
            })
            .catch((error) => {
              this.$emit("return-error",'虚拟机模板创建失败');
              this.disabled = false;
            });
          }
        });
      
    },
    // 判断网络属于子网
    ipInSubnet(ip, subnet) {
      let [subnetIp, maskBits] = subnet.split('/');
      maskBits = parseInt(maskBits, 10);
      let ipBinary = ip.split('.').map(octet => parseInt(octet, 10).toString(2).padStart(8, '0')).join('');
      let subnetBinary = subnetIp.split('.').map(octet => parseInt(octet, 10).toString(2).padStart(8, '0')).join('');
      for(let i=0; i<maskBits; i++){
          if(ipBinary[i] != subnetBinary[i]){
              return false;
          }
      }
      return true;
    },
  }
}
</script>
