<style lang="less">
  @import "../networkResource.less";
</style>
<template>
  <div class="physical_network_area">
    <div class="table-button-area">
      <div>
        <Button type="info" ghost @click="newWLnode=true" style="margin-bottom: 10px" >新建网络</Button>
      </div>
      <div>
        <!-- <Input v-model="tablePageForm.search_str" search enter-button placeholder="请输入名称" style="width: 300px" @on-search="tableSearchInput"/> -->
      </div>
    </div>
    <ul class="physical_network_content" style="height:100%">
      <li>
        <div class="physical_network_title">
          <span>管</span>
          <span>理</span>
          <span>网</span>
          <span>络</span>
        </div>
        <div style="width: 95%;">
          <Table :columns="columnALL" :data="GLdata"></Table>
        </div>
      </li>
      <li>
        <div class="physical_network_title">
          <span>业</span>
          <span>务</span>
          <span>网</span>
          <span>络</span>
        </div>
        <div style="width: 95%;">
          <Table :columns="columnALL" :data="YWdata" ></Table>
        </div>
      </li>
      <li>
        <div class="physical_network_title">
          <span>存</span>
          <span>储</span>
          <span>网</span>
          <span>络</span>
        </div>
        <div style="width: 95%;">
          <Table :columns="columnALL" :data="CCdata"></Table>
        </div>
      </li>
    </ul>
    <!-- 新建网络弹框 -->
    <Modal v-model="newWLnode" title="新建网络" @on-ok="newWLnodeOk" @on-cancel="cancel" :mask-closable="false">
				<Form :model="formItem" :label-width="120">
          <FormItem label="选择网络组">
            <Select v-model="formItem.type" >
              <Option v-for="item in networkGroop" :value="item.value" :key="item.value">{{ item.label }}</Option>
            </Select>
        	</FormItem>
					<FormItem label="节点名">
            <Input v-model="formItem.name" placeholder="输入节点名"></Input>
        	</FormItem>
          <FormItem label="IP地址">
            <Input v-model="formItem.cidr" placeholder="例：***********/24"></Input>
        	</FormItem>
          <FormItem label="VLAN ID">
            <Input v-model="formItem.vlanid" placeholder="输入VLAN ID"></Input>
        	</FormItem>
          <FormItem label="桥接">
            <Input v-model="formItem.bridge" placeholder="输入网桥ID"></Input>
        	</FormItem>
          <FormItem label="网卡绑定">
            <Input v-model="formItem.bonding" placeholder="输入bond ID"></Input>
        	</FormItem>
          <FormItem label="物理网卡">
            <Input v-model="formItem.devinfo" placeholder="例：etho,eth1,..."></Input>
        	</FormItem>
          <FormItem label="速率">
            <Input v-model="formItem.speed" placeholder="例：1000MB,10000MB,..."></Input>
        	</FormItem>
				</Form>
		</Modal>
    <!-- 修改网络弹框 -->
    <Modal v-model="editWLnode" title="新建网络" @on-ok="editWLnodeOk" @on-cancel="cancel" :mask-closable="false">
				<Form :model="editFormItem" :label-width="120">
          <FormItem label="所属网络">
            <Select v-model="editFormItem.type" >
              <Option v-for="item in networkGroop" :value="item.value" :key="item.value">{{ item.label }}</Option>
            </Select>
        	</FormItem>
					<FormItem label="节点名">
            <Input v-model="editFormItem.name"  placeholder="输入节点名"></Input>
        	</FormItem>
          <FormItem label="IP地址">
            <Input v-model="editFormItem.cidr" placeholder="例：***********/24"></Input>
        	</FormItem>
          <FormItem label="VLAN ID">
            <Input v-model="editFormItem.vlanid" placeholder="输入VLAN ID"></Input>
        	</FormItem>
          <FormItem label="桥接">
            <Input v-model="editFormItem.bridge" placeholder="输入网桥ID"></Input>
        	</FormItem>
          <FormItem label="网卡绑定">
            <Input v-model="editFormItem.bonding" placeholder="输入bond ID"></Input>
        	</FormItem>
          <FormItem label="物理网卡">
            <Input v-model="editFormItem.devinfo" placeholder="例：etho,eth1,..."></Input>
        	</FormItem>
          <FormItem label="速率">
            <Input v-model="editFormItem.speed" placeholder="例：1000MB,10000MB,..."></Input>
        	</FormItem>
				</Form>
		</Modal>
  </div>
</template>
<script>
import {networkTablePhysicalQuery,networkTablePhysicalNewBuilt,networkTablePhysicalModify,networkTablePhysicalDelete} from '@/api/network';
export default {
  data() {
    return {
      // 管理网络
      columnALL:[
        { title: "节点名", key: "name" },
        { title: "IP地址段", key: "cidr" ,align: "center"},
        { title: "VLAN ID", key: "vlanid",align: "center"},
        { title: "桥接", key: "bridge",align: "center" },
        { title: "网卡绑定", key: "bonding",align: "center" },
        { title: "物理网卡", key: "devinfo",align: "center",
          render: (h, params) => {
            return h('div', [
              h('span',params.row.devinfo.split(',')[0]),
              h('br'),
              h('span',params.row.devinfo.split(',')[1])
            ]);
          }
        },
        { title: "速率", key: "speed",align: "center",
          render: (h, params) => {
            return h('div', [
              h('span',params.row.speed.split(',')[0]),
              h('br'),
              h('span',params.row.speed.split(',')[1])
            ]);
          }
        },
        { title: "操作", key: "operation",width:120,
          render: (h, params) => {
            return h("div", [
              // 表格icon图标添加悬浮显示
              h("Tooltip", [
                h("Icon", {
                  props: {
                    type: "ios-chatboxes",
                    size: 20
                  },
                  style: {
                    cursor: "pointer",
                    paddingRight:'10px',
                  },
                  nativeOn: {
                    click: () => {
                      this.editWLnode = true;
                      if(params.row.type == "1") {
                        this.formItem.sswl = "管理网络"
                      }else if(params.row.type == "2") {
                        this.formItem.sswl = "业务网络"
                      }else if(params.row.type == "3") {
                        this.formItem.sswl = "存储网络"
                      }
                      this.editFormItem.type = params.row.type
                      this.editFormItem.name = params.row.name
                      this.editFormItem.cidr = params.row.cidr
                      this.editFormItem.vlanid = params.row.vlanid
                      this.editFormItem.bridge = params.row.bridge
                      this.editFormItem.bonding = params.row.bonding
                      this.editFormItem.devinfo = params.row.devinfo
                      this.editFormItem.speed = params.row.speed
                      this.editFormItem.id = params.row.id
                    }
                  }
                }),
                h('span', {slot: 'content', style: {whiteSpace: 'normal', wordBreak: 'break-all'}}, "编辑")
              ]),
              h("Tooltip", [
                h("Icon", {
                  props: {
                    type: "ios-trash",
                    size: 20
                  },
                  style: {
                    cursor: "pointer",
                    paddingRight:'10px',
                  },
                  nativeOn: {
                    click: () => {
                      if(params.row.type == "1") {
                        this.formItem.sswl = "管理网络"
                      }else if(params.row.type == "2") {
                        this.formItem.sswl = "业务网络"
                      }else if(params.row.type == "3") {
                        this.formItem.sswl = "存储网络"
                      }
                      this.$Modal.confirm({
                          title: '删除物理节点',
                          content: '<p>是否删除<span sytle="color:green">'+this.formItem.sswl+'</span>下的<span style="color:red">'+params.row.name+'</span>节点？</p>',
                          onOk: () => {
                            networkTablePhysicalDelete({data:{id:params.row.id}}).then(em=>{
                              this.physicalNetwork()
                            })
                          },
                          onCancel: () => {
                               
                          }
                      });
                    }
                  }

                }),
                h('span', {slot: 'content', style: {whiteSpace: 'normal', wordBreak: 'break-all'}}, "删除")
              ]),
            ])
          },
        },
      ],
      GLdata:[],
      // 业务网络
      YWdata:[],
      // 存储网络
      CCdata:[],
      // 新建网络
      newWLnode:false,
      networkGroop:[
        { label:'管理网络',value:"1" },
        { label:'业务网络',value:"2" },
        { label:'存储网络',value:"3" },
      ],
      formItem: {
        sswl:'',
        type:'1',
        id:0,
        name:'',
        cidr:'',
        vlanid:'',
        bridge:'',
        bonding:'',
        devinfo:'',
        speed:'',
      },
      // 编辑网络
      editWLnode:false,
      editFormItem: {
        id:0,
        type:'',
        name:'',
        cidr:'',
        vlanid:'',
        bridge:'',
        bonding:'',
        devinfo:'',
        speed:'',
      },
    }
  },
  created() {
    this.physicalNetwork()
  },
  methods: {
    // 物理网络表格数据
    physicalNetwork() {
      this.GLdata = new Array()
      this.YWdata = new Array()
      this.CCdata = new Array()
      networkTablePhysicalQuery().then(em=>{
        for(var i=0;i<em.data.length;i++){
          if(em.data[i].type == "1") {
            this.GLdata.push(em.data[i])
          }else if(em.data[i].type == "2") {
            this.YWdata.push(em.data[i])
          }else if(em.data[i].type == "3") {
            this.CCdata.push(em.data[i])
          }
        }
      })
    },
    cancel() {

    },
    newWLnodeOk() {
      let formData = new Object()
      formData.type = this.formItem.type
      formData.name = this.formItem.name
      formData.cidr = this.formItem.cidr
      formData.vlanid = this.formItem.vlanid
      formData.bridge = this.formItem.bridge
      formData.bonding = this.formItem.bonding
      formData.devinfo = this.formItem.devinfo
      formData.speed = this.formItem.speed
      networkTablePhysicalNewBuilt(formData).then(em=>{
        this.physicalNetwork()
      })
    },
    editWLnodeOk() {
      networkTablePhysicalModify(this.editFormItem).then(em=>{
        this.physicalNetwork()
      })
    },
  }
}
</script>
