<style lang="less">
@import "./safeGroupTab.less";
</style>>
<template>
  <div class="security_group_area">
    <!-- 侧边虚拟机文件夹组 -->
    <Menu theme="light" :active-name="activeID" @on-select="menu_select">
      <p>安全组</p>
      <div class="security_group_group">
        <Tooltip content="新建安全组" placement="top-start" v-show="powerAcitons.anquanzutianjia">
          <Button type="text" @click="newAQZclik" style="padding: 0px" icon="md-add-circle"></Button>
        </Tooltip >
        <Tooltip content="编辑安全组" placement="top" v-show="powerAcitons.anquanzubianji">
          <Button type="text" @click="editAQZclik" style="padding: 0px" icon="ios-create" :disabled="defaultAQZ"></Button>
        </Tooltip >
        <Tooltip content="刷新安全组" placement="top" v-show="powerAcitons.anquanzushuaxin">
          <Button type="text" @click="refreshAQZ" style="padding: 0px" :loading="loadRefresh">
            <span v-if="!loadRefresh"><Icon type="md-refresh" /></span>
            <span v-else></span>
          </Button>
        </Tooltip >
        <Tooltip content="删除安全组" placement="top" v-show="powerAcitons.anquanzushanchu">
          <Button type="text" @click="deletAQZ" style="padding: 0px" icon="ios-trash" :disabled="defaultAQZ"></Button>
        </Tooltip >
      </div>
      <MenuItem name='default' v-show="powerAcitons.anquanzuliebiao">default</MenuItem>
      <Menu-item
        v-show="powerAcitons.anquanzuliebiao"
        v-for="(item) in groupData"
        :name="item.id"
        :key="item.id"
        v-if="item.name!=='default'"
      >
        <Tooltip :content="item.name" placement="right">
          <span class="text_overflow security_group_menu">{{ item.name }}</span>
        </Tooltip >
        <!-- {{ item.name }} -->
      </Menu-item>
    </Menu>
    <!-- 管理规则列表数据 -->
    <div class="rule_table_area">
      <Spin fix v-if="spinShow" size="large" style="color:#ef853a">
        <Icon type="ios-loading" size=50 class="demo-spin-icon-load"></Icon>
        <div style="font-size:16px;padding:20px">Loading...</div>
      </Spin>
      <p>管理规则</p><span class="rule_table_line"></span>
      <div class="table-button-area">
        <div>
          <Button class="move_in" @click="addRuleClick" v-show="powerAcitons.anquanzutianjia"><span class="icon iconfont icon-a-17Edaoru2"></span> 添加规则</Button>
          <Button class="close_btn" @click="deletRule(tableSelec)" v-show="powerAcitons.anquanzushanchu"><span class="icon iconfont icon-a-17Hdaochu"></span> 删除规则</Button>
        </div>
        <div >
          <!-- <Input v-model="httpTableData.search_str" search enter-button placeholder="请输入名称" style="width: 300px" @on-search="tableSearchInput" /> -->
        </div>
      </div>
      <div class="table_currency_area" v-show="powerAcitons.anquanzuliebiao">
        <Table :columns="ruleColumn" :data="ruleData" @on-selection-change="tableChange">
          <!-- 方向 -->
          <template v-slot:direction="{ row }">
            <span>{{ row.direction == "ingress"?"入口":"出口" }}</span>
          </template>
          <!-- IP协议 -->
          <template v-slot:protocol="{ row }">
            <span>{{ row.protocol !== null?row.protocol:"所有协议" }}</span>
          </template>
          <!-- 端口范围 -->
          <template v-slot:port_range_max="{ row }">
            <span>{{ portRange(row) }}</span>
          </template>
          <!-- CIDR -->
          <template v-slot:remote_ip_prefix="{ row }">
            <span>{{ row.remote_ip_prefix !== null?row.remote_ip_prefix:'-' }}</span>
          </template>
          <!-- 远端安全组 -->
          <template v-slot:remote_group_id="{ row }">
            <span>{{ remoteGroup(row) }}</span>
          </template>
          <!-- 描述 -->
          <template v-slot:description="{ row }">
            <span>{{ row.description !== null&&row.description !== ""?row.description:'-' }}</span>
          </template>
          <!-- 操作 -->
          <template v-slot:operation="{ row }">
            <Button class="close_btn" @click="deletRule([row])"><span class="icon iconfont icon-a-17Hdaochu"></span> 删除规则</Button>
          </template>
        </Table>
        <!-- <Table :columns="ruleColumn" :data="ruleData" @on-sort-change="sortColumn" @on-selection-change="tableChange"></Table> -->
        <div class="pages" v-if="this.ruleData.length>0">
          <!-- <Page :total="totalAll" show-total show-sizer :page-size="httpTableData.pagecount" placement="top" :page-size-opts="[10, 20, 30]" @on-change="onSelectedPage" @on-page-size-change="dataPerPage"/> -->
        </div>
      </div>
    </div>
    <!-- 新建安全组弹框 -->
    <groupNew
      :newgroupTime="newgroupTime"
      :groupNameAll="groupNameAll"
      @custom-error="customError"
      @custom-ok="customOK"
    ></groupNew>
    <!-- 编辑安全组弹框 -->
    <groupEdit
      :editgroupTime="editgroupTime"
      :groupNameAll="groupNameAll"
      :activeID="activeID"
      :activeName="activeName"
      @custom-error="customError"
      @custom-ok="customOK"
    ></groupEdit>
    <!-- <newNetwork
      :newTime="newTime"
      :vlanData="vlanData"
      :nameAll="nameAll"
      @custom-error="customError"
      @custom-ok="customOK"
    ></newNetwork> -->
    <!-- 添加规则弹框 -->
    <Modal v-model="addRuleModel" width="600" :mask-closable="false">
      <p slot="header" >
        <span>添加规则</span>
      </p>
      <Form :model="formRule" ref="formRule" :rules="rulesForm" :label-width="100">
        <FormItem label="规则">
          <Select v-model="formRule.ruleid">
            <Option v-for="item in ruleSelect" :value="item.value" :key="item.value">{{ item.label }}</Option>
          </Select>
        </FormItem>
        <FormItem label="描述">
          <Input v-model="formRule.describe" type="textarea" :rows="4" placeholder="请输入安全组描述内容" ></Input>
        </FormItem>
        <FormItem label="方向">
          <Select v-model="formRule.directionID">
            <Option v-for="item in directionData" :value="item.value" :key="item.value">{{ item.label }}</Option>
          </Select>
        </FormItem>
        <FormItem label="打开端口" v-if="openduankou">
          <Select v-model="formRule.openPortID">
            <Option v-for="item in openPortData" :value="item.value" :key="item.value">{{ item.label }}</Option>
          </Select>
        </FormItem>
        <FormItem label="类型" prop="type" v-if="typeCode">
          <Input v-model="formRule.type" placeholder="请输入-1:255的ICMP类型值范围" ></Input>
        </FormItem>
        <FormItem label="编码" prop="codes" v-if="typeCode">
          <Input v-model="formRule.codes" placeholder="请输入-1:255的ICMP类型值范围" ></Input>
        </FormItem>
        <FormItem label="编码" prop="codes" v-if="typeCode" style="display:none">
          <Input v-model="formRule.codes" placeholder="请输入-1:255的ICMP类型值范围" ></Input>
        </FormItem>
        <FormItem label="端口号" prop="portNumber" v-if="judgePort">
          <Input v-model="formRule.portNumber" placeholder="请输入大于0小于65535的整数" ></Input>
        </FormItem>
        <FormItem label="起始端口号" prop="startPort" v-if="startEnd">
          <Input v-model="formRule.startPort" placeholder="请输入大于0小于65535的整数" ></Input>
        </FormItem>
        <FormItem label="终止端口号" prop="endPort" v-if="startEnd">
          <Input v-model="formRule.endPort" placeholder="请输入大于0小于65535的整数" ></Input>
        </FormItem>
        <FormItem label="远程">
          <Select v-model="formRule.longRangeID">
            <Option v-for="item in longRangeData" :value="item.value" :key="item.value">{{ item.label }}</Option>
          </Select>
        </FormItem>
        <FormItem label="CIDR" prop="cidr" v-if="judgelongRange">
          <Input v-model="formRule.cidr" placeholder="例:192.168.1.0/24" ></Input>
        </FormItem>
        <FormItem label="安全组" prop="aqz" v-if="!judgelongRange">
          <Select v-model="formRule.aqz">
            <Option v-for="item in groupData" :value="item.id" :key="item.id">{{ item.name }}</Option>
          </Select>
        </FormItem>
        <!-- <FormItem label="以太网类型" prop="endPort" v-if="!judgelongRange">
          <Select v-model="formRule.ethernetID">
            <Option v-for="item in ethernetData" :value="item.id" :key="item.id">{{ item.name }}</Option>
          </Select>
        </FormItem> -->
      </Form>
      <div slot="footer">
        <Button type="text" @click="addRuleModel = false">取消</Button>
        <Button type="primary" @click="ruleok" :disabled="addRuleDisabled">确认</Button>
      </div>
    </Modal>
  </div>
</template>
<script>
import { powerCodeQuery } from '@/api/other'; // 权限可用性 查询

import {securityGroupQuery,securityGroupDelete,ruleTableQuery,ruleTableNewBuilt,ruleTableDelete} from '@/api/network';
import groupNew from "./groupNew.vue"; // 安全组新建
import groupEdit from "./groupEdit.vue"; // 安全组新建


export default {
  components: {
    groupNew,
    groupEdit,
  },
  props: {
    tabSelected: String,
  },
  watch: {
    tabSelected(value) {
      if(value == "安全组") {
        this.actionQuery()
      }
    },
    // 远程
    'formRule.longRangeID':{
      handler(news,old){
        if(news == "aqz") {
          this.judgelongRange = false
        }else {
          this.judgelongRange = true
        }
      }
    },
    // 端口
    'formRule.openPortID':{
      handler(news,old){
        if(news == "port") {
          this.judgePort = true
          this.startEnd = false
        }else if(news == "range") {
          this.judgePort = false
          this.startEnd = true
        }else {
          this.judgePort = false
          this.startEnd = false
        }
      }
    },
    // 规则
    'formRule.ruleid':{
      handler(news,old){
        if(news=="GZ_ICMP") {
          this.openduankou = false
          this.typeCode = true
          this.judgePort = false
          this.formRule.openPortID = 'all'
        }else if(news=="XY_ICMP" || news=="XY_TCP" || news=="XY_UDP") {
          this.openduankou = false
          this.typeCode = false
          this.judgePort = false
          this.formRule.openPortID = 'all'
         }else {
          this.openduankou = true
          this.typeCode = false
        }
      }
    },
  },
  mounted() {
    if(this.$store.state.power.networkResourceTab == '安全组') {
      this.actionQuery()
    }
  },
  data() {
    const propNumber =(rule,value,callback)=>{
      let list = /^[0-9]*$/;
      list.test(value)?callback():callback(new Error('请输入正确的端口号'))
    }
    const cidrValidator=(rule,value,callback)=>{
      let list = /^(\d{1,3}\.){3}\d{1,3}\/\d{1,2}$/
      list.test(value)?callback():callback(new Error('请输入正确的CIDR地址'))
    }
    const propstart =(rule,value,callback)=>{
      let list = /^[0-9]*$/;
      list.test(value)?callback():callback(new Error('请输入正确的端口号'))
    }
    const propend =(rule,value,callback)=>{
      let list = /^[0-9]*$/;
      if(list.test(value)) {
        this.formRule.startPort*1<value*1?callback():callback(new Error('终止端口号应大于起始端口号'))
      }else {
        callback(new Error('请输入正确的端口号'))
      }
    }
    return {
      groupData: [], // 侧边数据
      defaultAQZ: true, // 禁用图表按钮
      activeID: "default", // 组ID
      activeName: '', // 组名称
      activeDefault: {
        name:"default",
        id:""
      },

      groupNameAll:[], // 安全组名称
      newgroupTime: '', // 新建安全组
      editgroupTime: '', // 编辑安全组名称
      
      loadRefresh:false, // 刷新
      // 管理规则
      spinShow:false, // 表格刷新图表
      ruleColumn: [],
      ruleData:[],
      tableSelec:[],
      hostSLECTdata:[],
      securityroupID: '',
      securityroupName: '',
      totalAll:0, // 表格总数量
      httpTableData: {
        page: 1, // 当前页
        pagecount: 10, // 每页条数
        search_str: "", // 搜索字段
        order_type: "asc", // 排序顺序
        order_by: "", // 排序列
      },
      // 添加规则
      addRuleModel:false,
      addRuleDisabled:false,
      formRule:{
        ruleid:"GZ_TCP", // 规则
        describe:"", // 描述
        directionID:"enter", // 方向
        openPortID:"all", // 打开端口
        type:"", // 类型
        codes:"", // 编码
        portNumber:"", // 端口
        startPort:"", // 起始端口号
        endPort:"", // 终止端口号
        longRangeID:"cidr", // 远程
        aqz:"", // 安全组
        ethernetID:"ipv4", // 以太网类型
        cidr:"", // CIDR
      },
      openduankou:true,
      typeCode:false,
      judgePort:false,
      startEnd:false,
      judgelongRange:true,
      // 规则
      ruleSelect:[
        { label: "定制TCP规则",value:"GZ_TCP" },
        { label: "定制UDP规则",value:"GZ_UDP" },
        { label: "定制ICMP规则",value:"GZ_ICMP" },
        { label: "所有ICMP协议",value:"XY_ICMP" },
        { label: "所有TCP协议",value:"XY_TCP" },
        { label: "所有UDP协议",value:"XY_UDP" },
      ],
      // 方向
      directionData:[
        { label: "入口",value:"enter" },
        { label: "出口",value:"out" },
      ],
      // 打开端口
      openPortData:[
        { label: "所有端口",value:"all" },
        { label: "指定端口",value:"port" },
        { label: "端口范围",value:"range" },
      ],
      // 远程
      longRangeData:[
        { label: "CIDR",value:"cidr" },
        { label: "安全组",value:"aqz" },
      ],
      ethernetData:[
        { label: "IPv4",value:"ipv4" },
        { label: "IPv6",value:"ipv6" },
      ],
      // 正则规定集群名
      rulesForm:{
        
        portNumber:[
          { required: true, message: '必填项'},
          { validator: propNumber, trigger:'change' },
        ],
        cidr:[
          { required: true, message: '必填项', trigger: 'blur' },
          { validator: cidrValidator, trigger:'change' },

        ],
        aqz:[{ required: true, message: '必选项', trigger: 'blur' }],
        startPort:[
          { required: true, message: '必填项' },
          { validator: propstart, trigger:'change' },
        ],
        type:[{ required: true, message: '必填项', trigger: 'blur' }],
        codes:[{ required: true, message: '必填项', trigger: 'blur' }],
        endPort:[
          { required: true, message: '必填项', trigger: 'blur'},
          { validator: propend, trigger:'change' },
        ],
      },

      powerAcitons: [], // 操作权限数据
    };
  },
  methods: {
    clounmsChange(columns) {
      this.ruleColumn=[
        { type: 'selection',width: 30,align: 'center' },
        { title: "方向", key: "direction", sortable: true, width: 80, slot: "direction" },
        { title: "以太网类型", key: "ethertype",align: "center",sortable: true },
        { title: "IP协议", key: "protocol",align: "center", sortable: true, slot: "protocol" },
        { title: "端口范围", key: "port_range_max",align: "center", slot: "port_range_max" },
        { title: "CIDR", key: "remote_ip_prefix",align: "center", slot: "remote_ip_prefix" },
        { title: "远端安全组", key: "remote_group_id",align: "center", slot: "remote_group_id" },
        { title: "描述", key: "description",align: "center", slot: "description" },
        ...(this.powerAcitons.anquanzushanchuguize?[{ title: "操作", key: "operation",width:120,slot: "operation" }]:[]),
      
      ]
    },
    // 获取安全组数据
    refreshAQZ() {
      this.loadRefresh = true
      securityGroupQuery().then(callback=>{
        setTimeout(() => {
          this.loadRefresh = false
          this.groupData = callback.data
          let arr = new Array()
          callback.data.forEach(em => {
            arr.push(em.name)
            em.name=="default"?this.activeDefault.id =em.id:""
          });
          this.groupNameAll = arr
          this.formRule.aqz =this.groupData[0].id // 添加规则弹框 安全组默认数据
          this.securityGroupTable(this.activeID)
        }, 500);
      })
    },
    // 侧边虚拟机组选中事件
    menu_select(item) {
      this.activeID = item
      if(item !=="default"){
        this.defaultAQZ = false
      }else {
        this.defaultAQZ = true
      }
      this.groupData.forEach(em=>{
        if(item == em.id) {
          this.activeName = em.name
        }
      })
      this.securityGroupTable(this.activeID)
    },
    // 新建安全组 按钮
    newAQZclik(){
      this.newgroupTime = "" + new Date();
    },
    
    // 编辑安全组按钮
    editAQZclik() {
      this.editgroupTime = "" + new Date();
      this.groupData.forEach(em=>{
        if(em.id==this.activeID) {
          this.activeName = em.name
        }
      })
    },
    
    // 删除安全组
    deletAQZ() {
      this.groupData.forEach(em=>{
        if(em.id==this.activeID) {
          this.activeName = em.name
        }
      })
      this.$Modal.confirm({
        title: '删除安全组',
        content: '<p>是否删除<span style="font-weight: 600;color:red;word-wrap: break-word;">'+this.activeName+'</span>安全组?</p>',
        onOk: () => {
          securityGroupDelete({id:this.activeID,name:this.activeName}).then(callback=>{
            if(callback.data.msg=="ok") {
              this.activeID = "default"
              this.refreshAQZ()
              this.defaultAQZ = true
              this.$Message.success({
                background: true,
                closable: true,
                duration: 5,
                content: '删除安全组完成'
              });
            }else {
              this.$Message.error({
                background: true,
                closable: true,
                duration: 5,
                content: callback.data.msg
              });
            }
          })
        },
        onCancel: () => {}
      });
    },
    // 获取规则列表数据
    securityGroupTable(ids) {
      let listID = this.activeDefault.id
      this.tableSelec = new Array()
      if(ids=="default") {
        listID = this.activeDefault.id
      }else {
        listID = ids
      }
      this.spinShow =true;
      ruleTableQuery({id:listID}).then(callback=>{
        this.spinShow =false;
        this.securityroupID = callback.data.security_group.id
        this.securityroupName = callback.data.security_group.name
        this.ruleData = callback.data.security_group.security_group_rules
      })
      .catch((error) => {
        this.spinShow =false;
      });
    },
    // 勾选规则列表
    tableChange(item){
      this.tableSelec = item
    },
    // 规则列表 选择页
    onSelectedPage(item) {
      this.httpTableData.page = item;
      this.securityGroupTable(this.activeID)
    },
    // 规则列表 页条数
    dataPerPage(item) {
      this.httpTableData.pagecount = item;
      this.securityGroupTable(this.activeID)
    },
    // 规则列表 列排序
    sortColumn(column, key, order) {
      if (column.key !== "normal") {
        this.httpTableData.order_by = column.key;
        this.httpTableData.order_type = column.order;
        this.securityGroupTable(this.activeID)
      }
    },
    // 搜索规则列表
    tableSearchInput(){
      this.securityGroupTable(this.activeID)
    },
    // 添加规则按钮
    addRuleClick(){
      this.addRuleModel = true
      this.addRuleDisabled = false
      this.formRule.ruleid = "GZ_TCP"
      this.formRule.directionID = "enter"
      this.formRule.openPortID = "all"
      this.formRule.longRangeID = "cidr"
      this.formRule.ethernetID = "ipv4"
      this.$refs['formRule'].resetFields()
    },
    // 添加规则弹框
    ruleok(){
      this.addRuleDisabled = true
      this.$refs['formRule'].validate((valid) => {
        if(valid){
          this.addRuleModel = false;
          let range_min=null
          let range_max=null
          if(this.formRule.openPortID == "port"){
            range_min=this.formRule.portNumber
            range_max=this.formRule.portNumber
          }else if(this.formRule.openPortID == "range"){
            range_min=this.formRule.startPort
            range_max=this.formRule.endPort
          }
          let arr = new Object()
          arr.security_group_id = this.securityroupID
          arr.security_group_name = this.securityroupName
          switch(this.formRule.ruleid){
            case "GZ_TCP":
              arr.protocol = "tcp"
            break;
            case "GZ_UDP":
              arr.protocol = "udp"
            break;
            case "GZ_ICMP":
              arr.protocol = "icmp"
              range_min=this.formRule.type
              range_max=this.formRule.codes
            break;
            case "XY_TCP":
              arr.protocol = "tcp"
            break;
            case "XY_UDP":
              arr.protocol = "udp"
            break;
            case "XY_ICMP":
              arr.protocol = "icmp"
            break;
          }
          arr.direction = this.formRule.directionID == "enter"?"ingress":"egress"
          arr.port_range_min = range_min
          arr.port_range_max = range_max
          arr.remote_group_id = this.formRule.longRangeID=="aqz"?this.formRule.aqz:""
          arr.remote_ip_prefix = this.formRule.longRangeID=="cidr"?this.formRule.cidr:""
          arr.description = this.formRule.describe
          ruleTableNewBuilt(arr).then(callback=>{
            if(callback.data.msg == "ok") {
              this.securityGroupTable(this.activeID)
              this.$Message.success({
                background: true,
                closable: true,
                duration: 5,
                content: "规则创建成功",
              });
            }else {
              this.addRuleDisabled = false
              this.$Message.error({
                background: true,
                closable: true,
                duration: 5,
                content: callback.data.msg,
              });
            }
          })
        }else{
          this.addRuleDisabled = false
        }
      })
    },
    // 删除规则
    deletRule(item){
      if(item.length == 0){
        this.$Message.warning({
          background: true,
          closable: true,
          duration: 5,
          content: "未选择表格数据",
        });
      }else {
        let ids = new Array();
        item.forEach((em) => {
          ids.push(em.id);
        });
        this.$Modal.confirm({
          title: '删除规则',
          content: '<p>是否删除所选规则?</p>',
          onOk: () => {
            ruleTableDelete({ids:ids,names:this.securityroupName}).then(callback=>{
              if(callback.data.msg=="ok") {
                this.securityGroupTable(this.activeID)
                this.$Message.success({
                  background: true,
                  closable: true,
                  duration: 5,
                  content: '删除规则完成'
                });
              }else {
                this.$Message.error({
                  background: true,
                  closable: true,
                  duration: 5,
                  content: callback.data.msg
                });
              }
            })
          },
          onCancel: () => {}
        });
      }
    },
    cancel() {},
    // 端口范围
    portRange(row){
      let list = '所有端口'
      if(row.port_range_min!==null) {
        if(row.port_range_min == row.port_range_max) {
          list = row.port_range_min
        }else {
          list = row.port_range_min+'-'+row.port_range_max
        }
      }
      return list
    },
    // 远端安全组
    remoteGroup(row){
      let list = '所有端口'
      let ids = row.remote_group_id?row.remote_group_id:row.security_group_id
      this.groupNameAll.forEach(em=>{
        if(ids == em.id) {
          list = em.name
        }
      })
      return list
    },
    // 子组件返回错误
    customError(data) {
      this.$Message.error({
        background: true,
        closable: true,
        duration: 5,
        content: data,
      });
    },
    // 子组件返回成功
    customOK(data) {
      this.refreshAQZ()
      this.$Message.success({
        background: true,
        closable: true,
        duration: 5,
        content: data,
      });
    },
    // 操作权限获取
    actionQuery(item){
      powerCodeQuery({
        module_code:[
          'anquanzuliebiao',
          'anquanzutianjia',
          'anquanzushanchu',
          'anquanzubianji',
          'anquanzushuaxin',
          'anquanzutianjiaguize',
          'anquanzushanchuguize',
        ]
      }).then(callback=>{
        this.powerAcitons = callback.data.data
        this.powerAcitons.anquanzuliebiao?this.refreshAQZ():null
        this.clounmsChange()
      })
    },
  },
};
</script>
