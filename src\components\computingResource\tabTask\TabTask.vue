<style scoped>
@import "../computingResource.less";
</style>
<template>
  <div class="task_area">
    <!-- <Button class="close_btn" @click="shuaxin">刷新</Button> -->
    <div class="radio_chart_select">
      <div class="radio_area">
        <div class="radio_piece" v-for="every in radioData" @click="radioClick(every.lable)">
          <span class="radio_title" :style="{color:trendItem==every.lable?'#121529':'#717379'}">{{every.title}}</span>
          <span class="radio_selected" :style="{background:trendItem==every.lable?'#fe6902':''}"></span>
        </div>
      </div>
      <div style="float: right">
        <Input  v-model="tablePageForm.search_str" search enter-button placeholder="请输入名称" style="width: 300px" @on-search="tableSearch" />
      </div>
    </div>
    <div class="table_currency_area">
      <Table :columns="taskColumn" :data="taskData" @on-selection-change="taskSlect"  @on-sort-change="sortColumn">
        <!-- 创建时间 -->
        <template v-slot:dest_node="{row}">
          <span>{{row.dest_node?row.dest_node:"-"}}</span>
        </template>
        <!-- 状态 -->
        <template v-slot:status="{row}">
          <span :style="{color:stateTranslation('color',row.status)}">{{ stateTranslation('text',row.status) }}</span>
        </template>
      </Table>
      <!-- 虚拟机表格 分页  -->
      <div class="pages" v-if="this.taskData.length>0">
        <Pagination  :total="tableTotal" :page-size="tablePageForm.pagecount"  @page-change="onPageChange" @page-size-change="onPageSizeChange"/>
      </div>
    </div>
  </div>
</template>
<script>
// document.addEventListener('keydown', function(event) {
//     if (event.ctrlKey && event.altKey) {
//       alert("同时按下了Ctrl和Alt键")
//     }
// });
import {
  newVMtask, // 新建虚拟机任务
  migrationVMtask, // 迁移虚拟机任务
} from '@/api/virtualMachine';
import Pagination from '@/components/public/Pagination.vue';
export default {
  components: {
    Pagination
  },
  props: {
    tabSelected: String,
  },
	data(){
		return{
      trendItem:"new",
      radioData:[
        {title:"新建虚拟机任务",lable:"new"},
        {title:"迁移虚拟机任务",lable:"migrate"},
      ],
			// 镜像表格数据
			taskColumn:[],
			taskData:[],
			tableSelectData:[],
      // 分页
      tablePageForm: {
        page: 1, // 当前页
        pagecount: this.$store.state.power.pagecount, // 每页条
        search_str: "", // 收索
        order_type: "desc", // 排序规则
        order_by: "", // 排序列
      },
      // 虚拟机分页总条数
      tableTotal: 0,
      timedRefresh:null,
		}
	},
  watch: {
    tabSelected(value) {
      if(value=='任务'){
        this.radioClick(this.trendItem)
      }else {
        clearInterval(this.timedRefresh)
      }
    },
  },
  mounted() {
    if(this.$store.state.power.computingResourceTab == '任务') {
      this.radioClick(this.trendItem)
    }
  },
  updated() {
    this.tablePageForm.pagecount=this.$store.state.power.pagecount
  },
	methods: {
    // 切换任务
    radioClick(item){
      this.trendItem = item
      if(item == "new"){
        this.taskColumn = [
          // {type: 'selection',width: 30,align: 'center'},
          { title: "名称", key: "name" },
          { title: "集群组", key: "availability_zone",align: "center" },
          { title: "创建方式", key: "iso",align: "center"},
          { title: "系统类型", key: "os_type",align: "center" },
          { title: "创建时间", key: "created_at",align: "center",
            render: (h, params) => {
              return h("span",this.creatTime(params.row.created_at))
            }
          },
          { title: "状态", key: "status",align: "center",slot: 'status'},
        ]
        this.tableTotal = 0
        this.tablePageForm.page = 1
        clearInterval(this.timedRefresh)
        this.timedRefresh = setInterval(() => {
          this.taskTableQuery()
        },5000)
        this.taskTableQuery()
      }else {
        this.taskColumn = [
          // {type: 'selection',width: 30,align: 'center'},
          { title: "名称", key: "name" },
          { title: "源主机", key: "source_compute",align: "center" },
          { title: "目标主机", key: "dest_node",align: "center",slot: 'dest_node'},
          { title: "创建时间", key: "created_at",align: "center",
            render: (h, params) => {
              return h("span",this.creatTime(params.row.created_at))
            }
          },
          { title: "变更时间", key: "updated_at",align: "center",
            render: (h, params) => {
              return h("span",this.creatTime(params.row.updated_at))
            }
          },
          { title: "状态", key: "status",align: "center",slot: 'status'}
        ]
        this.tableTotal = 0
        this.tablePageForm.page = 1
        clearInterval(this.timedRefresh)
        this.timedRefresh = setInterval(() => {
          this.migrateTableQuery()
        },5000)
        this.migrateTableQuery()
      }
    },
		// 表格数据
		taskTableQuery() {
      newVMtask(this.tablePageForm).then(callback=>{
        this.tableTotal = callback.data.total
        this.taskData = callback.data.data
			}).catch((error) => {
      });
		},
    // 表格数据
		migrateTableQuery() {
      migrationVMtask(this.tablePageForm).then(callback=>{
        if(callback.data.msg =="error"){
          this.taskData = []
        }else {
          this.tableTotal = callback.data.total
          this.taskData = callback.data.data
        }
			}).catch((error) => {
        
      });
		},
    
		// 表格选中数据
		taskSlect(item) {
			this.tableSelectData = item
      let name = new Array()
      let id = new Array()
			item.forEach(em=>{
				name.push(em.name)
				id.push(em.id)
			})
      this.tableSelectName = name
      this.tableSelectID = id
		},
     // 当前分页
    onPageChange(item) {
      this.tablePageForm.page = item;
      this.taskTableQuery();
    },
    // 每页条数
    onPageSizeChange(item) {
      this.$store.state.power.pagecount = item
      this.tablePageForm.pagecount = this.$store.state.power.pagecount
      this.tablePageForm.page = 1
      this.tableTotal = 0
      this.tablePageForm.search_str = ""
      this.taskTableQuery();
    },
    // 虚拟机列表 列排序
    sortColumn(column, key, order) {
      if (column.key !== "normal") {
        this.tablePageForm.order_by = column.key;
        this.tablePageForm.order_type = column.order;
        this.tablePageForm.page = 1
        this.tableTotal = 0
        this.tablePageForm.search_str = ""
        this.taskTableQuery();
      }
    },
    // 搜索
    tableSearch(){
      this.tablePageForm.page = 1
      this.tableTotal = 0
      this.taskTableQuery();
    },
		cancel() { },
    shuaxin(){
      this.$Modal.info({
          title: "新建虚拟机",
          content: "新建虚拟机创建中，任务列表 可查看任务详情!"
      });
      setTimeout(() => {
        this.$Modal.remove();
      }, 5000);
    },
    creatTime(datetime){
      let time = new Date(datetime)//时间戳为10位要*1000，13位不用*1000
      let year = time.getFullYear()
      let month = time.getMonth() + 1
      let date = time.getDate()
      let hours = time.getHours()
      let minute = time.getMinutes()
      let second = time.getSeconds()
      if (month < 10) { month = '0' + month }
      if (date < 10) { date = '0' + date }
      if (hours < 10) { hours = '0' + hours }
      if (minute < 10) { minute = '0' + minute }
      if (second < 10) { second = '0' + second }
      return year + '-' + month + '-' + date + ' ' + hours + ':' + minute + ':' +second
    },
    
    // 状态转译
    stateTranslation(selec,item){
      let text = "进行中";
      let colo = "#eba275"
      switch (item) {
        case "complete":
          text =  "完成";
          colo = "#84e79f"
          break;
        case "complete":
          text =  "完成";
          colo = "#84e79f"
          break;
        case "failed":
          text =  "失败";
          colo = "red"
          break;
        case "error":
          text =  "错误";
          colo = "red"
          break;
        case "completed":
          text =  "完成";
          colo = "#84e79f"
          break;
        case "migrating":
          text = "进行中";
          colo = "#eba275"
          break;
        case "reverted":
          text = "撤销";
          colo = "#ccc"
          break;
        case "confirmed":
          text = "完成";
          colo = "#84e79f"
          break;
      }
      if(selec == "color"){
        return colo
      }else {
        return text
      }
    }
	},
  beforeDestroy() {
    clearInterval(this.timedRefresh)
  },
}
</script>
