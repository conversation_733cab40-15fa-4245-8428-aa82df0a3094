{"_from": "raw-body@2.5.1", "_id": "raw-body@2.5.1", "_inBundle": false, "_integrity": "sha512-qqJBtEyVgS0ZmPGdCFPWJ3FreoqvG4MVQln/kCgF7Olq95IbOp0/BWyMwbdtn4VTvkM8Y7khCQ2Xgk/tcrCXig==", "_location": "/raw-body", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "raw-body@2.5.1", "name": "raw-body", "escapedName": "raw-body", "rawSpec": "2.5.1", "saveSpec": null, "fetchSpec": "2.5.1"}, "_requiredBy": ["/body-parser"], "_resolved": "https://registry.npmmirror.com/raw-body/-/raw-body-2.5.1.tgz", "_shasum": "fe1b1628b181b700215e5fd42389f98b71392857", "_spec": "raw-body@2.5.1", "_where": "/root/docker/node_modules/body-parser", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "bugs": {"url": "https://github.com/stream-utils/raw-body/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "dependencies": {"bytes": "3.1.2", "http-errors": "2.0.0", "iconv-lite": "0.4.24", "unpipe": "1.0.0"}, "deprecated": false, "description": "Get and validate the raw body of a readable stream.", "devDependencies": {"bluebird": "3.7.2", "eslint": "7.32.0", "eslint-config-standard": "14.1.1", "eslint-plugin-import": "2.25.4", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "5.2.0", "eslint-plugin-standard": "4.1.0", "mocha": "9.2.1", "nyc": "15.1.0", "readable-stream": "2.3.7", "safe-buffer": "5.2.1"}, "engines": {"node": ">= 0.8"}, "files": ["HISTORY.md", "LICENSE", "README.md", "SECURITY.md", "index.d.ts", "index.js"], "homepage": "https://github.com/stream-utils/raw-body#readme", "license": "MIT", "name": "raw-body", "repository": {"type": "git", "url": "git+https://github.com/stream-utils/raw-body.git"}, "scripts": {"lint": "eslint .", "test": "mocha --trace-deprecation --reporter spec --bail --check-leaks test/", "test-ci": "nyc --reporter=lcovonly --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "version": "2.5.1"}