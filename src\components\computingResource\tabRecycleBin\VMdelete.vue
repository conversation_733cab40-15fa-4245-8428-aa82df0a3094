<template>
  <div>
    <Modal
      v-model="model"
      width="600"
      :mask-closable="false"
    >
      <template #header><p>彻底删除虚拟机</p></template>
      <div style="padding: 5px">
        <span>是否彻底删除下列虚拟机？</span>
        <p style="color:red;word-wrap: break-word">{{tableNames.toString()}}</p>
        <div style="padding:15px 5px">
          <!-- <Checkbox v-model="reserveDisk">保留磁盘</Checkbox> -->
        </div>
      </div>
      <div slot="footer">
        <Button type="text" @click="model = false">取消</Button>
        <Button type="primary" @click="modelOK" :disabled="disabled">确认</Button>
      </div>
    </Modal>
  </div>
</template>
<script>

import { recyCompletelyDeleteVM } from '@/api/virtualMachine';  // 彻底删除虚拟机
export default {
  props: {
    tableDelet: Array,
    deleteTime: String,
  },
  watch: {
    deleteTime(news){
      this.tableNames = this.tableDelet.map(em=>{ return em.name})
      this.tableIDS = this.tableDelet.map(em=>{ return em.id})
      this.macs = this.tableDelet.map(em=>{ return em.ip.map(i=>{return i['OS-EXT-IPS-MAC:mac_addr']})})
      this.model = true
      this.disabled = false
      this.reserveDisk = false 
    }
  },
  data(){
    
    return {
      model:false,
      disabled: false,
      reserveDisk: false,
      tableNames: [],
      tableIDS: [],
      macs: []
    }
  },
  methods: {
    modelOK() {
      this.disabled = true
      if(this.reserveDisk) {
        console.log("保留磁盘")
      }else {
        recyCompletelyDeleteVM({
          names: this.tableNames,
          ids: this.tableIDS,
          mac_addr: this.macs,
          // ...(this.action_reserve_disk? "" : { volume_status: this.action_reserve_disk }),
        })
        .then((callback) => {
          if(callback.data.msg == 'ok') {
            this.model = false;
            this.$emit("return-ok",'彻底删除虚拟机完成');
          }else {
            this.disabled = false
            this.$emit("return-error",callback.data.msg);
          }
        })
        .catch((error) => {
          this.disabled = false
          this.$emit("return-error",'彻底删除虚拟机失败');
        })
      }
    },
  }
}
</script>