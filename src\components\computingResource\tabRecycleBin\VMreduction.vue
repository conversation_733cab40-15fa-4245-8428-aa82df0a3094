<template>
  <div>
    <Modal
      v-model="model"
      width="600"
      :mask-closable="false"
    >
      <template #header><p>还原虚拟机</p></template>
      <div style="padding: 5px">
        <span>是否还原下列虚拟机？</span>
        <p style="color:green;word-wrap: break-word">{{tableNames.toString()}}</p>
      </div>
      <div slot="footer">
        <Button type="text" @click="model = false">取消</Button>
        <!-- <Button type="primary" :loading="disabled" @click="modelOK">
          <span v-if="!disabled">确认</span>
          <span v-else>还原中</span>
        </Button> -->
        <Button type="primary" @click="modelOK" :disabled="disabled">确认</Button>
      </div>
    </Modal>
  </div>
</template>
<script>
import { vmGroupTableAction } from '@/api/virtualMachine';  // 虚拟机组表格 操作
export default {
  props: {
    tableReduction: Array,
    reductionTime: String,
  },
  watch: {
    reductionTime(news){
      this.tableNames = this.tableReduction.map(em=>{ return em.name})
      this.tableIDS = this.tableReduction.map(em=>{ return em.id})
      this.model = true
      this.disabled = false
    }
  },
  data(){
    return {
      model:false,
      disabled: false,
      tableNames: [],
      tableIDS: [],
    }
  },
  methods: {
    modelOK() {
      this.disabled = true
      for (let i = 0; i < this.tableIDS.length; i++) {
        if(i==this.tableIDS.length-1) {
          vmGroupTableAction({
            id: this.tableIDS[i],
            name: this.tableNames[i],
            action: 'restore',
            data: '',
          })
          .then((callback) => {
            this.model = false;
            if(callback.data.msg == 'ok') {
              this.$emit("return-ok",this.tableNames[i]+'还原虚拟机完成');
            }else {
              this.$emit("return-error",this.tableNames[i]+callback.data.msg);
            }
          })
          .catch((error) => {
            this.$emit("return-error",this.tableNames[i]+'还原虚拟机失败');
          })
        }else {
          vmGroupTableAction({
            id: this.tableIDS[i],
            name: this.tableNames[i],
            action: 'restore',
            data: '',
          })
          .then((callback) => {
            // this.model = false;
            if(callback.data.msg == 'ok') {
              // this.$emit("return-ok",this.tableNames[index]+'还原虚拟机完成');
            }else {
              this.$emit("return-error",this.tableNames[i]+callback.data.msg);
            }
          })
          .catch((error) => {
            this.$emit("return-error",this.tableNames[i]+'还原虚拟机失败');
          })
        }
      }
    }
  }
}
</script>