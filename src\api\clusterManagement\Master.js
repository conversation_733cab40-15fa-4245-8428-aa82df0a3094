// import request from "@/utils/request";

// 查询主机table列表
export function getHostTableList(query) {
  return request({
    url: "/api/api/host",
    method: "get",
    params: query
  });
}

// 新增主机数据
export function addHost(data) {
  return request({
    url: "/api/api/host",
    method: "post",
    data: data
  });
}

//删除主机数据
export function delHost(hostName) {
  return request({
    url: "/api/api/host/" + hostName,
    method: "delete"
  });
}

//关闭节点
export function closeHost(data) {
  return request({
    url: `/v2/v2/shutNodeByHost?hostName=${data.hostname}&usrname=${data.usrname}`,
    method: "post",
    data: data
  });
}
//重启节点
export function rebootHost(data) {
  return request({
    url: `/v2/v2/rebootNodeByHost?hostName=${data.hostname}&usrname=${data.usrname}`,
    method: "post",
    data: data
  });
}

// 运行服务
export const SERVERS_TYPE = {
  mds: "元数据",
  mgr: "API服务",
  mon: "管理",
  osd: "存储单元",
  rgw: "对象存储",
  "tcmu-runner": "iscsi存储"
};
