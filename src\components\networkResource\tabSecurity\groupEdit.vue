<template>
  <Modal v-model="model" width="600" :mask-closable="false">
    <template #header><p>编辑安全组名称</p></template>
    <Form :model="formItem" ref="formItem" :rules="rulesForm" :label-width="150">
      <FormItem label="当前安全组名称">
        <Input v-model="activeName" disabled></Input>
      </FormItem>
      <FormItem label="安全组新名称" prop="name">
        <Input v-model="formItem.name" placeholder="请输入安全组新名称" ></Input>
      </FormItem>
    </Form>
    <div slot="footer">
      <Button type="text" @click="model = false">取消</Button>
      <Button type="primary" @click="modelOK" :disabled="disabled">确认</Button>
    </div>
  </Modal>
</template>
<script>
import {securityGroupModify} from '@/api/network';

export default {
  props: {
    editgroupTime: String,
    activeName: String,
    activeID: String,
    groupNameAll: Array,
  },
  watch: {
    editgroupTime(news){
      // this.formItem.title = '编辑 '+this.activeName+' 安全组名称'
      this.$refs.formItem.resetFields();
      this.model = true
      this.disabled = false
    }
  },
  data(){
    // 名称
    const propName = (rule, value, callback) => {
      let list = /^[\u4e00-\u9fa5_a-zA-Z0-9@_.-]{2,32}$/;
      if(list.test(value)) {
        if(this.groupNameAll.indexOf(value)==-1) {
          callback()
        }else {
          callback(new Error('安全组名称已存在'))
        }
      }else {
        callback(new Error('2-32 个中文、英文、数字、特殊字符(@_.-)'))
      }
    };
    return {
      model: false,
      disabled: false,
      formItem:{
        title:'',
        name:'',
        describe: '',
      },
      
      // 正则验证
      rulesForm: {
        name: [
          { required: true, message: "必填项", trigger: "change" },
          { validator: propName, trigger: "change" },
        ],
      },
    }
  },
  methods: {
    // 编辑网络确认事件
    modelOK() {
      this.$refs.formItem.validate((valid) => {
        if(valid){
          this.disabled = true
          securityGroupModify({id:this.activeID,name:this.formItem.name,desc:""})
          .then(callback=>{
            if(callback.data.msg == 'ok'){
              this.$emit("custom-ok",'编辑安全组完成');
              this.model = false;
            }else {
              this.$emit("custom-error",callback.data.msg);
              this.disabled = false;
            }
          })
          .catch((error) => {
            this.$emit("custom-error",'编辑安全组失败');
            this.disabled = false;
          });
        }
      })
    },
    
  }
}
</script>
