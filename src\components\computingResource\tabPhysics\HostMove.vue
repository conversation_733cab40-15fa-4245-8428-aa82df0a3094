<template>
  <Modal v-model="model" width="600" :mask-closable="false">
    <template #header><p>主机移入<span style="green">{{groupSelect.name}}</span>集群组</p></template>
    <Spin fix v-if="spinShow" size="large" style="color:#ef853a">
      <Icon type="ios-loading" size=50 class="demo-spin-icon-load"></Icon>
      <div style="font-size:16px;padding:20px">Loading...</div>
    </Spin>
    <Table
      :columns="tableColumn"
      :data="tableData"
      no-data-text="暂无空闲主机"
      @on-selection-change="tableChange"
    >
      <template v-slot:state="{ row }">
        {{ row.state =="up" ? '良好' : "错误" }}
      </template>
      <template v-slot:status="{ row }">
        {{ row.status =="enabled" ? '已启动' : "未启动" }}
      </template>
    </Table>
    <div slot="footer">
      <Button type="text" @click="model = false">取消</Button>
      <Button type="primary" @click="modelOK" :disabled="disabled">确认</Button>
    </div>
  </Modal>
</template>
<script>
import {
  hostUnusedQuery, // 主机未使用 查询
  clusterGroupMovein, // 集群组 移入
} from '@/api/physics';
export default {
  props: {
    moveTime: String,
    groupSelect: Object,
  },
  watch: {
    moveTime(news){
      // this.formItem.title = '主机移入 '+this.groupSelect.name+ ' 集群组'
      this.formItem.groupID = this.groupSelect.id
      this.hostQuery()
      this.model = true
      this.disabled = false
    }
  },
  data() {
    
    return {
      model: false,
      disabled: false,
      formItem:{
        title: '',
        groupID: null,
      },
      spinShow: false,
      tableColumn: [
        { type: 'selection',width: 30,align: 'center' },
        { title: '主机',key: 'hypervisor_hostname' },
        { title: '健康状态',key: 'state',align: "center", slot: "state" },
        { title: '运行状态',key: 'status',align: "center", slot: "status" }
      ],
      tableData:[],
      tableSelect:[],
    };
  },
  methods: {
    // 查询剩余主机
    hostQuery(){
      this.spinShow = true
      hostUnusedQuery()
      .then(callback =>{
        this.spinShow =false;
        this.tableData = callback.data
      })
      .catch((error) => {
        this.spinShow =false;
      });
    },
    // 表格选中
    tableChange(item) {
      this.tableSelect = item
    },
    modelOK() {
      this.disabled = true
      if(this.tableSelect.length !== 0) {
        this.model = false
        for(var i=0;i<this.tableSelect.length;i++) {
          if(i==this.tableSelect.length-1) {
            clusterGroupMovein({id:this.formItem.groupID,name:this.groupSelect.name,hostname:this.tableSelect[i].hypervisor_hostname})
            .then(callback=>{
              this.model = false
              this.$emit("host-ok",'移入主机操作完成');
            })
          }else{
            clusterGroupMovein({id:this.formItem.groupID,name:this.groupSelect.name,hostname:this.tableSelect[i].hypervisor_hostname})
          }
        }
      }else {
        this.disabled = false
        this.$emit("host-error",'未选择主机');
      }
    },
  },
};
</script>