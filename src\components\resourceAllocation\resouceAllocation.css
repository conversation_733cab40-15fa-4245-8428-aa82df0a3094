/* 分配虚拟机 */
.vm_allocation_background {
  height:100%;
  padding:15px 15px 0 15px;
}
.vm_allocation_area {
  height:100%;
  padding:5px;
  background:#fff;
}
.vm_allocation_btn {
  padding: 10px 0;
  display: flex;
  justify-content: space-between;
}
.vm_fenpei_table {
  height: calc(100% - 120px);
  overflow: auto;
}
.vm_allocation_title {
  width: 100%;
  font-size: 14px;
  padding-left: 10px;
  border-left: 4px solid #fb6129;
  display: inline-block;
  color: #333;
}
.ivu-menu-light.ivu-menu-vertical .ivu-menu-item-active:not(.ivu-menu-submenu) {
  color: #fff;
  background: #fb6129;
}
.ivu-menu-light.ivu-menu-vertical .ivu-menu-item:hover,
.ivu-menu-light.ivu-menu-vertical .ivu-menu-submenu-title:hover {
  color: #333;
  background: #ffe9e1;
}
.ivu-menu-light.ivu-menu-vertical
  .ivu-menu-item-active:not(.ivu-menu-submenu):after {
  background: none;
}
#lunbo {
  /* transform: translate3d(0px, -452px, 0px); */
  /* will-change: transform; */
}
#lunbo>li {
  width: 100%;
  padding: 10px 0;
  height: 475px;
}
#lunbo>li .ivu-form-item {
  padding: 0 0 10px 0;
}
.vm_allocation_menu .ivu-menu-light {
  width: 160px!important;
  min-width: 160px!important;
  max-width: 160px!important;
}
.vm_allocation_menu {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
}
.modalcontent {
  width: 750px ;
  padding: 0 5px 0 15px;
}
.xinxigailan > span {
  width: 100px;
  display: inline-block;
  text-align: right;
  margin-bottom: 20px;
}
.xinxigailan > div {
  display: flex;
  flex-direction: row;
  justify-content: space-evenly;
}
.xinxigailan .imitateInput{
  width: 180px;
  display: inline-block;
  height:32px;
  line-height:32px;
  background: #f3f3f3;
  border: 1px solid #ccc;
  cursor: not-allowed;
  padding-left:5px;
  margin-left:5px;
  overflow: hidden;
}
.imitateTitle {
  display: inline-block;
  width: 110px;
  text-align: right;
}
.xinxigailan ul li{
  padding: 10px 1px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.ivu-btn:focus{
  box-shadow: none!important;
}
.xinxigailan {
  padding: 30px 0;
}
.cipan {
  display: block;
}
.cipan >div>ul> li {
  display: flex;
  flex-direction: row;
  justify-content: space-evenly;
  align-items: center;
  margin-bottom: 24px;
}
.cipan span {
  width: 100px;
  text-align: right;
}
