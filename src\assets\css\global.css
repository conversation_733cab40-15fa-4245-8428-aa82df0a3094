html,
body,
#app {
  height: 100%;
  width: 100%;
  margin: 0;
  padding: 0;
  min-width: 1024px;
  min-height: 600px;
  color:#333;
  font-family: "Microsoft YaHei";
  font-size: 14px;
  /* user-select: none; */
}
ul li {
  list-style: none;
}
/* 全局按钮颜色 */
/* 创建 */
.new_built {
  background:#ff6902;
  color: #fff;
  border:none
}
.new_built:hover {
  background:#e55c00;
  color: #fff;
  border:none
}
.new_built:focus {
  background:#cc5300;
  color: #fff;
  border:none
}
/* 删除 */
.delet_btn {
  background:#fd1f1a;
  color: #fff;
  border:none
}
.delet_btn:hover {
  background:#e31a17;
  color: #fff;
  border:none
}
.delet_btn:focus {
  background:#c91714;
  color: #fff;
  border:none
}
/* 其他按钮 */
.other_btn {
  background:#3a8fea;
  color: #fff;
  border:none
}
.other_btn:hover {
  background:#3480d1;
  color: #fff;
  border:none
}
.other_btn:focus {
  background:#2e70b8;
  color: #fff;
  border:none
}
/* 次要 */
.secondary_btn {
  background:#ffff;
  color: #ff6902;
  border:1px solid #ff6902;
}
.secondary_btn:hover {
  background:#ffdbc3;
  color: #ff6902;
  border:1px solid #ff6902;
}
.secondary_btn:focus {
  background:#ff6902;
  color: #fff;
  border:1px solid #ff6902;
}
/* 禁用 */
.forbidden {
  background:#f7f2f3;
  border:1px solid #e2e2e2;
}

/* 新建 */
.plus_btn {
  background:#fb6129;
  color: #fff;
  border:none
}
.plus_btn:hover {
  background:#da4c18;
  color: #fff;
  border-color: #fb6129;
}
/* 删除 */
.close_btn {
  background:#fd1f1a;
  color: #fff;
  border:none
}
.close_btn:hover {
  background:#d51a16;
  color: #fff;
  border-color: #fd1f1a;
}
/* 还原 */
.rollback {
  background:#fb6129;
  color: #fff;
  border:none
}
.rollback:hover {
  background:#da4c18;
  color: #fff;
  border-color: #fb6129;
}
/* 移入 */
.move_in {
  background:#fb6129;
  color: #fff;
  border:none
}
.move_in:hover {
  background:#da4c18;
  color: #fff;
  border-color: #fb6129;
}
/* 移出 */
.remove {
  background:#529ae8;
  color: #fff;
  border:none
}
.remove:hover {
  background:#3281d7;
  color: #fff;
  border-color: #529ae8;
}
/* 大屏按钮 */
.nav_large_screen {
  color: #999;
  padding:0 5px;
  cursor: pointer;
  font-size:24px;
}
.nav_large_screen:hover {
  color:blue;
}

.ivu-badge-count {
  top: 3px;
}
/* 首页 */
.logo_thxh {
  display:inline-block;
}
.logo_thxh div {
  height: 50px;
  padding: 0 10px;
  display: flex;
  align-items: center;
}
.logo_thxh img {
  height:30px;
}
.nav_fold {
  width: 35px;
  line-height: 50px;
  border-right: 2px solid #f6f4f4;
  margin-left: 10px;
  display: inline-block;
  position: absolute;
}
.user_setup {
  float: right;
  line-height: 50px;
  display: inline-block;
  font-size: 22px;
  display: flex;
  align-items: center;
  justify-content: space-around;
}
.assistant:hover{
  cursor:pointer;
}
.nav_icon {
  line-height: 50px;
  padding: 0 10px;
}
.messages {
  font-size: 26px;
  line-height: 50px;
  cursor: pointer;
}

/* 头部及侧边导航 */
.layout {
  height:100%;
  overflow: hidden;
}

.ivu-layout {
  height: 100%;
}

.layout-header-bar {
  height: 50px;
  padding: 0;
  position: relative;
  z-index: 999;
  background: #fff;
  box-shadow: 0 5px 10px #ddd;
  margin-bottom: 1px;
}

/* 表格 */
/* .ivu-table td, .ivu-table th {
  height: 35px;
} */
.ivu-table-cell {
  padding-left: 5px;
  padding-right: 5px;
}
.ivu-table-cell .ivu-btn:hover {
  color: #fb6129;
  border-color: #fb6129;
}
.ivu-table-wrapper {
  overflow: initial;
}
.ivu-table{
  color: #666;
  font-weight: 400
}

/* 侧边导航 */
/* .ivu-menu .iconfont{
  font-size:16px;
} */
.ivu-layout-sider-children {
  background: #fff
}
.menu_area {
  height:calc(100% - 250px);
  padding-top:20px;
  overflow: auto
}
.ivu-menu-vertical .ivu-menu-item, .ivu-menu-vertical .ivu-menu-submenu-title {
  padding:10px;
  margin: 0 20px 20px 20px;
  border-radius: 5px;
}
/* 选中后的导航 */
.ivu-menu-light.ivu-menu-vertical .ivu-menu-item-active:not(.ivu-menu-submenu) {
  color: #fff6f3;
  background: #fb6129;
}
/* 浮动后的导航 */
.ivu-menu-light.ivu-menu-vertical .ivu-menu-item:hover, .ivu-menu-light.ivu-menu-vertical .ivu-menu-submenu-title:hover {
  color: #fff6f3;
  background: #fd946e;
}
/* 导航选中后的侧边条 */
.ivu-menu-light.ivu-menu-vertical .ivu-menu-item-active:not(.ivu-menu-submenu):after {
  background: none;
}
/* 导航右边框条颜色 */
.ivu-menu-vertical.ivu-menu-light:after {
  background: none;
}


/* 导航时间 */
.nav_time {
  background:url("../monitorIMG/calendar.png") no-repeat;
  width: 200px;
  height: 200px;
  position: fixed;
  left: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: flex-end;
  font-weight: 800;
}
.nav_time_week {
  padding-bottom: 30px;
}
.nav_time span:nth-child(1) {
  font-size: 25px;
}
.nav_time span:nth-child(2) {
  font-weight: 400;
  line-height: 25px;
}
.content_area {
  width: 100%;
  background: #f6f7fb;
  min-height: 260px
}

.pages {
  padding: 10px 0;
  display: flex;
  /* justify-content: center; */
  justify-content: flex-end;
  position: relative;
}

.ivu-modal-confirm-body {
  word-wrap: break-word;
}
.ivu-message-custom-content {
  font-size:14px;
}
.ivu-message-custom-content > .ivu-icon {
  font-size:14px!important;
}
.ivu-message-notice-close i.ivu-icon {
  font-size: 24px;
}

.demo-spin-icon-load{
  animation: ani-demo-spin 2s linear infinite;
}


/* 滑块 */
.slider_area {
  display:flex;
  justify-content: space-between;
}
.ivu-slider-wrap {
  height: 8px;
  margin: 13px 0;
}
.ivu-slider-button-wrap {
  top: -6px;
}
.ivu-slider-bar {
  height: 8px;
  background: #f39a4d;
}
.ivu-slider-button{
  width: 20px;
  height: 20px;
  border-color: #f39a4d;
}
.ivu-slider-button:hover{
  border-color: #f39a4d;
}


/* 弹框 按钮 */
.ivu-btn-primary {
  background:#fb6129;
  color: #fff;
  border:none
}
.ivu-btn-primary:hover {
  background:#da4c18;
  color: #fff;
  border-color: #fb6129;
}

/* 搜索 */
.ivu-input-with-search:hover .ivu-input {
  border-color: #fb6129;
  box-shadow:none
}
.ivu-input-group .ivu-input:focus {
  box-shadow:none;
  border-color: #fb6129;
}
.ivu-input-search:hover,.ivu-input-search {
  background: #fb6129!important;
  border-color: #fb6129!important;
}

/* 抽屉头部 */
.ivu-drawer-header p, .ivu-drawer-header-inner{
  border-left: 4px solid #fb6129;
  padding-left: 10px;
}
.ivu-drawer-body {
  padding: 0px;
}
.ivu-btn {
  padding: 0 10px;
}
/* Tooltip文字提示框背景色 */
.ivu-tooltip-inner {
  /* background-color: #fb6129cb; */
  max-width:none;
}
/* .ivu-tooltip-popper[x-placement^=bottom] .ivu-tooltip-arrow {
  border-bottom-color: #fb6129cb;
}
.ivu-tooltip-popper[x-placement^=top] .ivu-tooltip-arrow {
  border-top-color: #fb6129cb
} */

/* 数字输入框 */
.ivu-input-number-focused {
  border-color: #fb6129;
  box-shadow:0 0 0 2px #fb612933;
}
.ivu-input-number:hover {
  border-color: #fb6129
}
.ivu-input:focus{
  border-color: #fb6129;
  box-shadow:0 0 0 2px #fb612933;
}
.ivu-input:hover {
  border-color: #fb6129
}
/* 下拉选框 */
.ivu-select-single .ivu-select-selection .ivu-select-placeholder, .ivu-select-single .ivu-select-selection .ivu-select-selected-value{
  color: #121529;
}
.ivu-select-item-selected, .ivu-select-item-selected:hover {
  color: #fe6902;
  background-color: #fff0e0;
}
.ivu-select-visible .ivu-select-selection {
  border-color: #fb6129;
  box-shadow:0 0 0 2px #fb612933;
}
.ivu-select-selection:hover {
  border-color: #fb6129;
}


/* 概览圆环字体 */
.ivu-chart-circle-inner {
  font-size: 24px;
}
/* 表格使用率100%颜色 */
.ivu-progress-success .ivu-progress-text {
  color: #333;
}
/* 下拉选中后 */
.ivu-select-selection-focused,.ivu-select-selection:hover {
  border-color: #fb6129;
}
/* 提示框 */
.ivu-notice {
  width: 600px;
}

/* 文本超出隐藏省略号 */
.text_overflow{
  display: inline-block;
  width: 100%;
  /* 强制一行显示 */
	white-space:nowrap;
  /* 超出隐藏 */
	overflow:hidden;
  /* 省略号 */
	text-overflow:ellipsis;
}
/* 开关样式 */
.ivu-switch-checked,.ivu-switch-disabled.ivu-switch-checked {
  border-color: #fb6129;
  background-color: #fb6129;
}
.ivu-switch:not(.ivu-switch-disabled):focus {
  box-shadow: 0 0 0 2px #fb612920
}

.ivu-affix {
  /* top:auto!important;
  left:auto!important;
  width:auto!important; */
}


/* 标签页 */
.ivu-tabs, .ivu-tabs .ivu-tabs-content-animated {
  height: 100%;
}
.ivu-tabs,.ivu-menu {
  color: #333;
}
.tabs_template>.ivu-tabs-bar {
  height: 50px;
  background: #fff;
  margin-bottom: 20px;
  border-radius: 25px;
  padding: 0 20px;
}
.tabs_template .ivu-tabs-nav-scroll {
  height: 50px;
}
.tabs_template .ivu-tabs-ink-bar {
  display: none;
}
.tabs_template .ivu-tabs-nav .ivu-tabs-tab {
  height: 50px;
  line-height: 50px;
  padding: 0 20px;
  margin-left: 20px;
}
.tabs_template .ivu-tabs-nav .ivu-tabs-tab:hover {
  font-weight: 800;
  color: #000;
  background: #fff3eb;
}
.tabs_template .ivu-tabs-nav .ivu-tabs-tab-active {
  font-weight: 800;
  color: #000;
  background: #fff3eb;
}
.yunyingpan .ivu-tabs .ivu-tabs-tabpane,.network_topology_area .ivu-tabs .ivu-tabs-tabpane {
  padding: 0px;
}

/* 标签页红色条 */
.select_tab_border {
  display: inline-block;
  width: 3px;
  height: 30px;
  position: relative;
  top: 10px;
  left: -20px;
}
/* 通用表格区域 */
.table_currency_area {
  height: calc(100% - 150px);
  overflow: auto;
}

/* 单选框按钮模式 */
.ivu-radio-group-button .ivu-radio-wrapper-checked {
  background: #fb6129;
  border-color: #fb6129;
  color:#fff;
}
.ivu-radio-group-button .ivu-radio-wrapper:hover {
  background: #da4c18;
  border-color: #da4c18;
  color:#fff;
}
.ivu-radio-group-button .ivu-radio-wrapper-checked:first-child {
  border-color: #da4c18;
}

.ivu-radio-group-button-solid .ivu-radio-wrapper-checked:not(.ivu-radio-wrapper-disabled) {
  background: #fb6129;
  border-color: #fb6129
}
.ivu-radio-group-button-solid .ivu-radio-wrapper-checked:not(.ivu-radio-wrapper-disabled):hover{
  background: #da4c18;
  border-color: #fb6129!important;
  color:#fff;
}
.ivu-radio-group-button .ivu-radio-wrapper-checked.ivu-radio-focus,.ivu-radio-group-button .ivu-radio-wrapper-checked.ivu-radio-focus:first-child {
  box-shadow:-1px 0 0 0 #fb6129;
}

/* 分页器 */
.pages .ivu-page-item-active {
  color: #fb6129;
  border-color: #fb6129;
}
.pages .ivu-page-item-active {
  color: #fb6129;
  border-color: #fb6129;
}
.pages .ivu-page-item-active a,.pages .ivu-page-item:hover a ,.pages .ivu-page-item:hover{
  color: #fb6129;
  border-color: #fb6129;
}

.pages .ivu-icon:hover,.pages .ivu-page-item-jump-prev:hover .ivu-icon,.pages .ivu-page-item-jump-next:hover .ivu-icon{
  color: #fb6129!important;
}

.pages .ivu-page-next:hover,.pages .ivu-page-prev:hover {
  color: #fb6129;
  border-color: #fb6129;
}
.pages .ivu-page-next:hover .ivu-icon,.pages .ivu-page-prev:hover .ivu-icon{
  color: #fb6129;
}
.pages .ivu-select-selection:hover,.pages .ivu-select-visible .ivu-select-selection {
  color: #fb6129;
  border-color: #fb6129;
  box-shadow:none
}
.pages .ivu-select-item-selected, .ivu-select-item-selected:hover {
  color: #fb6129;
}

/* 时间选择器 */
.ivu-date-picker-next-btn-arrow-double {
  margin: 2px 0 0 20px;
  color: #000;
}
.ivu-date-picker-prev-btn-arrow-double {
  margin: 2px 20px 0 0;
  color: #000;

}
.ivu-picker-panel-icon-btn {
  width: 30px;
}
.ivu-picker-panel-icon-btn i {
  font-size: 18px;
}
.table-button-area {
  margin-bottom: 10px;
  display: flex;
  justify-content: space-between;
}

/* 滚动条 */
::-webkit-scrollbar {
	width: 3px !important;
	height: 3px !important;
}
::-webkit-scrollbar-track-piece {
	background-color: var(--next-bg-main-color);
}
::-webkit-scrollbar-thumb {
	background-color: rgba(144, 147, 153, 0.3);
	background-clip: padding-box;
	min-height: 28px;
	border-radius: 5px;
	transition: 0.3s background-color;
}
::-webkit-scrollbar-thumb:hover {
	background-color: rgba(144, 147, 153, 0.5);
}