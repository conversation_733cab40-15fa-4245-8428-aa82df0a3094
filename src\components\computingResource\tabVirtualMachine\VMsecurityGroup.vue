<template>
  <div>
    <Modal
      v-model="model"
      width="600"
      :mask-closable="false"
    >
      <template #header><p><span style="color:green">{{vmRow.name}}</span>虚拟编辑安全组</p></template>
      <Spin fix v-if="spinshow" size="large" style="color: #ef853a">
        <Icon type="ios-loading" size="50" class="demo-spin-icon-load"></Icon>
        <div style="font-size: 16px; padding: 20px">Loading...</div>
      </Spin>
      <div
        style="
          border-bottom: 1px solid #e9e9e9;
          padding-bottom: 6px;
          margin-bottom: 6px;
        "
      >
        <Checkbox
          :indeterminate="indeterminate"
          :value="checkAll"
          @click.prevent.native="handleCheckAll"
          >全选</Checkbox
        >
      </div>
      <CheckboxGroup v-model="aqzCheak" @on-change="checkAllGroupChange">
        <Checkbox
          style="width: 30%; padding-bottom: 10px"
          v-for="(item, index) in anzData"
          :label="item"
          :key="index"
        ></Checkbox>
      </CheckboxGroup>
      <template #footer>
      <Button type="text" @click="model = false">取消</Button>
      <Button
        type="info"
        class="plus_btn"
        @click="modelOK"
        :disabled="disabled"
        >确定</Button
      >
    </template>
    </Modal>
  </div>
</template>
<script>
import {
  securityGroupQuery, // 安全组 查询
  securityGroupSelected, // 安全组 查询
  securityGroupEdit, // 安全组 编辑
} from '@/api/virtualMachine';
export default {
  props: {
    vmRow: Object,
    securityGroupTime: String,
  },
  watch: {
    securityGroupTime(news) {
      this.vmid = this.vmRow.id
      this.editgroup()
      this.disabled = false
      this.model = true
    },
  },
  data() {
    return {
      disabled: true,
      model: false,
      spinshow: false,
      aqzCheak: [],
      anzData: [],
      vmid: "",
      // 全选
      indeterminate:false,
      checkAll:false,
    };
  },
  methods: {
    editgroup() {
      this.spinshow = true;
      // 全部
      securityGroupQuery()
      .then((callback) => {
        let arr = new Array();
        callback.data.forEach((em) => {
          arr.push(em.name);
        });
        this.anzData = arr;
      });
      // 已选
      securityGroupSelected({ id: this.vmid })
      .then((callback) => {
        let arr = new Array();
        callback.data.forEach((em) => {
          arr.push(em.name);
        });
        this.aqzCheak = arr;
        this.spinshow = false;
      });
    },
     // 安全组选中状态
    checkAllGroupChange(data){
      if (data.length ==this.anzData.length) {
          this.indeterminate = false;
          this.checkAll = true;
      } else if (data.length > 0) {
          this.indeterminate = true;
          this.checkAll = false;
      } else {
          this.indeterminate = false;
          this.checkAll = false;
      }
    },
    // 安全组全选
    handleCheckAll(){
      if (this.indeterminate) {
          this.checkAll = false;
      } else {
          this.checkAll = !this.checkAll;
      }
      this.indeterminate = false;

      if (this.checkAll) {
          this.aqzCheak = this.anzData
      } else {
          this.aqzCheak = [];
      }
    },
    // 编辑安全组
    modelOK(){
      this.disabled = true
      securityGroupEdit({
        id:this.vmid,
        seclist:this.aqzCheak,
        vm_name: this.vmRow.name
      })
      .then(callback=>{
        if(callback.data.msg == "ok"){
          this.model = false;
          this.$Message.success({
            background: true,
            closable: true,
            duration: 5,
            content: "编辑安全组操作完成",
          });
        }else {
          this.disabled = false
          this.$emit("return-error", "编辑安全组操作失败");
        }
      })
      .catch((error) => {
        this.disabled = false
        this.$emit("return-error", "编辑安全组操作失败");
      });
    },
  },
};
</script>