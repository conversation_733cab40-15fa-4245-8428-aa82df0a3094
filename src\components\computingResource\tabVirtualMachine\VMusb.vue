<template>
  <Modal v-model="model" width="600" :mask-closable="false">
    <template #header><p><span style="color:green">{{vmRow.name}}</span>虚拟机配置USB</p></template>
    <Spin fix v-if="spinshow" size="large" style="color: #ef853a">
      <Icon type="ios-loading" size="50" class="demo-spin-icon-load"></Icon>
      <div style="font-size: 16px; padding: 20px">Loading...</div>
    </Spin>
    <Table
      :columns="columnsTable"
      :data="dataTable"
    >
      <template v-slot:radio="{ row }">
        <Radio :value="tableID == row.id" @on-change="tableID = row.id"></Radio>
      </template>
      <template v-slot:usbplug="{row}">
        <span :style="{color:row.usbplug=='plug_in'?'green':'#ccc'}">{{ row.usbplug=='plug_in'?'已插入':'已拔出'}}</span>
      </template>
      <template v-slot:vm_name="{row}">
        <span>{{ row.vm_name==''?'-未绑定-':row.vm_name}}</span>
      </template>
    </Table>
    <template #footer>
      <Button type="text" @click="model = false">取消</Button>
      <Button
        type="info"
        class="plus_btn"
        @click="modelOK"
        :disabled="disabled"
        >确定</Button
      >
    </template>
  </Modal>
</template>
<script>
import {usbQuery,usbVMunhook} from '@/api/other';  // USB查询，解挂
export default {
  props: {
    vmRow: Object,
    usbTime: String,
  },
  watch: {
    usbVMtime(news) {
      // this.title = this.vmRow.name+" 虚拟机配置USB"
      this.vmid = this.vmRow.id
      this.init()
      this.model = true
    },
  },
  data() {
    return {
      title:"",
      disabled: true,
      model: false,
      spinshow: false,
      tableID: "", // id(表格选中)
      columnsTable:[
        { title: "单选",width: 60, key: "radio",align: "center", slot: "radio" },
        { title: "设备名称", key: "product_name" },
        { title: "服务IP", key: "host_ip",align: "center" },
        { title: "服务端口", key: "host_port",align: "center" },
        { title: "虚拟机", key: "vm_name",align: "center",slot: 'vm_name'  },
        { title: "状态", key: "usbplug",align: "center",slot: 'usbplug' },
      ],
      dataTable:[],
    };
  },
  methods: {
    init() {
      this.spinshow = true;
      usbQuery(this.vmPageForm)
        .then((callback) => {
          this.dataTable = callback.data.data
          this.tableID = callback.data.id
          this.spinshow = false
        })
        .catch((error) => {
          this.spinshow = false
        });
    },
    
    // 确认事件
    modelOK(){
      usbVMunhook({id:this.vmid})
      .then(callback=>{
        if(callback.data.msg == "ok"){
          this.model = false;
          this.$emit("return-ok", "虚拟机挂载USB操作完成");
        }else {
          this.model = false;
          this.$emit("return-error", "虚拟机挂载USB操作失败");
        }
      }).catch((error) => {
          this.model = false;
          this.$emit("return-error", "虚拟机挂载USB操作失败");
        });
    },
    cancel() {},
  },
};
</script>