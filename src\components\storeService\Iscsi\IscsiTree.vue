<template>
  <div class="iscsi-tree-wrap">
    <div class="tree-list-wrap">
      <el-tree
        :data="data"
        node-key="label"
        default-expand-all
        @check-change="handleCheckChange"
        @node-click="handleCheckChange"
        :expand-on-click-node="false">
        <span class="custom-tree-node" slot-scope="{ node, data }">
          <img src="@/assets/imager/file.svg" alt="" class="svg-img">
          <span class="ml-5">{{ node.label }}</span>
        </span>
      </el-tree>
    </div>
    <div class="file-info-wrap" v-if="fileInfoData.clickType">
      <div class="disks-wrap" v-if="fileInfoData.clickType == 'disks'">
        <h3>{{ fileInfoData.label }}详情</h3>
        <table class="table-wrap">
          <tr>
            <th width="40%">名称</th>
            <th>参数</th>
          </tr>
          <tr>
            <th>lun</th>
            <th>{{ fileInfoData.lun }}</th>
          </tr>
          <tr>
            <th>wwn</th>
            <th>{{ fileInfoData.wwn }}</th>
          </tr>
        </table>
      </div>
      <div class="initiators-wrap" v-else>
        <h3>{{ fileInfoData.label }}详情</h3>
         <table class="table-wrap">
          <tr>
            <th width="40%">名称</th>
            <th>参数</th>
          </tr>
          <tr>
            <th>ip地址</th>
            <th>{{ fileInfoData.info.ip_address }}</th>
          </tr>
          <tr>
            <th>用户名</th>
            <th>{{ fileInfoData.auth.user }}</th>
          </tr>
          <tr>
            <th>密码</th>
            <th>{{ fileInfoData.auth.password }}</th>
          </tr>
        </table>
      </div>
    </div>
    <div v-else class="no-data-wrap">
       <img src="@/assets/imager/no-datas.svg" alt="" class="mt-20">
       <p class="no-datas-wrap">暂无数据查看</p>
    </div>
  </div>
</template>

<script>
  export default {
   props: {
     infoData: {
       type: Object,
       default: () => {
         return {}
       }
     }
   }, 
   data() {
     return{
       fileInfoData: {
         clickType: ''
       },
       data:  [
        {
        id: 1,
        label: '办公文档',
        children: [
          { 
            id: 2,
            label: '磁盘组(Disks)',
            children: []
          },
           { 
            id: 3,
            label: '入口集群(portals)',
            children: []
          },
           { 
            id: 4,
            label: '客户端(Initiators)',
            children: []
          },
        ]
      }]
     }
    },
    mounted(){
      this.data[0].label = this.infoData.label
      setTimeout(() => {
       this.setMapObj()
      }, 20);
    },
    methods:{
      setMapObj() {
        const infoData = this.infoData
        const dataFirst = this.data[0].children
        // 处理客户端数据
        if (infoData.clients.length) {
          infoData.clients.map((clients) => {
            clients.label = clients.client_iqn
            clients.clickType = 'clients'
            clients.luns.forEach(item => {
              item.label = `${item.pool}_${item.image}`
              item.noClick = true
            })
            clients.children = clients.luns
            return clients
          })
        } else {
          infoData.clients.children = []
        }
        // 处理Disks'数据
        if (infoData.disks.length) {
          infoData.disks.map( disks => {
            disks.label = `${disks.pool}_${disks.image}`
            disks.clickType = 'disks'
            return disks
          })
        } else {
          infoData.disks.children = []
        }
        // 处理Portals数据
        if (infoData.portals.length) {
          infoData.portals.map( portals => {
            portals.label = `${portals.host}_${portals.ip}`
            return portals
          })
        } else {
          infoData.portals.children = []
        }
        dataFirst[0].children = infoData.disks
        dataFirst[1].children = infoData.portals
        dataFirst[2].children = infoData.clients
      },
      handleCheckChange(data) {
        this.fileInfoData = JSON.parse(JSON.stringify(data))
      },
    },
    watch: {
    }
  }
</script>

<style scoped lang='less'>
  .iscsi-tree-wrap {
    width: 100%;
    height: 100%;
    display: flex;
    .tree-list-wrap {
      width: 500px;
      height: 100%;
      padding-left: 65px;
    }
    .file-info-wrap {
      flex: 1;
      padding-left: 3px;
      .disks-wrap {
        width: 100%;
        height: 100%;
      }
      .initiators-wrap {
        width: 100%;
        height: 100%;
      }
    }
    .icon-label-wrap {
      display: flex;
      align-items: center;
    }
    .svg-img {
      width: 16px;
      height: 16px;
    }
    .no-data-wrap {
      display: flex;
      flex: 1;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      img {
        width: 200px;
        height: 160px;
      }
    }
    .table-wrap {
      width: 80%;
      margin-top: 8px;
      border: 1px solid #ff6902;
      border-collapse: collapse;
      color: #666666;
      font-weight: 500 !important;
      margin-bottom: 18px;
      // margin-top: 40px;
      th {
        border: 1px solid #ff6902;
        height: 36px;
        font-weight: 500 !important;
        padding-left:8px ;
        font-size: 14px;
      }
      .endth-wrap {
        height: 80px;
      }
      .add-background {
        // background: #f0f7fb;
      }
      .set-width-wrap {
        width: 95%;
        margin-left: 10px;
      }
      .required-icons {
        color: #de3d54;
        font-size: 22px;
      }
    }
    /deep/ .el-tree {
      background: none !important;
    }
  }
</style>