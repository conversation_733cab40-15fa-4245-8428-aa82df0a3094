<style lang="less">
  @import "../../networkResource.less";
</style>
<template>
  <div class="topology_module">
    <div  class="topology_module_btn">
      <Button class="remove" @click="shuaxin" ><span class="icon iconfont icon-a-15Jhuanyuan"></span> 刷新</Button>
    </div>
    <Spin fix v-if="spinShow" size="large" style="color:#ef853a">
      <Icon type="ios-loading" size=50 class="demo-spin-icon-load"></Icon>
      <div style="font-size:1.5rem;padding:20px">Loading...</div>
    </Spin>
    <div id="mount_node" v-if="tuopu"></div>
    <div class="no_data_prompt" v-if="kong">
      <h5>没有可以显示的网络，路由或者连接的实例。</h5>
    </div>
  </div>
</template>
<script>
import {networkTablePhysicalTopologyQuery} from '@/api/network';

import G6 from "@antv/g6";
export default {
  props: {
    tabTuopu: String,
  },
  data() {
    return {
      tuopu:true,
      kong:false,
      spinShow:false,
      echartsData:undefined,
    };
  },
  watch: {
    tabTuopu(value) {
      value=='wulituopu'?this.shuaxin():""
    },
    echartsData(news){
      if(news.edges.length>0) {
        this.tuopu=true
        this.kong=false
        this.settings(news)
      }else {
        this.tuopu=false
        this.kong=true
      }
    },
  },
  methods: {
    shuaxin(){
      let mountNode = document.getElementById("mount_node");
      if(mountNode!==null) {
        if(mountNode.childNodes.length!==0){
          mountNode.removeChild(mountNode.childNodes[1])
          mountNode.removeChild(mountNode.childNodes[0])
          this.netGET();
        }else {
          this.netGET();
        }
      }else {
        this.netGET();
      }
      // let childs = mountNode.childNodes
      // if(childs.length!==0){
      //   mountNode.removeChild(childs[1])
      //   mountNode.removeChild(childs[0])
      //   this.netGET();
      // }
    },
    netGET(){
      this.spinShow = true
      this.echartsData=undefined
      networkTablePhysicalTopologyQuery().then(callback=>{
        this.echartsData=callback.data
        this.spinShow = false
      })
    },
    settings(data) {
      const minimap = new G6.Minimap({
        size: [150, 100],
      });
      let mountNode = document.getElementById("mount_node");
      const graph = new G6.Graph({
        container: mountNode,
        // width: 1000,
        // height: 700,
        fitView: true,
        fitViewPadding: 20,
        // 节点默认配置
        defaultNode: {
          style:{
            fill: "#fb6129",
            stroke:"#000",
            lineWidth: 1,
            
          },
          labelCfg: {
            style: {
              fill: "#000",
            },
          },
          // linkPoints: {
          //   top: true,
          //   bottom: true,
          //   left: true,
          //   right: true,
          //   // ... 四个圆的样式可以在这里指定
          // },
        },
        // 边默认配置
        defaultEdge: {
          style:{
            opacity: 1,
            stroke:"#fb6129",
            endArrow: true,
            // startArrow: true
          },
          labelCfg: {
            // autoRotate: true,
            style: {
              stroke: "#fb6129",
            },
          },
        },
        // 节点在各状态下的样式
        nodeStateStyles: {
          // hover 状态为 true 时的样式
          hover: {
            fill: "#da4c18",
          },
          // click 状态为 true 时的样式
          click: {
            stroke: "#fb6129",
            fill: "#da4c18",
            lineWidth: 2,
            shadowOffsetX: 10,
            shadowOffsetY: 10,
            shadowColor: '#da4c18',
            shadowBlur: 15,
          },
        },
        // 边在各状态下的样式
        edgeStateStyles: {
          hover: {
            fill: "#000",
            stroke: "#000",
            lineWidth: 2,
          },
          // click 状态为 true 时的样式
          click: {
            stroke: "#da4c18",
            lineWidth: 2,
          },
        },
        // 布局
        layout: {
          // type: "random",        // 随机布局 √
          // type: "force",         // 经典力导向布局 √
          // type: "circular",      // 环形布局 
          type: "dagre",         // 层次布局 √
          // type: "concentric",    // 同心圆布局 √
          // type: "grid",          // 格子布局 √
          // type: "fruchterman",   // Fruchterman 布局，一种力导布局 ×
          // type: "compactBox",    // 经典力导向布局 ×
          // type: "mds",           // 高维数据降维算法布局  ×
          // type: "dendrogram",    // 树状布局（叶子节点布局对齐到同一层) ×
          // type: "compactBox",    // 紧凑树布局 ×
          // type: "mindmap",       // 脑图布局 ×
          // type: "indented",      // 缩进布局 ×
          linkDistance: 300,
          preventOverlap: true,
          nodeStrength: 30, 
          edgeStrength: 1,
        },
        // 内置交互
        modes: {
          default: ["drag-canvas", "zoom-canvas", "drag-node"],
          edit: ["click-select"]
        },
        plugins: [minimap],
      });
      
      const main = function () {
        const remoteData = data
        // const remoteData = {
        //   nodes: [
        //     { id: "0", label: "根文件", class: "c0" },
        //     { id: "1", label: "一级左", class: "c1up" },
        //     { id: "2", label: "一级中", class: "c1down" },
        //     { id: "3", label: "一级右", class: "c1up" },
        //     { id: "4", label: "左二级1", class: "c2up" },
        //     { id: "5", label: "左二级2", class: "c2up" },
        //     { id: "6", label: "中二级1", class: "c2up" },
        //     { id: "7", label: "中二级2", class: "c2down" },
        //     { id: "8", label: "右二级1", class: "c2down" },
        //     { id: "9", label: "右二级2", class: "c2up" },
        //     { id: "10", label: "左1三级1", class: "c3up" },
        //     { id: "11", label: "左1三级2", class: "c3up" },
        //     { id: "12", label: "中1三级1", class: "c3up" },
        //     { id: "13", label: "中2三级2", class: "c3down" },
        //     { id: "14", label: "右1三级1", class: "c3up" },
        //     { id: "14", label: "右1三级2", class: "c3up" },
        //   ],
        //   edges: [
        //     { source: "0", target: "1", label: "192.168.0.1" },
        //     { source: "0", target: "2", label: "192.168.0.1" },
        //     { source: "0", target: "3", label: "192.168.0.1" },
        //     { source: "1", target: "4", label: "192.168.1.1" },
        //     { source: "1", target: "5", label: "192.168.1.1" },
        //     { source: "2", target: "6", label: "192.168.1.1" },
        //     { source: "2", target: "7", label: "192.168.1.1" },
        //     { source: "3", target: "8", label: "192.168.1.1" },
        //     { source: "3", target: "9", label: "192.168.1.1" },
        //     { source: "4", target: "10", label: "192.168.1.1" },
        //     { source: "4", target: "11", label: "192.168.1.1" },
        //     { source: "5", target: "12", label: "192.168.1.1" },
        //     { source: "5", target: "13", label: "192.168.1.1" },
        //     { source: "6", target: "14", label: "192.168.1.1" },
        //     { source: "6", target: "15", label: "192.168.1.1" },
        //     { source: "7", target: "16", label: "192.168.1.1" },
        //     { source: "7", target: "17", label: "192.168.1.1" },
        //     { source: "8", target: "18", label: "192.168.1.1" },
        //     { source: "8", target: "119", label: "192.168.1.1" },
        //   ],
        // };
        const nodes = remoteData.nodes;
        nodes.forEach((node) => {
          switch (node.class) {
            case "c0": {
              node.type = "image";
              node.size = [100, 100];
              node.img = require('../../../../assets/G6/5jhj_u.png');
              break;
            }
            case "c0d": {
              node.type = "image";
              node.size = [100, 100];
              node.img = require('../../../../assets/G6/5jhj_d.png');
              break;
            }
            case "c1up": {
              node.type = "image";
              node.size = [100, 100];
              node.img = require('../../../../assets/G6/6wk_u.png');
              break;
            }
            case "c1down": {
              node.type = "image";
              node.size = [100, 100];
              node.img = require('../../../../assets/G6/6wk_d.png');
              break;
            }
            case "c2up": {
              node.type = "image";
              node.size = [100, 100];
              node.img = require('../../../../assets/G6/7wq_u.png');
              break;
            }
            case "c2down": {
              node.type = "image";
              node.size = [100, 100];
              node.img = require('../../../../assets/G6/7wq_d.png');
              break;
            }
            case "c3up": {
               node.type = "image";
              node.size = [100, 100];
              node.img = require('../../../../assets/G6/2xnwl_u.png');
              break;
            }
            case "c3down": {
               node.type = "image";
              node.size = [100, 100];
              node.img = require('../../../../assets/G6/2xnwl_d.png');
              break;
            }
          }
        });
        graph.data(remoteData);
        graph.render();

        // 监听鼠标进入节点
        graph.on("node:mouseenter", (e) => {
          const nodeItem = e.item;
          // 设置目标节点的 hover 状态 为 true
          graph.setItemState(nodeItem, "hover", true);
        });
        // 监听鼠标离开节点
        graph.on("node:mouseleave", (e) => {
          const nodeItem = e.item;
          // 设置目标节点的 hover 状态 false
          graph.setItemState(nodeItem, "hover", false);
        });
        // 监听鼠标点击节点
        graph.on("node:click", (e) => {
          // 先将所有当前有 click 状态的节点的 click 状态置为 false
          const clickNodes = graph.findAllByState("node", "click");
          clickNodes.forEach((cn) => {
            graph.setItemState(cn, "click", false);
          });
          // 删除节点
          // const nodeID = e.item._cfg.model.id;
          // graph.removeItem(graph.findById(nodeID));
          // 设置目标节点的 click 状态 为 true
          // 刷新
          // remoteData.nodes.forEach((en,index)=>{
          //   if(en.id==e.item._cfg.model.id) {
          //     en.label ="爱我中华"
          //   }
          // })
          // const nodeID = e.item._cfg.model.id;
          // graph.refreshItem(graph.findById(nodeID));
          graph.setItemState(e.item, "click", true);
        });
         // 监听鼠标进入线
        graph.on('edge:mouseenter', (e) => {
          const nodeItem = e.item;
          // 设置目标节点的 hover 状态 为 true
          graph.setItemState(nodeItem, "hover", true);
        });
        // 监听鼠标离开线
        graph.on('edge:mouseleave', (e) => {
          const nodeItem = e.item;
          // 设置目标节点的 hover 状态 为 true
          graph.setItemState(nodeItem, "hover", false);
        });
        // 监听鼠标点击线
        graph.on("edge:click", (e) => {
          // 先将所有当前有 click 状态的边的 click 状态置为 false
          const clickEdges = graph.findAllByState("edge", "click");
          clickEdges.forEach((ce) => {
            graph.setItemState(ce, "click", false);
          });
          const edgeItem = e.item;
          // 设置目标边的 click 状态 为 true
          graph.setItemState(edgeItem, "click", true);
        });
      };
      main();
    },
  },
};
</script>