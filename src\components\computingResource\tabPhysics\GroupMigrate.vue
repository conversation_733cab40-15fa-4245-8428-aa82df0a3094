<template>
  <Modal v-model="model" width="600" :mask-closable="false">
    <template #header><p>迁移设置</p></template>
    <div>
      <div style="padding:20px 0;display: flex;justify-content: space-around;">
        <RadioGroup v-model="formItem.radio">
          <Radio label="disable">不参与迁移调度</Radio>
          <Radio label="auto">自动迁移</Radio>
          <Radio label="half_auto">半自动迁移</Radio>
        </RadioGroup>
      </div>
      <div v-if="formItem.radio=='disable'">
        主机CPU阈值：
        <Slider :min="1" :max="100" v-model="formItem.cpuUpper" show-input style="padding-bottom:20px"></Slider>
        内存负载阈值：
        <Slider :min="1" :max="100" v-model="formItem.ramUpper" show-input></Slider>
      </div>
      <div v-if="false">
        主机负载平衡阈值：
        <Slider :min="1" :max="100" v-model="formItem.hostHorizon" show-input></Slider>
      </div>
    </div>
    <div slot="footer">
      <Button type="text" @click="model = false">取消</Button>
      <Button type="primary" @click="modelOK" :disabled="disabled">确认</Button>
    </div>
  </Modal>
</template>
<script>
import {
  migrationConfirmation, // 迁移确认
  migrationConfiguration, // 迁移配置
} from '@/api/physics'; 

export default {
  props: {
    migrateTime: String,
    groupSelect: Object,
  },
  watch: {
    migrateTime(news){
      // this.formItem.title = this.groupSelect.name+' 集群组迁移设置'
      this.formItem.groupID = this.groupSelect.id
      this.migrateConfigQuery()
      this.model = true
      this.disabled = false
    }
  },
  data() {
    return {
      model: false,
      disabled: false,
      formItem: {
        title: '',
        groupID: '',
        radio: '',
        clusterID: '',
        cpuUpper: 90,
        ramUpper: 90,
        hostHorizon: 50,
      },
    }
  },
  methods: {
    // 迁移配置
    migrateConfigQuery(item){
      migrationConfiguration({cluster_id:this.formItem.groupID})
      .then(callback =>{
        this.radio = callback.data.migrate_type
        this.formMove.clusterID = callback.data.cluster_id
        this.formMove.cpuUpper = callback.data.cpu_usage_upper-0
        this.formMove.ramUpper = callback.data.memory_usage_upper-0
        this.formMove.hostHorizon = callback.data.host_horizon_usage-0
      })
    },
    // 迁移确认
    modelOK(){
      this.disabled = true
      migrationConfirmation({
        migrate_type: formItem.radio,
        cluster_id: formItem.clusterID,
        cpu_upper: formItem.cpuUpper,
        ram_upper: formItem.ramUpper,
        host_horizon: formItem.hostHorizon,
      })
      .then(callback =>{
        if(callback.data.msg=="ok") {
          this.$emit("custom-ok",'迁移配置已完成');
          this.model = false;
        }else {
          this.$emit("custom-error",'迁移配置失败');
          this.disabled = false;
        }
      })
      .catch((error) => {
        this.$emit("custom-error",'迁移配置失败');
        this.model = false;
      });
    },
  }
}
</script>