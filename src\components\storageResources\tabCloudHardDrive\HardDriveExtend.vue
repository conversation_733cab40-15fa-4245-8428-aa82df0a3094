<template>
  <div>
    <Modal v-model="model" width="600" :mask-closable="false">
      <template #header><p>云硬盘扩展</p></template>
      <Form
        :model="formItem"
        :label-width="120"
      >
        <FormItem label="扩容的云硬盘" >
          <Input v-model="tableRow.name" disabled ></Input>
        </FormItem>
        <FormItem label="云硬盘容量">
          <div class="slider_area">
            <div style="width:350px">
              <Slider v-model="formItem.size"  :min='1' :max='10240' :tip-format="formMemory" ></Slider>
            </div>
            <div style="width:80px">
              <InputNumber :min="1" :max="10240" v-model="formItem.size" :formatter="value => `${value}GB`" :parser="value => value.replace('GB', '')"></InputNumber>
            </div>
          </div>
        </FormItem>
      </Form>
      <div slot="footer">
        <Button type="text" @click="model = false">取消</Button>
        <Button type="primary" @click="modelOK" :disabled="disabled">确认</Button>
      </div>
    </Modal>
  </div>
</template>
<script>
import {
  cloudDiskExpansion, // 云硬盘 扩容
} from '@/api/storage';
export default {
  props: {
    extendTime: String,
    tableRow: Object,
  },
  watch: {
    extendTime(news){
      this.formItem.size = parseInt(this.tableRow.size)
      this.formItem.min = parseInt(this.tableRow.size)
      this.model = true
      this.disabled = false
    }
  },
  data(){
    return {
      model: false,
      disabled: false,
      formItem: {
        size: 0,
        min: 0,
      },
    }
  },
  methods: {
    // 云硬盘大小
    formMemory (val) {
      return val + ' GB';
    },
    // 确认事件
    modelOK() {
      if (this.formItem.size > this.formItem.min) {
        this.disabled = true;
        cloudDiskExpansion({
          id: this.tableRow.id,
          data: this.formItem.size,
          action: 'extend',
          name: this.tableRow.name,
        }).then(callback=>{
          if(callback.data.msg !== "error"){
            this.$emit("return-ok",'扩容云硬盘操作完成');
            this.model = false;
          }else {
            this.$emit("return-error","扩容云硬盘操作失败");
            this.disabled = false;
          }
        })
        .catch((error) => {
          this.$emit("return-error",'扩容云硬盘操作失败');
          this.disabled = false;
        });
      }else {
        this.$emit("return-error",'选择扩容的值不能小于默认值');
      }
    },
  }
}
</script>
