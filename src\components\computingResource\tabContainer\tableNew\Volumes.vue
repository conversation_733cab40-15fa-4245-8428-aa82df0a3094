<template>
  <div class="general_page_area">
    <Form :model="formItem" ref="formItem" :rules="ruleValidate" :label-width="120">
			<FormItem label="挂载盘大小" prop="size">
        <Input v-model="formItem.size" type='number' placeholder="请输入挂载盘大小(GB)"></Input>
    	</FormItem>
      <FormItem label="挂载目录">
        <Input v-model="formItem.destination" placeholder="请输入指定容器上卷的挂在点目录"></Input>
    	</FormItem>
      <FormItem >
        <Button type="info" ghost @click="addClick" :disabled="disabled">添加卷</Button>
    	</FormItem>
      <Table :columns="tableColumn" :data="tableData" style="margin-left:15px">
        <!-- 操作 -->
        <template v-slot:operation="{ row }">
          <Button class="close_btn" @click="deletClick(row)">删除</Button>
        </template>
      </Table>
		</Form>
  </div>
</template>
<script>
export default {
  props: {
    times:String,
  },
  watch: {
    times(value) {
      this.$emit("returnOK",{
        page: 2,
        data: this.tableData,
      });
      // this.$refs.formItem.validate((valid) => {
      //   if (valid) {
      //     this.$emit("returnOK",{
      //       page: 2,
      //       data: this.formItem,
      //     });
      //   }
      // })
    },
    'formItem.size'(news){
      if(news=='' || this.formItem.destination=='') {
        this.disabled = true
      }else {
        this.disabled = false
      }
    },
    'formItem.destination'(news){
      if(news=='' || this.formItem.size=='') {
        this.disabled = true
      }else {
        this.disabled = false
      }
    },
  },
  data(){
    const propSize = (rule, value, callback) => {
      if (value>0 || value === '') {
        callback()
      } else {
        callback(new Error("请输入大于0的数字"))
      }
    }
    return {
      disabled: true,
      formItem: {
        size: '',
        destination: '',
      },
      ruleValidate:{
        size:[{ validator: propSize, trigger: "change" }],
      },
      tableColumn: [
        {title: '挂载盘大小', key: 'size' },
        {title: '挂载目录', key: 'destination' },
        { title: "操作", key: "operation", width: 90, slot: "operation" },
      ],
      tableData: [],
    }
  },
  methods: {
    // 添加卷
    addClick(){
      this.tableData.push({
        size: this.formItem.size,
        destination: this.formItem.destination,
        source: '',
      })
      this.formItem.size = ''
      this.formItem.destination = ''
    },
    // 删除卷
    deletClick(row){
      this.tableData.splice(row._index,1)
    },
  },
}
</script>
<style lang="less" scoped>
  .general_page_area {
    width: 100%;
    height: 100%;
    overflow: auto;
  }
</style>