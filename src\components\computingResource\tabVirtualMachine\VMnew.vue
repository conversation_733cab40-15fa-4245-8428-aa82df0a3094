<style scoped>
@import "./virtualMachineTab.css";
</style>
<template>
  <div>
    <Modal
      v-model="initialmodal"
      width="800"
      :mask-closable="false"
    >
      <p slot="header">
        <span>新建虚拟机</span>
      </p>
      <div style="height: 480px; overflow: hidden" class="vm_new_dialog">
        <Menu theme="light" :active-name="modalName" @on-select="modalMenuItem">
          <Menu-item :name="i" v-for="(item, i) in menniuItemData" :key="i">
            <span :class="item.icon"></span>
            <span>{{ item.name }}</span>
          </Menu-item>
        </Menu>
        <div class="modalcontent">
          <ul id="lunbo">
            <li>
              <Form :model="newVMForm" :rules="ruleValidate" :label-width="125">
                <FormItem label="虚拟机名称" prop="name">
                  <Input
                    v-model="newVMForm.name"
                    placeholder="请输入虚拟机名称"
                    style="width: 350px"
                  ></Input>
                </FormItem>
                <FormItem label="创建方式">
                  <RadioGroup v-model="newVMForm.radio">
                    <Radio label="从镜像创建">镜像创建</Radio>
                    <Radio label="从ISO创建" style="margin-right: 30px">ISO创建</Radio>
                  </RadioGroup>
                </FormItem>
                <FormItem label="镜像选择" prop="imageTypeId" v-if="newVMForm.radio=='从镜像创建'">
                  <Select
                    v-model="newVMForm.imageTypeId"
                    :label-in-value="true"
                    style="width: 350px"
                    @on-change="netIMGchange"
                  >
                    <Option
                      v-for="item in imageOption"
                      :value="item.id"
                      :key="item.id"
                      >{{ item.name }}</Option
                    >
                  </Select>
                </FormItem>
                <FormItem label="操作系统类型" prop="operatingSystemName" v-if="newVMForm.radio=='从ISO创建'&&architecture">
                  <Select
                    v-model="newVMForm.operatingSystemName"
                    :label-in-value="true"
                    style="width: 350px"
                    @on-change="iosIMGchange"
                  >
                    <Option
                      v-for="item in systemType"
                      :value="item.name"
                      :key="item.id"
                      >{{ item.name }}</Option
                    >
                  </Select>
                </FormItem>
                <FormItem label="网络名称" prop="networkId">
                  <Select
                    v-model="newVMForm.networkId"
                    :disabled="newVMForm.presetsIP"
                    :label-in-value="true"
                    style="width: 350px"
                    @on-change="netSlectChange"
                  >
                    <Option
                      v-for="item in netOption"
                      :value="item.id"
                      :key="item.id"
                      >{{ item.name }}</Option
                    >
                  </Select>
                </FormItem>
                <!-- <FormItem label="存储类型" prop="storage_type">
                  <Select
                    v-model="newVMForm.storage_type"
                    :label-in-value="true"
                    style="width: 350px"
                  >
                    <Option
                      v-for="item in types_data"
                      :value="item.name"
                      :key="item.id"
                      >{{ item.name }}</Option
                    >
                  </Select>
                </FormItem> -->
                
                <FormItem label="系统版本">
                  <Select
                    v-model="newVMForm.sys_version"
                    :label-in-value="true"
                    style="width: 350px"
                    placement="top"
                  >
                    <Option
                      v-for="item in versionData"
                      :value="item.value"
                      :key="item.value"
                      >{{ item.value }}</Option
                    >
                  </Select>
                </FormItem>
                <FormItem label="预设IP" v-if="this.newVMForm.radio=='从镜像创建'">
                  <div style="display: flex;">
                    <Checkbox v-model="newVMForm.presetsIP"></Checkbox>
                    <FormItem prop="ipPash"  v-if="this.newVMForm.presetsIP">
                      <div style="display: flex;">
                        <Input v-model="newVMForm.ipPash" placeholder="请输入IP地址" style="width: 250px"></Input>
                        <Button class="plus_btn" v-if="newVMForm.radio == '从镜像创建'" :disabled="newVMForm.yuseDisabled" style="display: flex; align-items: center; justify-content: center;" icon="ios-add-circle-outline"  @click="addIPpool"></Button>
                      </div>
                    </FormItem>
                  </div>
                </FormItem>
                <!-- <FormItem label="IP地址" prop="ipPash"  v-if="this.newVMForm.presetsIP">
                  <div style="display: flex;">
                    <Input
                      v-model="newVMForm.ipPash"
                      placeholder="请输入IP地址"
                      style="width: 350px"
                    >
                    </Input>
                    <Button class="plus_btn" v-if="newVMForm.radio == '从镜像创建'" :disabled="newVMForm.yuseDisabled" style="display: flex; align-items: center; justify-content: center;" icon="ios-add-circle-outline"  @click="addIPpool"></Button>
                  </div>
                </FormItem> -->
                <div v-if="newVMForm.radio == '从镜像创建'" style="display: flex;flex-wrap: wrap;height: 120px;overflow: auto;">
                  <span style="width:135px;display: flex;justify-content: flex-end;align-items: center;" v-for="(itm,index) in newVMForm.ipPoolData">{{itm}}<Icon type="md-trash" style="font-size:18px;color:red;padding-left: 5px" @click="deleIPpool" /></span>
                  <!-- {{this.newVMForm.ipPoolData.join(",")}} -->
                </div>
              </Form>
            </li>
            <li>
              <Table
                :columns="computingResourceColumn"
                :data="computingResourceData"
                height="470"
              ></Table>
            </li>
            <li>
              <div class="cipan">
                <Form :model="newVMForm" :label-width="120">
                  <FormItem label="虚拟机数量" style="padding-top: 20px" v-if="this.newVMForm.radio=='从镜像创建'">
                    <div class="slider_area">
                      <div style="width:370px">
                        <!-- <Slider
                          :disabled="newVMForm.presetsIP"
                          v-model="newVMForm.vmNumber"
                          show-tip="always"
                          :min="1"
                          :max="200"
                          :tip-format="formvm"
                        ></Slider> -->
                        <Slider
                          :disabled="newVMForm.presetsIP"
                          v-model="newVMForm.vmNumber"
                          :min="1"
                          :max="200"
                          :tip-format="formvm"
                        ></Slider>
                      </div>
                      <div style="width:80px">
                        <InputNumber :disabled="newVMForm.presetsIP" :min="1" :max="200" v-model="newVMForm.vmNumber" :formatter="value => `${value}个`" :parser="value => value.replace('个', '')"></InputNumber>
                      </div>
                    </div>
                  </FormItem>
                  <FormItem label="VCPU核数">
                    <div class="slider_area">
                      <div style="width:370px">
                        <Slider
                          v-model="newVMForm.cpu"
                          :min="1"
                          :max="64"
                          :tip-format="formCpu"
                        ></Slider>
                      </div>
                      <div style="width:80px">
                        <InputNumber :min="1" :max="64" v-model="newVMForm.cpu" :formatter="value => `${value}核`" :parser="value => value.replace('核', '')"></InputNumber>
                      </div>
                    </div>
                  </FormItem>
                  <FormItem label="内存容量">
                    <div class="slider_area">
                      <div style="width:370px">
                        <Slider
                          v-model="newVMForm.memory"
                          :min="1"
                          :max="256"
                          :tip-format="formMemory"
                        ></Slider>
                      </div>
                      <div style="width:80px">
                        <InputNumber :min="1" :max="256" v-model="newVMForm.memory" :formatter="value => `${value}GB`" :parser="value => value.replace('GB', '')"></InputNumber>
                      </div>
                    </div>
                  </FormItem>
                  <FormItem label="硬盘容量">
                    <div class="slider_area">
                      <div style="width:370px">
                        <Slider
                          v-model="newVMForm.disk"
                          :min="1"
                          :max="2048"
                          :tip-format="formDisk"
                          @on-change="diskONchange"
                        ></Slider>
                      </div>
                      <div style="width:80px">
                        <InputNumber @on-blur="diskONblur" :min="1" :max="2048" v-model="newVMForm.disk" :formatter="value => `${value}GB`" :parser="value => value.replace('GB', '')"></InputNumber>
                      </div>
                    </div>
                  </FormItem>
                  <FormItem label="加载ISO镜像" prop="imageTypeId"  v-if="this.newVMForm.radio!=='从镜像创建'">
                    <Select
                      v-model="newVMForm.imageTypeId"
                      style="width: 350px"
                      :label-in-value="true"
                      @on-change="netIMGchange"
                    >
                      <Option
                        v-for="item in imageOptionISO"
                        :value="item.id"
                        :key="item.id"
                        >{{ item.name }}</Option
                      >
                    </Select>
                  </FormItem>
                  <FormItem label="启用驱动" prop="driveimageId"  v-if="this.newVMForm.radio!=='从镜像创建'">
                    <div style="display: flex;">
                      <Checkbox v-model="newVMForm.driveDisabled"></Checkbox>
                      <div style="display: flex;" v-if="this.newVMForm.driveDisabled">
                        <Select
                          v-model="newVMForm.driveimageId"
                          style="width: 276px"
                          :label-in-value="true"
                          @on-change="driveSelect"
                        >
                          <Option
                            v-for="item in imageOptionISO"
                            :value="item.id"
                            :key="item.id"
                            >{{ item.name }}</Option
                          >
                        </Select>
                      </div>
                    </div>
                  </FormItem>
                  <FormItem label="启用数据盘" v-if="this.newVMForm.radio=='从镜像创建'">
                    <div style="display: flex;">
                      <Checkbox v-model="newVMForm.data_disk"></Checkbox>
                      <div v-if="this.newVMForm.data_disk" class="shujupan">
                        <InputNumber :min="1" :max="10240" v-model="newVMForm.ex_volume_size" placeholder="请输入数据盘大小" />
                        <span style="padding:0 30px 0 5px">GB</span>
                        <!-- <Select
                          v-model="newVMForm.data_type"
                          :label-in-value="true"
                          style="width:150px"
                        >
                          <Option
                            v-for="item in types_data"
                            :value="item.name"
                            :key="item.id"
                            >{{ item.name }}</Option
                          >
                        </Select> -->
                      </div>
                    </div>
                  </FormItem>
                </Form>
              </div>
            </li>
            <li>
              <div class="xinxigailan">
                <ul>
                  <li>
                    <span class="imitateTitle">虚拟机名称</span>
                    <div class="imitateInput">{{ newVMForm.name }}</div>
                  </li>
                  <li>
                    <span class="imitateTitle">集群组名称</span>
                    <Tooltip placement="top">
                      <div class="imitateInput text_overflow">{{ newVMForm.computingResourceName}}</div>
                      <template #content>
                        <p>{{newVMForm.computingResourceName}}</p>
                      </template>
                    </Tooltip>
                  </li>
                  <li>
                    <span class="imitateTitle">创建方式</span>
                    <div class="imitateInput">{{ newVMForm.radio }}</div>
                  </li>
                  <li >
                    <span class="imitateTitle">{{this.newVMForm.radio=='从镜像创建'?'镜像名称':'加载的ISO镜像'}}</span>
                    <div class="imitateInput">{{ newVMForm.imageTypeName }}</div>
                  </li>
                  <li v-if="this.newVMForm.radio=='从镜像创建'">
                    <span class="imitateTitle">创建数量</span>
                    <div class="imitateInput">{{ newVMForm.vmNumber }} 台</div>
                  </li>
                  <li>
                    <span class="imitateTitle">系统版本</span>
                    <div class="imitateInput">{{ newVMForm.sys_version }}</div>
                  </li>
                  <li>
                    <span class="imitateTitle">内存容量</span>
                    <div class="imitateInput">{{ newVMForm.memory }} GB</div>
                  </li>
                  <li>
                    <span class="imitateTitle">VCPU核数</span>
                    <div class="imitateInput">{{ newVMForm.cpu }} 核</div>
                  </li>
                  <li>
                    <span class="imitateTitle">硬盘容量</span>
                    <div class="imitateInput">{{ newVMForm.disk }} GB</div>
                  </li>
                  <!-- <li>
                    <span class="imitateTitle">存储类型</span>
                    <div class="imitateInput">{{ newVMForm.storage_type }}</div>
                  </li> -->
                  <li v-if="newVMForm.data_disk&&newVMForm.radio=='从镜像创建'">
                    <span class="imitateTitle">数据盘容量</span>
                    <div class="imitateInput">{{ newVMForm.ex_volume_size }} GB</div>
                  </li>
                  <!-- <li v-if="newVMForm.data_disk&&newVMForm.radio=='从镜像创建'">
                    <span class="imitateTitle">数据盘类型</span>
                    <div class="imitateInput">{{ newVMForm.data_type }}</div>
                  </li> -->
                  <li>
                    <span class="imitateTitle">网络</span>
                    <Tooltip placement="left">
                      <div class="imitateInput text_overflow">{{ newVMForm.networkName}}</div>
                      <template #content>
                        <p>{{newVMForm.networkName}}</p>
                      </template>
                    </Tooltip>
                  </li>
                  <li v-if="newVMForm.driveDisabled&&newVMForm.radio!=='从镜像创建'">
                    <span class="imitateTitle">驱动</span>
                    <div class="imitateInput">{{ newVMForm.driveName }}</div>
                  </li>
                  <li>
                    <span class="imitateTitle"></span>
                    <div ></div>
                  </li>
                </ul>
              </div>
            </li>
          </ul>
        </div>
      </div>
      <div slot="footer">
        <Button v-if="modalName>0" type="primary" @click="modalName--">上一步</Button>
        <Button type="text" @click="initialmodal = false">取消</Button>
        <Button v-if="modalName<3" type="primary" @click="modalName++">下一步</Button>
        <Button v-if="modalName==3" type="primary" :loading="loading" @click="modalOK">
          <span v-if="!loading">确认</span>
          <span v-else>创建中</span>
        </Button>
      </div>
    </Modal>
  </div>
</template>
<script>
import {typeQuery} from '@/api/storage'; // 类型管理 查询
import {networkTableQuery,networkCheckip} from '@/api/network';
import {imageTableQuery} from '@/api/image'; // 镜像 查询
import {physicsTableDetail} from '@/api/physics'; // 物理机表细节
import {
  vmTemplateNewBuilt, //虚拟机 模板新建
  vmGroupTableNewFromMirror, //虚拟机 从镜像新建
  vmGroupTableNewFromISO, //虚拟机 从ISO新建
} from '@/api/virtualMachine';
export default {
  props: {
    newvmTime: String,
  },
  watch: {
    newvmTime(news){
      if(window.gurl.architecture == "ARM") {
        this.architecture = false
        this.versionData = [{value:"linux"}]
        this.newVMForm.sys_version="linux";
      }else {
        this.architecture = true
        this.versionData = [
          {value:"windows 其它版本"},
          {value:"windows 2003"},
          {value:"windows 2008"},
          {value:"windows 2012"},
          {value:"windows 2016"},
          {value:"linux"}
        ]
        this.newVMForm.sys_version="windows 其它版本";
      }
      this.oldTotal = news.split('/')[0]*1
      this.hostTableData() // 计算资源
      this.networkData() // 网络
      this.imgsData() // 镜像
      this.sysTypeQuery() // 系统类型查询
      // 初始化表单
      this.modalName = 0; // 第一步
      this.newVMForm.name = ""; //
      this.newVMForm.radio = "从镜像创建";
      this.newVMForm.imageTypeName = "";
      this.newVMForm.imageTypeId = "";
      this.newVMForm.presetsIP = false;
      this.newVMForm.yuseDisabled = true;
      this.newVMForm.ipPoolData = [],
      this.newVMForm.ipPash = "";
      this.newVMForm.driveDisabled=false;
      this.newVMForm.driveName = "";
      this.newVMForm.vailabilityZone = "";
      this.newVMForm.computingResourceName = "";
      this.newVMForm.vmNumber = 1;
      this.newVMForm.cpu = 2;
      this.newVMForm.memory = 4;
      this.newVMForm.disk = 100;
      this.newVMForm.data_disk=false;
      this.newVMForm.ex_volume_size=1;
      this.newVMForm.operatingSystemName = this.systemType[0].name
      this.newVMForm.description = "";
      this.newVMForm.hostip = "";
      this.newVMForm.storageid = "";
      this.newVMForm.storagename = "";
      this.newVMForm.filename = "";
      this.loading = false
      this.initialmodal = true
    },
    modalName:{
      handler(news, old) {
        let carousel = document.querySelector("#lunbo");
        carousel.style.transform = `translateX(-${news * 600}px)`;
      },
      // deep:true,
      // immediate:true,
    },
    modalTabs(news, old) {
      if (news == 3) {
        this.modalTabs = 1;
      }
    },
    'newVMForm.radio'(news, old) {
      if (news == "从镜像创建") {
        this.menniuItemData = [
          { name: "基本信息", icon: "icon iconfont icon-erji-yingyonggailan" },
          { name: "计算资源", icon: "icon iconfont icon-cpu" },
          { name: "虚拟机配置", icon: "icon iconfont icon-yunzhuji" },
          { name: "信息概览", icon: "icon iconfont icon-zonghegailan" },
        ];
        this.newVMForm.imageTypeId = this.imageOption[0].id;
        this.newVMForm.imageTypeName = this.imageOption[0].name;
        this.newVMForm.vmNumber = 1
        this.newVMForm.cpu = 2
        this.newVMForm.memory = 4
        this.newVMForm.disk = 4
        // 选中镜像初始化最小硬盘容量
        this.newVMForm.diskMin = this.imageOption[0].size
        this.newVMForm.disk>this.imageOption[0].size?this.newVMForm.disk=100:this.newVMForm.disk=this.imageOption[0].size
      } else if (news == "从ISO创建") {
        this.menniuItemData = [
          { name: "基本信息", icon: "icon iconfont icon-erji-yingyonggailan" },
          { name: "计算资源", icon: "icon iconfont icon-cpu" },
          { name: "硬件配置", icon: "icon iconfont icon-erji-xitongxinxi" },
          { name: "信息概览", icon: "icon iconfont icon-zonghegailan" },
        ];
        this.newVMForm.imageTypeId = this.imageOptionISO[0].id;
        this.newVMForm.imageTypeName = this.imageOptionISO[0].name;
        this.newVMForm.operatingSystemName = this.systemType[0].name
        this.newVMForm.driveimageId = this.imageOptionISO[0].id;
        this.newVMForm.driveName = this.imageOptionISO[0].name;
        // 选中iso初始化最小硬盘容量
        this.newVMForm.diskMin = this.imageOptionISO[0].size
        // this.newVMForm.disk>this.imageOptionISO[0].size?this.newVMForm.disk=100:this.newVMForm.disk=this.imageOptionISO[0].size
      }
    },
    "newVMForm.presetsIP"(news) {
      if(news){
        
      }else {
        this.newVMForm.vmNumber=1
        this.newVMForm.ipPash = ""
        this.newVMForm.ipPoolData = []
      }
    }
  },
  data(){
    const determine = (rule, value, callback) => {
      let list = /^[\u4e00-\u9fa5_a-zA-Z0-9@_.-]{2,32}$/;
      if (list.test(value)) {
        callback();
      } else {
        callback(new Error("请输入2-32个中文或字符（特殊字符可用@_.-）"));
      }
    };
    const propipPash = (rule, value, callback) => {
      let list = /^(25[0-4]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-4]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-4]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-4]|2[0-4][0-9]|[01]?[1-9][0-9]?|1[0-9]{2}|2[0-4][0-9])$/
      let subnet = this.newVMForm.networkName.split(':')[1]
      if(list.test(value)) {
        if(this.ipInSubnet(value, subnet)) {
          if(this.newVMForm.radio == '从镜像创建' && this.newVMForm.ipPoolData.indexOf(value)!==-1) {
            this.newVMForm.yuseDisabled = true
            callback(new Error("该ip不可用"))
          }else {
            this.newVMForm.yuseDisabled = false
            callback()
          }
        }else {
          this.newVMForm.yuseDisabled = true
          callback(new Error("IP地址与所选网段不匹配"))
        }
      }else {
        this.newVMForm.yuseDisabled = true
        callback(new Error("该ip不可用"))
      }
    };
    return {
      initialmodal:false,
      loading:false, // 禁止多次触发
      architecture:false, // 架构是否启用
      // 新建虚拟机 弹框 导航默认
      menniuItemData: [
        { name: "基本信息", icon: "icon iconfont icon-erji-yingyonggailan" },
        { name: "计算资源", icon: "icon iconfont icon-cpu" },
        { name: "虚拟机配置", icon: "icon iconfont icon-yunzhuji" },
        { name: "信息概览", icon: "icon iconfont icon-zonghegailan" },
      ],
      
      // 操作系统类型
      systemType: [
        { name: "Microsoft Windows Server 2019(64位)", id: 2 },
        { name: "Microsoft Windows Server 2016(64位)", id: 3 },
        { name: "Microsoft Windows Server 2012 R2(64位)", id: 4 },
        { name: "Microsoft Windows Server 2012(64位)", id: 1 },
        { name: "Microsoft Windows Server 2008 R2(64位)", id: 5 },
        { name: "Microsoft Windows Server 2008(64位)", id: 6 },
        { name: "Microsoft Windows Server 2008(32位)", id: 7 },
        { name: "Microsoft Windows Server 2003 R2(64位)", id: 8 },
        { name: "Microsoft Windows Server 2003(64位)", id: 9 },
        { name: "Microsoft Windows Server 2003(32位)", id: 10 },
        { name: "Microsoft Windows 10(64位)", id: 11 },
        { name: "Microsoft Windows 10(32位)", id: 12 },
        { name: "Microsoft Windows 8.1(64位)", id: 13 },
        { name: "Microsoft Windows 8.1(32位)", id: 14 },
        { name: "Microsoft Windows 8(64位)", id: 15 },
        { name: "Microsoft Windows 8(32位)", id: 16 },
        { name: "Microsoft Windows 7(64位)", id: 17 },
        { name: "Microsoft Windows 7(32位)", id: 18 },
        { name: "Microsoft Windows XP(64位)", id: 19 },
        { name: "Microsoft Windows XP(32位)", id: 20 },
        { name: "Red Hat Enterprise Linux 8(64位)", id: 21 },
        { name: "Red Hat Enterprise Linux 8(32位)", id: 22 },
        { name: "Red Hat Enterprise Linux 7(64位)", id: 23 },
        { name: "Red Hat Enterprise Linux 7(32位)", id: 24 },
        { name: "Red Hat Enterprise Linux 6(64位)", id: 25 },
        { name: "Red Hat Enterprise Linux 6(32位)", id: 26 },
        { name: "Red Hat Enterprise Linux 5(64位)", id: 27 },
        { name: "Red Hat Enterprise Linux 5(32位)", id: 28 },
        { name: "CentOS 8(64位)", id: 29 },
        { name: "CentOS 8(32位)", id: 30 },
        { name: "CentOS 7(64位)", id: 31 },
        { name: "CentOS 7(32位)", id: 32 },
        { name: "CentOS 6(64位)", id: 33 },
        { name: "CentOS 6(32位)", id: 34 },
        { name: "CentOS 5(64位)", id: 35 },
        { name: "CentOS 5(32位)", id: 36 },
        { name: "Ubuntu Linux(64位)", id: 37 },
        { name: "Ubuntu Linux(32位)", id: 38 },
        { name: "Oracle Linux7(64位)", id: 39 },
        { name: "Oracle Linux7(32位)", id: 40 },
        { name: "Oracle Linux6(64位)", id: 41 },
        { name: "Oracle Linux6(32位)", id: 42 },
        { name: "Oracle Linux5(64位)", id: 43 },
        { name: "Oracle Linux5(32位)", id: 44 },
        { name: "Oracle Linux4(64位)", id: 45 },
        { name: "Oracle Linux4(32位)", id: 46 },
        { name: "银河麒麟服务器版V10(64位)", id: 47 },
        { name: "银河麒麟桌面版V10(64位)", id: 48 },
        { name: "UOS 20 SP1 桌面版(64位)", id: 49 },
        { name: "UOS SP2 桌面版(64位)", id: 50 },
        { name: "Other Linux(32位)", id: 51 },
        { name: "Other Linux(64位)", id: 52 },
      ],
      types_data:[], // 存储数据
      netOption: [], // 网络名称数据
      versionData:[
        {value:"windows 其它版本"},
        {value:"windows 2003"},
        {value:"windows 2008"},
        {value:"windows 2012"},
        {value:"windows 2016"},
        {value:"linux"},
      ], // 系统型号
       // 新建虚拟机 弹框 硬件配置项 及信息概览
      newVMForm: {
        // 基本信息
        name: "", // 虚拟机名
        radio: "从镜像创建", // 创建方式 
        operatingSystemName:"", // 操作系统类型
        imageTypeName: "", // 镜像选择(选中) 
        networkId: "", // 网络名称(选中)
        storage_type:"", // 存储类型
        sys_version:"windows 其它版本", // 系统型号
        presetsIP:false, // 预设IP
        ipPash:"", // IP地址(输入框)
        yuseDisabled:true, // IP地址添加动作
        ipPoolData:[], //IP地址池(多个IP)
        // 计算资源
        vailabilityZone: "", // 集群id(表格选中)
        computingResourceName: "", // 集群组(表格选中)
        // 虚拟机配置
        vmNumber: 1, // 虚拟机数量
        cpu: 2, // VCPU核数
        memory: 4, // 内存容量
        disk: 100, // 硬盘容量
        diskMin:100, // 最小硬盘容量
        // 硬件配置
        imageTypeId: "", // 加载ISO镜像
        driveDisabled:false, // 是否启用驱动
        driveimageId:"", // 驱动选择
        data_disk:false, // 是否启用数据盘
        ex_volume_size:1, // 数据盘容量
        data_type:"", // 数据盘类型

        // 信息概览
        driveName:"", // 驱动 
        networkName: "", // 网络名称
        // description: "", // 其他
      },
      // 计算资源列
      computingResourceColumn: [
        { title: "单选",width: 60, key: "radio",align: "center",
          render: (h, params) => {
            let flag = false;
            if (this.newVMForm.vailabilityZone == params.row.availability_zone) {
              flag = true
            }else {
              flag = false
            }
            return h('div',[
              h('Radio',{
                props: {
                  value:flag,
                },
                on:{
                  'on-change':()=>{
                    this.newVMForm.vailabilityZone = params.row.availability_zone
                    this.newVMForm.computingResourceName = params.row.name;
                  },
                }
              })
            ])
          }
        },
        { title: "集群组", key: "name" },
        { title: "CPU使用率", key: "cpu",align: "center",
          render: (h, params) => {
            return h("Progress",{
              props: {
                strokeColor:params.row.cpu>70?["#f45d3f","#f45d3f"]:["#2ebe76","#2ebe76"],
                percent: parseFloat(
                  params.row.cpu > 100 ? 99 : params.row.cpu
                ),
              },
            },params.row.cpu == null ? 0 : params.row.cpu+"%");
          },
        },
        { title: "内存使用率", key: "ram" ,align: "center",
          render: (h, params) => {
            return h("Progress",{
              props: {
                strokeColor:params.row.ram>70?["#f45d3f","#f45d3f"]:["#2ebe76","#2ebe76"],
                percent: parseFloat(
                  params.row.ram > 100 ? 99 : params.row.ram
                ),
              },
            },params.row.ram == null ? 0 : params.row.ram+"%");
          },
        },
        { title: "虚拟机数量", key: "vms",align: "center",
          render: (h, params) => {
            return h("span",params.row.vms)
          },
        },
      ],
      computingResourceData: [], // 计算资源行
      imageOption: [], // 镜像选择
      imageOptionISO: [], // 驱动选择
      // 新建虚拟机ISO创建
      modalName: 0, // Menu导航选中
      modalTabs: null,
      oldTotal:0, // 表格总数

      ruleValidate:{
        // 虚拟机名称
        name:[
          { validator: determine, trigger: "change" },
          { validator: determine, trigger: "blur" },
        ],
        // 镜像选择-加载ISO镜像
        imageTypeId:[{ required: true, message: "必选项", trigger: "blur" }],
        // 操作系统类型
        operatingSystemName:[{ required: true, message: "必选项", trigger: "blur" }],
        // 网络名称
        networkId:[{ required: true, message: "必填项", trigger: "blur" }],
        storage_type:[{ required: true, message: "必填项", trigger: "blur" }],
        sys_version:[{ required: true, message: "必选项", trigger: "blur" }],
        // IP地址
        ipPash:[
          { required: true, message: " "},
          { validator: propipPash, trigger: "change" },
        ],
        // 驱动选择
        driveimageId:[{ required: true, message: "必选项", trigger: "blur" }],
      }
    }
  },
  methods: {
    // 获取计算资源表数据
    hostTableData(){
      physicsTableDetail()
      .then((callback) => {
        // callback.data[0]['_highlight'] = true
        this.computingResourceData = callback.data;
        this.newVMForm.vailabilityZone = callback.data[0].availability_zone;
        this.newVMForm.computingResourceName = callback.data[0].name;
      }).catch((error) => {})
    },
    // 获取网络下拉框数据
    networkData(){
      networkTableQuery().then(callback=>{
        let arr = new Array();
        callback.data.forEach(item=>{
          item.cidr.forEach((em,i) => {
            arr.push({
              id: item.id+":"+item.subnets[i],
              name: em,
            })
          })
        })
        this.netOption = arr;
        this.newVMForm.networkId = this.netOption[0].id
        this.newVMForm.networkName = this.netOption[0].name
      })
    },
    // 获取镜像下拉框数据
    imgsData(){
      imageTableQuery()
      .then((callback) => {
        let iso = new Array();
        let jx = new Array();
        for (var i = 0; i < callback.data.length; i++) {
          if (callback.data[i].disk_format == "iso") {
            iso.push({ id: callback.data[i].id, name: callback.data[i].name, size: Math.ceil((callback.data[i].size/1024/1024/1024)) });
          } else {
            jx.push({ id: callback.data[i].id, name: callback.data[i].name, size: Math.ceil((callback.data[i].size/1024/1024/1024)) });
          }
        }
        this.imageOptionISO = iso;
        this.imageOption = jx;
        this.newVMForm.imageTypeId = this.imageOption[0].id;
        this.newVMForm.imageTypeName = this.imageOption[0].name;
        // 初始化硬盘容量
        this.newVMForm.diskMin = this.imageOption[0].size
        this.newVMForm.disk>this.imageOption[0].size?this.newVMForm.disk=100:this.newVMForm.disk=this.imageOption[0].size
        this.newVMForm.diskMin = this.imageOption[0].size
      });
    },
    // 获取系统类型下拉数据
    sysTypeQuery(){
      typeQuery().then(callback=>{
        let arr = new Array()
        callback.data.forEach(em=>{
          arr.push({
            id:em.id,
            name:em.name=="__DEFAULT__"?"默认类型": em.name,
            extra_specs:em.extra_specs,
            description:em.description,
          })
        })
        this.types_data = arr
        this.newVMForm.storage_type = arr[0].name
        this.newVMForm.data_type = arr[0].name
      })
    },
    // 预设IP 加如池  
    addIPpool(){
      networkCheckip({
        now_ipv4:'',
        new_ipv4:this.newVMForm.ipPash
      }).then((callback)=>{
        if(callback.data.msg == 'ok') {
          this.newVMForm.ipPoolData.push(this.newVMForm.ipPash)
          this.newVMForm.ipPash = ""
          this.newVMForm.yuseDisabled = true
          this.newVMForm.vmNumber= this.newVMForm.ipPoolData.length
        }else {
          this.$Message.error({
            background: true,
            closable: true,
            duration: 10,
            content: callback.data.msg
          });
        }
      })
    },
    // 预设IP 去除池 
    deleIPpool(index){
      this.newVMForm.ipPoolData.splice(index, 1)
      this.newVMForm.vmNumber= this.newVMForm.ipPoolData.length
    },
    // 虚拟机选择
    formvm(val) {
      return val + "个";
    },
    // cpu选择
    formCpu(val) {
      return val + "核";
    },
    // 内存选择
    formMemory(val) {
      return val + " GB";
    },
    diskONchange(value){
      this.newVMForm.diskMin>this.newVMForm.disk?this.newVMForm.disk=this.newVMForm.diskMin:""
    },
    diskONblur(){
      this.newVMForm.diskMin>this.newVMForm.disk?this.newVMForm.disk=this.newVMForm.diskMin:""
    },
    // 硬盘选择
    formDisk(val) {
      return val + " GB";
    },
    // 镜像选择
    netIMGchange(item) {
      if(item!==undefined) {
        this.newVMForm.imageTypeName = item.label;
        this.newVMForm.imageTypeId = item.value;
        if(this.newVMForm.radio=="从镜像创建") {
          // 从镜像创建虚拟机判断镜像容量赋值给硬盘容量
          this.imageOption.forEach(em=>{
            if(em.id==item.value) {
              this.newVMForm.disk>em.size?this.newVMForm.disk=100:this.newVMForm.disk=em.size
              this.newVMForm.diskMin = em.size
            }
          })
        }else {
          // 从ISO创建虚拟机判断镜像容量赋值给硬盘容量
          this.imageOptionISO.forEach(emiso=>{
            if(emiso.id==item.value) {
              // this.newVMForm.disk>emiso.size?this.newVMForm.disk=100:this.newVMForm.disk=emiso.size
              this.newVMForm.diskMin = emiso.size
            }
          })
        }
      }
    },
    netSlectChange(item) {
      if(item!==undefined) {
        this.newVMForm.networkName = item.label;
        this.newVMForm.networkId = item.value;
      }
    },
    // 驱动选择
    driveSelect(item){
      this.newVMForm.driveName = item.label;
      this.newVMForm.driveimageId = item.value;
    },
    // 操作系统类型
    iosIMGchange(item) {
      this.newVMForm.operatingSystemName = item.value;
    },
    modalMenuItem(item) {
      this.modalName = item;
    },
    modalOK() {
      let ruleName = /^[\u4e00-\u9fa5_a-zA-Z0-9@_.-]{2,32}$/;
      if (ruleName.test(this.newVMForm.name)) {
          this.loading = true // 确认按钮状态不可用
          let templateData = { // 模板参数
            name: this.newVMForm.cpu +"-" +this.newVMForm.memory +"-" +this.newVMForm.disk, //模板名称
            ram: this.newVMForm.memory, // 模板内存
            vcpus: this.newVMForm.cpu, // 模板cpu
            disk: this.newVMForm.disk, // 模板硬盘
            extdisk: 0, // 模板未用到的参数
            ...(this.newVMForm.sys_version=="windows 其它版本"||this.newVMForm.sys_version=="linux"?"":{metadata_sys_version:this.newVMForm.sys_version}) // 模板海光系统特定字段
          } 
          vmTemplateNewBuilt(templateData)
            .then((callbackTemplate) => {
              let vmData = { // VM参数
                flavorRef:callbackTemplate.data.id, // 挂载模板id
                name:this.newVMForm.name, // 名称
                availability_zone:this.newVMForm.vailabilityZone, // 挂载计算资源
                imageRef:this.newVMForm.imageTypeId, // vm 挂载镜像
                networkRef:this.newVMForm.networkId.split(":")[0], // 网络ID
                subnet_id:this.newVMForm.networkId.split(":")[1], // 子网ID
                os_type: this.newVMForm.sys_version=="linux"?"linux":"windows", // 系统类型默认填充
                ipv4:!this.newVMForm.presetsIP?"":this.newVMForm.radio == "从镜像创建"?this.newVMForm.ipPoolData.join(","):this.newVMForm.ipPash, // 使用预设IP
                ...(this.newVMForm.radio == "从镜像创建"?{count:this.newVMForm.vmNumber}:""), // vm 数量(仅限镜像)
                ...(this.newVMForm.radio == "从镜像创建"&&this.newVMForm.data_disk?{ex_volume_size:this.newVMForm.ex_volume_size}:""), // 使用数据盘(仅限镜像)
                ...(this.newVMForm.radio !== "从镜像创建"?{driveimageId:this.newVMForm.driveDisabled?this.newVMForm.driveimageId:""}:"") // 使用驱动(仅限ISO)
              };
              if(this.newVMForm.radio == "从镜像创建") {
                vmGroupTableNewFromMirror(vmData)
                .then((callbackIMG) => {
                  if(callbackIMG.data.msg == "ok") {
                    this.$emit("return-ok",'新建虚拟机操作完成');
                    this.initialmodal = false; // 弹框关闭
                  }else {
                    this.$emit("return-error",'新建虚拟机操作失败');
                  }
                })
                .catch((error) => {
                  this.disabled = false
                  this.$emit("return-error",'新建虚拟机操作失败');
                })
              }else {
                vmGroupTableNewFromISO(vmData)
                .then((callbackISO) => {
                  this.loading = false
                  if(callbackISO.data.msg == "ok") {
                    this.$emit("return-ok",'新建虚拟机操作完成');
                    this.initialmodal = false; // 弹框关闭
                  }else {
                    this.loading = true
                    this.$emit("return-error",'新建虚拟机操作失败');
                  }
                })
                .catch((error) => {
                  this.disabled = false
                  this.$emit("return-error",'新建虚拟机操作失败');
                })
              }
            })
      } else {
        this.$Message.warning({
          background: true,
          closable: true,
          duration: 5,
          content: "有不符合规定的数据，无法创建。",
        });
      }
    },
    cancel(){
      // this.initialmodal =false
    },
    // 判断网络属于子网
    ipInSubnet(ip, subnet) {
      let [subnetIp, maskBits] = subnet.split('/');
      maskBits = parseInt(maskBits, 10);
      let ipBinary = ip.split('.').map(octet => parseInt(octet, 10).toString(2).padStart(8, '0')).join('');
      let subnetBinary = subnetIp.split('.').map(octet => parseInt(octet, 10).toString(2).padStart(8, '0')).join('');
      for(let i=0; i<maskBits; i++){
          if(ipBinary[i] != subnetBinary[i]){
              return false;
          }
      }
      return true;
    },
  }
}
</script>
<style>
  .shujupan .ivu-input-number {
    width:150px;
  }
</style>