<template>
  <div>
    <Modal
      v-model="model"
      width="600"
      :mask-closable="false"
    >
      <template #header><p><span style="color:green">{{vmRow.name}}</span>{{vmRow.status!=='SHUTOFF'?'虚拟机热迁移设置':'虚拟机冷迁移设置'}}</p></template>
      <Form :model="formItem"  ref="formItem" :rules="ruleValidate" :label-width="150">
        <FormItem label="当前虚拟机名称">
          <Input v-model="vmRow.name" disabled></Input>
        </FormItem>
        <FormItem label="源主机">
          <Input v-model="vmRow.hostname" disabled></Input>
        </FormItem>
        <FormItem label="目标主机" v-if="vmRow.status!=='SHUTOFF'" prop="hostName">
          <Select v-model="formItem.hostName" placeholder="无可用主机">
            <Option
              v-for="item in hostData"
              :value="item.value"
              :key="item.value"
              >{{ item.label }}</Option
            >
          </Select>
        </FormItem>
      </Form>
      <div slot="footer">
        <Button type="text" @click="model = false">取消</Button>
        <Button type="primary" @click="modelOK" :disabled="disabled">确认</Button>
      </div>
    </Modal>
  </div>
</template>
<script>
import {
  vmMigratablePhysicalMachine, // 虚拟机组表格 可迁移物理机
  vmGroupTableAction, // 虚拟机组表格 操作(开机 关机 重启 控制台等接口调用)
} from '@/api/virtualMachine'; 
export default {
  props: {
    vmRow: Object,
    coldHeatTime: String,
  },
  watch: {
    coldHeatTime(news){
      this.vmRow.status == 'enable'?this.moveData='ok':'no'
      let arr = new Array()
      vmMigratablePhysicalMachine().then((callback) => {
        callback.data.forEach((item,index) => {
          if (item.host_name !== this.vmRow.hostname) {
            arr.push({
              label: item.host_name,
              value: item.host_name,
            });
          }
        })
        this.hostData = arr;
        if(arr == 0) {}else {
          this.formItem.hostName = arr[0].value
        }
      })
      this.disabled = false
      this.model = true
    }
  },
  data(){
    const propHost = (rule, value, callback) => {
      if(value!==""){
        callback();
      }else {
        callback(new Error("必选项"))
      }
    }
    return {
      model:false,
      disabled:  false,
      formItem: {
        hostName: '',
      },
      hostData: [],
      // 正则判断用户输入
      ruleValidate: {
        hostName:[
          { required: true, message: "必选项", trigger: "change" },
          { validator: propHost, trigger: "change" },
        ],
      },
    }
  },
  methods: {
    // 弹框OK
    modelOK() {
      this.$refs.formItem.validate((valid) => {
        if(valid) {
          this.disabled = true;
          vmGroupTableAction({
            id: this.vmRow.id,
            action: this.vmRow.status !== 'SHUTOFF'?'live_migrate':'migrate',
            data: this.vmRow.status !== 'SHUTOFF'?this.formItem.hostName:'',
            name:this.vmRow.name
          })
          .then((callback) => {
            if(callback.data.msg == "ok") {
              this.model = false
              this.$emit("return-ok",(this.vmRow.status!=='SHUTOFF'?'热迁移设置':'冷迁移设置')+'操作完成');
            }else {
              this.disabled = false
              this.$emit("return-error",(this.vmRow.status!=='SHUTOFF'?'热迁移设置':'冷迁移设置')+'操作失败');
            }
          })
          .catch((error) => {
            this.disabled = false
            this.$emit("return-error",(this.vmRow.status!=='SHUTOFF'?'热迁移设置':'冷迁移设置')+'操作失败');
          })
        }
      })
    },
  }
}
</script>