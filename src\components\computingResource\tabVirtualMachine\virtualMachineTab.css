.ivu-icon-md-add-circle {
  padding: 0 7px;
  cursor: pointer;
}
.vm_manage_area {
  position: relative;
  display: flex;
  height: calc(100% - 70px);
  border-radius: 10px;
  background-color: #FFF;
  padding: 15px 5px;
}
.vm_group_area {
    min-width: 200px;
    border-right: 1px solid #ccc;
}
/* 新建虚拟机侧边栏 */
.ivu-menu-light.ivu-menu-vertical .ivu-menu-item-active:not(.ivu-menu-submenu) {
  color: #fff;
  background: #fb6129;
}
.ivu-menu-light.ivu-menu-vertical .ivu-menu-item:hover,
.ivu-menu-light.ivu-menu-vertical .ivu-menu-submenu-title:hover {
  color: #333;
  background: #ffe9e1;
}
.ivu-menu-light.ivu-menu-vertical
  .ivu-menu-item-active:not(.ivu-menu-submenu):after {
  background: none;
}
/* 虚拟机表格区域 */
.vm_table_area {
  width: 100%;
  height: 100%;
  padding: 0 10px;
  /* overflow: auto; */
}

/* 详情 */
.vm_drawer_basic {
  height: 70px;
  border-bottom: 1px solid #ccc;
  display: flex;
  align-items: center;
}

.vm_drawer_basic li {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 60px;
  float: left;
  overflow: hidden;
}
.vm_drawer_charts ul {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column
}
.vm_drawer_charts ul li {
  width: 100%;
  padding: 20px 0;
  border-bottom: 1px solid #ccc;
}
.vm_drawer_charts p {
  padding: 0 0 10px 20px;
  font-size: 14px;
  color: #333;
}
.xq{
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  padding-left:20px;
}
.xq .divxq {
  display: flex;
}
.xq .lable {
  width: 110px;
  color: #999;
  display: inline-block;
}
.xq .key {
  width: 230px;
  color: #333;
  display: inline-block;
  white-space: nowrap;
  overflow: hidden;
}
.ivu-icon {
  font-size:16px;
}
#lunbo {
  width: 2460px;
  height: 480px;
  transform: translateX(0px);
  transition: transform 300ms;
  visibility: visible;
  /* transform: translate3d(0px, -452px, 0px); */
  /* will-change: transform; */
}
#lunbo>li {
  float: left;
  width: 600px;
  height: 480px;
  padding: 0 10px;
  overflow: auto;
}
.vm_new_dialog .ivu-menu-light {
  width: 160px!important;
  min-width: 160px!important;
  max-width: 160px!important;
}
.vm_new_dialog {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
}
.modalcontent {
  width: 608px;
  overflow: hidden;
}
.xinxigailan ul {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-around;
}
.xinxigailan ul li{
  width: 49%;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
}
.xinxigailan .imitateTitle {
  display: inline-block;
  width: 110px;
  text-align: right;
}
.xinxigailan .imitateInput{
  width: 170px;
  display: inline-block;
  height:32px;
  line-height:32px;
  background: #f3f3f3;
  border: 1px solid #ccc;
  /* cursor: not-allowed; */
  padding-left:5px;
  margin-left:5px;
  overflow: hidden;
}
.vm_manage_area p {
  font-weight: 600;
  border-left: 4px solid #fb6129;
  margin: 0 0 5px 5px ;
  padding-left: 10px;
  color: #333;
  font-size: 14px;
}
.virtual_unit {
  display: flex;
  flex-direction: row;
  justify-content: space-evenly;
  height: 36px;
  border-top: 1px solid #ccc;
  /* border-bottom: 1px solid #ccc; */
  /* margin-bottom: 2px; */
}
.virtual_unit >.ivu-tooltip {
  line-height: 36px;
}
.ivu-btn:focus{
  box-shadow: none!important;
}
.xinxigailan {
  padding: 30px 0;
}
.cipan {
  display: block;
}
.cipan >div>ul> li {
  display: flex;
  flex-direction: row;
  justify-content: space-evenly;
  align-items: center;
  margin-bottom: 24px;
}
.cipan span {
  width: 100px;
  text-align: right;
}

/* 虚拟机分组 */
.vm_tree_area {
  width: 200px;
  height: calc(100% - 70px);
  overflow:auto;
}

/* 任务模块 */
.vm_task_aera {
  width: 100%;
  height: calc(100% - 690px);
  overflow:auto;
}
.task_title {
  display: flex;
  align-items: center;
}
.task_title>h4 {
  width: 40px;
}
.task_title>span {
  display: inline-block;
  border-bottom: 1px solid #ccc;
  width: 100%;
}