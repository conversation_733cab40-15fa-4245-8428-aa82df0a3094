<template>
  <div class="general_page_area">
    <Form :model="formItem" ref="formItem" :rules="ruleValidate" :label-width="120">
			<FormItem label="标签">
        <Input v-model="formItem.labels" type="textarea" :rows="14" placeholder="请输入标签数据。例：KEY1=VALUE1,KEY2=VALUE2..."></Input>
    	</FormItem>
		</Form>
  </div>
</template>
<script>
export default {
  props: {
    times:String,
  },
  watch: {
    times(value) {
      this.$emit("returnOK",{
        page: 4,
        data: this.formItem,
      });
      // this.$refs.formItem.validate((valid) => {
      //   if (valid) {
      //     this.$emit("returnOK",{
      //       page: 4,
      //       data: this.formItem,
      //     });
      //   }
      // })
      
    }
  },
  data(){
    return {
      formItem: {
        labels: '',
      },
      ruleValidate:{
        labels:[{ required: true, message: '必填项', trigger: 'change' }],
      }
    }
  },
  methods: {

  },
}
</script>
<style lang="less" scoped>
  .general_page_area {
    width: 100%;
    height: 100%;
    overflow: auto;
  }
</style>