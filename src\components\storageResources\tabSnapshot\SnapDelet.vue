<template>
  <div>
    <Modal
      v-model="model"
      width="600"
      :mask-closable="false"
    >
      <template #header><p>删除快照</p></template>
      <div style="padding: 5px">
        <span>是否删除下列快照？</span>
        <p style="color:red;word-wrap: break-word">{{tableNames.toString()}}</p>
      </div>
      <div slot="footer">
        <Button type="text" @click="model = false">取消</Button>
        <Button type="primary" @click="modelOK" :disabled="disabled">确认</Button>
      </div>
    </Modal>
  </div>
</template>
<script>
import {
  snapshotDelete, // 快照 删除
} from '@/api/storage';
export default {
  props: {
    tableDelet: Array,
    deleteTime: String,
  },
  watch: {
    deleteTime(news){
      this.tableNames = this.tableDelet.map(em=>{ return em.name})
      this.tableIDS = this.tableDelet.map(em=>{ return em.id})
      this.model = true
      this.disabled = false
    }
  },
  data(){
    return {
      model:false,
      disabled: false,
      tableNames: [],
      tableIDS: [],
    }
  },
  methods: {
    modelOK() {
      this.disabled = true
      snapshotDelete({data:{
        ids: this.tableIDS,
        names: this.tableNames
      }})
      .then(callback=>{
        if(callback.data.msg=="ok") {
          this.model = false
          this.$emit("return-ok",'删除快照操作完成');
        }else {
          this.disabled = false
          this.$emit("return-error",callback.data.msg);
        }
      })
      .catch((error) => {
        this.disabled = false
        this.$emit("return-error",'删除快照操作失败');
      })
    },
    
  },
}
</script>