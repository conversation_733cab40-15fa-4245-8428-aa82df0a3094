// import util from "../../libs/util";
const state ={
    //lock开关
    lockState:false,
}
const mutations ={
    //更改updateModal的函数,使控件出现
    updateTrue(state) {
        state.updateModal = true;
    },
    //更改updateModal的函数，使控件隐藏
    updateFalse(state) {
        state.updateModal = false;
    },
    //更改客户端标签的title
    clientTitle(state, title) {
        state.clientTitle = title;
    },
    //获得当前行的客户id
    getClientId(state, id) {
        state.clientId = id;
    },
    //获取当前方法需要的url
    getUrl(state, url) {
        state.url = url;
    },
    //clice-basic数据
    getBasic(state, obj) {
        state.returnMessage = obj.row;
    },
}
export default{
    state,
    mutations,
}