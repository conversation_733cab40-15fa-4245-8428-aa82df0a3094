<template>
  <div class="general_page_area">
    <Form :model="formItem" ref="formItem" :rules="ruleValidate" :label-width="120">
			<FormItem label="名称" prop="name">
        <Input v-model="formItem.name"></Input>
    	</FormItem>
		</Form>
  </div>
</template>
<script>
export default {
  props: {
    times:String,
  },
  watch: {
    tabSelected(value) {
      this.$refs.formItem.validate((valid) => {
        if (valid) {
          this.$emit("returnOK",{
            page: 0,
            type: 'ok'
          });
        }
      })
      
    }
  },
  data(){
    return {
      formItem: {
        name: '',
      },
      ruleValidate:{
        name:[
          { required: true, message: '必填项', trigger: 'change' },
        ],
      }
    }
  },
  methods: {

  },
}
</script>
<style lang="less" scoped>
  .general_page_area {
    width: 100%;
    height: 100%;
    overflow: auto;
  }
</style>