<template>
  <div>
    <Modal
      v-model="model"
      width="600"
      :mask-closable="false"
    >
      <template #header><p>删除用户</p></template>
      <div style="padding: 5px">
        <span>是否删除下列用户？</span>
        <p style="color:red;word-wrap: break-word">{{tableNames.toString()}}</p>
      </div>
      <div slot="footer">
        <Button type="text" @click="model = false">取消</Button>
        <!-- <Button type="primary" :loading="disabled" @click="modelOK">
          <span v-if="!disabled">确认</span>
          <span v-else>删除中</span>
        </Button> -->
        <Button type="primary" @click="modelOK" :disabled="disabled">确认</Button>
      </div>
    </Modal>
  </div>
</template>
<script>
import { userTableDelete } from "@/api/system";
export default {
  props: {
    tableDelete: Array,
    deleteTime: String,
  },
  watch: {
    deleteTime(news){
      this.tableNames = this.tableDelete.map(em=>{ return em.name})
      this.tableIDS = this.tableDelete.map(em=>{ return em.id})
      this.model = true
      this.disabled = false
    }
  },
  data(){
    return {
      model:false,
      disabled: false,
      tableNames: [],
      tableIDS: [],
    }
  },
  methods: {
    modelOK() {
      this.disabled = true
      userTableDelete(
        {data:{
          userids: this.tableIDS,
          usernames:this.tableNames
        }}
      )
      .then(callback=>{
        if(callback.data.msg=="ok") {
          this.$emit("custom-ok",'删除用户操作完成')
          this.model = false;
        }else {
          this.$emit("custom-error",'删除用户操作失败')
          this.disabled = false;
        }
      })
      .catch((error) => {
        this.$emit("custom-error",'删除用户操作失败');
        this.disabled = false;
      })
     
    }
  }
}
</script>