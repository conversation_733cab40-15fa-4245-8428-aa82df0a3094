.logo_crh {
  background-image: url("../../assets/login_crh.png");
  background-size: 100% 100%;
}
.logo_ygl {
  background-image: url("../../assets/login_ygl.jpg");
  background-size: 100% 100%;
}
.login_form {
  width: 100%;
  height: 100%;
}
.login_form .login_logo {
  position: absolute;
  z-index: 2;
  top: 50px;
}

.login_form .center {
  width: 100%;
  min-width: calc(var(--font) *1536);
  margin-top: 10%;
}

.login_container {
  /* width:calc(var(--font) *430);
  height: calc(var(--font) *304); */
  width: 340px;
  padding: 36px 0;
  z-index: 2;
  top: 0;
  margin-top: 13.7%;
  margin-left: 77%;
  transform: translateX(-50%);
  background: #fff;
  position: absolute;
  border-radius: 5px;
  box-shadow: 0 0 10px #fff0eb;
}
.login_container > h2 {
  text-align: center;
  padding-bottom: 24px;
  color: #333;
}
.login_page_logo {
  position: absolute;
  left: 25%;
  top: 5%;
  transform:translateX(-50%);
}

.loginform_style {
  padding: 0 36px;
  /* margin-top: 50px; */
}

/* 新用户注册层 */
.new_user_moda {
  margin: 5% auto;
  width: 60%;
  height: 90%;
  border: 1px solid #777;
  position: relative;
  box-shadow: -4px 4px 4px #777;
  padding: 30px 10px;
}

.new_user_moda h1 {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  top: -20px;
  display: block;
  /* border: 1px solid red; */
  padding: 0px 15px;
  background: #fff
}

.new_user_moda .ivu-form {
  width: 50%;
  margin: 10% auto;
}

/* 温馨提示 */
.reminder {
  padding: 20px;
}
.reminder ul li {
  padding: 10px 0;
  display: block;
  height:50px;
}
/* 登录按钮 */
.login_but {
  width: 100%;
  color:#fff!important;
  font-size: 12px!important;
  border:none;
  background-image: linear-gradient(to right,#f7d58e,#fb6129)!important;
}
.login_but:hover {
  background-image: linear-gradient(to right,#f0c568,#fa5012)!important;
  border-color: #fff!important;
  color:#fff!important;
}