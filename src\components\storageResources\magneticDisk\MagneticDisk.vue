<template>
  <!-- 存储单元 -->
  <div style="height: 90%;overflow: auto;">
    <div class="table-button-area">
      <div>
        <Button class="plus_btn" @click="newClick"><span class="icon iconfont icon-plus-circle"></span> 新建存储</Button>
        <Button class="plus_btn" @click="MasterTableData"><span class="icon iconfont icon-plus-circle"></span> 刷新</Button>
      </div>
      <div >
        <!-- <Input v-model="httpTableData.search_str" search enter-button placeholder="请输入名称" style="width: 300px" @on-search="tableSearchInput" /> -->
      </div>
    </div>
    <div style="position:relative;">
      <Table :columns="storageColumns" :data="storageData"></Table>
      <Spin fix v-if="spinShow" size="large" style="color:#ef853a">
        <Icon type="ios-loading" size=50 class="demo-spin-icon-load"></Icon>
        <div style="font-size:16px;padding:20px">Loading...</div>
      </Spin>
    </div>
  </div>
</template>

<script>
  export default {
    props: {
      tabName: String,
    },
    watch: {
      tabName(value) {
        value=='cuncudanyuan'?this.MasterTableData():clearInterval(this.times)
      },
    },
    data() {
      return{
        spinShow:false,
        // 卷池表数据
        storageColumns:[
          // {type: 'selection',width: 30,align: 'center'},
				  { title: "存储主机节点",key: "node_name",sortable: true,width:120,
            render: (h, params) => {
              return h("span",params.row.host.name)
            }
          },
				  { title: "ID",key: "id",sortable: true,align: "center",width:50},
				  { title: "状态",key: "state",sortable: true,align: "center",width:80,
            render: (h, params) => {
              let ins = "green"
              let ups = "green"
              params.row.in==1?ins = "green":ins = "#CCC"
              params.row.up==1?ins = "green":ins = "#CCC"
              return h("div",[
                h('span',{style:{padding:'2px',backgroundColor:ins,color:'#fff',marginRight:'3px'}},"in"),
                h('span',{style:{padding:'2px',backgroundColor:ups,color:'#fff'}},"up"),
              ])
            }
          },
				  { title: "设备类型",key: "device_class",sortable: true,align: "center",width:90,
            render: (h, params) => {
              return h("span",{style:{padding:'2px',backgroundColor:'#ccc'}},params.row.tree.device_class)
            }
          },
				  { title: "容量",key: "capacity",sortable: true,align: "center",width:80,
            render: (h, params) => {
              let sizes = this.byteUnitConversion("",params.row.osd_stats.statfs.total)
              return h("span",sizes)
            }
          },
				  { title: "使用率",key: "rate_u",sortable: true,align: "center",width:150,
            render: (h, params) => {
              let total = params.row.osd_stats.statfs.total
              let available = params.row.osd_stats.statfs.available
              let lv = ((1-available/total)*100).toFixed(0)
              return h(
                "Progress",
                {
                  props: {
                    strokeColor:lv>70?["#f45d3f","#f45d3f"]:["#2ebe76","#2ebe76"],
                    percent: parseFloat(
                      lv > 100 ? 99 : lv
                    ),
                  },
                },
                lv == null ? 0 : lv+"%"
              );
            }
          },
				  { title: "读速率",key: "op_out_bytes",sortable: true,align: "center",
            render: (h, params) => {
              return h('div',[
                h('div',{
                  style:{height:'50px',margin:'0',padding:'0'},
                  on:{},
                  attrs:{id:'du'+params.index}
                })
              ])
            }
          },
				  { title: "写速率",key: "op_in_bytes",sortable: true,align: "center",
            render: (h, params) => {
              return h('div',[
                h('div',{
                  style:{height:'50px',margin:'0',padding:'0'},
                  on:{},
                  attrs:{id:'xie'+params.index}
                })
              ])
            }
          },
				  { title: "读OPS",key: "ops_r",sortable: true,align: "center",width:90,
            render: (h, params) => {
              return h('span',Math.floor(params.row.stats.op_r)+'/s')
            }
          },
				  { title: "写OPS",key: "ops_w",sortable: true,align: "center",width:90,
            render: (h, params) => {
              return h('span',Math.floor(params.row.stats.op_w)+'/s')
            }
          },
				  { title: "操作",key: "action",width:120,
            render: (h, params) => {
              return h("Dropdown",{ props:{trigger:'click'}}, [
                h("Button", "配置 ▼"),
                h("DropdownMenu", { slot: "list" }, [
                  h("DropdownItem",{
                    nativeOn: {
                      click: () => {
                        // this.tableOperationEvent(params.row,"deleted")
                        console.log('aaa',params)
                      },
                    },
                  },"删除"),
                  h("DropdownItem",{
                    nativeOn: {
                      click: () => {
                        this.tableOperationEvent(params.row,"edit")
                      },
                    },
                  },"编辑"),
                  h("DropdownItem",{
                    nativeOn: {
                      click: () => {
                        this.tableOperationEvent(params.row,"stop")
                      },
                    },
                  },"停用"),
                  h("DropdownItem",{
                    nativeOn: {
                      click: () => {
                        this.tableOperationEvent(params.row,"stop")
                      },
                    },
                  },"启用"),
                  h("DropdownItem",{
                    nativeOn: {
                      click: () => {
                        this.tableOperationEvent(params.row,"stop")
                      },
                    },
                  },"下线"),
                  h("DropdownItem",{
                    nativeOn: {
                      click: () => {
                        this.tableOperationEvent(params.row,"stop")
                      },
                    },
                  },"更换硬盘"),
                ])
              ])
            }
          },
        ],
        storageData:[],
        

        times:null,
      }
    },
    updated(){
      let _this= this;
      _this.storageData.forEach((em,index )=> {
        _this.padingChart(index,em)
      });
    },
    mounted(){
      // this.MasterTableData()
    },
    methods:{
      // 获取存储单元表数据
      MasterTableData() {
        this.spinShow = true
        // this.storageData=[
        //   {node_name:'aaaa',id:"假",rate_r:[1,2,3,4,5,6,7,8,9,0,11,12,13,14,15,16,17,18,19,1],rate_w:[],ops_r:[],ops_w:[]},
        //   {node_name:'bbbb',id:"假",rate_r:[],rate_w:[1,10,100,50,200,20,10,70],ops_r:[],ops_w:[]},
        //   {node_name:'cccc',id:"假",rate_r:[],rate_w:[],ops_r:[1,10,100,50,200,20,10,70,1,10,100,50,200,20,10,70],ops_w:[]},
        //   {node_name:'cccc',id:"假",rate_r:[],rate_w:[],ops_r:[],ops_w:[2,20,90,101,135,64,78,25,63,112,2,20,90,101,135,64,78,25,63,112]},
        // ]
        this.$axios.get("/storage/v1/storageunit").then(callback=>{
          this.storageData=callback.data.data
          this.shuaxin()
          this.spinShow = false
        })
      },
      // 新建存储
      newClick(){
        this.MasterTableData()
      },
      shuaxin(){
        this.times = setInterval(() => {
          this.$axios.get("/storage/v1/storageunit").then(callback=>{
            this.storageData=callback.data.data
            this.spinShow = false
          })
        }, 5000);
      },
      // 表格操作事件封装
      tableOperationEvent(row,action){
        switch (action) {
          case "restart":
            this.$Modal.confirm({
              title: "重启节点",
              content:
                '<p>是否重启名称为<span style="font-weight: 800;color:#000;word-wrap: break-word;">' +
                row.nodename +
                "</span>的节点？</p>",
              onOk: () => {
                // this.$axios.post('/thecss/v2/rebootNodeByHost?hostName=$'+row.hostname+'&usrname=$'+row.usrname,row).then((callback) => {
                this.$axios.post('/storage/v1/storageunit',{action:action,storage_nodes:row.storage_nodes}).then((callback) => {
                  console.log('重启节点',callback)
                }).catch((error) => {
                });
              },
              onCancel: () => {
                
              },
            });
          break;
          case "close":
            this.$Modal.confirm({
              title: "关闭节点",
              content:
                '<p>是否关闭名称为<span style="font-weight: 800;color:red;word-wrap: break-word;">' +
                row.nodename +
                "</span>的节点？</p>",
              onOk: () => {
                this.$axios.post('/storage/v1/node',{action:action,storage_nodes:row.storage_nodes}).then((callback) => {
                  console.log('关闭节点',callback)
                }).catch((error) => {
                  console.log('关闭节点失败','/thecss/v2/shutNodeByHost?hostName=$'+row.hostname+'&usrname=$'+row.usrname)
                });
              },
              onCancel: () => {
              },
            });
          break;
        }
      },
      // 绘制图表,i是当前表格数据的每一条的下标，params当前行的数据
      padingChart(i,params){
        let time1Data = params.stats_history.op_out_bytes.map(function(item) {
          return item[0];
        });
        let duData = params.stats_history.op_out_bytes.map(function(item) {
          return item[1];
        });
        this.paintChart(i,duData,"du")
        let time2Data = params.stats_history.op_in_bytes.map(function(item) {
          return item[0];
        });
        let xieData = params.stats_history.op_in_bytes.map(function(item) {
          return item[1];
        });
        this.paintChart(i,xieData,"xie")
        // if(params.stats_history.op_out_bytes.length>0){
        //   let time1Data = params.stats_history.op_out_bytes.map(function(item) {
        //     return item[0];
        //   });
        //   let duData = params.stats_history.op_out_bytes.map(function(item) {
        //     return item[1];
        //   });
        //   this.paintChart(i,duData,"du")
        // }
        // if(params.stats_history.op_in_bytes.length>0){
        //   let time2Data = params.stats_history.op_in_bytes.map(function(item) {
        //     return item[0];
        //   });
        //   let xieData = params.stats_history.op_in_bytes.map(function(item) {
        //     return item[1];
        //   });
        //   this.paintChart(i,xieData,"xie")
        // }
        // else if(params.ops_r.length>0){
        //   this.paintChart(i,params.ops_r,"ops_r")
        // }else if(params.ops_w.length>0){
        //   this.paintChart(i,params.ops_w,"ops_w")
        // }
      },
      paintChart(i,datas,ids){
        // debugger
        let _thi = this
        let titalname = '读速率'
        if(ids=='du') {
          titalname ='读速率'
        }else if(ids=='xie') {
          titalname ='写速率'
        }
        let lineChart = this.$echarts.init(document.getElementById(ids+i));
        let seriesData = datas
        const colors = ['#66e361', '#ea5858'];
        let option = {
          color: colors,
          tooltip: {
            trigger: 'item',
            position: [0, 0],
            backgroundColor:'rgba(50,50,50,0.2)',
            borderColor:'rgba(50,50,50,0.1)',
            // formatter: '{a0}: {c0}',
            formatter:function(params){
              return  params.seriesName+'： '+_thi.byteUnitConversion("",params.data)
            },
            textStyle: {
              color: '#ff6902'
            }
          },
          grid: {
            left: '1%',
            right: '1%',
            height:'90%',
            top:'5%',
            // bottom: '1%',

            // bottom: '90%',
            // data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
            containLabel: true
          },
          // toolbox: {
          //   feature: {
          //     saveAsImage: {}
          //   }
          // },
          xAxis: {
            type: 'category',
            // boundaryGap: false,
            show:false
          },
          yAxis: {
            type: 'value',
            show:false,
          },
          series: [
            {
              name: titalname,
              type: 'line',
              stack: 'Total',
              data: seriesData
            },
            
          ]
        };
        lineChart.setOption(option)

      },
      // 字节+单位转换
      byteUnitConversion(type,size) {
        const units = ['B','KB','MB','GB', 'TB', 'PB'];
        let unitIndex = 0;
        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }
        if(type=="byte") {
          return Math.floor(size * 100) / 100 
        }else if(type=="unit") {
          return units[unitIndex] 
        }else {
          return Math.floor(size * 100) / 100 + ' ' + units[unitIndex];
        }
      },
    },
    beforeDestroy() {
      clearInterval(this.times)
    },
  }
</script>