{"_from": "has-symbols@^1.0.3", "_id": "has-symbols@1.0.3", "_inBundle": false, "_integrity": "sha512-l3LCuF6MgDNwTDKkdYGEihYjt5pRPbEg46rtlmnSPlUbgmB8LOIrKJbYYFBSbnPaJexMKtiPO8hmeRjRz2Td+A==", "_location": "/has-symbols", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "has-symbols@^1.0.3", "name": "has-symbols", "escapedName": "has-symbols", "rawSpec": "^1.0.3", "saveSpec": null, "fetchSpec": "^1.0.3"}, "_requiredBy": ["/get-intrinsic"], "_resolved": "https://registry.npmmirror.com/has-symbols/-/has-symbols-1.0.3.tgz", "_shasum": "bb7b2c4349251dce87b125f7bdf874aa7c8b39f8", "_spec": "has-symbols@^1.0.3", "_where": "/root/docker/node_modules/get-intrinsic", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "bugs": {"url": "https://github.com/ljharb/has-symbols/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes"}], "deprecated": false, "description": "Determine if the JS environment has Symbol support. Supports spec, or shams.", "devDependencies": {"@ljharb/eslint-config": "^20.2.3", "aud": "^2.0.0", "auto-changelog": "^2.4.0", "core-js": "^2.6.12", "eslint": "=8.8.0", "get-own-property-symbols": "^0.9.5", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.5.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "greenkeeper": {"ignore": ["core-js"]}, "homepage": "https://github.com/ljharb/has-symbols#readme", "keywords": ["Symbol", "symbols", "typeof", "sham", "polyfill", "native", "core-js", "ES6"], "license": "MIT", "main": "index.js", "name": "has-symbols", "repository": {"type": "git", "url": "git://github.com/inspect-js/has-symbols.git"}, "scripts": {"lint": "eslint --ext=js,mjs .", "posttest": "aud --production", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run --silent lint", "test": "npm run tests-only", "test:shams": "npm run --silent test:shams:getownpropertysymbols && npm run --silent test:shams:corejs", "test:shams:corejs": "nyc node test/shams/core-js.js", "test:shams:getownpropertysymbols": "nyc node test/shams/get-own-property-symbols.js", "test:staging": "nyc node --harmony --es-staging test", "test:stock": "nyc node test", "tests-only": "npm run test:stock && npm run test:staging && npm run test:shams", "version": "auto-changelog && git add CHANGELOG.md"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "version": "1.0.3"}