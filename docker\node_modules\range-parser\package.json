{"_from": "range-parser@~1.2.1", "_id": "range-parser@1.2.1", "_inBundle": false, "_integrity": "sha512-Hrgsx+orqoygnmhFbKaHE6c296J+HTAQXoxEF6gNupROmmGJRoyzfG3ccAveqCBrwr/2yxQ5BVd/GTl5agOwSg==", "_location": "/range-parser", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "range-parser@~1.2.1", "name": "range-parser", "escapedName": "range-parser", "rawSpec": "~1.2.1", "saveSpec": null, "fetchSpec": "~1.2.1"}, "_requiredBy": ["/express", "/send"], "_resolved": "https://registry.npmmirror.com/range-parser/-/range-parser-1.2.1.tgz", "_shasum": "3cf37023d199e1c24d1a55b84800c2f3e6468031", "_spec": "range-parser@~1.2.1", "_where": "/root/docker/node_modules/express", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://tjholowaychuk.com"}, "bugs": {"url": "https://github.com/jshttp/range-parser/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "deprecated": false, "description": "Range header field string parser", "devDependencies": {"deep-equal": "1.0.1", "eslint": "5.16.0", "eslint-config-standard": "12.0.0", "eslint-plugin-import": "2.17.2", "eslint-plugin-markdown": "1.0.0", "eslint-plugin-node": "8.0.1", "eslint-plugin-promise": "4.1.1", "eslint-plugin-standard": "4.0.0", "mocha": "6.1.4", "nyc": "14.1.1"}, "engines": {"node": ">= 0.6"}, "files": ["HISTORY.md", "LICENSE", "index.js"], "homepage": "https://github.com/jshttp/range-parser#readme", "keywords": ["range", "parser", "http"], "license": "MIT", "name": "range-parser", "repository": {"type": "git", "url": "git+https://github.com/jshttp/range-parser.git"}, "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec", "test-cov": "nyc --reporter=html --reporter=text npm test", "test-travis": "nyc --reporter=text npm test"}, "version": "1.2.1"}