<template>
  <div>
    <Modal
      v-model="model"
      width="600"
      :mask-closable="false"
    >
    <template #header><p><span style="color:green"></span>重置密码</p></template>
    <Form :model="formItem" ref="formItem" :rules="rulesForm" :label-width="150">
      <FormItem label="当前虚拟机">
        <Input v-model="vmRow.name" disabled></Input>
      </FormItem>
      <FormItem label="虚拟机新密码" prop="password">
        <Input v-model="formItem.password" type="password" password placeholder="请输入虚拟机新密码"></Input>
      </FormItem>
      <p style="text-align:center;color:red">提示：该功能需要虚拟机中预先安装依赖文件，否则重置密码将无法生效。</p>
    </Form>
    <div slot="footer">
        <Button type="text" @click="model = false">取消</Button>
        <Button type="primary" @click="modelOK" :disabled="disabled">确认</Button>
      </div>
    </Modal>
  </div>
</template>
<script>
import {
  vmGroupTableConfig, // 虚拟机 修改配置 群操作
} from '@/api/virtualMachine';

export default {
  props: {
    vmRow: Object,
    pwdTime: String,
  },
  watch: {
    pwdTime(news){
      this.disabled = false
      this.model = true
      this.$refs.formItem.resetFields()
    }
  },
  data(){
    const propPwd =(rule,value,callback)=>{
      let list = /^[a-zA-Z0-9@_.]{8,32}$/;
      if(list.test(value)){
        callback()
      }else{
        callback(new Error("8-32 个英文、数字、特殊字符(@_.)"))
      }
    }
    return {
      disabled:true,
      model:false,
      // 虚拟机 修改配置弹框 
      formItem: {
        password: '',
      },
      rulesForm: {
        password:[
          { required: true, message: '必填项', trigger: 'change' },
          { validator: propPwd, trigger:'change' }
        ],
      }
    }
  },
  methods: {
    // 虚拟机表操作（设置）确认事件
    modelOK() {
      this.$refs.formItem.validate((valid) => {
        if(valid){
          this.disabled = true
          this.$Modal.confirm({
            title: '提示',
            content: `<p>是否对 ${this.vmRow.name} 虚拟机，进行重置密码操作?</p>`,
            onOk: () => {
              vmGroupTableConfig({
                actions: ["changePassword"],
                admin_pass: this.formItem.password,
                ids: [this.vmRow.id],
                names: [this.vmRow.name]
              })
              .then((callback) => {
                if(callback.data[0].msg == "ok"){
                  this.$emit("return-ok",'虚拟机重置密码操作完成');
                  this.model = false
                }else {
                  this.$emit("return-error",'虚拟机重置密码操作失败');
                  this.disabled = false
                }
              }).catch((error) => {
                this.disabled=false
                this.$emit("return-error",'虚拟机重置密码操作失败');
              })
            },
            onCancel: () => {this.disabled=false}
          });
        }
      });
    },
    // cpu选择
    formCpu(val) {
      return val + "核";
    },
    // 内存选择
    formMemory(val) {
      return val + " GB";
    },
    // 硬盘选择
    formDisk(val) {
      return val + " GB";
    },
  }
}
</script>