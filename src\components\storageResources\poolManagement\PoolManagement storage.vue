<template>
  <!-- 卷池管理页面 -->
  <div>
    <div class="table-button-area">
      <div class="table-button-area">
        <div>
          <Button class="plus_btn" @click="increaseClick"><span class="icon iconfont icon-plus-circle"></span> 添加卷池</Button>
        </div>
        <div>
          <!-- <Input v-model="tablePageForm.search_str" search enter-button placeholder="请输入名称" style="width: 300px" @on-search="tableSearchInput" /> -->
        </div>
      </div>
    </div>
    <div style="position:relative;">
      <Table :columns="vpColumns" :data="vpData"></Table>
      <Spin fix v-if="spinShow" size="large" style="color:#ef853a">
        <Icon type="ios-loading" size=50 class="demo-spin-icon-load"></Icon>
        <div style="font-size:16px;padding:20px">Loading...</div>
      </Spin>
    </div>
    <!-- 添加卷池 -->
    <Modal v-model="increaseModal" title="添加卷池" @on-ok="addOK" @on-cancel="cancel" :mask-closable="false">
      <div>
        <Form :model="FormItem" :label-width="120" :rules="ruleForm">
          <FormItem label="卷池名称" prop="pool">
            <Input v-model="FormItem.pool" placeholder="请输入类型名称"></Input>
          </FormItem>
          <FormItem label="应用类型" prop="application_metadata">
            <Select v-model="FormItem.application_metadata">
              <Option v-for="item in applicationData" :value="item.key" :key="item.key" >{{ item.value }}</Option>
            </Select>
          </FormItem>
          <FormItem label="安全类型" prop="pool_type">
            <Select v-model="FormItem.pool_type" @on-change="securityChange">
              <Option v-for="item in securityData" :value="item.key" :key="item.key" >{{ item.value }}</Option>
            </Select>
          </FormItem>
          <FormItem label="安全级别" prop="size">
            <Select v-model="FormItem.size">
              <Option v-for="item in levelData" :value="item.key" :key="item.key" >{{ item.value }}</Option>
            </Select>
          </FormItem>
          <FormItem v-if="erasureDelete" label="纠删码级别" prop="erasure_code_profile">
            <Select v-model="FormItem.erasure_code_profile">
              <Option v-for="item in erasureData" :value="item.key" :key="item.key" >{{ item.value }}</Option>
            </Select>
          </FormItem>
          <!-- <FormItem label="卷池大小">
            <InputNumber v-model="FormItem.size" style="width:200px" :min="1" />（GB）
          </FormItem> -->
          <FormItem label="压缩模式" prop="compression_mode">
            <Select v-model="FormItem.compression_mode">
              <Option v-for="item in compressData" :value="item.key" :key="item.key" >{{ item.value }}</Option>
            </Select>
          </FormItem>
        </Form>
      </div>
    </Modal>
  </div>
</template>

<script>
  export default {
    props: {
      tabName: String,
    },
    watch: {
      tabName(value) {
        value=='juanchi'?this.volumePoolData():""
      },
    },
    data() {
      const propPoolname = (rule, value, callback) => {
        let list = /^[\u4e00-\u9fa5_a-zA-Z0-9@_.-]{2,32}$/;
        if (list.test(value)) {
          callback();
        } else {
          callback(new Error("请输入2-32个中文或字符（特殊字符可用@_.-）"));
        }
      };
      return{
        spinShow:false,
        // 卷池表数据
        vpColumns:[
          // {type: 'selection',width: 30,align: 'center'},
				  { title: "卷池名称",key: "pool_name",sortable: 'custom' },
				  { title: "硬盘域",key: "crush_rule",sortable: 'custom',align: "center" },
				  { title: "应用类型",key: "application_metadata",sortable: 'custom',align: "center",
            render: (h, params) => {
              return h("span",params.row.application_metadata[0])
            }
          },
				  { title: "安全类型",key: "type",sortable: 'custom',align: "center" },
				  { title: "安全级别",key: "size",sortable: 'custom',align: "center" },
				  { title: "容量",key: "quota_max_bytes",sortable: 'custom',align: "center" },
				  { title: "操作",key: "action",width:120,
            render: (h, params) => {
              return h("div", [
                h("Button", {
                  style:{color:'red'},
                  on: {
                    click: () => {
                      this.$Modal.confirm({
                        title: "删除卷池",
                        content:
                          '<p>是删除名称为<span style="font-weight: 600;color:red;word-wrap: break-word;">' +
                          params.row.pool_name +
                          "</span>的卷池？</p>",
                        onOk: () => {
                          let pattern = /^.{3}$/
                          let one = /^2/
                          this.$axios.delete("/storage/v1/pool/"+params.row.pool_name).then((callback) => {
                            if(pattern.test(callback.status.toString())&&one.test(callback.status.toString())){
                               this.$Message.success({
                                background: true,
                                closable: true,
                                duration: 5,
                                content: "删除卷池完成",
                              });
                              this.volumePoolData()
                            }
                          }).catch((error) => {
                            this.$Message.success({
                                background: true,
                                closable: true,
                                duration: 5,
                                content: "删除卷池失败",
                              });
                          });
                        },
                        onCancel: () => {
                          
                        },
                      });
                    },
                  },
                },"删除"),
              ])
            }
          },
        ],
        vpData:[],
        // 添加卷池
        increaseModal:false,
        erasureDelete:false,
        FormItem:{
          pool:"",
          pool_type:"replicated",
          erasure_code_profile:"2",
          application_metadata:"rbd",
          compression_mode:"none",
          size:2,

          pg_num:0,
          flags:"",
          // rule_name:"replicated_rule",
          configuration:"",
        },
        // 正则 
        ruleForm:{
          pool:[
            { required: true, message: "必填项", trigger: "blur" },
            { validator: propPoolname, trigger: "change" },
          ],
          application_metadata:[{ required: true, message: "必选项", trigger: "blur" }],
          pool_type:[{ required: true, message: "必选项", trigger: "blur" }],
          size:[{ required: true, message: "必选项", trigger: "blur" }],
          erasure_code_profile:[{ required: true, message: "必选项", trigger: "blur" }],
          compression_mode:[{ required: true, message: "必选项", trigger: "blur" }],
        },
        applicationData:[
          {value:"块存储",key:"rbd"},
          {value:"文件系统",key:"cephfs"},
          {value:"对象网关",key:"rgw"},
        ],
        securityData:[
          {value:"副本类型",key:"replicated"},
          {value:"纠删码型",key:"erasure"},
        ],
        levelData:[
          {value:2,key:2},
          {value:3,key:3},
          {value:4,key:4},
        ],
        erasureData:[
          {value:'2',key:'2'},
          {value:'3',key:'3'},
          {value:'4',key:'4'},
        ],
        compressData:[
          {value:"关闭",key:"none"},
          {value:"snappy算法",key:"snappy"},
          {value:"zlib算法",key:"zlib"},
          {value:"lzo算法",key:"lzo"},
        ],
      }
    },
    mounted(){
      // this.volumePoolData()
    },
    methods:{
      // 获取卷池管理表数据
      volumePoolData() {
        this.spinShow = true
        this.$axios.get("/storage/v1/pool").then(callback=>{
          if(callback.data.msg=="ok") {
            this.vpData =callback.data.data
          }
          this.spinShow = false
        }).catch((error) => {
          this.spinShow = false
        });
      },
      // 添加卷池按钮
      increaseClick(){
        this.increaseModal = true;
        this.erasureDelete = false;
        this.FormItem.pool = ""
        this.FormItem.pool_type = "replicated"
        this.FormItem.erasure_code_profile = "2"
        this.FormItem.application_metadata = "rbd"
        this.FormItem.compression_mode = "none"
        this.FormItem.size = 2
      },
      // 安全级别下拉选择
      securityChange(item) {
        item=='erasure'?this.erasureDelete=true:this.erasureDelete=false
      },

      // 添加卷池弹框确认
      addOK(){
        let datas = new Object()
        datas.pool = this.FormItem.pool
        datas.pool_type = this.FormItem.pool_type
        this.erasureDelete?datas.erasure_code_profile = this.FormItem.erasure_code_profile:""
        datas.application_metadata = this.FormItem.application_metadata
        datas.compression_mode = this.FormItem.compression_mode
        datas.size = this.FormItem.size
        datas.pg_num = 0
        datas.flags = ""
        // datas.rule_name = "replicated_rule"
        datas.configuration = ""
        
        let ruleName = /^[\u4e00-\u9fa5_a-zA-Z0-9@_.-]{2,32}$/;
        if (ruleName.test(this.FormItem.pool)) {
          this.$axios.post("/storage/v1/pool",JSON.stringify(datas)).then(callback=>{
            let pattern = /^.{3}$/
            let one = /^2/
            if(pattern.test(callback.status.toString())&&one.test(callback.status.toString())){
              this.$Message.success({
                background: true,
                closable: true,
                duration: 5,
                content: "添加卷池完成",
              });
              this.volumePoolData()
            }else {
              this.$Message.error({
                background: true,
                closable: true,
                duration: 5,
                content: "添加卷池失败",
              });
            }
          })
        }else {
          this.$Message.error({
            background: true,
            closable: true,
            duration: 5,
            content: "输入的卷池名称不符合规定",
          });
        }
        this.increaseModal = false;
      },
      cancel(){
        this.volumePoolData()
      },
    },
  }
</script>