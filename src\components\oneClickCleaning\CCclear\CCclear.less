.one_click_clear {
  height: 100%;
  padding: 0 10px;

  .one_click_title {
    padding: 5px 0;
    border-bottom: 1px solid #ccc;

    p {
      border-left: 4px solid #fb6129;
      padding-left: 10px;
      display: inline-block;
      color: #333;
      font-size: 14px;
    }
  }

  /* 进入清理页面时时的文本提示 */
  .one_click_describe {
    width: 50%;
    height: 50%;
    margin: 8% auto;
    display: flex;
    align-items: center;

    img {
      padding: 0 10px;
    }

    ul li {
      padding: 10px;
    }
  }

  .one_click_picture {
    width: 350px;
    height: 350px;
    position: relative;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%)
  }

  .one_click_slected {
    height: 90%;
    display: flex;
    flex-direction: column;
  }

  .clear_slected_area {
    background-image: url(../../../assets/rwsbg.png);
    background-size: 100% 100%;
    width: 100%;
    display: flex;
    padding: 0 30%;
    justify-content: space-evenly;
    align-items: center;

    img {
      height: 70%;
    }

    .clear_data_area {
      display: flex;
      flex-direction: column;

      .release_storage {
        display: flex;
        display: flex;
        width: 300px;
        justify-content: space-around;

        p {
          display: inline-block;
          width: 49%;
        }

        span {
          padding: 3px 0;
          font-size: 20px;
        }
      }

      .clear_btn_area {
        padding-top: 10px;
        display: flex;
        width: 300px;
        justify-content: space-around;
      }
    }
  }

  .clear_data {
    padding: 10px 0;
    height: 100%;
    overflow: auto;

    .clear_result_area {
      padding: 10px;
      display: flex;
    }
  }
}