<template>
  <div>
    <Modal v-model="model" width="600" :mask-closable="false">
      <template #header><p><span style="color:green">{{tableRow.name}}</span> 云硬盘创建快照</p></template>
      <Form
        :model="formItem"
        ref="formItem"
        :rules="rulesForm"
        :label-width="120"
      >
        <FormItem label="快照名称" prop="name">
          <Input v-model="formItem.name" placeholder="请输入快照名称"></Input>
        </FormItem>
      </Form>
      <div slot="footer">
        <Button type="text" @click="model = false">取消</Button>
        <Button type="primary" @click="modelOK" :disabled="disabled">确认</Button>
      </div>
    </Modal>
  </div>
</template>
<script>
import {
  cloudDiskSnapshot, // 云硬盘 修改
} from '@/api/storage';
export default {
  props: {
    snapshotTime: String,
    tableRow: Object,
  },
  watch: {
    snapshotTime(news){
      this.$refs.formItem.resetFields();
      this.model = true
      this.disabled = false
    }
  },
  data(){
    const propName =(rule,value,callback)=>{
      let list = /^[\u4e00-\u9fa5_a-zA-Z0-9@_.-]{2,32}$/;
      if(list.test(value)){
        callback()
      }else{
        callback(new Error("2-32 个中文、英文、数字、特殊字符(@_.-)"));
      }
    }
    return {
      model: false,
      disabled: false,
      formItem: {
        name: '',
      },
      typeData: [],
      // 正则验证
      rulesForm: {
        name:[
          { required: true, message: '必填项', trigger: 'change' },
          { validator: propName, trigger:'change' }
        ],
      },
    }
  },
  methods: {
    // 确认事件
    modelOK() {
      this.$refs.formItem.validate((valid) => {
        if (valid) {
          this.disabled = true;
          cloudDiskSnapshot({
            id: this.tableRow.id,
            volumename: this.tableRow.name,
            snapname: this.formItem.name,
          }).then(callback=>{
            if(callback.data.msg == "ok"){
              this.$Message.success({
                background: true,
                closable: true,
                duration: 5,
                content: "创建快照操作已完成",
              });
              this.model = false;
            }else {
              this.$emit("return-error","创建快照操作失败");
              this.disabled = false;
            }
          })
          .catch((error) => {
            this.$emit("return-error",'创建快照操作失败');
            this.disabled = false;
          });
        }
      });
    },
  }
}
</script>
