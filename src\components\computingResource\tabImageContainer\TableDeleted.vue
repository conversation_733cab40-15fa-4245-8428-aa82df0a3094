<template>
  <div>
    <Modal v-model="model" width="600" :mask-closable="false">
      <template #header><p>删除容器镜像</p></template>
      <div style="padding: 5px">
        <span>是否删除下列容器镜像？</span>
        <p style="color: red; word-wrap: break-word">
          {{ tableNames.toString() }}
        </p>
      </div>
      <div slot="footer">
        <Button type="text" @click="model = false">取消</Button>
        <!-- <Button type="primary" :loading="disabled" @click="modelOK">
          <span v-if="!disabled">确认</span>
          <span v-else>删除中</span>
        </Button> -->
        <Button type="primary" @click="modelOK" :disabled="disabled"
          >确认</Button
        >
      </div>
    </Modal>
  </div>
</template>
<script>
import { containerImageDeleted } from "@/api/container"; // 容器镜像表格 删除
export default {
  props: {
    tableArr: Array,
    deletionTime: String,
  },
  watch: {
    deletionTime(news) {
      this.tableNames = this.tableArr.map((em) => {
        return em.repo+':'+em.tag;
      });
      this.tableIDS = this.tableArr.map((em) => {
        return em.uuid;
      });
      this.model = true;
      this.disabled = false;
    },
  },
  data() {
    return {
      model: false,
      disabled: false,
      tableNames: [],
      tableIDS: [],
    };
  },
  methods: {
    modelOK() {
      this.disabled = true;
      containerImageDeleted({
          ids: this.tableIDS,
          names: this.tableNames,
      })
        .then((callback) => {
          this.model = false;
          if (callback.data.msg == "ok") {
            this.$emit("return-ok", {
              msg: "删除容器镜像操作完成",
              type: "ok",
            });
          } else {
            this.$emit("return-ok", {
              msg: "删除容器镜像操作失败",
              type: "error",
            });
          }
        })
        .catch((error) => {
          this.$emit("return-ok", {
            msg: "删除容器镜像操作失败",
            type: "error",
          });
        });
    },
  },
};
</script>