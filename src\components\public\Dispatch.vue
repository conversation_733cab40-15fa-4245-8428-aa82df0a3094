<template>
  <div>
    <Modal
      v-model="model"
      width="600"
      :mask-closable="false"
    >
      <template #header><p>动态资源调度</p></template>
      <Form :model="formItem" :label-width="120">
        <FormItem label="集群资源调度">
          <Checkbox v-model="formItem.addCheck"></Checkbox>
          <span class="span-color-info">启用集群资源调度后，系统根据各主机的资源负载情况，智能调度虚拟机的运行位置，确保业务持续稳定运行。</span>
        </FormItem>
        <FormItem label="调度方式">
          <RadioGroup v-model="formItem.auto">
            <Radio label="true" border :disabled="!formItem.addCheck">自动</Radio>
            <Radio label="false" border :disabled="!formItem.addCheck">手动</Radio>
          </RadioGroup>
          <br>
          <span class="span-color-info" ></span>
          <span class="span-color-info">{{translationAuto(formItem.auto)}}</span>
        </FormItem>
        <FormItem label="衡量因素">
          <Checkbox v-model="formItem.cpuCheck" :disabled="!formItem.addCheck"> CPU </Checkbox>
          <Checkbox v-model="formItem.memCheck" :disabled="!formItem.addCheck"> 内存 </Checkbox>
        </FormItem>
        <FormItem label="分级策略">
          <Select v-model="formItem.strategy" :disabled="!(this.formItem.addCheck && (this.formItem.cpuCheck||this.formItem.memCheck))">
            <Option v-for="item in strategyData" :value="item.value" :key="item.value">{{ item.label }}</Option>
          </Select>
          <span class="span-color-info">主机负载大于<span class="span-color-keynote"> {{translationInfo('dy',formItem.strategy)}}</span>，且主机负载差值超过<span class="span-color-keynote"> {{translationInfo('cg',formItem.strategy)}}</span></span>
        </FormItem>
      </Form>
      <div slot="footer">
        <Button type="text" @click="model = false">取消</Button>
        <Button type="primary" @click="modalOK" :disabled="disabled">确认</Button>
      </div>
    </Modal>
  </div>
</template>
<script>
import { dynamicResourceSchedulingQuery,dynamicResourceSchedulingEdit } from '@/api/other';  // 动态资源调度
export default {
  props: {
    datas:Number,
  },
  watch: {
    datas(news){
      this.queryData()
      this.model = true
      this.disabled = false
    }
  },
  data(){
    return {
      model: false,
      disabled: false,
      formItem:{
        id: null,
        addCheck: false,
        auto: 'true',
        cpuCheck: false,
        memCheck: false,
        strategy: 'neutral',
      },
      strategyData: [
        { value: 'conservative',label: '篇保守' },
        { value: 'neutral',label: '中性' },
        // { value: 'radical',label: '偏激进' },
        // { value: 'most_radical',label: '最激进' },
      ]
    }
  },
  methods: {
    queryData(){
      dynamicResourceSchedulingQuery()
      .then(callback => {
        if(callback.data.data==null) {
          this.formItem.addCheck = false
          this.formItem.auto = 'true'
          this.formItem.cpuCheck = false
          this.formItem.memCheck = false
          this.formItem.strategy = 'neutral'
        }else {
          this.formItem.id = callback.data.data.id
          this.formItem.addCheck = callback.data.data.enabled=="true"
          this.formItem.auto = callback.data.data.auto
          this.formItem.cpuCheck = callback.data.data.cpu_enabled=="true"
          this.formItem.memCheck = callback.data.data.mem_enabled=="true"
          this.formItem.strategy = callback.data.data.strategy
        }
      })
    },
    modalOK(){
      this.disabled = true
      let data = {
        id: this.formItem.id,
        enabled: this.formItem.addCheck?'true':'false',
        auto: this.formItem.auto,
        cpu_enabled: this.formItem.cpuCheck?'true':'false',
        mem_enabled: this.formItem.memCheck?'true':'false',
        strategy: this.formItem.strategy,
      }
      dynamicResourceSchedulingEdit(data)
      .then((callback) => {
        if(callback.data.msg=='ok'){
          this.model=false
          this.$Message.success({
            background: true,
            closable: true,
            duration: 5,
            content: '动态资源调度完成',
          });
        }else {
          this.$Message.error({
            background: true,
            closable: true,
            duration: 5,
            content: '动态资源调度失败',
          });
          this.disabled = false
        }
      })
      .catch((error) => {
        this.$Message.error({
          background: true,
          closable: true,
          duration: 5,
          content: '动态资源调度失败',
        });
        this.disabled = false
      })
    },
    translationInfo(list,item){
      let q = '70%'
      let h = '30%'
      if(item =='conservative') {
        q = '70%'
        h = '30%'
      }else if(item =='neutral'){
        q = '70%'
        h = '20%'
      }else if(item =='radical'){
        q = '50%'
        h = '10%'
      }else if(item =='most_radical'){
        q = '50%'
        h = '5%'
      }
      if(list == 'dy') {
        return q
      }else {
        return h
      }
    },
    translationAuto(item){
      if(item == 'true'){
        return '系统将根据集群资源负载情况，按照规则自动进行调度。'
      }else {
        return '系统将根据资源负载情况给出调度建议，用户需手动执行调度建议。'
      }
    },
  }
}
</script>
<style scoped>
  .span-color-info{
    color: #ccc
  }
  .span-color-keynote{
    color: #b97
  }
</style>