{"_from": "side-channel@^1.0.4", "_id": "side-channel@1.0.4", "_inBundle": false, "_integrity": "sha512-q5XPytqFEIKHkGdiMIrY10mvLRvnQh42/+GoBlFW3b2LXLE2xxJpZFdm94we0BaoV3RwJyGqg5wS7epxTv0Zvw==", "_location": "/side-channel", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "side-channel@^1.0.4", "name": "side-channel", "escapedName": "side-channel", "rawSpec": "^1.0.4", "saveSpec": null, "fetchSpec": "^1.0.4"}, "_requiredBy": ["/qs"], "_resolved": "https://registry.npmmirror.com/side-channel/-/side-channel-1.0.4.tgz", "_shasum": "efce5c8fdc104ee751b25c58d4290011fa5ea2cf", "_spec": "side-channel@^1.0.4", "_where": "/root/docker/node_modules/qs", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "bugs": {"url": "https://github.com/ljharb/side-channel/issues"}, "bundleDependencies": false, "dependencies": {"call-bind": "^1.0.0", "get-intrinsic": "^1.0.2", "object-inspect": "^1.9.0"}, "deprecated": false, "description": "Store information about any JS value in a side channel. Uses WeakMap if available.", "devDependencies": {"@ljharb/eslint-config": "^17.3.0", "aud": "^1.1.3", "auto-changelog": "^2.2.1", "eslint": "^7.16.0", "nyc": "^10.3.2", "safe-publish-latest": "^1.1.4", "tape": "^5.0.1"}, "exports": {"./package.json": "./package.json", ".": [{"default": "./index.js"}, "./index.js"]}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "homepage": "https://github.com/ljharb/side-channel#readme", "keywords": ["weakmap", "map", "side", "channel", "metadata"], "license": "MIT", "main": "index.js", "name": "side-channel", "repository": {"type": "git", "url": "git+https://github.com/ljharb/side-channel.git"}, "scripts": {"lint": "eslint .", "posttest": "npx aud --production", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublish": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run tests-only", "tests-only": "nyc tape 'test/**/*.js'", "version": "auto-changelog && git add CHANGELOG.md"}, "version": "1.0.4"}