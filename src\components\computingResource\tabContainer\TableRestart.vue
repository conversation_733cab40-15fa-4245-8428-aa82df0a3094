<template>
  <div>
    <Modal
      v-model="model"
      width="600"
      :mask-closable="false"
    >
      <template #header><p><span style="color:green">{{row.name}}</span>重启容器</p></template>
      <Form :model="formItem" ref="formItem" :rules="ruleValidate" :label-width="120">
          <FormItem label="重启容器" prop="timeout">
            <Input v-model="formItem.timeout" type='number' placeholder="指定秒数作为关机超时（默认：10）"></Input>
          </FormItem>
				</Form>
      <div slot="footer">
        <Button type="text" @click="model = false">取消</Button>
        <Button type="primary" @click="modalOK" :disabled="disabled">确认</Button>
      </div>
    </Modal>
  </div>
</template>
<script>
import {containerOperate} from '@/api/container'; // 容器表 操作
export default {
  props: {
    row:Object,
    restartTime:String,
  },
  watch: {
    restartTime(news){
      this.model = true
      this.formItem.id = this.row.uuid
      this.formItem.name = this.row.name
    }
  },
  data(){
    const propTime = (rule, value, callback) => {
      if (value>0) {
        callback()
      } else {
        callback(new Error("请输入大于0的数字"))
      }
    }
    return {
      model:false,
      disabled: false,
      formItem: {
        id: '',
        name: '',
        action: 'restart',
        timeout: '',
      },
      ruleValidate:{
        timeout:[{ validator: propTime, trigger: "change" }],
      }
    }
  },
  methods: {
    modalOK() {
      this.$refs.formItem.validate((valid) => {
        if(valid) {
          this.disabled = true
          containerOperate(this.formItem)
          .then((callback) => {
            this.model = false;
            this.$emit("return-ok",{
              msg: '重启容器操作已完成',
              type: 'ok'
            });
          })
          .catch((error) => {
            this.disabled = false
            this.$emit("return-ok",{
              msg: '重启容器操作失败',
              type: 'error'
            });
          })
        }
      })
    },
  }
}
</script>