<template>
  <div class="general_page_area">
    <Form :model="formItem" ref="formItem" :rules="ruleValidate" :label-width="120">
			<FormItem label="名称" prop="name">
        <Input v-model="formItem.name" placeholder="请输入名称"></Input>
    	</FormItem>
      <FormItem label="镜像名称" prop="imgName">
        <Input v-model="formItem.imgName" placeholder="请输入镜像名称"></Input>
    	</FormItem>
      <!-- <FormItem label="Image Driver">
        <Select v-model="formItem.driver">
          <Option value="docker" >Docker Hub</Option>
          <Option value="glance" >Glance</Option>
        </Select>
    	</FormItem> -->
      <FormItem label="镜像拉取策略">
        <Select v-model="formItem.policy" :label-in-value="true">
          <Option value="ifnotpresent" >如果没有，才进行下载</Option>
          <Option value="always" >总是下载</Option>
          <Option value="never" >从不下载</Option>
        </Select>
    	</FormItem>
      <FormItem label="选择网络" prop="netID">
        <Select v-model="formItem.netID" :label-in-value="true" @on-change="onChange">
          <Option v-for="item in netData" :value="item.id" :key="item.id" >{{ item.name }}</Option>
        </Select>
    	</FormItem>
      <FormItem label="命令">
        <Input v-model="formItem.command" placeholder="请输入要发送到容器的命令"></Input>
    	</FormItem>
      <FormItem label="创建后启动容器">
        <Checkbox v-model="formItem.start"></Checkbox>
    	</FormItem>
      <!-- <CheckboxGroup v-model="formItem.social">
            <Checkbox label="L1">AAA</Checkbox>
            <Checkbox label="L2">BBB</Checkbox>
            <Checkbox label="L3">CCC</Checkbox>
        </CheckboxGroup> -->
		</Form>
  </div>
</template>
<script>
import {networkTableQuery} from '@/api/network';
export default {
  props: {
    times:String,
  },
  watch: {
    times(value) {
      this.$refs.formItem.validate((valid) => {
        if (valid) {
          this.$emit("returnOK",{
            page: 0,
            type: true,
            data: this.formItem,
          });
        }else {
          this.$emit("returnOK",{
            page: 0,
            type: false,
            data: this.formItem,
          });
        }
      })
      
    }
  },
  data(){
    return {
      formItem: {
        name: '',
        imgName: '',
        driver: 'docker',
        policy: '',
        netID: '',
        netName: '',
        command: '',
        start: true,
      },
      netData: [],
      ruleValidate:{
        name:[{ required: true, message: '必填项', trigger: 'change' }],
        imgName:[{ required: true, message: '必填项', trigger: 'change' }],
        netID:[{ required: true, message: '必填项', trigger: 'change' }],
      },

      social: ['L1'],
    }
  },
  mounted() {
    this.networkData()
  },
  methods: {
    // 获取网络下拉框数据
    networkData(){
      networkTableQuery().then(callback=>{
        let arr = new Array();
        callback.data.forEach(item=>{
          item.cidr.forEach((em,i) => {
            arr.push({
              id: item.id+":"+item.subnets[i],
              name: em,
            })
          })
        })
        this.netData = arr;
        this.formItem.netID = this.netData[0].id
        this.formItem.netName = this.netData[0].name
      })
    },
    // 选择网络
    onChange(item) {
      if(item!==undefined) {
        this.formItem.netName = item.label;
        this.formItem.netID = item.value;
      }
    }
  },
}
</script>
<style lang="less" scoped>
  .general_page_area {
    width: 100%;
    height: 100%;
    overflow: auto;
  }
</style>