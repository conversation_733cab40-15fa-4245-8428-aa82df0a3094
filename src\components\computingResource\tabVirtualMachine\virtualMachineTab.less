.vm_manage_area {
  position: relative;
  display: flex;
  height: calc(100% - 70px);
  border-radius: 10px;
  background-color: #ffffff;
  padding: 15px 5px;
  // 虚拟机分组
  .vm_group_area {
    min-width: 200px;
    width: 200px;
    border-right: 1px solid #ccc;
    .vm_tree_area {
      width: 195px;
      height: calc(100% - 100px);
      overflow: auto;
    }
    .virtual_unit {
      display: flex;
      flex-direction: row;
      justify-content: space-evenly;
      height: 40px;
      border-top: 1px solid #ccc;

      >.ivu-tooltip {
        line-height: 40px;
      }

      .ivu-icon {
        font-size: 20px;
      }
    }
  }

  // 虚拟机表格
  .vm_table_area {
    width: 100%;
    height: 100%;
    padding: 0 10px;

    .vm_title_area {
      display: flex;
      justify-content: space-between;
    }
    .vm_table_content {
      width: 100%;
      padding: 1px;
      // overflow: auto;
    }
    /* 任务模块 */
    .vm_task_aera {
      position: fixed;
      bottom: 0;
      width: 100%;
      height: 180px;
      // box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.3);
      // height: calc(100% - 640px);
      // overflow: auto;
      .ivu-table td,
        .ivu-table th {
          height: 35px;
        }
      .task_title {
        display: flex;
        align-items: center;

        h4 {
          width: 40px;
        }

        span {
          display: inline-block;
          border-bottom: 1px solid #ccc;
          width: 100%;
        }
      }
    }
  }
}

// 虚拟机详情
.vm_drawer_basic {
  height: 70px;
  border-bottom: 1px solid #ccc;
  display: flex;
  align-items: center;

  li {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 60px;
    float: left;
    overflow: hidden;
  }
}

.vm_drawer_charts {
  ul {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column
  }

  li {
    width: 100%;
    padding: 20px 0;
    border-bottom: 1px solid #ccc;

    p {
      padding: 0 0 10px 20px;
      font-size: 14px;
      color: #333;
    }

    .xq {
      display: flex;
      flex-wrap: wrap;
      justify-content: flex-start;
      padding-left: 20px;

      .divxq {
        display: flex;

        .lable {
          width: 110px;
          color: #999;
          display: inline-block;
        }

        .key {
          width: 230px;
          color: #333;
          display: inline-block;
          white-space: nowrap;
          overflow: hidden;
        }
      }
    }
  }
}

.ivu-btn:focus {
  box-shadow: none !important;
}

.vm_manage_area p {
  font-weight: 600;
  border-left: 4px solid #fb6129;
  margin: 0 0 5px 5px;
  padding-left: 10px;
  color: #333;
  font-size: 14px;
}