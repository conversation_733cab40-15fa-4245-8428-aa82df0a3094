<template>
  <div>
    <Modal v-model="model" width="1000" :mask-closable="false">
      <template #header><p>动态资源扩展待确认表格</p></template>
      <div>
        <Table :columns="tableColumns" :data="tableData" height="500">
          <!-- VCPU推荐值 -->
          <template v-slot:suggestion_vcpus="{row}">
            <span v-if="row.cpu_expansion=='false'">-</span>
            <span v-else><InputNumber :max="128" v-model="row.suggestion_vcpus" /></span>
          </template>
          <!-- 内存 -->
          <template v-slot:ram="{row}">
            <span>{{ row.ram }} GB</span>
          </template>
          <!-- 内存推荐值 -->
          <template v-slot:suggestion_ram="{row}">
            <span v-if="row.mem_expansion =='false'">-</span>
            <span v-else><InputNumber :max="128" :min='row.ram+1' v-model="row.suggestion_ram" /> GB</span>
          </template>
          <!-- 操作 -->
          <template v-slot:operation="{row}">
            <Button @click="extendClick(row)">扩展</Button>
          </template>
        </Table>
      </div>
      <div slot="footer">
        <Button type="text" @click="model = false">取消</Button>
      </div>
    </Modal>
  </div>
</template>
<script>
import { vmextendQuery, vmextendConfirm} from '@/api/virtualMachine';

export default {
  props: {
    datas: Number,
  },
  watch: {
    datas(news){
      this.tablesQuery()
      this.model = true
    }
  },
  data() {
    return {
      model: false,
      tableColumns: [
        { title: "虚拟机名称", key: "name" },
        { title: "VCPU", key: "vcpus" },
        { title: "VCPU推荐值", key: "suggestion_vcpus", slot: 'suggestion_vcpus' },
        { title: "内存", key: "ram", slot: 'ram' },
        { title: "内存推荐值", key: "suggestion_ram", slot: 'suggestion_ram' },
        { title: "操作", key: "operation", width:80, slot: 'operation' }
      ],
      tableData: [],
    };
  },
  methods: {
    tablesQuery(){
      // let arr = new Array()
      // for(let i=0;i<100;i++){
      //   arr.push({ name: 'test'+i, cpu: i, mem: i })
      // }
      // this.tableData = arr
      vmextendQuery().then(callback=>{
        if(callback.data.value!==undefined) {
          this.tableData = callback.data.value.map(item=>{
            item.ram = Math.floor(item.ram / 1024)
            item.suggestion_ram = Math.floor(item.suggestion_ram / 1024)
            return item
          })
        }else{
          this.tableData = new Array()
        }
      })
    },
    extendClick(row){
      let arr = document.cookie.split('; ');
      let arrData = new Object()
      arr.forEach(item=>{
        let li = item.split('=')
        arrData[li[0]]=li[1]
      })
      let cpu = row.cpu_expansion=='true'?row.suggestion_vcpus:row.vcpus
      let ram = row.mem_expansion=='true'?row.suggestion_ram:row.ram
      console.log({
        vm_id: row.vm_id,
        vm_name: row.vm_name,
        username: arrData.username,
        role: arrData.role,
        name: cpu+"-"+ram+"-"+row.disk,
        vcpus: cpu,
        ram: ram,
        disk: row.disk,
        extdisk: 0,
        disk: row.disk,
      })
      let text = ``
      row.cpu_expansion=='true'?text += `是否将VCPU由 ${row.vcpus} 扩展至 ${cpu} ?<br>`:''
      row.mem_expansion=='true'?text += `是否将内存由 ${row.ram}GB 扩展至 ${ram}GB ?<br>`:''
      this.$Modal.confirm({
        title: '扩展确认',
        content: text,
        onOk: () => {
          vmextendConfirm({
            vm_id: row.vm_id,
            vm_name: row.name,
            // username: arrData.username,
            // role: arrData.role,
            name: cpu+"-"+ram+"-"+row.disk,
            vcpus: cpu,
            ram: ram,
            disk: row.disk,
            extdisk: 0,
            disk: row.disk,
          }).then(callback=>{
            if(callback.data.msg == 'ok') {
              this.$emit("expansion-ok");
              this.tablesQuery()
              this.$Message.success({
                background: true,
                closable: true,
                duration: 5,
                content: '动态资源扩展完成',
              });
            }else {
              this.$Message.error({
                background: true,
                closable: true,
                duration: 5,
                content: '动态资源扩展失败',
              });
            }
          })
        },
        onCancel: () => {}
      });
      
    },
    
  },
};
</script>