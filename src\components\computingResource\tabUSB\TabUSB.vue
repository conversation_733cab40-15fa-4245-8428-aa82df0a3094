<template>
  <div class="usb_area">
    <Spin fix v-if="tableSpin" size="large" style="color:#ef853a">
      <Icon type="ios-loading" size=50 class="demo-spin-icon-load"></Icon>
      <div style="font-size:16px;padding:20px">Loading...</div>
    </Spin>
    <Table :columns="usbColumn" :data="usbData">
      <template v-slot:usbplug="{row}">
        <span :style="{color:row.usbplug=='plug_in'?'green':'#ccc'}">{{ row.usbplug=='plug_in'?'已插入':'已拔出'}}</span>
      </template>
      <template v-slot:vm_name="{row}">
        <span>{{ row.vm_name==''?'-未绑定-':row.vm_name}}</span>
      </template>
      <!-- 操作 -->
      <template v-slot:operation="{ row }">
        <Dropdown @on-click="dropdownClick($event, row)">
          <Button>配置 ▼</Button>
          <template #list>
            <DropdownMenu>
              <DropdownItem name="gzxj" :disabled="row.vm_name !== ''">挂载虚拟机</DropdownItem>
              <DropdownItem name="jgxj" :disabled="row.vm_name == ''">解挂虚拟机</DropdownItem>
            </DropdownMenu>
          </template>
        </Dropdown>
      </template>
    </Table>
    <!-- 挂载虚拟机 -->
    <USBmount :mounttime="mounttime" :mountrow="mountrow" @custom-succe="customSucce"></USBmount>
    <!-- 提示 -->
     <Modal width="600" v-model="promptModel" title="USB解挂虚拟机">
      <P>USB: {{this.mountrow.product_name}} 解挂虚拟机，如需生效请手动重启虚拟机。</P>
      <div slot="footer">
        <Button type="text" @click="promptModel = false">取消</Button>
        <Button type="primary" @click="promptOK" :disabled="disabled">确认</Button>
      </div>
    </Modal>
  </div>
</template>
<script>
import USBmount from './USBmount.vue'; // 配置USB
import {usbQuery,usbVMunhook} from '@/api/other';  // USB查询，解挂
export default {
  components: {
    USBmount,
  },
  props: {
    tabSelected: String,
  },
  watch: {
    tabSelected(value) {
      if(value=='USB分配'){
        this.tableData()
      }
    },
  },
  data() {
    return {
      tableSpin:false,
      usbColumn: [
        { title: "设备名称", key: "product_name" },
        { title: "服务器", key: "hostname",align: "center" },
        { title: "服务IP", key: "host_ip",align: "center" },
        { title: "服务端口", key: "host_port",align: "center" },
        { title: "虚拟机", key: "vm_name",align: "center",slot: 'vm_name'  },
        { title: "状态", key: "usbplug",align: "center",slot: 'usbplug' },
        { title: "操作", key: "operation",width: 120, slot:"operation" }
      ],
      usbData: [],
      // 挂载虚拟机
      mounttime:"",
      mountrow:{},
      // 提示
      promptModel:false,
      disabled:false,
      productName:"",
      formItem:{
        vm_id:"",
        vm_name:"",
        host_ip:"",
        host_port:"",
        host_name:"",
        sn:"",
        vid:undefined,
        pid:undefined,
        bus:undefined,
        device:undefined,
      },
    };
  },
  mounted() {
    if(this.$store.state.power.computingResourceTab == 'USB分配') {
      this.tableData()
    }
  },
  methods: {
    tableData(){
      this.tableSpin = true
      usbQuery(this.vmPageForm)
        .then((callback) => {
          this.usbData = callback.data.data
          this.tableSpin = false
        })
        .catch((error) => {
          this.tableSpin = false
        });
    },
    // 表格操作
    dropdownClick(event, row) {
      switch (event) {
        case "gzxj":
          this.mountrow = row
          this.mounttime = ""+new Date()
          break;
        case "jgxj":
          this.productName = row.product_name
          this.formItem.vm_id = row.vm_id
          this.formItem.vm_name = row.vm_name
          this.formItem.host_ip = row.host_ip
          this.formItem.host_port = row.host_port
          this.formItem.host_name = row.hostname
          this.formItem.sn = row.sn
          this.formItem.vid = row.vendor_id
          this.formItem.pid = row.product_id
          this.formItem.bus = row.bus.toString()
          this.formItem.device = row.device.toString()
          
          this.promptModel = true
          this.disabled = false
          break;
      }
    },
    // 挂载返回
    customSucce(data){
      this.tableData()
    },
    // 解挂
    promptOK(){
      this.disabled = true
      usbVMunhook(this.formItem)
      .then(callback=>{
        this.promptModel = false
        if(callback.data.msg == "ok") {
          this.$Message.success({
            background: true,
            closable: true,
            duration: 5,
            content: "USB解挂虚拟机操作完成",
          });
          this.tableData()
          
        }else {
          this.$Message.error({
            background: true,
            closable: true,
            duration: 5,
            content: "USB解挂虚拟机失败",
          });
        }
      })
    },
    
  }
};
</script>
