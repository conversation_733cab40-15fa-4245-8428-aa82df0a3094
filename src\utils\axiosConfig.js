import Axios from "axios";
import router from "../router/index";
import { Notice, Modal, Message } from "view-design";
import store from "../store/store";
// 请求拦截器
Axios.interceptors.request.use(
  config => {
    // 发送请求之前可以在这里写一些东西
    return config;
  },
  error => {
    // 请求报错的时候做一些事情
    return Promise.reject(error);
  }
);
// 响应拦截器
Axios.interceptors.response.use(
  response => {
    // 对响应数据做一些事情
    let hashValue = window.location.hash; // 获取当前路由地址
    let usernames = window.sessionStorage.getItem("username"); // 获取登录时浏览器存储名字
    let interface_cookie = document.cookie.split("; "); // 获取接口cookie
    let cookieValue = ""; // 接口数据添加时间
    if (hashValue == "#/login") {
      // 登录页面获取数据接口判定
      console.log("登录成功")
    } else {
      // 非登录页面逻辑
      let list = false; // 判断条件
      interface_cookie.forEach(item => {
        let li = item.split("=");
        if (li[0] == "username") {
          window.sessionStorage.setItem("username", li[1]); // 更新浏览器缓存名字
          usernames && usernames !== li[1] ? window.location.reload() : ""; // 当浏览器存储名字与cookie不一致时自动刷新页面更新
          list = true;
          cookieValue = item;
        }
      });
      if (response.config.url == "/login/logout") {
        console.log("退出成功");
      } else if (!list) {
        router.push("/login");
        Message.error({
          background: true,
          closable: true,
          duration: 5,
          content: "用户过期请重新登录",
        });
      } else {
        if (response.config.url == "/theapi/v2/alerts/all") {
          console.log("退出范围时间", store.state.power.exitTimeSave);
        } else {
          // 获取当前日期时间
          var currentDate = new Date();
          // 设置新的过期时间，例如，将 cookie 过期时间延长30分钟
          var newExpirationDate = new Date(
            currentDate.getTime() + store.state.power.exitTimeSave * 60 * 1000
          );
          // 格式化新的过期时间为 Cookie 字符串
          var cookieString =
            cookieValue + "; expires=" + newExpirationDate.toUTCString();
          // 设置修改后的 cookie
          document.cookie = cookieString;
        }
      }
    }
    return response;
  },
  error => {
    // 对响应数据做一些事情
    var path = window.location.href;
    var parts = path.split("/");
    var lastPart = parts[parts.length - 1]; 
    if (lastPart === "") {
      lastPart = parts[parts.length - 2];
    }
    // console.log("接口报错时", document.cookie);
    let interface_cookie = document.cookie.split("; ");
    let list = false;
    interface_cookie.forEach(item => {
      let li = item.split("=");
      if (li[0] == "username") {
        list = true;
      }
    });
    if (!list && lastPart !== "login") {
      router.push("/login");
      setTimeout(() => {
        window.location.reload()
      }, 1000);
      Message.error({
        background: true,
        closable: true,
        duration: 5,
        content: "用户过期请重新登录"
      });
    }
    // 请求错误时做些事
    
    let status = "";
    if (error.request) {
      status = error.request;
    } else if (error.response) {
      status = error.response;
    }
    if (status) {
      if (status.status == 401) {
        error.message = "未授权，请重新登录(401)";
        router.push("/login");
        // moduleShow("notice") ? noticefunction(status) : "";
      }else if (status.status>=500) {
        error.message = "服务器错误(500)";
        moduleShow("notice") ? noticefunction(status) : "";
      }
    } else {
      error.message = "连接服务器失败!";
    }
    return Promise.reject(error);
  }
);
function noticefunction(status){
  Notice.error({
    duration: 0,
    title: "错误" + status.status,
    desc: status.responseURL,
    render:h=>{
      return h(
        "div",
        {
          style: {
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center"
          }
        },
        [
          h("span", status.responseURL),
          h("Button",{
              on: {
                click: () => {
                  Modal.error({
                    title: "错误" + status.status,
                    content: status.responseText
                  });
                }
              }
            },
            "详情"
          )
          // h(
          //   "Tooltip",
          //   {
          //     props: {
          //       placement: "left",
          //       content: status.responseText,
          //       always:true
          //     }
          //   },
          //   "详情"
          // )
        ]
      );
    }
  });
}
// 模块显示隐藏判断
function  moduleShow(item){
  return window.gurl.PageModule.indexOf(item) == -1?false:true
}

export default Axios;
