<template>
<div>
  <Modal v-model="model" width="600" :mask-closable="false">
    <template #header><p>编辑子网</p></template>
    <Form
      ref="formItem"
      :model="formItem"
      :rules="rulesForm"
      :label-width="120"
    >
      <FormItem label="当前子网">
        <Select v-model="formItem.cidrid" :label-in-value="true" @on-change="cidrChange">
          <Option
            v-for="item in cidrData"
            :value="item.value"
            :key="item.value"
            >{{ item.label }}</Option
          >
        </Select>
      </FormItem>
      <FormItem label="子网名称" prop="name">
        <Input v-model="formItem.name" placeholder="输入网关"></Input>
      </FormItem>
      <FormItem label="网关地址" prop="gateway">
        <Input v-model="formItem.gateway" placeholder="输入网关"></Input>
      </FormItem>
      <!-- <FormItem label="启用DHCP">
            <Checkbox
              v-model="formItem.dhcp"
            ></Checkbox>
          </FormItem> -->
      <div v-if="formItem.dhcp">
        <FormItem label="IP池起始" prop="start">
          <Input
            v-model="formItem.start"
            placeholder="例：***********"
          ></Input>
        </FormItem>
        <FormItem label="IP池结束" prop="end">
          <Input
            v-model="formItem.end"
            placeholder="例：*************"
          ></Input>
        </FormItem>
      </div>
      <FormItem label="DNS">
        <Input v-model="formItem.dns" placeholder="输入DNS"></Input>
      </FormItem>
      <FormItem label="主机路由">
        <div style="display: flex; justify-content: space-evenly">
          <Input
            v-model="formItem.destination"
            placeholder="网段例：***********/24"
          ></Input>
          <Input
            v-model="formItem.nexthop"
            placeholder="下一跳例：********"
          ></Input>
          <Button type="text" @click="addRouter" icon="md-add-circle"></Button>
        </div>
      </FormItem>
      <div style="display: flex; flex-wrap: wrap">
        <ul
          v-for="(item, index) in formItem.routeData"
          style="margin: 0 2px; border-right: 1px solid #ccc"
        >
          <li style="display: inline-block; width: 120px">
            {{ item.destination }}
          </li>
          <li style="display: inline-block; width: 120px">
            {{ item.nexthop }}
          </li>
          <Icon
            type="ios-trash"
            @click="deletRoutes(index)"
            style="color: red; cursor: pointer; font-size: 20px"
          />
        </ul>
      </div>
    </Form>
    <template #footer>
      <Button type="text" @click="model = false">取消</Button>
      <Button
        type="info"
        class="plus_btn"
        @click="modalOK"
        :disabled="disabled"
        >确定</Button
      >
    </template>
  </Modal>
</div>
</template>
<script>
import {networkTableSubnetModify} from '@/api/network';

export default {
  props: {
    tableRow: Object,
    subnetTime: String,
  },
  watch: {
    subnetTime(news){
      this.formItem.title = this.tableRow.name
      let arr = new Array()
      this.tableRow.cidr.forEach((em,index)=>{
        arr.push({
          label:em,
          value:index,
        })
      })
      this.cidrData = arr // 子网数据
      this.gatewayData = this.tableRow.gateway_ip // 网关
      this.subnetData = this.tableRow.subnets // subnetID
      this.allocationPools = this.tableRow.allocation_pools // IP池
      this.dnsNameservers = this.tableRow.dns_nameservers // DNS
      this.hostRoute = this.tableRow.host_routes // 路由池
      this.formItem.cidrname = this.cidrData[0].label
      this.formItem.cidrid = this.cidrData[0].value
      this.formItem.name = this.formItem.cidrname.split(":")[0]
      this.formItem.gateway = this.gatewayData[this.formItem.cidrid]
      this.formItem.subnetID = this.subnetData[this.formItem.cidrid]
      this.formItem.dns = this.dnsNameservers[this.formItem.cidrid].toString()
      this.formItem.start = this.allocationPools[this.formItem.cidrid].start?this.allocationPools[this.formItem.cidrid].start:""
      this.formItem.end = this.allocationPools[this.formItem.cidrid].end?this.allocationPools[this.formItem.cidrid].end:""
      this.formItem.routeData = this.hostRoute[this.formItem.cidrid].length>0?this.hostRoute[this.formItem.cidrid].slice():[]
      this.model = true
      this.disabled = false
    }
  },
  data(){
    // 名称
    const propName = (rule, value, callback) => {
      let list = /^[\u4e00-\u9fa5_a-zA-Z0-9@_.-]{2,32}$/;
      if (list.test(value)) {
        callback()
      } else {
        callback(new Error("请输入2-32个中文或字符（特殊字符可用@_.-）"));
      }
    };
    // 网关
    const propNet = (rule, value, callback) => {
      let list = /^(25[0-4]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-4]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-4]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-4]|2[0-4][0-9]|[01]?[1-9][0-9]?|1[0-9]{2}|2[0-4][0-9])$/
      if (list.test(value)) {
        this.ipInSubnet(value, this.formItem.cidrname.split(":")[1]) ? callback() : callback(new Error("与子网不匹配"));
      } else {
        callback(new Error("该ip不可用"));
      }
    };
    // IP池起始
    const propStart = (rule, value, callback) => {
      let wg = this.formItem.gateway.split(".");
      let qs = value.split(".");
      for(var i=0;i<wg.length;i++) {
        if(i==3) {
          if(wg[i] !== qs[i]) {
            callback()
          }else {
            callback(new Error("网关地址不能在IP池范围内"))
            break
          }
        }else {
          if(wg[i] !== qs[i]) {
            callback(new Error("网关地址与IP池不在同一网段"));
            break
          }
        }
      }
    };
    // IP池结束
    const propEnd = (rule, value, callback) => {
      let qs = this.formItem.start.split(".");
      let js = value.split(".");
      for(var i=0;i<qs.length;i++) {
        if(i==3) {
          if(qs[i]*1 < js[i]*1) {
            callback()
          }else {
            callback(new Error("且结束IP应大于起始IP"))
            break
          }
        }else {
          if(qs[i] !== js[i]) {
            callback(new Error("网关地址与IP池不在同一网段"));
            break
          }
        }
      }
    };
    return {
      model: false,
      disabled: false,
      formItem:{
        title: '',
        cidrname: '',
        cidrid: 0,
        name: '',
        gateway: '',
        dhcp: true,
        subnetID: '',
        start: '',
        end: '',
        dns: '',
        destination: '',
        nexthop: '',
        routeData:[],
      },
      cidrData: [],
      gatewayData: [],
      subnetData: [],
      allocationPools: [],
      dnsNameservers: [],
      hostRoute:[],
      rulesForm: {
        name: [
          { required: true, message: "必填项" },
          { validator: propName, trigger: "blur" },
        ],
        gateway: [
          { required: true, message: "必填项" },
          { validator: propNet, trigger: "blur" },
        ],
        start: [
          { required: true, message: "必填项" },
          { validator: propStart, trigger: "blur" },
        ],
        end: [
          { required: true, message: "必填项" },
          { validator: propEnd, trigger: "blur" },
        ],
      },
    }
  },
  methods: {
    // 选择子网
    cidrChange(item){
      this.formItem.cidrname = item.label
      this.formItem.cidrid = item.value
      this.formItem.name = this.formItem.cidrname.split(":")[0]
      this.formItem.gateway = this.gatewayData[item.value]
      this.formItem.subnetID = this.subnetData[item.value]
      this.formItem.dns = this.dnsNameservers[item.value].toString()
      this.formItem.start = this.allocationPools[item.value].start?this.allocationPools[item.value].start:""
      this.formItem.end = this.allocationPools[item.value].end?this.allocationPools[item.value].end:""
      this.formItem.routeData = this.hostRoute[item.value].length>0?this.hostRoute[item.value].slice():[]
    },
    // 主机路由添加
    addRouter(){
      if(this.formItem.destination==""||this.formItem.nexthop==""){
        this.$Message.warning({
          background: true,
          closable: true,
          duration: 5,
          content: "主机路由输入有误",
        });
      }else {
        this.formItem.routeData.push({
          destination:this.formItem.destination,
          nexthop:this.formItem.nexthop,
        })
        this.formItem.destination = ""
        this.formItem.nexthop = ""
      }
    },
    // 主机路由删除
    deletRoutes(index){
      this.formItem.routeData.splice(index,1)
    },
    modalOK(){
      this.$refs['formItem'].validate((valid) => {
        if(valid){
          this.disabled = true
          networkTableSubnetModify({
            allocation_pools:this.formItem.dhcp?[{start:this.formItem.start,end:this.formItem.end}]:[],
            host_routes:this.formItem.routeData,
            gateway_ip:this.formItem.gateway,
            subnet_id:this.formItem.subnetID,
            dns_nameservers:this.formItem.dns!==""?this.formItem.dns.split(","):[],
            // name:this.formItem.cidrname,
            name:this.formItem.name,
          }).then((callback) => {
            this.model = false
            if (callback.data.msg == "ok") {
              this.$emit("return-ok",'编辑子网操作完成');
            } else if (callback.data.msg == "409") {
              this.$emit("return-error",'网关地址不能出现在DHCP地址范围内');
            } else {
               this.$emit("return-error",callback.data.msg);
            }
          }).catch((error) => {
            this.disabled = false
            this.$emit("return-error",'编辑子网操作失败');
          })
        }
      })
    },
    ipInSubnet(ip, subnet) {
      let [subnetIp, maskBits] = subnet.split('/');
      maskBits = parseInt(maskBits, 10);
      let ipBinary = ip.split('.').map(octet => parseInt(octet, 10).toString(2).padStart(8, '0')).join('');
      let subnetBinary = subnetIp.split('.').map(octet => parseInt(octet, 10).toString(2).padStart(8, '0')).join('');
      for(let i=0; i<maskBits; i++){
          if(ipBinary[i] != subnetBinary[i]){
              return false;
          }
      }
      return true;
    },
  }
}
</script>