<template>
  <div>
    <Modal
      v-model="model"
      width="600"
      :mask-closable="false"
    >
      <template #header><p>编辑容器</p></template>
      <Form :model="formItem" ref="formItem" :rules="ruleValidate" :label-width="120">
          <FormItem label="容器名称" prop="name">
            <Input v-model="formItem.name" placeholder="输入容器名"></Input>
          </FormItem>
          <FormItem label="CPU" prop="cpu">
            <Input v-model="formItem.cpu" type='number' placeholder="请输入容器虚拟CPU数量"></Input>
          </FormItem>
          <FormItem label="内存" prop="memory">
            <Input v-model="formItem.memory"  type='number' placeholder="请输入内存大小(MB)"></Input>
          </FormItem>
				</Form>
      <div slot="footer">
        <Button type="text" @click="model = false">取消</Button>
        <Button type="primary" @click="modalOK" :disabled="disabled">确认</Button>
      </div>
    </Modal>
  </div>
</template>
<script>
import {containerEdit} from '@/api/container'; // 容器表 编辑
export default {
  props: {
    row:Object,
    editTime:String,
  },
  watch: {
    editTime(news){
      console.log(this.row)
      this.model = true
      this.formItem.id = this.row.uuid
      this.formItem.name = this.row.name
      this.formItem.cpu = this.row.cpu
      this.formItem.memory = this.row.memory
    }
  },
  data(){
    const propCPU = (rule, value, callback) => {
      if (this.cpuJudge(value)) {
        callback()
      } else {
        callback(new Error("请输入大于等于0的数字"))
      }
    }
    const propMem = (rule, value, callback) => {
      if (this.memJudge(value)) {
        callback()
      } else {
        callback(new Error("请输入请输入128倍数的数字"))
      }
    }
    return {
      model:false,
      disabled: false,
      formItem: {
        id: '',
        name: '',
        cpu: '',
        memory: '',
      },
      ruleValidate:{
        name:[{ required: true, message: '必填项', trigger: 'change' }],
        cpu:[{ validator: propCPU, trigger: "change" }],
        memory:[{ validator: propMem, trigger: "change" }],
      }
    }
  },
  methods: {
    cpuJudge(value) {
      if (value === '') return true;
      const num = Number(value);
      return !isNaN(num) && num > 0;
    },
    memJudge(value) {
      if (value === '') return true;
      const num = Number(value);
      return !isNaN(num) && num > 0 && num % 128 === 0;
    },
    modalOK() {
      this.$refs.formItem.validate((valid) => {
        if(valid) {
          this.disabled = true
          containerEdit(this.formItem)
          .then((callback) => {
            this.model = false;
            this.$emit("return-ok",{
              msg: '编辑容器操作已完成',
              type: 'ok'
            });
          })
          .catch((error) => {
            this.disabled = false
            this.$emit("return-ok",{
              msg: '编辑容器操作失败',
              type: 'error'
            });
          })
        }
      })
    },
  }
}
</script>