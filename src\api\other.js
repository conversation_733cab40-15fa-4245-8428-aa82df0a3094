// 其余 类
import axios from "axios";
import * as basic_proxy from "@/api/config";

// 概览/大屏数据
export async function homepageData() {
  return await axios.get(basic_proxy.theapi + "/v2/home");
}
// 大屏 带宽
export async function largeScreenBandwidth(params) {
  return await axios.post(
    basic_proxy.theapi + "/v1/chart/ceph/throughput",
    params
  );
}
// 大屏 IOPS
export async function largeScreenIOPS(params) {
  return await axios.post(basic_proxy.theapi + "/v1/chart/ceph/iops", params);
}
// 大屏 机器数量
export async function largeScreenMachineNumber() {
  return await axios.get(basic_proxy.theapi + "/v1/screen/nodes");
}


// 通知信息
export async function notificationInformation() {
  return await axios.get(
    basic_proxy.theapi + "/v1/migrate/task/recommend/list"
  );
}
// 通知迁移确认
export async function notifyMigrationConfirmation(params) {
  return await axios.post(basic_proxy.theapi + "/v1/migrate/task/run", params);
}

// 运维助手 健康汇总
export async function healthSummaryQuery() {
  return await axios.get(basic_proxy.theapi + "/v1/health/allname");
}

// 运维助手 健康单查
export async function healthSummaryCheck(params) {
  return await axios.get(basic_proxy.theapi + "/v1/health/check?key=" + params);
}

// USB查询
export async function usbQuery() {
  return await axios.get(basic_proxy.thecloudapi + "/v1/usbserver/list");
}

// USB挂载虚拟机
export async function usbVMmount(params) {
  return await axios.post(
    basic_proxy.thecloudapi + "/v1/usbserver/bind/vm",
    params
  );
}

// USB解挂虚拟机
export async function usbVMunhook(params) {
  return await axios.post(
    basic_proxy.thecloudapi + "/v1/usbserver/unbind/vm",
    params
  );
}

// 分配虚拟机
export async function allocationVM(params) {
  return await axios.post(
    basic_proxy.theapi + "/v1/grant/instances/to/user",
    params
  );
}
// 取消分配虚拟机
export async function unassignVM(params) {
  return await axios.post(
    basic_proxy.theapi + "/v1/grant/instances/delete/user",
    params
  );
}

// 权限树获取
export async function powerTreeQuery(params) {
  return await axios.get(basic_proxy.acapi + "/v1/auth/userinfo", {
    params: params,
  });
}
// 权限树修改
export async function powerTreeEdit(params) {
  return await axios.put(basic_proxy.acapi + "/v1/auth/update", params);
}
// 权限树重置
export async function powerTreeReset(params) {
  return await axios.put(basic_proxy.acapi + "/v1/auth/default", params);
}
// 权限可用性
export async function powerUsabilityQuery(params) {
  return await axios.post(basic_proxy.acapi + "/v1/auth/module", params);
}
// 权限可用性code码
export async function powerCodeQuery(params) {
  return await axios.post(basic_proxy.acapi + "/v1/auth/check", params);
}

// 动态资源调度查询
export async function dynamicResourceSchedulingQuery() {
  return await axios.get(basic_proxy.theapi + "/v1/cluster/drs");
}
// 动态资源调度编辑
export async function dynamicResourceSchedulingEdit(params) {
  return await axios.post(
    basic_proxy.theapi + "/v1/cluster/drs/update",
    params
  );
}
// 动态资源调度表查询
export async function dispatchTableQuery() {
  return await axios.get(basic_proxy.theapi + "/v1/cluster/drs/table");
}
// 动态资源调度表确认
export async function dispatchTableConfirm(params) {
  return await axios.post(basic_proxy.theapi + "/v1/cluster/drs/table", params);
}