<template>
<Modal v-model="model" width="600" :mask-closable="false">
  <template #header><p>编辑集群组</p></template>
  <Form :model="formItem" ref="formItem" :rules="rulesForm" :label-width="120">
    <FormItem label="当前集群组名称">
        <Input v-model="groupSelect.name" disabled></Input>
      </FormItem>
    <FormItem label="集群组新名称" prop="name">
      <Input v-model="formItem.name" placeholder="请输入集群组新名称" ></Input>
    </FormItem>
  </Form>
  <div slot="footer">
    <Button type="text" @click="model = false">取消</Button>
    <Button type="primary" @click="modelOK" :disabled="disabled">确认</Button>
  </div>
</Modal>
</template>
<script>
import {clusterGroupEdit} from '@/api/physics';
export default {
  props: {
    editTime: String,
    groupSelect: Object,
  },
  watch: {
    editTime(news){
      // this.formItem.title = this.groupSelect.name+ ' 集群组编辑名称'
      this.$refs.formItem.resetFields()
      this.model = true
      this.disabled = false
    }
  },
  data() {
    // 名称
    const propName = (rule, value, callback) => {
      let list = /^[\u4e00-\u9fa5_a-zA-Z0-9@_.-]{2,32}$/;
      if(list.test(value)) {
        callback()
      }else {
        callback(new Error('2-32 个中文、英文、数字、特殊字符(@_.-)'))
      }
    };
    return {
      model: false,
      disabled: false,
      formItem:{
        title: '',
        name: '',
      },
      // 正则验证
      rulesForm: {
        name: [
          { required: true, message: "必填项", trigger: "change" },
          { validator: propName, trigger: "change" },
        ],
      },
    };
  },
  methods: {
    modelOK() {
      this.$refs.formItem.validate((valid) => {
        if(valid){
          this.disabled = true
          clusterGroupEdit({id:this.groupSelect.id,name:this.formItem.name})
          .then(callback=>{
            if(callback.data.msg=="error"){
              this.$emit("custom-error",'名称已使用，禁止创建同名集群');
              this.disabled = false
            }else {
              this.$emit("custom-ok",'编辑集群组完成');
              this.model = false
            }
          })
        }
      })
    },
  },
};
</script>