<style lang="less">
  @import "../networkResource.less";
</style>
<template>
  <div class="network_topology_area">
    <Tabs name="2" v-model="tabTuopu" @on-click="tabsClick">
      <TabPane icon="md-cloud-circle" label="物理拓扑" tab="2" name="wulituopu">
        <physicalTopology :tabTuopu="tabTuopu"></physicalTopology>
      </TabPane>
      <!-- <TabPane icon="md-filing" label="网络拓扑" tab="2" name="wangluotuopu">
        <netTopology :tabTuopu="tabTuopu"></netTopology>
      </TabPane> -->
      <TabPane icon="md-filing" label="网络拓扑" tab="2" name="network">
        <networkTopology :tabTuopu="tabTuopu"></networkTopology>
      </TabPane>
    </Tabs>
  </div>
</template>
<script>
import physicalTopology from "./physicalTopology/physicalTopology.vue";
import netTopology from "./netTopology/netTopology.vue";
import networkTopology from "./netTopology/networkTopology.vue";

export default {
	props: {
    tabSelected: String,
  },
	components: {
    physicalTopology,
    netTopology,
    networkTopology,
  },
	data() {
		return {
			tabTuopu: ''
		}
	},
	watch: {
    tabSelected(value) {
			value == "拓扑"?this.tabTuopu="wulituopu":this.tabTuopu="wangluotuopu"
		}
	},
	methods:{
		tabsClick(name) {
			this.tabTuopu = name
		},
	},
}
</script>
