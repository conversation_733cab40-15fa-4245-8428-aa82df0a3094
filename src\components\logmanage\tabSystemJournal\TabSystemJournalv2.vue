<style lang="less">
@import "../logmanage.less";
</style>
<template>
  <div class="log_page_area">
    <Spin fix v-if="spinShow" size="large" style="color:#ef853a">
      <Icon type="ios-loading" size=50 class="demo-spin-icon-load"></Icon>
      <div style="font-size:16px;padding:20px">Loading...</div>
    </Spin>
    <ul class="alarm_page_screen">
        <li>
          <span>时间:</span>
          <DatePicker type="datetimerange" separator=" 至 " v-model="tablePageForm.times" placeholder="必选项" :clearable="false" @on-change="intervalTime" style="width: 340px" />
        </li>
        <li v-if="this.roleIF">
          <span>账号:</span>
          <Input v-model="tablePageForm.username" placeholder="请输入登录账号" style="width: 100px" ></Input>
        </li>
        <li v-if="this.roleIF">
          <span>类别:</span>
          <!-- <Select v-model="tablePageForm.category" clearable style="width:120px" filterable> filterable开启选择搜索 -->
          <Select v-model="tablePageForm.category" clearable style="width:120px">
            <Option v-for="item in categorySelect" :value="item.value" :key="item.value">{{ item.value }}</Option>
          </Select>
        </li>
        <li v-if="this.roleIF&&moduleShow('tripleManage')">
          <span>角色:</span>
          <Select v-model="tablePageForm.role" clearable style="width:120px">
            <Option v-for="item in roleSelect" :value="item.value" :key="item.value">{{ item.label }}</Option>
          </Select>
        </li>
        <li>
          <span>类型:</span>
          <Select v-model="tablePageForm.action" clearable style="width:120px">
            <Option v-for="item in actionSelect" :value="item.value" :key="item.value">{{ item.value }}</Option>
          </Select>
        </li>
        <li>
          <span>结果:</span>
          <Select v-model="tablePageForm.result" clearable style="width:120px">
            <Option v-for="item in resultSelect" :value="item.value" :key="item.value">{{ item.value }}</Option>
          </Select>
        </li>
        <li>
          <Button class="plus_btn" icon="ios-search" @click="queryClick">查询</Button>
        </li>
        <li>
          <a :href="fileUrl">
            <Button type="primary" :disabled="disabled">
              <Icon type="ios-download-outline"></Icon>导出日志
            </Button>
          </a>
        </li>
    </ul>
    <div class="table_currency_area">
      <Table :columns="journalColumn" :data="journalData" ref="table"  @on-sort-change="sortColumn">
        <template v-slot:exectime="{row}">
          {{conversionTime(row.exectime)}}
        </template>
      </Table>
      <!-- 虚拟机表格 分页  -->
      <div class="pages" v-if="this.journalData.length>0">
        <!-- <Page
          :total="tableTotal"
          show-total
          show-sizer
          :page-size="tablePageForm.pagecount"
          placement="top"
          :page-size-opts="[10, 20, 30]"
          @on-change="onPageChange"
          @on-page-size-change="onPageSizeChange"
        /> -->
        <Pagination  :total="tableTotal" :page-size="tablePageForm.pagecount"  @page-change="onPageChange" @page-size-change="onPageSizeChange"/>
      </div>
    </div>
  </div>
</template>
<script>
import {
  queryCriteriaV2, // 日志 查询条件
  logDataQueryV2, // 日志数据 查询
} from '@/api/log';

// import FileSaver from "file-saver";
// import * as XLSX from "xlsx";
import Pagination from '@/components/public/Pagination.vue';

export default {
  components: {
    Pagination
  },
  props: {
    tabName: String,
  },
  data() {
    return {
      // 操作类型
      actionSelect: [],
      // 类别
      categorySelect:[],
      // 角色
      roleSelect: [],
      roleIF:true,
      // 结果
      resultSelect: [],
      // 表单查询数据
      tablePageForm: {
        result: "",
        username: '',
        action: "",
        category:"",
        role: "",
        site: "",
        times:[],
        exectime: "",
        end_date: "",
        page: 1, // 当前页
        pagecount: this.$store.state.power.pagecount, // 每页条数
        search_str: "", // 收索
        order_type: "desc", // 排序规则
        order_by: "", // 排序列
      },
      today:"",
      yesterday:"",
      eventStampEXE:0,
      eventStampEND:0,
      // 表格 列 行
      spinShow:false,
      journalColumn: [
        { title: "时间", key: "exectime", sortable: 'custom',slot: 'exectime'},
        { title: "类别", key: "category", align: "center"  },
        { title: "登录账号", key: "username", align: "center"  },
        { title: "角色", key: "role", align: "center",
          render: (h, params) => {
            let text = "其他"
            if(params.row.role == "sysadm") {
              text = "系统管理员"
            }else if(params.row.role == "adtadm") {
              text = "审计员"
            }else if(params.row.role == "secadm") {
              text = "安全员"
            }else if(params.row.role == "operator") {
              text = "操作员"
            }
            return h("span",text)
          }
        },
        { title: "操作类型", key: "action", align: "center" },
        { title: "描述", key: "description", align: "center" },
        { title: "结果", key: "result",align: "center",width:120,
          render: (h, params) => {
            if(params.row.result=="成功"){
              return h("span",{ style:{color:'#28bf01'}},params.row.result)
            }else {
              return h("span",{ style:{color:'red'}},params.row.result)
            }
          }
        },
      ],
      journalData: [],
      
      // 导出判定
      disabled:false,
			// 表格数据总数
      tableTotal:0,
      fileUrl:"#",
    };
  },
  created() {
  },
  watch: {
    
    tabName(value) {
      if(value=='日志'){
        this.default_times()
        this.load()
        this.queryClick()
        this.slectAllGET()
      }
    },
    'tablePageForm.times'(news){
      if(news[0]=="") {
        this.disabled = true
      }else {
        this.disabled = false
      }
    },
  },
  mounted() {
    if(this.$store.state.power.logManagementTab == '日志') {
      this.default_times()
      this.load()
      this.queryClick()
      this.slectAllGET()
    }
  },
  updated() {
    this.tablePageForm.pagecount=this.$store.state.power.pagecount
  },
  methods: {
     // 获取权限
    load() {
      if(document.cookie.length > 0) {
        let arr = document.cookie.split('; ');
        let arrData = new Object()
        arr.forEach(item=>{
          let li = item.split('=')
          arrData[li[0]]=li[1]
        })
        this.usernames = arrData.username
        let liset = new Array()
        if(arrData.role == 'sysadm'){
          liset.push(
            { label: "系统管理员", value: "sysadm" },
            { label: "审计员", value: "adtadm" },
            { label: "安全员", value: "secadm" },
            { label: "操作员", value: "operator" },
          )
          // 系统管理员 sysadm
        }else if(arrData.role == 'secadm') {
          liset.push(
            { label: "审计员", value: "adtadm" },
            { label: "操作员", value: "operator" },
          )
          // 安全管理员 secadm
        }else if(arrData.role == 'adtadm') {
          liset.push(
            { label: "系统管理员", value: "sysadm" },
            { label: "审计员", value: "adtadm" },
            { label: "安全员", value: "secadm" },
            { label: "操作员", value: "operator" },
          )
          // 审计员 adtadm
        }else if(arrData.role == 'operator') {
          // 操作员 operator
          this.roleIF = false
        }
        this.roleSelect = liset
      }
    },
    
    // 获取当前已有操作类型 来源 结果
    slectAllGET(){
      queryCriteriaV2()
      .then(callback=>{
        this.actionSelect = callback.data.action
        this.categorySelect = callback.data.category
        this.resultSelect = callback.data.result
      })
    },
    // 获取24小时内日志数据
    defaultLogGET() {
      // 当前时间戳
      let timestamp = Date.parse(new Date());
      // 24小时时间戳
      let basetime = 24 * 60 * 60 *1000;
      // 24小时前
      let yestoday0 = timestamp - basetime;
      let jieshu = getFormatTime(timestamp);
      let kaishi = getFormatTime(yestoday0);
      function getFormatTime(datetime) {
        let time = new Date(datetime)//时间戳为10位要*1000，13位不用*1000
        let year = time.getFullYear()
        let month = time.getMonth() + 1
        let date = time.getDate()
        let hours = time.getHours()
        let minute = time.getMinutes()
        let second = time.getSeconds()
        if (month < 10) { month = '0' + month }
        if (date < 10) { date = '0' + date }
        if (hours < 10) { hours = '0' + hours }
        if (minute < 10) { minute = '0' + minute }
        if (second < 10) { second = '0' + second }
        return year + '/' + month + '/' + date + ' ' + hours + ':' + minute + ':' +second
      }
      this.today =jieshu
      this.yesterday =kaishi
    },
     // 开始时间
    startTime(item) {
      let data = new Date(item).getTime();
      this.tablePageForm.exectime = item.replace(/-/g,"/")
      this.eventStampEXE=data
    },
    // 结束时间
    endTime(item) {
      let data = new Date(item).getTime();
      this.tablePageForm.end_date = item.replace(/-/g,"/")
      this.eventStampEND=data
    },
    // 时间选择
    intervalTime(time){
      let k =new Date(time[0]).getTime()
      let j =new Date(time[1]).getTime()
      this.tablePageForm.exectime = time[0].replace(/-/g,"/")
      this.eventStampEXE=k
      this.tablePageForm.end_date = time[1].replace(/-/g,"/")
      this.eventStampEND=j
    },
   // 当前分页
		onPageChange(item) {
      this.tablePageForm.page = item
      this.queryClick();
		},
		// 每页条数
		onPageSizeChange(item){
      this.$store.state.power.pagecount = item
      this.tablePageForm.pagecount = this.$store.state.power.pagecount
      this.tablePageForm.page = 1
      this.tableTotal = 0
      this.tablePageForm.search_str = ""
      this.queryClick();
		},
    // 表格排序按钮事件
		sortColumn(column, key, order) {
      if (column.key !== "normal") {
        this.tablePageForm.order_by = column.key;
        this.tablePageForm.order_type = column.order;
        this.tablePageForm.page = 1
        this.tableTotal = 0
        this.tablePageForm.search_str = ""
        this.queryClick();
      }
    },
    // 搜索
    tableSearchInput(){
      this.tablePageForm.page = 1
      this.tableTotal = 0
      this.queryClick();
    },
    // 查询按钮
    queryClick() {
      if(this.tablePageForm.times[0]==""){
        this.$Message.warning({
          background: true,
          closable: true,
          duration: 5,
          content: '请先选择时间在进行 查询操作',
        });
      }else {
        this.spinShow=true
        let datas = new Object()
        datas.action = typeof(this.tablePageForm.action) == 'undefined'?"":this.tablePageForm.action
        datas.role = !this.moduleShow('tripleManage')?'sysadm':typeof(this.tablePageForm.role) == 'undefined'?"":this.tablePageForm.role
        datas.result = typeof(this.tablePageForm.result) == 'undefined'?"":this.tablePageForm.result
        datas.username=this.tablePageForm.username
        datas.category=typeof(this.tablePageForm.category) == 'undefined'?"":this.tablePageForm.category
        datas.path =  ""
        datas.exectime = this.tablePageForm.exectime
        datas.end_date = this.tablePageForm.end_date
        datas.page = this.tablePageForm.page
        datas.pagecount = this.$store.state.power.pagecount
        datas.search_str = this.tablePageForm.search_str
        datas.order_type = this.tablePageForm.order_type
        datas.order_by = this.tablePageForm.order_by
        logDataQueryV2(datas)
        .then((callback) => {
          this.spinShow=false
          if(callback.data.hits==undefined){
            this.fileUrl = "#"
            this.journalData=new Array()
            this.$Message.warning({
              background: true,
              closable: true,
              duration: 5,
              content: '当前时间段没有可查询日志数据',
            });
          }else if(callback.data.hits.length>0){
            let fullURL = window.location.href; // http://*************:8009/#/system-setting
            let protocol = window.location.protocol; // http:
            let host = window.location.host; // *************:8009
            let hostname = window.location.hostname; // *************
            let port = window.location.port; // 8009
            this.fileUrl = protocol+"//"+hostname+"/thelog/v2/log/download?exectime="+this.tablePageForm.exectime+"&end_date="+this.tablePageForm.end_date
            this.tableTotal= window.gurl.architecture == "ARM"?callback.data.total.value:callback.data.total
            this.spinShow=false
            let arr = new Array();
            callback.data.hits.forEach(item =>{
              arr.push({
                exectime:item._source["timestamp"],
                category:item._source.category,
                description:item._source.description,
                action:item._source.action,
                result:item._source.result,
                role:item._source.role,
                username:item._source.username,
              })
            })
            this.journalData=arr
          }else {
            this.journalData=new Array()
          }
        })
        .catch((error) => {
          this.spinShow=false
        })
      }
    },
    // 导出表数据
    exportData() {
       function getFormatTime(datetime) {
        let time = new Date(datetime)//时间戳为10位要*1000，13位不用*1000
        let year = time.getFullYear()
        let month = time.getMonth() + 1
        let date = time.getDate()
        let hours = time.getHours()
        let minute = time.getMinutes()
        let second = time.getSeconds()
        if (month < 10) { month = '0' + month }
        if (date < 10) { date = '0' + date }
        if (hours < 10) { hours = '0' + hours }
        if (minute < 10) { minute = '0' + minute }
        if (second < 10) { second = '0' + second }
        return year + '-' + month + '-' + date + ' ' + hours + ':' + minute + ':' +second
      }
      let timestamp = Date.parse(new Date());
      this.$refs.table.exportCsv({
        filename: "日志数据"+getFormatTime(timestamp),
      });
    },
    // 获取24小时时间
    default_times(){
      // 当前时间戳
      let timestamp = Date.parse(new Date());
      // 24小时时间戳
      let basetime = 24 * 60 * 60 *1000;
      // 24小时前
      let yestoday = timestamp - basetime;
      let jieshu = getFormatTime(timestamp);
      let kaishi = getFormatTime(yestoday);
      function getFormatTime(datetime) {
        let time = new Date(datetime)//时间戳为10位要*1000，13位不用*1000
        let year = time.getFullYear()
        let month = time.getMonth() + 1
        let date = time.getDate()
        let hours = time.getHours()
        let minute = time.getMinutes()
        let second = time.getSeconds()
        if (month < 10) { month = '0' + month }
        if (date < 10) { date = '0' + date }
        if (hours < 10) { hours = '0' + hours }
        if (minute < 10) { minute = '0' + minute }
        if (second < 10) { second = '0' + second }
        return year + '-' + month + '-' + date + ' ' + hours + ':' + minute + ':' +second
      }
      this.tablePageForm.exectime = kaishi.replace(/-/g,"/")
      this.tablePageForm.end_date = jieshu.replace(/-/g,"/")
      this.tablePageForm.times = [kaishi,jieshu]
    },
    conversionTime(time){
      const date = new Date(time);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },


    // 模块显示隐藏判断
    moduleShow(item){
      return window.gurl.PageModule.indexOf(item) == -1?true:false
    },
  },
};
</script>