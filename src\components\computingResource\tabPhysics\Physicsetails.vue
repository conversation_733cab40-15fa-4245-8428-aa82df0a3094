<style lang="less">
@import "./physicsTab.less";
</style>
<template>
  <div>
    <!-- 主机详情抽屉 -->
    <Drawer width="50%" :title="drawerTitle" :closable="false" v-model="initialmodal">
      <ul class="host_drawer_basic">
        <li >
          <Button ghost type="info" shape="circle" icon="md-power" @click="actionPackage(drawerROW, 'start')" ></Button>
          <span>开机</span>
        </li>
        <li>
          <Button ghost type="error" shape="circle" icon="md-power" @click="actionPackage(drawerROW, 'stop')"></Button>
          <span>关机</span>
        </li>
        <li>
          <Button
            ghost
            type="warning"
            shape="circle"
            icon="md-refresh-circle"
            @click="actionPackage(drawerROW, 'reboot')"
          ></Button>
          <span>重启</span>
        </li>
        <li>
          <Button
            ghost
            type="primary"
            shape="circle"
            icon="ios-desktop"
            @click="actionPackage(drawerROW, 'getVNCConsole')"
          ></Button>
          <span>控制台</span>
        </li>
        <!-- <li>
          <Button
            ghost
            type="primary"
            shape="circle"
            icon="md-git-network"
          ></Button>
          <span>网络</span>
        </li>
        <li>
          <Button
            ghost
            type="success"
            shape="circle"
            icon="md-cloud-upload"
          ></Button>
          <span>存储</span>
        </li>
        <li>
          <Button ghost type="success" shape="circle" icon="ios-clock"></Button>
          <span>时间</span>
        </li> -->
      </ul>
      <div class="host_drawer_charts" style="border:none">
        <ul>
          <li>
            <p>主机详情</p>
            <div class="xq">
              <div v-for="(lable, i) in detailsKey" :key="i" class="divxq">
                <span class="lable">{{lable}}</span>
                <Tooltip :content="detailsValue[i]" placement="bottom-start">
                  <span class="key text_overflow">{{detailsValue[i]}}</span>
                </Tooltip >
              </div>
            </div>
          </li>
          <li>
            <div class="radio_area">
              <div class="radio_piece" v-for="item in radioData" @click="radioClick(item)">
                <span class="radio_title" :style="{color:selectRadio==item?'#121529':'#717379'}">{{item}}</span>
                <span class="radio_selected" :style="{background:selectRadio==item?'#fe6902':''}"></span>
              </div>
            </div>
            <Table :columns='tableCloumns' :data="tableData" height=200>
              <template v-slot:state="{row}">
                <span :style="{color:translationStatus('',row.state)}">{{ translationStatus('text',row.state) }}</span>
              </template>
            </Table>
          </li>
          <li v-for="item in detailsDta">
            <p>{{item.title}}</p>
            <lineTemplate :datas="item.datas" style="width:99%;height:400px"></lineTemplate>
          </li>
        </ul>
      </div>
    </Drawer>
  </div>
</template>
<script>
import {
  basicHostDetails, // 基本主机 详情
  hostMonitoringNET, // 主机监控 网络
  hostMonitoringCPU, // 主机监控 CPU
  hostMonitoringMEM, // 主机监控 内存
  fanCurrentSpeed, // 风扇速度
  temperatureCurrent, // 温度
  voltageCurrent, // 电压
} from '@/api/physics';
import lineTemplate from "@/components/public/Line.vue"
export default {
  components: {
    lineTemplate,
  },
  props: {
    detailstime:String,
    tableRow:Object,
  },
  
  watch: {
    detailstime(news){
      this.drawerTitle = this.tableRow.hypervisor_hostname+"详情"
      this.detailsEncapsulation(this.tableRow)
      this.chaxun()
      this.initialmodal = true
    }
  },
  data() {
    return {
      initialmodal:false,
      ids:"",
      drawerTitle:"详情",
      detailsKey:[],
      detailsValue:[],
      detailsDta:[
        {title:"网络使用情况",datas:null},
        {title:"CPU使用情况",datas:null},
        {title:"内存使用情况",datas:null},
      ],
      radioData: ['风扇','温度','电压'],
      selectRadio: '风扇',
      tableCloumns: [],
      tableData: [],
      fanData: [],
      temperatureData: [],
      voltageData: [],
    }
  },
  methods: {
    detailsEncapsulation(row){
      let key = ["主机名称：","主机ID：","主机IP：","电源状态：","可用状态：","虚拟机：","空闲内存：","已分配VCPU：","已使用内存：","物理CPU核心：","内存总量：","已分配内存："]
      this.detailsKey = key
      // 获取详情数据
      basicHostDetails({id:row.id})
      .then((callback) => {
        
        let arr = new Array()
        arr.push(callback.data.hypervisor_hostname)
        arr.push(callback.data.id)
        arr.push(callback.data.host_ip)
        arr.push(callback.data.state)
        arr.push(callback.data.status)
        arr.push(callback.data.running_vms+"台")

        arr.push(this.byteUnitConversion("",callback.data.free_ram_mb))

        arr.push(callback.data.vcpus_used+"核")
        arr.push(this.byteUnitConversion("",callback.data.memory_mb_used))
        arr.push(callback.data.vcpus+"核")

        arr.push(this.byteUnitConversion("",callback.data.memory_mb))
        arr.push(this.byteUnitConversion("",callback.data.allocation_ram))

        let homeSerialNumber = window.serial.hardwareSerialNumber
        homeSerialNumber.forEach(em=>{
          if(em.hostName == callback.data.hypervisor_hostname) {
            this.detailsKey.push('SN号：')
            arr.push(em.code)
          }
        })
        this.detailsValue = arr
         
        
      })
      // 网络使用情况
      hostMonitoringNET({ip:row.host_ip})
      .then(callback => {
        let arr = new Object()
        let value = new Array()
        value.push(
          { title:"上行", list:callback.data.u },
          { title:"下行", list:callback.data.d },
        )
        arr.time = callback.data.t
        arr.data = value
        arr.unit = "B"
        this.detailsDta[0].datas = arr
      })
      // CPU使用情况
      hostMonitoringCPU({ip:row.host_ip})
      .then((callback) => {
        let arr = new Object()
        let value = new Array()
        value.push(
          { title:"CPU使用率", list:callback.data.v },
        )
        arr.time = callback.data.t
        arr.data = value
        arr.unit = "%"
        this.detailsDta[1].datas = arr
      })
      // 内存使用情况
      hostMonitoringMEM({ip:row.host_ip})
      .then((callback) => {
        let arr = new Object()
        let value = new Array()
        value.push(
          { title:"内存使用率", list:callback.data.v },
        )
        arr.time = callback.data.t
        arr.data = value
        arr.unit = "%"
        this.detailsDta[2].datas = arr
      })
    },
    
    chaxun(){
      // let now = new Date();
      // let utcOffset = 8 * 60 * 60 * 1000; // 北京时间与UTC的时差
      // let currentBeijingTime = new Date(now.getTime() + utcOffset);
      // let twentyFourHoursAgo = new Date(currentBeijingTime.getTime() - 24 * 60 * 60 * 1000);
      // let formatTime = (date) => date.toISOString().slice(0, 19).replace('T', ' ');
      // let params={
      //   start: formatTime(twentyFourHoursAgo),
      //   end: formatTime(currentBeijingTime)
      // }
      fanCurrentSpeed().then((callback) => {
        callback.data.forEach(em=>{
          if(em.title == this.tableRow.host_ip) {
            this.fanData = em.list
            this.radioClick('风扇')
          }
        })
      })
      temperatureCurrent().then((callback) => {
        callback.data.data.forEach(em=>{
          if(em.title == this.tableRow.host_ip) {
            this.temperatureData = em.list
          }
        })
      })
      voltageCurrent().then((callback) => {
        callback.data.data.forEach(em=>{
          if(em.title == this.tableRow.host_ip) {
            this.voltageData = em.list
          }
        })
      })
    },
    // 单选显示数据
    radioClick(item){
      this.selectRadio = item
      if(item == '风扇') {
        this.tableCloumns = [
          { title: "名称", key: "name",align: 'center' },
          { title: "风扇转速", key: "volue",align: 'center' },
          { title: "风扇状态", key: "state",align: 'center',slot: 'state' },
        ]
        this.tableData = this.fanData
      }else if(item == '温度'){
        this.tableCloumns = [
          { title: "名称", key: "name",align: 'center' },
          { title: "温度(摄氏度)", key: "volue",align: 'center' },
        ]
        this.tableData = this.temperatureData
      }else if(item == '电压'){
        this.tableCloumns = [
          { title: "名称", key: "name",align: 'center' },
          { title: "电压", key: "volue",align: 'center' },
        ]
        this.tableData = this.voltageData
      }
    },
    translationStatus(unit,item){
      let text = '未知'
      let color = '#ccc'
      if(item == "0"){
        text = "正常"
        color = 'green'
      }else if(item == "1"){
        text = "警告"
        color = 'yellow'
      }else if(item == "2"){
        text =  "严重"
        color = 'red'
      }else{
        text = "未知"
        color = '#ccc'
      }
      if(unit=='text') {
        return text
      }else {
        return color
      }

    },
    // 字节+单位转换
    byteUnitConversion(type,size) {
      const units = ['MB','GB', 'TB', 'PB'];
      let unitIndex = 0;
      while (size >= 1024 && unitIndex < units.length - 1) {
          size /= 1024;
          unitIndex++;
      }
      if(type=="byte") {
        return Math.floor(size * 100) / 100 
      }else if(type=="unit") {
        return units[unitIndex] 
      }else {
        return Math.floor(size * 100) / 100 + ' ' + units[unitIndex];
      }
    },
  }
}
</script>