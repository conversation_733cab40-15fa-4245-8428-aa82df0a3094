<template>
  <div>
    <Modal
      v-model="model"
      width="600"
      :mask-closable="false"
    >
      <template #header><p><span style="color:green">{{vmRow.name}}</span>虚拟机绑定MAC地址</p></template>
      <Form :model="formItem" ref="formItem" :rules="ruleValidate"  :label-width="150">
        <FormItem label="当前IP">
          <Select v-model="formItem.oldMAC" :label-in-value="true" @on-change="nowIPChange">
            <Option
              v-for="item in currentIP"
              :value="item.mac"
              :key="item.mac"
              >{{ item.addr }}</Option
            >
          </Select>
        </FormItem>
        <FormItem label="当前MAC地址">
          <Input v-model="formItem.oldMAC" disabled></Input>
        </FormItem>
        <FormItem label="MAC新地址" prop="newMAC">
          <Input v-model="formItem.newMAC"></Input>
        </FormItem>
      </Form>
      <div slot="footer">
        <Button type="text" @click="model = false">取消</Button>
        <Button type="primary" @click="modelOK" :disabled="disabled">确认</Button>
      </div>
    </Modal>
  </div>
</template>
<script>
import {vmMACaddress} from '@/api/virtualMachine';
export default {
  props: {
    vmRow: Object,
    macTime: String,
  },
  watch: {
    macTime(news){
      let arr = new Array()
      this.vmRow.addresses.forEach(em=>{
        arr.push({
          mac:em['OS-EXT-IPS-MAC:mac_addr'],
          addr:em.addr
        })
      })
      this.currentIP = arr
      this.formItem.oldMAC = arr[0].mac
      this.formItem.oldIP = arr[0].addr
      this.formItem.vmid = this.vmRow.id
      this.formItem.vmName = this.vmRow.name
      this.model = true
      this.disabled = false
      this.$refs['formItem'].resetFields()
    }
    
  },
  data(){
    const propMAC = (rule, value, callback) => {
      let list = /^([0-9A-Fa-f]{2}[:]){5}([0-9A-Fa-f]{2})$/ 
      if(list.test(value)) {
        callback()
      }else {
        callback(new Error("请输入正确的MAC地址"))
      }
    };
    return {
      model:false,
      disabled:  false,
      rowips: [],
      currentIP: [], // 当前已有IP
      formItem: {
        oldMAC: '', // 当前IP选中
        oldIP: '', // 当前IP选中
        newMAC: '',
        vmid: '', // 虚拟机id
        vmName: '', // 虚拟机名称
      },
      ruleValidate: {
        newMAC: [
          { required: true, message: "必填项"},
          { validator: propMAC, trigger: "blur" },
        ],
      }
    }
  },
  methods: {
    // 当前ip
    nowIPChange(item){
      this.formItem.oldIP = item.label
      this.formItem.oldMAC = item.value;
    },
    // 修改IP
    modelOK(){
      this.$refs['formItem'].validate((valid) => {
        if(valid){
          this.disabled = true
          vmMACaddress({
            name: this.formItem.vmName,
            id: this.formItem.vmid,
            now_mac: this.formItem.oldMAC,
            new_mac: this.formItem.newMAC,
          })
          .then((callback)=>{
            if(callback.data.msg=='ok') {
              this.model=false
              this.$emit("return-ok",'虚拟机绑定MAC地址操作完成');
            }else{
              this.disabled = false
              this.$emit("return-error",callback.data.msg);
            }
          }).catch((error) => {
            this.disabled = false
            this.$emit("return-error",'虚拟机绑定MAC地址操作失败');
          })
        }else{
          this.disabled = false
        }
      })
    },
  }
}
</script>