<style>
  .qosBox .ivu-form .ivu-form-item-label {
    padding: 10px 1px 10px 0;
  }
</style>
<style scoped lang='less'>
.configBox {
  h2{
    border-bottom: 1px solid #ccc;
  }
  .stripBox {
    .ivu-form-item {
      margin-bottom: 0;
    }
  }
  h3{
    padding: 5px 0;
  }
}
</style>
<template>
  <div>
    <Modal
      v-model="blockModal"
      :loading="loading"
      width="1150"
      :title="dalogTitle"
      @on-ok="bockOK(dalogTitle)"
      @on-cancel="cancel"
      :mask-closable="false"
    >
      <div>
        <Form :model="formItem" :rules="ruleform" :label-width="100">
          <FormItem label="选择卷池"  v-if="dalogTitle=='添加块设备'">
            <Select v-model="formItem.imgType">
              <Option v-for="item in poolData" :value="item.value" :key="item.value">{{ item.value }}</Option>
            </Select>
          </FormItem>
          <FormItem label="名称" props="name">
            <Input v-model="formItem.name" ></Input>
        	</FormItem>
          <FormItem label="容量" >
            <InputNumber style="width:100%" :min='1' :max="2048" v-model="formItem.size" :formatter="value => `${value}GB`" :parser="value => value.replace('GB', '')"></InputNumber>
        	</FormItem>
        </Form>
      </div>
      <div class="configBox">
        <h2>高级配置+</h2>
        <div class="stripBox" v-if="dalogTitle=='添加块设备'">
          <h3>条带化</h3>
          <Form :model="formItem" :rules="ruleform" :label-width="100" inline>
            <FormItem label="分片大小">
              <Select v-model="formItem.imgType" style="width:200px">
                <Option v-for="item in sizesData" :value="item.value" :key="item.value">{{ item.value }}</Option>
              </Select>
            </FormItem>
            <FormItem label="条带大小" >
              <Select v-model="formItem.imgType" style="width:200px">
                <Option v-for="item in sizesData" :value="item.value" :key="item.value">{{ item.value }}</Option>
              </Select>
            </FormItem>
            <FormItem label="条带数">
              <InputNumber :min='1' style="width:200px" :max="2048" v-model="formItem.size" ></InputNumber>
            </FormItem>
          </Form>
        </div>
        <div class="qosBox">
          <h3>Qos</h3>
          <Form :model="formItem" :rules="ruleform" :label-width="90" inline>
            <FormItem label="总IOPS" >
              <InputNumber :min='1' :max="2048" v-model="formItem.size" ></InputNumber>
            </FormItem>
            <FormItem label="总速率" >
              <InputNumber :min='1' :max="2048" v-model="formItem.size" ></InputNumber>
            </FormItem>
            <FormItem label="读IOPS" >
              <InputNumber :min='1' :max="2048" v-model="formItem.size" ></InputNumber>
            </FormItem>
            <FormItem label="读速率" >
              <InputNumber :min='1' :max="2048" v-model="formItem.size" ></InputNumber>
            </FormItem>
            <FormItem label="写IOPS" >
              <InputNumber :min='1' :max="2048" v-model="formItem.size" ></InputNumber>
            </FormItem>
            <FormItem label="写速率" >
              <InputNumber :min='1' :max="2048" v-model="formItem.size" ></InputNumber>
            </FormItem>
          </Form>
          <Form :model="formItem" :rules="ruleform" :label-width="90" inline>
            <FormItem label="爆发总IOPS" >
              <InputNumber :min='1' :max="2048" v-model="formItem.size" ></InputNumber>
            </FormItem>
            <FormItem label="爆发总速率" >
              <InputNumber :min='1' :max="2048" v-model="formItem.size" ></InputNumber>
            </FormItem>
            <FormItem label="爆发读IOPS" >
              <InputNumber :min='1' :max="2048" v-model="formItem.size" ></InputNumber>
            </FormItem>
            <FormItem label="爆发读速率" >
              <InputNumber :min='1' :max="2048" v-model="formItem.size" ></InputNumber>
            </FormItem>
            <FormItem label="爆发写IOPS" >
              <InputNumber :min='1' :max="2048" v-model="formItem.size" ></InputNumber>
            </FormItem>
            <FormItem label="爆发写速率" >
              <InputNumber :min='1' :max="2048" v-model="formItem.size" ></InputNumber>
            </FormItem>
          </Form>
        </div>
      </div>
    </Modal>
  </div>
</template>
<script>
export default {
  props: {
    addModal: String,
  },
  watch: {
    addModal(value) {
      // this.poolGET()
      this.dalogTitle = value.split('/')[0]
      this.blockModal = true
    },
  },
  data() {
    const formname = (rule, value, callback) => {
      let list = /^[\u4e00-\u9fa5_a-zA-Z0-9@_.-]{2,32}$/;
      if (list.test(value)) {
        callback();
      } else {
        callback(new Error("请输入2-32个中文或字符（特殊字符可用@_.-）"));
      }
    };
    return {
      blockModal: false,
      loading:false,
      dalogTitle:"",
      ruleform: {
        name: [
          { required: true, message: "必填项" },
          { validator: formname, trigger: "change" },
          { validator: formname, trigger: "blur" },
        ],
      },

      formItem:{},
      poolData:[],
      sizesData:[
        {value:"4KiB"},
        {value:"8KiB"},
        {value:"16KiB"},
        {value:"32KiB"},
        {value:"64KiB"},
        {value:"128KiB"},
        {value:"512KiB"},
        {value:"1MiB"},
        {value:"2MiB"},
        {value:"4MiB"},
        {value:"8MiB"},
        {value:"16MiB"},
        {value:"32MiB"}
      ],
    };
  },
  methods:{
    // 获取池数据
    poolGET(){
      this.$axios.get("/thecephapi/api/pool?attrs=pool_name,type,application_metadata").then((callback) => {
        console.log('获取池数据',callback)
      })
    },
    bockOK(data){

    },
    cancel(){},
  },
};
</script>