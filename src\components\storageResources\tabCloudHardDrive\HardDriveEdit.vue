<template>
  <div>
    <Modal v-model="model" width="600" :mask-closable="false">
      <template #header><p>编辑云硬盘</p></template>
      <Form
        :model="formItem"
        ref="formItem"
        :rules="rulesForm"
        :label-width="120"
      >
        <FormItem label="当前云硬盘名称" >
          <Input v-model="tableRow.name" disabled ></Input>
        </FormItem>
        <FormItem label="云硬盘新名称" prop="name">
          <Input v-model="formItem.name" placeholder="请输入新的云硬盘名称"></Input>
        </FormItem>
      </Form>
      <div slot="footer">
        <Button type="text" @click="model = false">取消</Button>
        <Button type="primary" @click="modelOK" :disabled="disabled">确认</Button>
      </div>
    </Modal>
  </div>
</template>
<script>
import {
  cloudDiskModify, // 云硬盘 修改
} from '@/api/storage';
export default {
  props: {
    editTime: String,
    tableRow: Object,
  },
  watch: {
    editTime(news){
      this.$refs.formItem.resetFields();
      this.model = true
      this.disabled = false
    }
  },
  data(){
    const propName =(rule,value,callback)=>{
      let list = /^[\u4e00-\u9fa5_a-zA-Z0-9@_.-]{2,32}$/;
      if(list.test(value)){
        callback()
      }else{
        callback(new Error("2-32 个中文、英文、数字、特殊字符(@_.-)"));
      }
    }
    return {
      model: false,
      disabled: false,
      formItem: {
        name: '',
      },
      typeData: [],
      // 正则验证
      rulesForm: {
        name:[
          { required: true, message: '必填项', trigger: 'change' },
          { validator: propName, trigger:'change' }
        ],
      },
    }
  },
  methods: {
    // 确认事件
    modelOK() {
      this.$refs.formItem.validate((valid) => {
        if (valid) {
          this.disabled = true;
          cloudDiskModify({
            id: this.tableRow.id,
            name: this.formItem.name,
          }).then(callback=>{
            if(callback.data.msg !== "error"){
              this.$emit("return-ok",'编辑云硬盘操作完成');
              this.model = false;
            }else {
              this.$emit("return-error","编辑云硬盘操作失败");
              this.disabled = false;
            }
          })
          .catch((error) => {
            this.$emit("return-error",'编辑云硬盘操作失败');
            this.disabled = false;
          });
        }
      });
    },
  }
}
</script>
