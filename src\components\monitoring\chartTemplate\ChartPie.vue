<template>
<!-- 饼状图 -->
  <!-- 复制的line_zx -->
  <div ref="chart" style="width: 100%;height:100%;"></div>
</template>
<script>
  export default {
    // 接受父组件传值
    props: {
      datas: Object,
    },
    watch: {
      datas(value) {
        this.renderChart(value)
      },
    },
    mounted() {},
    methods: {
      renderChart(vls) {
        let chart = this.$echarts.init(this.$refs.chart);
        let color = ["#8B5CFF", "#00CA69"];
        const hexToRgba = (hex, opacity) => {
          let rgbaColor = "";
          let reg = /^#[\da-f]{6}$/i;
          if (reg.test(hex)) {
            rgbaColor = `rgba(${parseInt("0x" + hex.slice(1, 3))},${parseInt(
              "0x" + hex.slice(3, 5)
            )},${parseInt("0x" + hex.slice(5, 7))},${opacity})`;
          }
          return rgbaColor;
        };
        chart.setOption ({
          backgroundColor: "#fff",
          color: color,
          legend: {
            top: 20,
          },
          tooltip: {
            trigger: "axis",
            formatter: function (params) {
              let html = "";
              params.forEach((v) => {
                let nub = 0
                if(v.name<0){
                    nub = v.name *-1
                }
                html += `<div style="color: #666;font-size: 14px;line-height: 24px">
                        <span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${
                          color[v.componentIndex]
                        };"></span>
                        ${v.seriesName}${nub}  
                        <span style="color:${
                          color[v.componentIndex]
                        };font-weight:700;font-size: 18px;margin-left:5px">${
                  v.value
                }</span>
                        MB`;
              });
              return html;
            },
            extraCssText:
              "background: #fff; border-radius: 0;box-shadow: 0 0 3px rgba(0, 0, 0, 0.2);color: #333;",
            axisPointer: {
              type: "shadow",
              shadowStyle: {
                color: "#ffffff",
                shadowColor: "rgba(225,225,225,1)",
                shadowBlur: 5,
              },
            },
          },
          grid: {
            top: 100,
            containLabel: true,
          },
          xAxis: [
            {
              type: "category",
              boundaryGap: false,
              axisLabel: {
                formatter: "{value}",
                textStyle: {
                  color: "#333",
                },
              },
              axisLine: {
                lineStyle: {
                  color: "#D9D9D9",
                },
              },
              data: vls.time,
            },
          ],
          yAxis: [
            {
              type: "value",
              name: "单位（MB）",
              axisLabel: {
                textStyle: {
                  color: "#666",
                },
              },
              nameTextStyle: {
                color: "#666",
                fontSize: 12,
                lineHeight: 40,
              },
              // 分割线
              splitLine: {
                lineStyle: {
                  type: "dashed",
                  color: "#E9E9E9",
                },
              },
              axisLine: {
                show: false,
              },
              axisTick: {
                show: false,
              },
            },
          ],
          series: [
            {
              // name: "2018",
              name: "上行",
              type: "line",
              smooth: true,
              symbolSize: 8,
              zlevel: 3,
              lineStyle: {
                normal: {
                  color: color[0],
                  shadowBlur: 3,
                  shadowColor: hexToRgba(color[0], 0.5),
                  shadowOffsetY: 8,
                },
              },
              symbol: "circle", //数据交叉点样式
              areaStyle: {
                normal: {
                  color: new this.$echarts.graphic.LinearGradient(
                    0,
                    0,
                    0,
                    1,
                    [
                      {
                        offset: 0,
                        color: hexToRgba(color[0], 0.3),
                      },
                      {
                        offset: 1,
                        color: hexToRgba(color[0], 0.1),
                      },
                    ],
                    false
                  ),
                  shadowColor: hexToRgba(color[0], 0.1),
                  shadowBlur: 10,
                },
              },
              data: vls.up,
            },
            {
              name: "下行",
              type: "line",
              smooth: true,
              symbolSize: 8,
              zlevel: 3,
              lineStyle: {
                normal: {
                  color: color[1],
                  shadowBlur: 3,
                  shadowColor: hexToRgba(color[1], 0.5),
                  shadowOffsetY: 8,
                },
              },
              symbol: "circle", //数据交叉点样式 (实心点)
              areaStyle: {
                normal: {
                  color: new this.$echarts.graphic.LinearGradient(
                    0,
                    0,
                    0,
                    1,
                    [
                      {
                        offset: 0,
                        color: hexToRgba(color[1], 0.3),
                      },
                      {
                        offset: 1,
                        color: hexToRgba(color[1], 0.1),
                      },
                    ],
                    false
                  ),
                  shadowColor: hexToRgba(color[1], 0.1),
                  shadowBlur: 10,
                },
              },
              data: vls.down,
            },
          ],
        })
      }
    }
  }
</script>
