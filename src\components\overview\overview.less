.overview_page {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
  width: 100%;
  font-weight: 600;
  font-size: 12px;
  background: #f6f7fb;
  padding: 15px;

  .capacity {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    height: 20%;
    width: 100%;

    .capacity_mod {
      background-size: 100% 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 100%;
      width: 32.5%;
      font-weight: 600;
      padding: 0 3%;

      .quantity_module {
        p {
          text-align: left;
        }

        .measure_title {
          color: #666;
          font-size: 18px;
          font-weight: 400;
          display: flex;
          align-items: center;
        }

        .rate_title {
          color: #333;
          font-size: 36px;
        }

        .rate_title:hover {
          text-shadow: 2px 2px 3px #fff;
          cursor: pointer;
        }
      }

      >img {
        height: 60%;
      }
    }

    .physical_machine_img {
      background-image: url(../../assets/wljbg.png);
      background-size: 100% 100%;
    }

    .vm_img {
      background-image: url(../../assets/xnjbg.png);
      background-size: 100% 100%;
    }

    .storage_img {
      background-image: url(../../assets/rwsbg.png);
      background-size: 100% 100%;
    }
  }

  .utilization_rate {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    width: 100%;
    height: 37.5%;

    .circle {
      background: #fff;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      align-items: center;
      height: 100%;
      width: 32.5%;
      padding: 10px;
      /* border: 1px solid rgb(10, 10, 10); */
      overflow: hidden;
      border-radius: 10px;
      .titles {
        height: 25px;
        display: flex;
        justify-content: space-between;
        width: 100%;
        align-items: center;
        .help {
          border: 1px solid #ccc;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 20px;
          height: 20px;
          color:#ccc;
        }
        .help:hover {
          color: #a2f3d4;
          border-color: #a2f3d4;
        }
        >span {
          border-left: 4px solid #fb6129;
          padding-left: 10px;
          display: inline-block;
          color: #333;
          width: 100%;
          font-size: 14px;
          .ordinary {
            cursor: pointer;
            padding: 0 5px 2px 0;
            color: #fb6129;
            display: inline-block;
            background-image: url(../../assets/xh.png);
            background-size: 100% 100%;
          }

          .occupy {
            cursor: pointer;
            padding: 0 5px 2px 0;
          }

          .occupy:hover {
            color: #da4c18;
          }
        }
      }

      .rate_ring {
        display: flex;
        height: 100%;
        width: 100%;
        justify-content: space-between;
        align-items: center;
        padding: 0 24px;

        .rate_quantity {
          display: flex;
          justify-content: space-evenly;
          flex-direction: column;
          height: 80%;

          h3 {
            text-align: center;
            font-size: 24px;
            color: #333;
          }

          h3:hover {
            text-shadow: 2px 2px 3px #CCC;
            cursor: pointer;
          }

          p {
            text-align: center;
            font-size: 14px;
            color: #666;
            font-weight: 100;
          }
        }

        & .demo-Circle-custom {
          & h1 {
            color: #323232;
            font-size: 20px;
            margin-bottom: 10px;
            font-weight: 800;
          }

          & p {
            color: #7a7a7a;
            font-size: 14px;
            font-weight: 100;
          }
        }
      }
    }
  }

  .alert_reminder {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    width: 100%;
    height: 37.5%;

    .system_state {
      background: #fff;
      padding: 10px;
      height: 100%;
      width: 32.5%;
      overflow: hidden;
      border-radius: 10px;
      .titles {
          height: 25px;
          display: flex;
          justify-content: space-between;
          width: 100%;
          align-items: center;
      
          .help {
            border: 1px solid #ccc;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 20px;
            height: 20px;
            color: #ccc;
          }
      
          .help:hover {
            color: #a2f3d4;
            border-color: #a2f3d4;
          }

        >span {
          border-left: 4px solid #fb6129;
          padding-left: 10px;
          display: inline-block;
          color: #333;
          font-size: 14px;
        }
      }
      .systatus {
        padding-bottom: 30px;
        width: 90%;
        height: 100%;

        #system_dashboard {
          width: 100%;
          height: 100%;
        }
      }
    }

    .gaojing {
      background: #fff;
      padding: 10px;
      height: 100%;
      width: 66.25%;
      /* border: 1px solid rgb(3, 0, 0); */
      overflow: hidden;
      border-radius: 10px;
      .gaojing_title {
        >span {
          border-left: 4px solid #fb6129;
          color: #333;
          padding-left: 10px;
          font-size: 14px;
        }

        ul,
        li {
          padding: 0;
          margin: 0;
          list-style: none;
          float: right;
          padding-left: 15px;
          font-size: 15px;
          color: #323232;

          i.ivu-icon.ivu-icon-ios-notifications-outline {
            font-weight: 600;
          }
        }
      }
      .gaojing_content {
        padding: 5px;
        .ivu-table-row-highlight td,
        .gaojing .ivu-table-stripe .ivu-table-body tr.ivu-table-row-highlight:nth-child(2n) td,
        .ivu-table-stripe .ivu-table-fixed-body tr.ivu-table-row-highlight:nth-child(2n) td,
        tr.ivu-table-row-highlight.ivu-table-row-hover td {
          background-color: #fdcbb8 !important;
        }

        tr.ivu-table-row-hover td {
          background-color: #fff6f3 !important;
        }

        .odNumber td {
          background-color: #f3f8fd;
        }
      }
    }
  }
}