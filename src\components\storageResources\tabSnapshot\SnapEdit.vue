<template>
  <div>
    <Modal
      v-model="model"
      width="600"
      :mask-closable="false"
    >
      <template #header><p>编辑快照</p></template>
      <Form :model="formItem" ref="formItem" :rules="ruleValidate" :label-width="150">
        <FormItem label="当前快照名称">
          <Input
            v-model="tableRow.name"
            disabled
          ></Input>
        </FormItem>
        <FormItem label="新快照名称" prop="name">
          <Input
            v-model="formItem.name"
            placeholder="请输入新的快照名称"
          ></Input>
        </FormItem>
      </Form>
      <div slot="footer">
        <Button type="text" @click="model = false">取消</Button>
        <Button type="primary" @click="modelOK" :disabled="disabled">确认</Button>
      </div>
    </Modal>
  </div>
</template>
<script>
import {
  snapshotModify, // 快照 修改
} from '@/api/storage';

export default {
  props: {
    tableRow:Object,
    editTime:String,
  },
  watch: {
    editTime(news){
      this.formItem.id = this.tableRow.id
      this.model = true
      this.disabled = false
      this.$refs.formItem.resetFields()
    }
  },
  data(){
    const propName = (rule, value, callback) => {
      let list = /^[\u4e00-\u9fa5_a-zA-Z0-9@_.-]{2,32}$/;
      if (list.test(value)) {
        callback();
      } else {
        callback(new Error("2-32 个中文、英文、数字、特殊字符(@_.-"));
      }
    };
    return {
      model:false,
      disabled:false,
      formItem:{
        name: '',
        id: ''
      },
      ruleValidate:{
        name:[
          { required: true, message: '必填项', trigger: 'change' },
          { validator: propName, trigger: "change" },
        ]
      }
    }
  },
  methods: {
    // 确认事件
    modelOK(){
      this.$refs.formItem.validate((valid) => {
        if(valid){
          this.disabled = true
          snapshotModify({
            id: this.formItem.id,
            data: this.formItem.name
          })
          .then((callback) => {
            if(callback.data.msg == 'ok') {
              this.model=false
              this.$emit("return-ok",'编辑快照操作完成')
            }else {
              his.disabled = false
              this.$emit("return-error",'编辑快照操作失败');
            }
          }).catch((error) => {
            this.disabled = false
            this.$emit("return-error",'编辑快照操作失败');
          })
        }
      })
      
    },
  }
}
</script>