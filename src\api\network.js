// 网络
import axios from "axios";
import * as basic_proxy from "@/api/config";

// 网络
// 网络 查询
export async function networkTableQuery() {
  // return await axios.get(basic_proxy.theapi + "/v1/networks");
  // return await axios.get(basic_proxy.theapi + "/v2/networks");
  return await axios.get(basic_proxy.theapi + "/v3/networks");
}
// 网络 IP检测
export async function networkCheckip(params) {
  return await axios.post(basic_proxy.theapi + "/v1/instance/checkip", params);
}
// 网络 新建
export async function networkTableNewBuilt(params) {
  return await axios.post(
    basic_proxy.theapi + "/v1/create/adminnetworks",
    params
  );
}
// 网络探测
export async function netDetection(params) {
  return await axios.get(
    basic_proxy.theapi + "/v1/ping",
    {params: params}
  );
}
// 网络 修改
export async function networkTableModify(params) {
  // return await axios.post(basic_proxy.theapi + "/v1/networks/edit", params);
  return await axios.post(basic_proxy.theapi + "/v2/networks/edit", params);
}
// 网络 子网添加
export async function networkTableSubnetAdd(params) {
  return await axios.post(
    basic_proxy.theapi + "/v1/networks/create/subnet",
    params
  );
}
// 网络 子网修改
export async function networkTableSubnetModify(params) {
  return await axios.post(
    basic_proxy.theapi + "/v1/networks/edit/subnet",
    params
  );
}
// 网络 子网删除  
export async function networkTableSubnetRemove(params) {
  return await axios.post(
    basic_proxy.theapi + "/v1/networks/delete/subnet",
    params
  );
}

// 网络 删除
export async function networkTableDelete(params) {
  return await axios.delete(basic_proxy.theapi + "/v1/networks/delete", params);
}

// 拓扑
// 网络 拓扑网络查询
export async function networkTableTopologyQuery() {
  return await axios.get(basic_proxy.theapi + "/v1/networks/topology/work");
}
// 网络 拓扑网络节点
export async function networkTopologyNode() {
  return await axios.get(basic_proxy.theapi + "/v2/networks/topology/host");
}
// 网络 拓扑网络虚机
export async function networkTopologyVM(params) {
  return await axios.post(
    basic_proxy.theapi + "/v2/networks/topology/work",
    params
  );
}
// 网络 拓扑物理网络查询
export async function networkTablePhysicalTopologyQuery() {
  return await axios.get(basic_proxy.theapi + "/v1/networks/topology/manage");
}

// 安全组
// 安全组组查询
export async function securityGroupQuery() {
  return await axios.get(basic_proxy.theapi + "/v1/securitygroups/list");
}
// 安全组组新建
export async function securityGroupNewBuilt(params) {
  return await axios.post(
    basic_proxy.theapi + "/v1/securitygroups/create",
    params
  );
}
// 安全组表格编辑
export async function securityGroupModify(params) {
  return await axios.post(
    basic_proxy.theapi + "/v1/securitygroups/edit",
    params
  );
}
// 安全组表格删除
export async function securityGroupDelete(params) {
  return await axios.post(
    basic_proxy.theapi + "/v1/securitygroups/delete",
    params
  );
}
// 规则表格查询
export async function ruleTableQuery(params) {
  return await axios.post(
    basic_proxy.theapi + "/v1/securitygroups/detail",
    params
  );
}
// 规则表格新建
export async function ruleTableNewBuilt(params) {
  return await axios.post(
    basic_proxy.theapi + "/v1/securitygroups/rule/create",
    params
  );
}
// 规则表格删除
export async function ruleTableDelete(params) {
  return await axios.post(
    basic_proxy.theapi + "/v1/securitygroups/rule/delete",
    params
  );
}

// 物理网络
// 网络 物理网络查询
export async function networkTablePhysicalQuery() {
  return await axios.get(basic_proxy.theapi + "/v1/networks/physical");
}
// 网络 物理网络新建
export async function networkTablePhysicalNewBuilt(params) {
  return await axios.post(
    basic_proxy.theapi + "/v1/create/physical/networks",
    params
  );
}
// 网络 物理网络修改
export async function networkTablePhysicalModify(params) {
  return await axios.put(
    basic_proxy.theapi + "/v1/physical/networks/update",
    params
  );
}
// 网络 物理网络删除
export async function networkTablePhysicalDelete(params) {
  return await axios.delete(
    basic_proxy.theapi + "/v1/physical/networks/delete",
    params
  );
}
