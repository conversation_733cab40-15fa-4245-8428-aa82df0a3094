<template>
  <!-- 节点 -->
  <div>
    <div style="position:relative;">
      <Table :columns="nodeColumns" :data="nodeData"></Table>
      <Spin fix v-if="spinShow" size="large" style="color:#ef853a">
        <Icon type="ios-loading" size=50 class="demo-spin-icon-load"></Icon>
        <div style="font-size:16px;padding:20px">Loading...</div>
      </Spin>
    </div>
  </div>
</template>

<script>
  export default {
    props: {
      tabName: String,
    },
    watch: {
      tabName(value) {
        value=='jiedian'?this.MasterData():""
      },
    },
    data() {
      return{
        spinShow:false,
        // 卷池表数据
        nodeColumns:[
          // {type: 'selection',width: 30,align: 'center'},
				  { title: "存储节点",key: "hostname",sortable: true,width: 220 },
          { title: "版本",key: "ceph_version",sortable: true,align: "center",width: 220,
            render: (h, params) => {
              var pattern = /(\d+\.\d+\.\d+)/;
              var match = params.row.ceph_version.match(pattern);
              if (match && match.length > 1) {
                return h('span',match[1])
              }
            }
          },
          { title: "IP地址",key: "addr",sortable: true,align: "center",width: 220 },
				  { title: "运行的服务",key: "services",sortable: true,align: "center",
            render: (h, params) => {
              let list = new Array()

              if (params.row.services && params.row.services.length > 1) {
                params.row.services.forEach(em => {
                  list.push(em.type+'.'+em.id)
                });
                return h('span',list.join(","))
              }
            }
          },
				  // { title: "操作",key: "action",width:120,
          //   render: (h, params) => {
          //     return h("div", [
          //       h("Button", {
          //         style:{marginRight:'10px',color:'#356bfd'},
          //         on: {
          //           click: () => {
          //             this.tableOperationEvent(params.row,'restart')
          //           },
          //         },
          //       },"重启"),
          //        h("Button", {
          //          style:{color:'#ff6902'},
          //         on: {
          //           click: () => {
          //             this.tableOperationEvent(params.row,'close')
          //           },
          //         },
          //       },"关闭"),
          //     ])
          //   }
          // },
        ],
        nodeData:[],
      }
    },
    mounted(){
      // this.MasterData()
    },
    methods:{
      // 获取节点表数据
      MasterData() {
        this.spinShow = true
        this.$axios.get("/storage/v1/node").then(callback=>{
          this.nodeData = callback.data.data
          this.spinShow = false
        }).catch((error) => {
          // this.spinShow = false
        });
      },
      // 表格操作事件封装
      tableOperationEvent(row,action){
        switch (action) {
          case "restart":
            this.$Modal.confirm({
              title: "重启节点",
              content:
                '<p>是否重启名称为<span style="font-weight: 800;color:#000;word-wrap: break-word;">' +
                row.nodename +
                "</span>的节点？</p>",
              onOk: () => {
                // this.$axios.post('/thecss/v2/rebootNodeByHost?hostName=$'+row.hostname+'&usrname=$'+row.usrname,row).then((callback) => {
                this.$axios.post('/storage/v1/node',{action:action,storage_nodes:row.storage_nodes}).then((callback) => {
                }).catch((error) => {
                });
              },
              onCancel: () => {
                
              },
            });
          break;
          case "close":
            this.$Modal.confirm({
              title: "关闭节点",
              content:
                '<p>是否关闭名称为<span style="font-weight: 800;color:red;word-wrap: break-word;">' +
                row.nodename +
                "</span>的节点？</p>",
              onOk: () => {
                this.$axios.post('/storage/v1/node',{action:action,storage_nodes:row.storage_nodes}).then((callback) => {
                  console.log('关闭节点',callback)
                }).catch((error) => {
                  console.log('关闭节点失败','/thecss/v2/shutNodeByHost?hostName=$'+row.hostname+'&usrname=$'+row.usrname)
                });
              },
              onCancel: () => {
              },
            });
          break;
        }
      },
    },
  }
</script>