{"_from": "cookie@0.5.0", "_id": "cookie@0.5.0", "_inBundle": false, "_integrity": "sha512-YZ3GUyn/o8gfKJlnlX7g7xq4gyO6OSuhGPKaaGssGB2qgDUS0gPgtTvoyZLTt9Ab6dC4hfc9dV5arkvc/OCmrw==", "_location": "/cookie", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "cookie@0.5.0", "name": "cookie", "escapedName": "cookie", "rawSpec": "0.5.0", "saveSpec": null, "fetchSpec": "0.5.0"}, "_requiredBy": ["/express"], "_resolved": "https://registry.npmmirror.com/cookie/-/cookie-0.5.0.tgz", "_shasum": "d1f5d71adec6558c58f389987c366aa47e994f8b", "_spec": "cookie@0.5.0", "_where": "/root/docker/node_modules/express", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/jshttp/cookie/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "deprecated": false, "description": "HTTP server cookie parsing and serialization", "devDependencies": {"beautify-benchmark": "0.2.4", "benchmark": "2.1.4", "eslint": "7.32.0", "eslint-plugin-markdown": "2.2.1", "mocha": "9.2.2", "nyc": "15.1.0", "safe-buffer": "5.2.1", "top-sites": "1.1.97"}, "engines": {"node": ">= 0.6"}, "files": ["HISTORY.md", "LICENSE", "README.md", "SECURITY.md", "index.js"], "homepage": "https://github.com/jshttp/cookie#readme", "keywords": ["cookie", "cookies"], "license": "MIT", "name": "cookie", "repository": {"type": "git", "url": "git+https://github.com/jshttp/cookie.git"}, "scripts": {"bench": "node benchmark/index.js", "lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test", "update-bench": "node scripts/update-benchmark.js", "version": "node scripts/version-history.js && git add HISTORY.md"}, "version": "0.5.0"}