<style lang="less">
@import "../computingResource.less";
</style>
<style>
@keyframes move {
  0% {
    background-position: -28px 0;
  }
  100% {
    background-position: 0 0;
  }
}
.table_schedule {
  width: 100%;
  height: 15px;
  border-radius: 5px;
  border: 1px solid red;
  background-image: repeating-linear-gradient(
    -45deg,
    #f67f45,
    #f67f45 11px,
    #eee 10px,
    #eee 20px
  );
  background-size: 2000px 28px;
  animation: move 1s linear infinite;
}
</style>
<template>
  <div class="image_area">
    <Spin fix v-if="tableSpin" size="large" style="color: #ef853a">
      <Icon type="ios-loading" size="50" class="demo-spin-icon-load"></Icon>
      <div style="font-size: 16px; padding: 20px">Loading...</div>
    </Spin>
    <div class="table-button-area">
      <div>
        <Button class="plus_btn" @click="newClick" v-show="powerAcitons.xinjianjingxiang"
          ><span class="icon iconfont icon-plus-circle"></span> 新建镜像</Button
        >
        <Button class="close_btn" @click="deletClick(tableSelec)" v-show="powerAcitons.shanchujingxiang"
          ><span class="icon iconfont icon-close-circle"></span> 删除镜像</Button
        >
        <div>
          <!-- <Input v-model="tablePageForm.search_str" search enter-button placeholder="请输入名称" style="width: 300px" @on-search="tableVMsearchInput" /> -->
        </div>
      </div>
    </div>
    <div class="table_currency_area" v-show="powerAcitons.jingxiangliebiao">
      <Table
        :columns="tableColumn"
        :data="tableData"
        @on-sort-change="sortColumn"
        @on-selection-change="tableChange"
      >
        <!-- 大小 -->
        <template v-slot:size="{ row }">
          <span>{{ byteUnitConversion(row.size) }}</span>
        </template>
        <!-- 任务 -->
        <template v-slot:task="{ row }">
          <span v-if="row.status == 'active'">无</span>
          <div v-else class="table_schedule">
            {{ convertTask(row.task_state) }}
          </div>
        </template>
        <!-- 状态 -->
        <template v-slot:status="{ row }">
          <span>{{ row.status == "active" ? "可用" : "任务中" }}</span>
        </template>
        <!-- 导出镜像 -->
        <template v-slot:down_img="{ row }">
          <a :href="fileUrl" @click="downImg(row)"><Icon size=24 color="green" type="md-download" /></a>
        </template>
        <!-- 操作 -->
        <template v-slot:operation="{ row }">
          <Dropdown @on-click="dropdownClick($event, row)">
            <Button>配置 ▼</Button>
            <template #list>
              <DropdownMenu>
                <DropdownItem name="bjjx" v-show="powerAcitons.bianjijingxiang">编辑镜像</DropdownItem>
                <DropdownItem name="xtlx" v-show="powerAcitons.qiehuanxitongleixing">切换系统类型</DropdownItem>
                <DropdownItem name="yyp" v-show="powerAcitons.jingxiangchuangjianyunyingpan">创建云硬盘</DropdownItem>
                <!-- <DropdownItem name="gxjx">更新镜像</DropdownItem> -->
                <DropdownItem name="sc" v-show="powerAcitons.shanchujingxiang" style="color: red" divided
                  >删除</DropdownItem
                >
              </DropdownMenu>
            </template>
          </Dropdown>
        </template>
      </Table>
      <div class="pages" v-if="this.tableData.length > 0">
        <!-- <Pagination  :total="tableTotal" :page-size="tablePageForm.pagecount"  @page-change="onPageChange" @page-size-change="onPageSizeChange"/> -->
      </div>
    </div>
    <!-- 新建镜像弹框 -->
    <ImgNew
      :newTime="newTime"
      @return-ok="returnOK"
      @return-error="returnError"
    ></ImgNew>
    <!-- 编辑镜像 -->
    <ImgEdit
      :tableRow="tableRow"
      :editTime="editTime"
      @return-ok="returnOK"
      @return-error="returnError"
    ></ImgEdit>
    <!-- 创建云硬盘 -->
    <CloudDiskNew
      :tableRow="tableRow"
      :cloudDiskTime="cloudDiskTime"
      @return-ok="returnOK"
      @return-error="returnError"
    ></CloudDiskNew>
    <!-- 删除虚拟机 -->
    <ImgDelete
      :tableDelet="tableDelet"
      :deleteTime="deleteTime"
      @return-ok="returnOK"
      @return-error="returnError"
    ></ImgDelete>
  </div>
</template>
<script>
import { powerCodeQuery } from '@/api/other'; // 权限可用性 查询

import {
  imageTableQuery, // 镜像表 查询
  switcTypes, // 切换系统类型
} from "@/api/image";
import * as basic_proxy from "@/api/config";

import Pagination from "@/components/public/Pagination.vue";
import ImgNew from "./ImgNew.vue"; // 新建镜像
import ImgEdit from "./ImgEdit.vue"; // 编辑镜像
import CloudDiskNew from "./CloudDiskNew.vue"; // 新建云硬盘
import ImgDelete from "./ImgDelete.vue"; // 删除镜像
export default {
  components: {
    Pagination,
    ImgNew,
    ImgEdit,
    CloudDiskNew,
    ImgDelete,
  },
  props: {
    tabSelected: String,
  },
  data() {
    return {
      tableSpin: false,
      // 镜像表格数据
      tableColumn: [],
      tableData: [],
      tableSelec: [],
      tableTotal: 0,
      tablePageForm: {
        page: 1, // 当前页
        pagecount: this.$store.state.power.pagecount, // 每页条
        search_str: "", // 收索
        order_type: "desc", // 排序规则
        order_by: "", // 排序列
      },

      newTime: "", // 新建镜像
      tableRow: {},
      editTime: "", // 编辑镜像名称
      cloudDiskTime: "", // 新建云硬盘

      tableDelet: [],
      deleteTime: "", // 删除虚拟机
      // 计时器
      statuTimes: null,
      taskList: false,
      fileUrl:"#",

      powerAcitons: {}, // 操作权限数据
    };
  },
  updated() {
    this.tablePageForm.pagecount = this.$store.state.power.pagecount;
  },
  beforeDestroy() {
    clearInterval(this.statuTimes);
  },
  mounted() {
    if(this.$store.state.power.computingResourceTab == '镜像') {
      this.actionQuery()
    }
  },
  watch: {
    tabSelected(value) {
      if (value == "镜像") {
        this.actionQuery()
      } else {
        clearInterval(this.statuTimes);
      }
    },
    taskList(news) {
      if (news == true) {
        this.statuTimes = setInterval(() => {
          imageTableQuery().then((callback) => {
            this.tableData = callback.data;
            let tasknuber = 0;
            callback.data.forEach((em) => {
              if (em.status !== "active") {
                tasknuber++;
              }
            });
            if (tasknuber == 0) {
              clearInterval(this.statuTimes);
              this.taskList = false;
            }
          });
        }, 5000);
      }
    },
  },
  methods: {
    cloumnManage(){
      this.tableColumn = [
        { type: "selection", width: 30, align: "center" },
        { type: "expand", width: 50,
          render: (h, params) => {
            return h("div", { style: { width: "100%" } }, [
              h(
                "span",
                { style: { width: "45%", display: "inline-block" } },
                "ID：" + params.row.id
              ),
            ]);
          },
        },
        { title: "镜像名称", tooltip: true, key: "name", sortable: true },
        { title: "系统类型", key: "os_type", align: "center" },
        { title: "大小", key: "size", align: "center", sortable: true, slot: "size" },
        { title: "任务", key: "task", align: "center", slot: "task" },
        { title: "状态", key: "status", align: "center", slot: "status" },
        { title: "镜像格式", key: "disk_format", align: "center", sortable: true },
        ...(this.powerAcitons.daochujingxiang?[{ title: "导出镜像", key: "down_img", align: "center", slot: "down_img" }]:[]),
        ...(this.operationShow()?[{ title: "操作", key: "operation",width:120,slot: "operation" }]:[]),
      ]
    },
    // 当前分页
    onPageChange(item) {
      this.tablePageForm.page = item;
      this.imgTableData();
    },
    // 每页条数
    onPageSizeChange(item) {
      this.$store.state.power.pagecount = item;
      this.tablePageForm.pagecount = this.$store.state.power.pagecount;
      this.tablePageForm.page = 1;
      this.tableTotal = 0;
      this.tablePageForm.search_str = "";
      this.imgTableData();
    },
    // 虚拟机列表 列排序
    sortColumn(column, key, order) {
      if (column.key !== "normal") {
        this.tablePageForm.order_by = column.key;
        this.tablePageForm.order_type = column.order;
        this.tablePageForm.page = 1;
        this.tableTotal = 0;
        this.tablePageForm.search_str = "";
        this.imgTableData();
      }
    },
    // 搜索虚拟机列表
    tableVMsearchInput() {
      this.tablePageForm.page = 1;
      this.tableTotal = 0;
      this.imgTableData();
    },
    // 表格操作
    dropdownClick(event, row) {
      switch (event) {
        case "bjjx":
          this.tableRow = row;
          this.editTime = "" + new Date();
          break;
        case "xtlx":
          this.switchingSystemTypes(row);
          break;
        case "yyp":
          this.tableRow = row;
          this.cloudDiskTime = "" + new Date();
          break;
        case "gxjx":
          // 暂时未开发
          break;
        case "sc":
          this.deletClick([row]);
          break;
      }
    },
    // 切换系统类型
    switchingSystemTypes(row) {
      let type = row.os_type == "linux" ? "windows" : "linux";
      this.$Modal.confirm({
        title: row.name + "切换系统类型",
        content:
          '<p>是否将当前系统类型 <span style="font-style: italic;font-weight:100">' +
          row.os_type +
          '</span> 切换为 <span style="color:green;font-weight:800"> ' +
          type +
          "</span> ？</p>",
        onOk: () => {
          switcTypes({
            id: row.id,
            os_type: type,
            name: row.name,
          }).then((callback) => {
            if (callback.data.msg == "ok") {
              this.returnOK(row.name + "切换系统类型操作完成");
            } else {
              this.returnError(row.name + "切换系统类型操作失败");
            }
          });
        },
        onCancel: () => {},
      });
    },
    // 删除镜像
    deletClick(data) {
      if (data == 0) {
        this.$Message.warning({
          background: true,
          closable: true,
          duration: 5,
          content: "未选择表格数据",
        });
      } else {
        this.tableDelet = data;
        this.deleteTime = "" + new Date();
      }
    },
    // 导出镜像
    downImg(row){
      this.fileUrl = basic_proxy.upload+"/v1/images/download?id="+row.id+"&image_name="+row.name
    },
    // 单位转换字节到GB
    // 字节+单位转换
    byteUnitConversion(size) {
      if (size == undefined) {
        return "-";
      }
      const units = ["B", "KB", "MB", "GB", "TB", "PB"];
      let unitIndex = 0;
      while (size >= 1024 && unitIndex < units.length - 1) {
        size /= 1024;
        unitIndex++;
      }
      return Math.floor(size * 100) / 100 + " " + units[unitIndex];
    },
    // 镜像表格数据
    imgTableData() {
      this.tableSpin = true;
      imageTableQuery().then((callback) => {
        this.tableSpin = false;
        this.tableSelec = new Array();
        this.tableData = callback.data;
      });
    },
    // 表格选中数据
    tableChange(item) {
      this.tableSelec = item;
    },
    // 新建镜像 按钮点击
    newClick() {
      this.newTime = "" + new Date();
    },
    // 任务转换
    convertTask(item) {
      if (item) {
        this.taskList = true;
      }
    },
    // 子组件返回
    returnError(data) {
      this.$Message.error({
        background: true,
        closable: true,
        duration: 5,
        content: data,
      });
    },
    returnOK(data) {
      setTimeout(() => {
        this.imgTableData();
      }, 1000);
      this.$Message.success({
        background: true,
        closable: true,
        duration: 5,
        content: data,
      });
    },
    // 操作列
    operationShow(){
      let list = false
      if(this.powerAcitons.bianjijingxiang || this.powerAcitons.qiehuanxitongleixing || this.powerAcitons.jingxiangchuangjianyunyingpan || this.powerAcitons.shanchujingxiang){
        list = true
      }
      return list
    },
    // 操作权限获取
    actionQuery(){
      powerCodeQuery({
        module_code:[
          'jingxiangliebiao',
          'xinjianjingxiang',
          'bianjijingxiang',
          'shanchujingxiang',
          'qiehuanxitongleixing',
          'jingxiangchuangjianyunyingpan',
          'daochujingxiang',
          'jingxiangchaxun',
          'jingxiangfenpei',
        ]
      }).then(callback=>{
        this.powerAcitons = callback.data.data
        this.cloumnManage()
        this.powerAcitons.jingxiangliebiao?this.imgTableData():null
      })
    },
  },
};
</script>
