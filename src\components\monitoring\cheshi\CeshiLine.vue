<template>
  <div>
    <Button class="close_btn" @click="shuaxin">刷新</Button>
    <Button class="close_btn" @click="timeGet">时间</Button>
    <Select v-model="trendTime" style="width:150px" @on-change="timeONchange">
      <Option v-for="item in timeData" :value="item.value" :key="item.value">{{ item.label }}</Option>
    </Select>
    <ChartLine
      :datas="datas"
      :times="time"
      style="width: 700px; height: 400px"
    ></ChartLine>
    <div>过去时间：{{ oldTime }}</div>
    <div>当前时间：{{ newTime }}</div>
  </div>
</template>
<script>
import ChartLine from "../chartTemplate/ChartLine.vue";
export default {
  components: {
    ChartLine,
  },
  data() {
    return {
      datas: {},
      time:1,
      trendTime: 1,
      timeData:[{label:"最近1小时",value:1},{label:"最近24小时",value:24}],
      oldTime: "",
      newTime: "",
    };
  },
  methods: {
    shuaxin() {
      let list = {
        unit: "%",
        time: [
          "1:05",
          "1:10",
          "1:15",
          "1:20",
          "1:25",
          "1:30",
          "1:35",
          "1:40",
          "1:45",
          "1:50",
          "1:55",
          "2:00",
        ],
        data: [
          { title: "物理机1", list: [] },
          { title: "物理机2", list: [] },
        ],
      };
      for (var i = 0; i < 3; i++) {
        list.data[0].list.push(null);
      }
      for (var i = 0; i < 12; i++) {
        let a = Math.floor(Math.random() * 50) + 1;
        list.data[0].list.push(a);
      }
      for (var i = 0; i < 3; i++) {
        list.data[1].list.push(null);
      }
      for (var i = 0; i < 6; i++) {
        let a = Math.floor(Math.random() * 50) - 50;
        list.data[1].list.push(a);
      }
      for (var i = 0; i < 3; i++) {
        list.data[1].list.push(null);
      }
      this.datas = list;
    },

    timeGet(){
      this.timeONchange(this.trendTime)
    },
    timeONchange(time) {
      // 获取当前时间
      var currentDate = new Date();
      // 获取一小时前的时间
      var oneHourBefore = new Date(currentDate.getTime() - time*60 * 60 * 1000);
      // 获取年、月、日
      var year = currentDate.getFullYear();
      var month = ("0" + (currentDate.getMonth() + 1)).slice(-2); // 月份从0开始，所以要加1
      var day = ("0" + currentDate.getDate()).slice(-2);
      // 获取小时、分钟（当前时间）
      var hours = ("0" + currentDate.getHours()).slice(-2);
      var minutes = currentDate.getMinutes();
      // 调整分钟（当前时间）
      if (minutes % 10 < 5) {
        minutes = Math.floor(minutes / 10) * 10; // 当分钟尾数小于5时，取整为10的倍数
      } else {
        minutes = Math.ceil(minutes / 10) * 10 - 5; // 当分钟尾数大于6时，取整为10的倍数再减去5
      }
      // 格式化分钟（当前时间）
      minutes = ("0" + minutes).slice(-2);

      // 输出当前时间结果
      var formattedCurrentDateTime =
        year + "-" + month + "-" + day + " " + hours + ":" + minutes;
      this.newTime = formattedCurrentDateTime

      // 获取一小时前的年、月、日、时、分
      var beforeYear = oneHourBefore.getFullYear();
      var beforeMonth = ("0" + (oneHourBefore.getMonth() + 1)).slice(-2);
      var beforeDay = ("0" + oneHourBefore.getDate()).slice(-2);
      var beforeHours = ("0" + oneHourBefore.getHours()).slice(-2);
      var beforeMinutes = oneHourBefore.getMinutes();

      // 调整分钟（一小时前的时间）
      if (beforeMinutes % 10 < 5) {
        beforeMinutes = Math.floor(beforeMinutes / 10) * 10; // 当分钟尾数小于5时，取整为10的倍数
      } else {
        beforeMinutes = Math.ceil(beforeMinutes / 10) * 10 - 5; // 当分钟尾数大于6时，取整为10的倍数再减去5
      }

      // 格式化分钟（一小时前的时间）
      beforeMinutes = ("0" + beforeMinutes).slice(-2);

      // 输出一小时前的时间结果
      var formattedBeforeDateTime =
        beforeYear +
        "-" +
        beforeMonth +
        "-" +
        beforeDay +
        " " +
        beforeHours +
        ":" +
        beforeMinutes;
      this.oldTime = formattedBeforeDateTime
    },
  },
};
</script>