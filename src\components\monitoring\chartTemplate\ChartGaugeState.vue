<style lang="less" scoped>
.gauge_area {
  width: 100%;
  height: 100%;
}
</style>
<template>
  <!-- 折线图 -->
  <div class="gauge_area">
    <div ref="chartHealth" style="width: 100%; height: 100%"></div>
  </div>
</template>
<script>
export default {
  // 接受父组件传值
  props: {
    status: String,
  },
  watch: {
    status(value) {
      this.renderChart(value);
    },
    data() {
      return {
        chartdata: undefined,
      };
    },
  },
  mounted() {
    // this.renderChart("正常");
  },
  methods: {
    renderChart(vls) {
      let chartDom = this.$refs.chartHealth;
      let chart = this.$echarts.init(chartDom);
      // 存储健康状态
      // if()
      let healthStatus = {
        title: [
          {
            text: "{name|" + "健康状态：" + "}{status|" + vls + "}",
            bottom: "5%",
            left: "center",
            textStyle: {
              rich: {
                name: {
                  fontSize: "14",
                  color: "#333",
                },
                status: {
                  fontSize: "14",
                  color: vls == "正常" ? "#0caf1f" : "#f11e1e",
                },
              },
            },
            triggerEvent: true,
          },
        ],
        grid: [
          {
            show: false,
            backgroundColor: "rgba(0,0,0,0)",
            borderWidth: 0,
            y: "bottom",
          },
        ],
        series: [
          {
            type: "gauge",
            startAngle: 180,
            endAngle: 0,
            min: 0,
            max: 2,
            radius: "100%",
            center: ["50%", "75%"],
            axisLine: {
              show: true,
              lineStyle: {
                width: -15,
                shadowBlur: 2,
                color: [
                  [0, "transparent"],
                  [0.5, "#3367ff"],
                  [1, "#BCBEC2"],
                ],
              },
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              show: false,
            },
            splitLine: {
              show: false,
            },
            pointer: {
              width: "5%",
              length: "80%",
              color: "black",
            },
            itemStyle: {
              normal: {
                color: "#70b4f7",
                shadowBlur: 2,
              },
            },
            title: {
              show: false,
            },
            detail: {
              show: false,
            },
            data: [{ value: vls == "正常" ? 0.5 : 1.5, name: vls }],
          },
        ],
      };
      chart.setOption(healthStatus);
    },
  },
};
</script>
