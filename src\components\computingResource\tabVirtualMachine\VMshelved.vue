<template>
  <div>
    <Modal
      v-model="model"
      width="600"
      :mask-closable="false"
    >
      <template #header><p><span style="color:green">{{vmRow.name}}</span>虚拟机<span>{{vmRow.status == 'SHELVED_OFFLOADED'?'取消废弃':'废弃'}}</span></p></template>
      <p v-if="vmRow.status == 'SHELVED_OFFLOADED'">是否将虚拟机进行<span style="color:green;font-weight: 800;"> 取消废弃 </span>操作？取消废弃后虚拟机可正常使用。</p>
      <p v-else>是否将虚拟机进行<span style="color:red;font-weight: 800;"> 废弃 </span>操作？废弃的虚拟机将无法使用。</p>
      <div slot="footer">
        <Button type="text" @click="model = false">取消</Button>
        <Button type="primary" @click="modelOK" :disabled="disabled">确认</Button>
      </div>
    </Modal>
  </div>
</template>
<script>
import {
  vmAbandoned,vmCancelAbandonment
} from '@/api/virtualMachine';
export default {
  props: {
    vmRow:Object,
    shelvedTime:String,
  },
  watch: {
    shelvedTime(news){
      this.model = true
      this.disabled = false
    }
  },
  data(){
    return {
      model:false,
      disabled:false,
    }
  },
  methods: {
    // 确认事件
    modelOK(){
      if(this.vmRow.status == 'SHELVED_OFFLOADED') {
        vmCancelAbandonment({
          ids:[this.vmRow.id],
          names:[ this.vmRow.name]
        })
        .then((callback) => {
          this.model=false
          this.$emit("return-ok",'取消废弃虚拟机操作完成')
        })
        .catch((error) => {
          this.disabled = false
          this.$emit("return-error",'取消废弃虚拟机操作失败');
        })
      }else {
        vmAbandoned({
          ids:[this.vmRow.id],
          names:[ this.vmRow.name]
        })
        .then((callback) => {
          this.model=false
          this.$emit("return-ok",'废弃虚拟机操作完成')
        })
        .catch((error) => {
          this.disabled = false
          this.$emit("return-error",'废弃虚拟机操作失败');
        })
      }
    },
  }
}
</script>