<template>
  <div>
    <Modal v-model="model" width="800">
      <template #header><p><span style="color:green">{{vmRow.name}}</span>虚拟机分离云硬盘</p></template>
      <div style="position: relative">
        <!-- 局部加载装置 -->
        <Spin fix v-if="spinShow" size="large" style="color:#ef853a">
          <Icon type="ios-loading" size=50 class="demo-spin-icon-load"></Icon>
          <div style="font-size:16px;padding:20px">Loading...</div>
        </Spin>
        <Table :columns="tableColumn" :data="tableData">
          <template v-slot:operation="{row}">
            <Button :disabled='row.mountpoint == "/dev/vda"' ghost type="info" @click='separateClick(row)'>分离云硬盘</Button>
          </template>
        </Table>
      </div>
      <div slot="footer">
        <Button class="plus_btn" @click="model= false">关闭</Button>
      </div>
    </Modal>
  </div>
</template>
<script>
import {cloudDiskSeparate} from '@/api/storage'; // 云硬盘 分离
import {vmGroupTableSeparateClouddisc} from '@/api/virtualMachine'; // 分离云硬盘查询
export default {
  props: {
    vmRow: Object,
    unhookTime: String,
  },
  watch: {
    unhookTime(news){
      this.cloudHardDrive()
      this.model = true
    }
  },
  data(){
    return {
      spinShow: false,
      model: false,
      tableColumn: [
        { title: "名称", key: "name" },
        { title: "设备", key: "mountpoint", align: "center" },
        { title: "操作", key: "operation",width:120, slot: "operation"}
      ],
      tableData: [],
    }
  },
  methods: {
    // 查询云硬盘
    cloudHardDrive() {
      this.spinShow = true;
      vmGroupTableSeparateClouddisc({id:this.vmRow.id})
      .then(callback=>{
        this.spinShow = false;
        let arr = new Array()
        callback.data.attachment.forEach((itme,index)=>{
          arr.push({
            name: callback.data.mountname[index],
            mountpoint: callback.data.mountpoint[index],
            volumeid: callback.data.volume_id[index],
            vmid: callback.id,
          })
        })
        this.tableData = arr
      })
    },
    // 弹框确认
    separateClick(row){
      this.$Modal.confirm({
        title: "分离云硬盘",
        content:
          '<p>是否分离设备为<span style="font-weight: 600;color:red;" >' +
          row.mountpoint +
          "</span>的云硬盘?</p>",
        onOk: () => {
          this.spinShow = true;
          cloudDiskSeparate({
            volumeid: row.volumeid,
            volume_name: row.vmname,
            vmid: this.vmRow.id,
            vm_name: this.vmRow.name,
          }).then(em=>{
            this.cloudHardDrive()
          })
        },
        onCancel: () => {},
      });
    },
  }
}
</script>