<template>
  <div>
    <Modal
      v-model="model"
      width="600"
      :mask-closable="false"
    >
      <template #header><p>删除网络</p></template>
      <div style="padding: 5px">
        <span>是否删除下列网络？</span>
        <p style="color:red;word-wrap: break-word">{{tableNames.toString()}}</p>
      </div>
      <div slot="footer">
        <Button type="text" @click="model = false">取消</Button>
        <!-- <Button type="primary" :loading="disabled" @click="modelOK">
          <span v-if="!disabled">确认</span>
          <span v-else>删除中</span>
        </Button> -->
        <Button type="primary" @click="modelOK" :disabled="disabled">确认</Button>
      </div>
    </Modal>
  </div>
</template>
<script>
import { networkTableDelete } from "@/api/network";

export default {
  props: {
    tableDelete: Array,
    deleteTime: String,
  },
  watch: {
    deleteTime(news){
      this.tableNames = this.tableDelete.map(em=>{ return em.name})
      this.tableIDS = this.tableDelete.map(em=>{ return em.id})
      this.model = true
      this.disabled = false
    }
  },
  data(){
    return {
      model:false,
      disabled: false,
      tableNames: [],
      tableIDS: [],
    }
  },
  methods: {
    modelOK() {
      this.disabled = true
      for (let i = 0; i < this.tableNames.length; i++) {
        networkTableDelete({data:{
          id: this.tableIDS[i],
          name: this.tableNames[i],
        }})
        .then((callback) => {
          this.model = false;
          if(callback.data.msg == 'ok') {
            this.$emit("return-ok","网络：" + this.tableNames[i] + "删除操作完成");
          }else {
            this.$emit("return-error","网络：" + this.tableNames[i] + "删除操作失败");
          }
        })
        .catch((error) => {
          this.$emit("return-error","网络：" + this.tableNames[i] + "删除操作失败");
        })
      }
    }
  }
}
</script>