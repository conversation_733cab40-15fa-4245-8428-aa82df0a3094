.large_screen {
  width: 10rem;
  height: 5.625rem;
  background-image: url(../daping/BG.png);
  background-size: 10rem 5.625rem;
  background-repeat: no-repeat;
  background-size: cover;

  .screen_head {
    height: .4167rem;
    width: 10rem;
    background-image: url(../daping/DBbg.png);
    background-size: 10rem 5.625rem;
    background-repeat: no-repeat;
    background-size: cover;
    margin-bottom: .0625rem;

    ul {
      width: 10rem;
      height: .4167rem;
      display: flex;
      justify-content: space-evenly;

      li {
        width: 3.2813rem;
        height: 100%;
      }

      .screen_logo {
        display: flex;
        align-items: center;

        >img {
          width: .9219rem;
          height: .1823rem;
          margin-left: .0521rem;
        }
      }

      .project_name {
        display: flex;
        align-items: flex-end;
        justify-content: center;

        h4 {
          color: #fff;
          font-size: .1563rem;
          padding-bottom: .026rem;
          
        }
      }

      .system_time {
        display: flex;
        justify-content: flex-end;

        div {
          height: 100%;
          display: flex;
          flex-direction: column;
          justify-content: center;
          padding: 0 .0521rem;
        }
      }
    }
  }

  .screen_center {
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: space-evenly;

    .center_left {
      width: 2.3698rem;

      ul {
        height: 100%;
        color: #fff;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        li {
          width: 2.3698rem;
          height: 1.5938rem;
          margin-bottom: .1146rem;
          background-image: url(../daping/lRbg.png);
          background-size: 2.3698rem 1.5938rem;
          background-repeat: no-repeat;
        }
      }
    }

    /* 内容居中区域 */
    .centerMiddle {
      width: 4.875rem;

      ul {
        width: 4.875rem;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .middle_chart {
          width: 4.875rem;

          .machine_usage {
            width: 4.875rem;
            height: .7188rem;
            background-image: url(../daping/TBbg.png);
            background-size: 4.875rem .7188rem;
            background-repeat: no-repeat;
            color: #ddd;
            display: flex;
            justify-content: space-evenly;

            .machine_module {
              font-size: .0833rem;
              width: 2.1875rem;
              height: .7188rem;
              display: flex;
              align-items: center;

              img {
                height: .4479rem;
                margin-right: .2604rem;
              }

              .machine_quantity {
                width: 60%;
                height: 70%;
              }
            }
          }

          .dashboard_chart {
            width: 100%;
            height: 2.6979rem;
            background-image: url(../daping/keji1.gif);
            background-repeat: no-repeat;
            background-size: 100% 100%;
          }
        }

        .physical_machine_onitor {
          height: 1.5938rem;
          width: 4.875rem;
          padding: .0156rem;
          overflow: hidden;
          background-image: url(../daping/JKbg.png);
          background-size: 4.875rem 1.5938rem;
          background-repeat: no-repeat;
          color: #fff;

          .ivu-table {
            background: none;

            th {
              background: #141d3a00;
              color: #308dff;
              font-size: .0729rem;

              border-bottom: 0rem
            }

            td {
              background: #1f2d50;
              color: #fff;
              border: .0104rem solid #141d3a
                /* 背景 */
            }
          }

          .ivu-table:before {
            background: none;
          }

          .ivu-table-tbody tr:nth-child(odd) td:first-child {
            border-left: .0104rem solid #193580;
          }

          .ivu-table-tbody tr:nth-child(even) td:first-child {
            border-left: .0104rem solid #ff9400;
          }

          .ivu-progress-text {
            color: #fff;
          }
        }
      }
    }

    .center_right {
      width: 2.3698rem;

      ul {
        height: 100%;
        color: #fff;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        li {
          width: 2.3698rem;
          height: 1.5938rem;
          margin-bottom: .1146rem;
          background-image: url(../daping/lRbg.png);
          background-size: 2.3698rem 1.5938rem;
          background-repeat: no-repeat;

          .chart_monitor {
            width: 100%;
            height: 1.3542rem;

            .alarm_without_data {
              img{
                width: 1.1719rem;
                height: .8021rem;
              }
              font-size: .0938rem;
              width: 2.3698rem;
              height: 1.3333rem;
              display: flex;
              flex-direction: column;
              justify-content: space-around;
              align-items: center;
            }
          }
        }
      }
    }
  }

  /* 标题 */
  .module_title {
    font-size: .0938rem;
    line-height: .1979rem;
    padding-left: .1042rem;
  }

  /* 环 */
  .module_ring {
    height: 1.125rem;
    display: flex;
    align-items: center;
    justify-content: space-around;
    .ivu-chart-circle {
      width: 1.0417rem!important;
      height: 1.0417rem!important;
    }
  }

  /* 环 底部 */
  .module_usage {
    height: .2344rem;
    font-size: .0729rem;
    display: flex;
    justify-content: space-around;
  }

  .orange_usage {
    color: #ff9d00;
    font-size: .125rem;
    font-weight: 600;
  }

  .blue_usage {
    color: #308dff;
    font-size: .125rem;
    font-weight: 600;
  }
}