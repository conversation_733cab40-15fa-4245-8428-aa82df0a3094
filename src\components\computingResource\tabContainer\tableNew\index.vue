<template>
  <div>
    <Modal
      v-model="initialmodal"
      width="800"
      :mask-closable="false"
    >
      <p slot="header">
        <span>新建容器</span>
      </p>
      <div class="vm_new_dialog">
        <Menu theme="light" :active-name="activeNumber" @on-select="modalMenuItem">
          <Menu-item :name="i" v-for="(item, i) in menniuItemData" :key="i">
            <span>{{ item }}</span>
          </Menu-item>
        </Menu>
        <div class="modalcontent" v-if="initialmodal">
          <Basic v-show="activeNumber == 0" :times='time0' @returnOK='returnOK'></Basic>
          <Spec v-show="activeNumber == 1" :times='time1' @returnOK='returnOK'></Spec>
          <Volumes v-show="activeNumber == 2" :times='time2' @returnOK='returnOK'></Volumes>
          <Miscellaneous v-show="activeNumber == 3" :times='time3' @returnOK='returnOK'></Miscellaneous>
          <Labels v-show="activeNumber == 4" :times='time4' @returnOK='returnOK'></Labels>
        </div>
      </div>
      <div slot="footer">
        <Button v-if="activeNumber>0" type="primary" @click="activeNumber--">上一步</Button>
        <Button type="text" @click="initialmodal = false">取消</Button>
        <Button v-if="activeNumber<4" type="primary" @click="activeNumber++">下一步</Button>
        <Button v-if="activeNumber==4" type="primary" :disabled="basucType&&specType" @click="modalOK">确认</Button>
      </div>
    </Modal>
  </div>
</template>
<script>
import Basic from "./Basic.vue";
import Spec from "./Spec.vue";
import Volumes from "./Volumes.vue";
import Miscellaneous from "./Miscellaneous.vue";
import Labels from "./Labels.vue";
import {containerNew} from '@/api/container'; // 容器表 查询
export default {
  components: {
    Basic,
    Spec,
    Volumes,
    Miscellaneous,
    Labels,
  },
  props: {
    newTime: String,
  },
  watch: {
    newTime(news){
      this.initialmodal = true
      this.basucType = true
      this.specType = true
      this.activeNumber = 0
    },
    activeNumber(news) {
      if(news==4) {
        this.time0 = "" + new Date()
        this.time1 = "" + new Date()
        this.time2 = "" + new Date()
        this.time3 = "" + new Date()
      }
    }
    // activeNumber:{
    //   handler(news, old) {
    //     let carousel = document.querySelector("#lunbo");
    //     carousel.style.transform = `translateX(-${news * 600}px)`;
    //   },
    //   // deep:true,
    //   // immediate:true,
    // },
  },
  data(){
    return {
      initialmodal: false,
      basucType: true,
      specType: true,
      menniuItemData: ['基本信息','容器配置','数据盘','环境变量','标签'],
      activeNumber: 0,
      time0: '',
      time1: '',
      time2: '',
      time3: '',
      time4: '',
      time5: '',
      time6: '',
      time7: '',
      basicStatus: false,
      formItem: {},
    }
  },
  methods: {
    // 导航点击
    modalMenuItem(item) {
      this.activeNumber = item;
    },
    // 返回参数
    returnOK(data) {
      if(data.page == 0) {
        this.basucType = !data.type
        this.formItem['name'] = data.data.name
        this.formItem['image'] = data.data.imgName
        this.formItem['image_driver'] = data.data.driver
        this.formItem['image_pull_policy'] = data.data.policy // 占时不知道字段
        this.formItem['nets'] = {
          networks: [{uuid:data.data.netID,name:data.data.netName}]
        }
        this.formItem['command'] = data.data.command
        this.formItem['run'] = data.data.start
      }else if(data.page == 1) {
        this.specType = !data.type
        this.formItem['hostname'] = data.data.hostname
        this.formItem['runtime'] = data.data.runtime
        this.formItem['cpu'] = data.data.cpu
        this.formItem['memory'] = data.data.memory
        this.formItem['disk'] = data.data.disk
        // this.formItem['domain'] = data.data.domain // 占时不用
        this.formItem['restart_policy'] = {
          Name: data.data.restartPolicy,
          MaximumRetryCount: data.data.retry
        }
        this.formItem['auto_heal'] = data.data.heal
      }else if(data.page == 2) {
        this.formItem['mounts'] = data.data
      }else if(data.page == 3) {
        this.formItem['workdir'] = data.data.directory
        this.formItem['environment'] = data.data.variables
        this.formItem['interactive'] = data.data.interactive
      }else if(data.page == 4) {
        this.formItem['labels'] = data.data.labels
      }
    },
    modalOK() {
      this.time4 = "" + new Date()
      this.disabled = true
      containerNew(this.formItem)
      .then((callback) => {
        this.initialmodal = false;
        this.$emit("return-ok",{
          msg: '新建容器操作完成',
          type: 'ok'
        });
      })
      .catch((error) => {
        this.disabled = false
        this.$emit("return-ok",{
          msg: '新建容器操作失败',
          type: 'error'
        });
      })
    },
  }
}
</script>
<style scoped lang="less">
.vm_new_dialog {
  height: 500px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  // align-items: center;
  .ivu-menu-light {
    width: 160px!important;
    min-width: 160px!important;
    max-width: 160px!important;
  }
}
.modalcontent {
  width: calc(100% - 180px);
  border-left: 1px solid #ccc;
  height: 100%;
  overflow: hidden;
}
</style>
