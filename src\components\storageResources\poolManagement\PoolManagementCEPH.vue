<template>
  <!-- 卷池管理页面 -->
  <div class="pool-management">
    <div class="butss-wrap">
     <el-button type="primary" @click="createdHost" icon="el-icon-plus">创建</el-button>
     <!-- <el-button type="primary" @click="getPoolInfo" icon="el-icon-plus">shuaxin</el-button> -->
   </div>
   <div class="table-wrap" ref="getTableWrap">
     <el-table
      stripe
      ref="multipleTable"
      :data="tableData"
      v-loading="loading"
      element-loading-text="数据加载中..."
      :header-cell-style="{background:'#f7f3f0',color:'#101010'}"
      tooltip-effect="dark"
      style="width: 100%"
      @selection-change="handleSelectionChange">
      <el-table-column
        type="selection"
        width="55"
        v-if="false"
        align="center">
      </el-table-column>
      <el-table-column
        label="卷池名称"
        show-overflow-tooltip>
        <template slot-scope="scope">{{ scope.row.pool_name  }}</template>
      </el-table-column>
      <el-table-column
        prop="crush_rule"
        label="硬盘域">
      </el-table-column>
      <el-table-column
        prop="application_metadata_map"
        label="应用类型">
      </el-table-column>
      <el-table-column
        prop="type_map"
        label="安全类型">
      </el-table-column>
       <el-table-column
        prop="size"
        label="安全级别">
        <template slot-scope="scope">
          <div v-if="scope.row.erasure_code_profile">
            {{ scope.row.erasure_code_profile }}
          </div>
          <div v-else>
            {{ scope.row.size }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="quota_max_bytes"
        label="容量">
        <template slot-scope="scope">{{ scope.row.quota_max_bytes ? scope.row.quota_max_bytes : '不限制'  }}</template>
      </el-table-column>
      <!-- <el-table-column
        prop="name"
        label="健康状态">
        <template slot-scope="scope">
          <div class="health-state-wrap">
            <div class="border-radius-wrap mr-10"></div>
            <span>正常</span>
          </div>
        </template>
      </el-table-column> -->
      <el-table-column
        fixed="right"
        label="操作"
        width="130">
        <template slot-scope="scope">
          <div class="set-buts-wrap">
            <div class="icons-title-wrap" @click="editButs(scope.row)">
              <img src="@/assets/imager/editIcon.svg" alt="" class="min-editimgs-wrap">
              <span>编辑</span>
            </div>
            <div class="icons-title-wrap" @click="deleteButs(scope.row)">
              <img src="@/assets/imager/deleteIcon.svg" alt="">
              <span>删除</span>
            </div>
          </div>  
        </template>
      </el-table-column>
      <template slot="empty">
        <!-- <img src="@/assets/imager/no-datas.svg" alt="" class="mt-20"> -->
        <p class="no-datas-wrap">暂无数据</p>
      </template>
    </el-table>
    <!-- <pagination
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getTableList"
      /> -->
   </div>
    <!-- 新建弹框 -->
    <el-dialog
      :append-to-body="true"
      :title="`${selectType}卷池`"
      :visible.sync="dialogVisible"
      v-if="dialogVisible"
      :close-on-click-modal="false"
      :show-close="true"
      width="46%"
      >
      <div class="table-list-wrap" element-loading-text="加载中...">
        <el-form :label-position="labelPosition" label-width="100px" :model="formData" :rules="rules" ref="formData">
          <el-form-item label="卷池名称" prop="pool">
            <el-input v-model.trim="formData.pool" placeholder="请输入名称" clearable></el-input>
          </el-form-item>
          <el-form-item label="硬盘域" v-if="false">
            <el-select v-model="formData.crush_rule" placeholder="请选择硬盘域" clearable class="input-wrap">
              <el-option label="区域一" value="shanghai"></el-option>
              <el-option label="区域二" value="beijing"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="应用类型" prop="application_metadata[0]">
            <el-select v-model="formData.application_metadata[0]" placeholder="请选择应用类型" clearable class="input-wrap">
              <el-option
                v-for="item in application_metadataList"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="安全类型" prop="type" v-if="selectType !== operation.edit">
            <el-select v-model="formData.pool_type" placeholder="请选择安全类型" clearable class="input-wrap" >
               <el-option
                v-for="item in POOL_TYPE"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="安全级别" v-if="formData.pool_type !== 'erasurecode' && selectType !== operation.edit " prop="size" >
            <el-select v-model="formData.size" placeholder="请选择安全级别" clearable class="input-wrap">
              <el-option
                v-for="item in SECURITY_LEVEL"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="纠删码级别" v-if="formData.pool_type == 'erasurecode' && selectType !== operation.edit " prop="size">
            <el-select v-model="formData.erasure_code_profile" placeholder="请选择纠删码级别" clearable class="input-wrap">
              <el-option
                v-for="item in SECURITY_LEVEL"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="卷池大小(GB)">
            <!-- <el-input v-model="formData.quota_max_bytes" placeholder="请输入池大小" clearable></el-input> -->
             <el-input-number v-model="formData.quota_max_bytes" :controls="false"  controls-position="right" class="input-wrap"></el-input-number>
          </el-form-item>
          <el-form-item label="开启缓存层" v-if="false">
            <el-input v-model="formData.type" placeholder="请输入缓存层" clearable></el-input>
          </el-form-item>
          <el-form-item label="开启压缩">
            <el-select v-model="formData.compression_mode" placeholder="请选择压缩" clearable class="input-wrap">
              <el-option label="关闭" value="none"></el-option>
              <el-option label="被动" value="passive"></el-option>
              <el-option label="主动" value="aggressive"></el-option>
              <el-option label="强制" value="force"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="压缩算法"v-if="formData.compression_mode !== 'none'">
            <el-select v-model="formData.compression_algorithm" placeholder="请选择压缩算法" clearable class="input-wrap">
              <el-option
                v-for="item in COMPRESSION_ALGORITHMS"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="压缩块" v-if="formData.compression_mode  !== 'none'">
            <div class="row-input-wrap">
              <span style="width: 12%">最小</span>
              <el-input-number v-model="formData.compression_min_blob_size" :controls="false"  controls-position="right" style="width: 31%" placeholder="压缩块最小" clearable></el-input-number> KB
              <span style="width: 12%;padding-left:5px">最大</span>
              <el-input-number v-model="formData.compression_max_blob_size" :controls="false"  controls-position="right" style="width: 31%" placeholder="压缩块最大" clearable></el-input-number> KB
            </div>
          </el-form-item>
          <el-form-item label="压缩机别" v-if="formData.compression_mode  !== 'none'">
            <el-select v-model="formData.compression_required_ratio" placeholder="请选择压缩机别" clearable class="input-wrap">
               <el-option
                v-for="item in COMPRESSION_LEVEL"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="resetForm('formData')" icon="el-icon-circle-close">取消</el-button>
        <el-button type="primary" @click="submitForm('formData')" icon="el-icon-circle-check">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
  import {addPool , putPool, getPoolManagementList, getCreatedPoolInfo, delPoolManagement, APP_LICATION_METADATA, POOL_TYPE, SECURITY_LEVEL, ERASURE_CODE_PROFILE, COMPRESSION_ALGORITHMS, COMPRESSION_MODES, COMPRESSION_LEVEL} from '@/api/storageFacilities/PoolManagement'
  export default {
   data() {
     return{
      POOL_TYPE: POOL_TYPE,
      SECURITY_LEVEL: SECURITY_LEVEL,
      COMPRESSION_LEVEL: COMPRESSION_LEVEL,
      COMPRESSION_MODES: COMPRESSION_MODES,
      // ERASURE_CODE_PROFILE: ERASURE_CODE_PROFILE,
      APP_LICATION_METADATA: APP_LICATION_METADATA,
      COMPRESSION_ALGORITHMS: COMPRESSION_ALGORITHMS,
      dialogVisible: false,
      loading: false,
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10
      },
      selectType: '添加',
      operation: {
        add: '添加',
        edit: '编辑'
      },
      labelPosition: 'right',
      formData: {},
      oldFormData:{
        pool : '',
        erasure_code_profile: '',
        compression_required_ratio: '0.4',
        pool_type: 'replicated',
        quota_max_bytes: '',
        pg_autoscale_mode: 'on',
        pg_num: 2,
        size: 3,
        compression_mode: 'none',
        compression_algorithm: '',
        application_metadata: [''],
        compression_min_blob_size: 128,
        compression_max_blob_size: 512,
        compression_required_ratio: 0.4
      },
      application_metadataList: [
        {
          label: '块设备',
          value: 'rbd'
        },
        {
          label: '文件系统',
          value: 'cephfs'
        },
        {
          label: '对象网关',
          value: 'rgw'
        },
      ],
      tableHeight: null,
      tableData: [],
      editData: {},
      rules: {
        pool: [
          { required: true, message: '请输入卷池名称', trigger: 'blur' },
        ],
        'application_metadata[0]': [
            { required: true, message: '请选择类型', trigger: 'change' }
        ],
      },
      multipleSelection: [],
      createdPoolInfoData: {}
    }
    },
    mounted(){
      this.tableHeight = this.$refs.getTableWrap.clientHeight - 90
      this.getPoolInfo()
      this.getTableList()
    },
    methods:{
      editButs(data) {
        this.selectType = this.operation.edit
        this.formData = JSON.parse(JSON.stringify(data))
        this.dialogVisible = !this.dialogVisible
      },
      deleteButs(rowData) {
        this.$confirm('此操作将删除该卷池, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$axios.get("/thecephapi/api/pool/"+rowData.pool_name).then(res => {
            this.$message({message: '删除成功', type: 'success'});
            this.getTableList()
          })
        })
      },
      createdHost() {
        this.formData = JSON.parse(JSON.stringify(this.oldFormData))
        this.selectType = this.operation.add
        this.dialogVisible = !this.dialogVisible
      },
      handleSelectionChange(val) {
        this.multipleSelection = val;
      },
      resetForm(formName) {
        this.$refs[formName].resetFields();
        this.dialogVisible = false
      },
      async submitForm(formName) {
        this.$refs[formName].validate((valid) => {
          if (valid) {
            let indexOfFind = this.createdPoolInfoData.pool_names.indexOf(this.formData.pool)
            if (indexOfFind != -1) {
              this.$message.error('卷池名字已存在，请重新编辑')
              return false
            }
            const data = JSON.parse(JSON.stringify(this.formData))
            data.quota_max_bytes = data.quota_max_bytes * 1024*1024*1024
            data.compression_min_blob_size = data.compression_min_blob_size * 1024
            data.compression_max_blob_size = data.compression_max_blob_size * 1024
            if (this.selectType === this.operation.add) {
              this.$axios.post("/thecephapi/api/pool",data).then((res) => {
                this.$message({message: '添加成功', type: 'success'});
                this.dialogVisible = !this.dialogVisible;
                this.getTableList()
              })
            } else {
              const oldPoolName = data.pool_name
              const deletaData =  this.deletePutPoolObject(data)
              this.$axios.put("/thecephapi/api/pool/$"+oldPoolName,deletaData).then((res) => {
                this.$message({message: '修改成功', type: 'success'});
                this.dialogVisible = !this.dialogVisible;
                this.getTableList()
              })
            }
          } else {
            return false;
          }
        });
      },
      // 获取卷池名称  防止重复
      getPoolInfo() {
        this.$axios.get("/thecephapi/ui-api/pool/info").then(res => {
          this.createdPoolInfoData = res.data
        })
      },
      // 获取表格数据
      getTableList() {
        this.loading = !this.loading
        this.$axios.get("/thecephapi/api/pool").then(res => {
          this.tableData = res.data.map( item => {
            item.application_metadata_map = item.application_metadata[0] ? (APP_LICATION_METADATA.find( itemf => itemf.value == item.application_metadata[0]) ? APP_LICATION_METADATA.find( itemf => itemf.value == item.application_metadata[0]).name : '暂无') : ''
            // item.application_metadata_map = item.application_metadata[0]
            item.type_map = item.type ? POOL_TYPE.find( itemf => itemf.value == item.type) ? POOL_TYPE.find( itemf => itemf.value == item.type).name : '' : ''
            item.pool_type = item.type
            item.pool = item.pool_name
            item.quota_max_bytes = item.quota_max_bytes / (1024*1024*1024)
            item.compression_min_blob_size = item.options && item.options.compression_min_blob_size / 1024
            item.compression_max_blob_size = item.options && item.options.compression_max_blob_size / 1024
            item.compression_algorithm = item.options && item.options.compression_algorithm
            item.compression_mode = item.options && item.options.compression_mode
            item.compression_required_ratio = item.options && item.options.compression_required_ratio
            return item
          })
          this.total = res.data.length
          this.loading = !this.loading
          }
        );
      },
      reSelectEditData() {
        for (let item in this.formData) {
          if (item == 'chanQuanDengJiChange' || item == 'gongShangDengJiChange') {
            this.formData[item] = 0
          } else {
            this.formData[item] = null
          }
        }
      },
       deletePutPoolObject(data) {
        for(let obj in data) {
          if (
            obj !== 'pool' 
            && obj !== 'quota_max_bytes' 
            && obj !== 'quota_max_objects' 
            && obj !== 'application_metadata' 
            && obj !== 'compression_min_blob_size' 
            && obj !== 'compression_max_blob_size' 
            && obj !== 'compression_algorithm'
            && obj !== 'compression_mode'
            && obj !== 'compression_required_ratio'
          ) {
            delete data[obj]
          }
        }
        return  data
      }
    },
  }
</script>

<style scoped lang='less'>
  .pool-management {
    width: 100%;
    height: 100%;
    padding: 18px 18px 18px 18px;
    display: flex;
    background: #f2efed;
    flex-direction: column;
    .butss-wrap {
      width: 100%;
      margin-bottom: 20px;
    }
    .table-wrap {
      flex: 1 1 auto;
      height: 0;
      overflow-y: auto;
      padding: 18px 18px 0 18px;
      background: #fff;
      border-radius: 10px;
    }
    .health-state-wrap {
      display: flex;
      align-items: center;
      .border-radius-wrap {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background: #65e85b; // #d21717
      }
    }
    .set-buts-wrap {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      img {
        width: 19px;
        height: 18px;
        cursor: pointer;
        margin-right: 3px;
      }
      .icons-title-wrap {
        display: flex;
        align-items: center;
        cursor: pointer;
      }
    }
    .table-list-wrap {
      width: 95%;
    }
    .input-wrap {
      width: 100%;
    }
    .row-input-wrap {
      display: flex;
      align-items: center;
    }
  }
</style>