<style lang="less">
@import "./logmanage.less";
</style>
<template>
  <div class="log_tab">
    <Tabs class="tabs_template" name="1" v-model="tabName" @on-click="tabsClick">
      <TabPane
        v-if="item.show"
        v-for="item in tabsData"
        :key="item.name"
        :name="item.name"
        tab="1"
        :label="renderTabLabel(item.name)">
        <TabClusterAlarm v-if="item.name === '集群告警'" :tabName="tabName" />
        <TabStorageAlarm v-if="item.name === '存储告警'" :tabName="tabName" />
        <TabAlarmRules v-if="item.name === '告警规则'" :tabName="tabName" />
        <TabSystemJournal v-if="item.name === '日志旧'" :tabName="tabName" />
        <TabSystemJournalv2 v-if="item.name === '日志'" :tabName="tabName" />
      </TabPane>
    </Tabs>
  </div>
</template>
<script>
import { powerCodeQuery } from '@/api/other'; // 权限可用性 查询

import TabClusterAlarm from "./tabClusterAlarm/TabClusterAlarm.vue";
import TabStorageAlarm from "./tabStorageAlarm/TabStorageAlarm.vue";
import TabAlarmRules from "./tabAlarmRules/TabAlarmRules.vue";
import TabSystemJournal from "./tabSystemJournal/TabSystemJournal.vue";
import TabSystemJournalv2 from "./tabSystemJournal/TabSystemJournalv2.vue";

export default {
  components: {
    TabClusterAlarm,
    TabStorageAlarm,
    TabAlarmRules,
    TabSystemJournal,
    TabSystemJournalv2,
  },
  data() {
    return {
      tabName: '',
      tabsData: [
        { name: '集群告警', code: 'jiqungaojing', show: false },
        { name: '存储告警', code: 'cunchugaojing', show: false },
        { name: '告警规则', code: 'gaojingguize', show: false },
        { name: '日志', code: 'rizhi', show: false },
      ],
    }
  },
  watch: {
    '$store.state.power.logManagementTab'(news){
      this.tabName = news
    }
  },
  mounted(){
    this.tabsQuery()
  },
  
  methods: {
    renderTabLabel(item){
      return (h) => {
        return h('div', [
          h('span', {
            class: 'select_tab_border',
            style: { background: this.tabName === item ? '#fb6129' : '' }
          }),
          h('span', item)
        ]);
      };
    },
    // 查询标签页权限
    tabsQuery(){
      powerCodeQuery({
        module_code:[
          'jiqungaojing',
          'cunchugaojing',
          'gaojingguize',
          'rizhi'
        ]
      }).then(callback=>{
        this.tabsData.forEach(item => {
          if (callback.data.data.hasOwnProperty(item.code)) {
            item.show = callback.data.data[item.code];
          }
        });
        if(this.$store.state.power.logManagementTab == '集群告警') {
          this.tabName = '集群告警'
        }else if(this.$store.state.power.logManagementTab == '存储告警') {
          this.tabName = '存储告警'
        }else {
          if(callback.data.data.jiqungaojing) {
            this.tabName = '集群告警'
          }else if(callback.data.data.cunchugaojing) {
            this.tabName = '存储告警'
          }else if(callback.data.data.gaojingguize) {
            this.tabName = '告警规则'
          }else if(callback.data.data.rizhi) {
            this.tabName = '日志'
          }
          this.$store.state.power.logManagementTab = this.tabName
        }
      })
    },

    tabsClick(name){
      this.$store.state.power.logManagementTab = name
    },
  },
}
</script>
