<style lang="less">
@import "./computingResource.less";
</style>
<template>
  <div class="cmputing_resource_area">
    <Tabs class="tabs_template" name="1" v-model="tabName" @on-click="tabsClick">
      <TabPane
        v-if="item.show"
        v-for="item in tabsData"
        :key="item.name"
        :name="item.name"
        tab="1"
        :label="renderTabLabel(item.name)">
        <TabPhysics v-if="item.name === '物理资源'" :tabSelected="tabName" />
        <TabVirtualMachine v-if="item.name === '虚拟机'" :tabSelected="tabName"/>
        <TabImage v-if="item.name === '镜像'" :tabSelected="tabName" />
        <TabExternalCloud v-if="item.name === '多云纳管'" :tabSelected="tabName" />
        <TabRecycleBin v-if="item.name === '回收站'" :tabSelected="tabName" />
        <TabTask v-if="item.name === '任务'" :tabSelected="tabName" />
        <TabUSB v-if="item.name === 'USB分配'" :tabSelected="tabName" />
        <TabContainer v-if="item.name === '容器'" :tabSelected="tabName" />
        <TabImageContainer v-if="item.name === '容器镜像'" :tabSelected="tabName" />
      </TabPane>
      <!-- <TabPane name="容器" :label="renderTabLabel('容器')">
        <TabContainer v-if="moduleShow('rongqi')" :tabSelected="tabName"/>
      </TabPane>
      <TabPane name="容器镜像" :label="renderTabLabel('容器镜像')">
        <TabImageContainer v-if="moduleShow('rongqi')" :tabSelected="tabName"/>
      </TabPane> -->
      
    </Tabs>
  </div>
</template>
<script>
import { powerCodeQuery } from '@/api/other'; // 权限可用性 查询

import TabPhysics from "./tabPhysics/TabPhysics.vue";
import TabVirtualMachine from "./tabVirtualMachine/TabVirtualMachine.vue";
import TabImage from "./tabImage/TabImage.vue";
import TabExternalCloud from "./tabExternalCloud/TabExternalCloud.vue";
import TabRecycleBin from "./tabRecycleBin/TabRecycleBin.vue";
import TabTask from "./tabTask/TabTask.vue";
import TabUSB from "./tabUSB/TabUSB.vue";
import TabContainer from "./tabContainer/index.vue";
import TabImageContainer from "./tabImageContainer/index.vue";

export default {
  components: {
    TabPhysics,
    TabVirtualMachine,
    TabImage,
    TabExternalCloud,
    TabRecycleBin,
    TabTask,
    TabUSB,
    TabContainer,
    TabImageContainer,
  },
  data() {
    return {
      tabName: '',
      tabsData: [
        { name: '物理资源', code: 'wuliziyuan', show: false },
        { name: '虚拟机', code: 'xuniji', show: false },
        { name: '镜像', code: 'jingxiang', show: false },
        { name: '多云纳管', code: 'duoyunnaguan', show: false },
        { name: '回收站', code: 'huishouzhan', show: false },
        { name: '任务', code: 'renwu', show: false },
        { name: 'USB分配', code: 'usbfenpei', show: false },
        { name: '容器', code: 'rongqi', show: false },
        { name: '容器镜像', code: 'rongqijingxiang', show: false },
      ],
    };
  },
  watch: {
    '$store.state.power.computingResourceTab'(news){
      this.tabName = news
    }
  },
  mounted(){
    this.tabsQuery()
  },
  methods: {
    // 获取权限
    load() {
       if(document.cookie.length > 0) {
        let arr = document.cookie.split('; ');
        let arrData = new Object()
        arr.forEach(item=>{
          let li = item.split('=')
          arrData[li[0]]=li[1]
        })
        return arrData.role
      }
    },
    renderTabLabel(item){
      return (h) => {
        return h('div', [
          h('span', {
            class: 'select_tab_border',
            style: { background: this.tabName === item ? '#fb6129' : '' }
          }),
          h('span', item)
        ]);
      };
    },
    // 查询标签页权限
    // secadm
    tabsQuery(){
      powerCodeQuery({
        module_code:[
          'wuliziyuan',
          this.load()!=='secadm'?'xuniji':null,
          'jingxiang',
          'duoyunnaguan',
          'huishouzhan',
          'renwu',
          'usbfenpei',
        ]
      }).then(callback=>{
        this.tabsData.forEach(item => {
          if (callback.data.data.hasOwnProperty(item.code)) {
            item.show = callback.data.data[item.code];
          } else {
            item.show = this.moduleShow('rongqi')
          }
        });
        if(this.$store.state.power.computingResourceTab == '虚拟机') {
          this.tabName = '虚拟机'
        }else {
          if(callback.data.data.wuliziyuan) {
            this.tabName = '物理资源'
          }else if(callback.data.data.xuniji) {
            this.tabName = '虚拟机'
          }else if(callback.data.data.jingxiang) {
            this.tabName = '镜像'
          }else if(callback.data.data.duoyunnaguan) {
            this.tabName = '多云纳管'
          }else if(callback.data.data.huishouzhan) {
            this.tabName = '回收站'
          }else if(callback.data.data.renwu) {
            this.tabName = '任务'
          }else if(callback.data.data.usbfenpei) {
            this.tabName = 'USB分配'
          }
          this.$store.state.power.computingResourceTab = this.tabName
        }
      })
    },
    tabsClick(name){
      this.$store.state.power.computingResourceTab = name
    },
    // 模块显示隐藏判断
    moduleShow(item){
      return window.gurl.PageModule.indexOf(item) == -1?false:true
    },
  },
};
</script>
